export const statusList = [
  {
    label: '定界中',
    value: '0',
  },
  {
    label: '待锁定',
    value: '1',
  },
  {
    label: '已锁定',
    value: '2',
  },
  {
    label: '已闭环',
    value: '3',
  },
];
export interface ProblemHandleFilters {
  id: number | null;
  dtsNum: string;
  appName: string;
  status: string;
  owner: string;
  guarantor: string;
  level: string;
  dtsStatus: string;
  warnTime: [number, number] | null;
  planTime: [number, number] | null;
  levelList: [];
  statusList: [];
  isOverdue: [];
  overdueType: [];
  closeTime: null | [];
}
export const moduleList = [
  {
    label: '4796',
    value: '0',
  },
  {
    label: '垂域专精',
    value: '1',
  },
  {
    label: '长尾',
    value: '2',
  },
];
export const sourceList = [
  {
    label: 'FUT',
    value: '0',
  },
  {
    label: 'BetaClub',
    value: '1',
  },
  {
    label: '热线',
    value: '2',
  },
  {
    label: 'VOC',
    value: '3',
  },
  {
    label: 'VIP',
    value: '4',
  },
];
export const modelTemplate = {
  warnTime: new Date(),
  module: '',
  appName: '',
  desc: '',
  level: '',
  opinionVolume: '',
  status: '',
  progress: '',
  reviewResult: '',
  irStatus: '',
  planTime: new Date(),
  guarantor: '',
  owner: '',
  moduleOwner: '',
  product: '',
  dtsNum: null,
  informer: '',
  source: '',
  isOverdue: null,
  overdueType: '',
  closeTime: null,
};
export function formatDateTime(date, format) {
  date = new Date(date);
  const o = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12, // 小时
    'H+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
    a: date.getHours() < 12 ? '上午' : '下午', // 上午/下午
    A: date.getHours() < 12 ? 'AM' : 'PM', // AM/PM
  };
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      );
    }
  }
  return format;
}
export function getDatesBetween(startDate, endDate) {
  var dates = [];
  var currentDate = new Date(startDate);
  while (currentDate <= endDate) {
    dates.push(formatDateTime(currentDate, 'yyyy-MM-dd'));
    currentDate.setDate(currentDate.getDate() + 1);
  }
  return dates;
}

export function getDefaultProblemHandleFilters() {
  return {
    lastTestConclusionList: ['KCP5：测试中', 'KCP6：测试中', 'KCP7：测试中'],
    roundList: [],
    functionEnhancementPriorityList: [],
    label: [],
    isServiceCompanyUndertakes: '',
    dateType: '0',
    isOnlyAppId: '1',
    dateList: null,
    onlyTop2k:true
  };
}
export const testResList = [
  {
    label: 'KCP5：通过',
    value: 'KCP5：通过',
  },
  {
    label: 'KCP5：不通过',
    value: 'KCP5：不通过',
  },
  {
    label: 'KCP5：驳回',
    value: 'KCP5：驳回',
  },
  {
    label: 'KCP5：测试中',
    value: 'KCP5：测试中',
  },
  {
    label: 'KCP5：带风险通过',
    value: 'KCP5：带风险通过',
  },
  {
    label: 'KCP6：通过',
    value: 'KCP6：通过',
  },
  {
    label: 'KCP6：不通过',
    value: 'KCP6：不通过',
  },
  {
    label: 'KCP6：驳回',
    value: 'KCP6：驳回',
  },
  {
    label: 'KCP6：测试中',
    value: 'KCP6：测试中',
  },
  {
    label: 'KCP6：带风险通过',
    value: 'KCP6：带风险通过',
  },
  {
    label: 'KCP7：通过',
    value: 'KCP7：通过',
  },
  {
    label: 'KCP7：不通过',
    value: 'KCP7：不通过',
  },
  {
    label: 'KCP7：驳回',
    value: 'KCP7：驳回',
  },
  {
    label: 'KCP7：测试中',
    value: 'KCP7：测试中',
  },
  {
    label: 'KCP7：带风险通过',
    value: 'KCP7：带风险通过',
  },
];
export const getLastIndex = (index, names, types, data) => {
  const sameNameIndex = data.slice(index).findIndex((item) => {
    return names.some((name, i) => item[types[i]] !== name);
  });
  return sameNameIndex === -1 ? data.length : sameNameIndex;
};
export const functionEnhancementPriorityList = [
  {
    label: '高',
    value: '高',
  },
  {
    label: '中',
    value: '中',
  },
  {
    label: '其他',
    value: '其他',
  },
];
export const labelList = [
  {
    label: '4796',
    value: '4796',
  },
  {
    label: '生态丰富度5165',
    value: '生态丰富度5165',
  },
  {
    label: '垂域专精',
    value: '垂域专精',
  },
  {
    label: '心愿单',
    value: '心愿单',
  },
  {
    label: '企业内部办公应用',
    value: '企业内部办公应用',
  },
  {
    label: '头部互联网',
    value: '头部互联网',
  },
  {
    label: '区域重点',
    value: '区域重点',
  },
  {
    label: '行业总部',
    value: '行业总部',
  },
];
export const isServiceList = [
  {
    label: '是',
    value: '是',
  },
  {
    label: '否',
    value: '否',
  },
];
