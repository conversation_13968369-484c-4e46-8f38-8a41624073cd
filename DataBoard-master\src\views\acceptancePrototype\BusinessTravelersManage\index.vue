<template>
  <div ref="containerRef">
    <n-float-button
      v-if="showHelp"
      shape="circle"
      style="z-index: 9999; width: 44px; height: 44px"
      type="relative"
      position="fixed"
      bottom="40"
      right="20"
      menu-trigger="hover"
      v-model:show-menu="showMenu"
    >
      <n-icon :size="26">
        <HelpCircleOutline />
      </n-icon>
      <template #menu>
        <n-float-button
          shape="square"
          style="z-index: 9999; width: 80px; height: 44px; right: 40px"
          @click="hideMenu"
        >
          <template #description> 问题反馈 </template>
        </n-float-button>
      </template>
    </n-float-button>
    <n-tabs type="line" animated :value="tabsValue" :on-update:value="(value: string | number) => { tabsValue = value }">
      <n-tab-pane name="差旅信息统计" v-if="isAdmin" tab="差旅信息统计">
        <BarChart :leaderList="leaderList" />
      </n-tab-pane>
      <n-tab-pane name="差旅申请登记" tab="差旅申请登记">
        <InformationRegistration
          :leaderList="leaderList"
          :isAdmin="isAdmin"
          :currentUser="userInfo"
        />
      </n-tab-pane>
      <n-tab-pane name="差旅信息表" tab="差旅信息表">
        <BusinessTripInformation :leaderList="leaderList" />
      </n-tab-pane>
      <n-tab-pane name="差旅人员基本信息" v-if="isAdmin" tab="差旅人员基本信息">
        <UserBasicInformation :leaderList="leaderList" />
      </n-tab-pane>
    </n-tabs>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, ref } from 'vue';
  import { HelpCircleOutline } from '@vicons/ionicons5';
  import { useUserStore } from '@/store/modules/user';
  import BusinessTripInformation from './component/BusinessTripInformation.vue';
  import UserBasicInformation from './component/UserBasicInformation.vue';
  import InformationRegistration from './component/InformationRegistration.vue';
  import BarChart from './component/BarChart.vue';
  import { UserRoleEnum } from '@/enums/UserRoleEnum';
  import { getEmployeeList } from '@/api/system/usermanage';
  import { uniqBy } from 'lodash-es';
  const userInfo = useUserStore().getUserInfo;
  const isAdmin = ref(false);
  const leaderList = ref([]);
  const containerRef = ref();
  const showMenu = ref(false);
  const showHelp = ref(false);
  const tabsValue = ref('差旅信息统计');

  const initESpaceCtrl = () => {
    eSpaceCtrl.init();
    eSpaceCtrl.ready(function (data) {
      console.log('link success');
      showHelp.value = true;
    });
    eSpaceCtrl.error(() => {
      console.log('link error');
      showHelp.value = false;
    });
  };

  const hideMenu = () => {
    showMenu.value = false;
    eSpaceCtrl.showImDialog('l30035137', function (err) {
      console.log(err);
    });
  };

  onMounted(async () => {
    initESpaceCtrl();
    let { data } = await getEmployeeList();
    const leaderAccouts: string[] = []
    for (let i = 0; i < data.length; i++) {
      const item = data[i]
      if (item.leader && !leaderAccouts.includes(item.leader) && item.leaderName) {
        const leaderInfo = {
          value: item.leader,
          label: item.leaderName,
          email: data.filter((user) => user.account === item.leader)?.[0]?.email,
        }
        leaderAccouts.push(item.leader)
        leaderList.value.push(leaderInfo)
      }
    }
    isAdmin.value = leaderList.value.some((item) => item.value === userInfo.account);
    if (isAdmin.value) {
      tabsValue.value = '差旅信息统计';
    } else {
      tabsValue.value = '差旅申请登记';
    }
  });
</script>

<style lang="less" scoped>
  .layout-page-header {
    margin-top: 20px;
    .n-select {
      min-width: 250px;
    }
  }
  .unfold-icon {
    display: flex;
    align-items: center;
    height: 100%;
    margin-left: -3px;
  }
</style>
