pipeline {
    agent any

    environment {
        NODE_VERSION = '16.20.2'  // 使用您当前的 Node.js 版本
        PNPM_VERSION = '7.9.0'    // 指定 pnpm 版本
        NPM_CONFIG_PREFIX = "$HOME/.npm-global"
        PATH = "$HOME/.npm-global/bin:$PATH"  // 添加 pnpm 到 PATH
        CUSTOM_WORKSPACE = "/home/<USER>/4796admin-jenkins/"
        PROJECT_FOLDER = "4796admin"
        // 添加您的Outlook邮箱地址
        EMAIL_RECIPIENT = '<EMAIL>'
    }

    stages {
        stage('Setup Workspace') {
            steps {
                // 创建自定义工作空间目录
                sh "mkdir -p ${CUSTOM_WORKSPACE}/${PROJECT_FOLDER}"
                // 切换到自定义工作空间
                dir("${CUSTOM_WORKSPACE}/${PROJECT_FOLDER}") {
                    // 使用凭证克隆代码到自定义工作空间
                    sh "git --version"
                    sh "ssh -V"
                    sh "env | grep GIT"
                    git credentialsId: '7d60e6a2-efab-4297-8458-110b3fc2aa91', url: 'https://codehub-dg-g.huawei.com/s30052949/4796-admin.git', branch: 'master'
                }
            }
        }

        stage('Environment Setup') {
            steps {
                dir("${CUSTOM_WORKSPACE}/${PROJECT_FOLDER}") {
                    script {
                        // 显示 Node.js 和 npm 版本
                        sh "node -v"
                        sh "npm -v"
                        
                        // 设置 npm 配置
                        sh """
                            npm config set registry https://mirrors.tools.huawei.com/npm/
                            npm config set @ohos:registry https://repo.harmonyos.com/npm
                            npm config set strict-ssl false
                            npm config set sslVerify false
                        """
                        
                        // 设置 npm 全局安装路径并安装 pnpm
                        sh """
                            mkdir -p $HOME/.npm-global
                            npm config set prefix '$HOME/.npm-global'
                            npm install -g pnpm@${PNPM_VERSION}
                            export PATH=$HOME/.npm-global/bin:$PATH
                            pnpm -v
                        """
                        
                        // 为 pnpm 设置相同的配置并自动安装对等依赖
                        sh """
                            pnpm config set registry https://mirrors.tools.huawei.com/npm/
                            pnpm config set @ohos:registry https://repo.harmonyos.com/npm
                            pnpm config set strict-ssl false
                            pnpm config set sslVerify false
                            pnpm config set auto-install-peers true
                            pnpm config set strict-peer-dependencies false
                        """
                    }
                }
            }
        }

        stage('Dependencies') {
            steps {
                dir("${CUSTOM_WORKSPACE}/${PROJECT_FOLDER}") {
                    // 安装项目依赖并放宽���等依赖的严格要求
                    sh """
                        echo 'auto-install-peers=true' >> .npmrc
                        echo 'strict-peer-dependencies=false' >> .npmrc
                        pnpm install
                    """
                }
            }
        }

        stage('Build') {
            steps {
                dir("${CUSTOM_WORKSPACE}/${PROJECT_FOLDER}") {
                    // 构建项目
                    sh "pnpm run build"
                }
            }
        }

        // ... 其他阶段保持不变，但都需要包裹在 dir 步骤中
    }

    // post {
    //     always {
    //         script {
    //             // 发送邮件
    //             emailext body: "构建结果: ${currentBuild.result}\n\n详细信息请查看: ${env.BUILD_URL}",
    //                      subject: "Jenkins构建通知: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
    //                      to: "${EMAIL_RECIPIENT}"
    //         }
    //     }
    // }
}
