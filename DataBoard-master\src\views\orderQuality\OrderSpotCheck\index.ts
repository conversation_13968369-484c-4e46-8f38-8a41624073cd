import service from "@/utils/axios";

/**
 * 最高分
 */
export const HIGHEST_SCORE = 10;

/**
 * 答复规范&跟踪工单规范  沟通/效率规范  内部流程规范  分隔符号
 */
export const SYMBOL = '|';

/**
 * 筛选form
 */
export const filterForm = {
  severityLevel: '', // 严重级别
  type: '', // 类型
  title: '', // 标题
  projectName: '', // 项目名称
  group: '', // 分组
  issueCategory: '', // 问题分类
  field: '', // 专家领域
  highestOperationLevel: '', // 最高运营级别
  firstLevelOperation: '', // 一级运营
  secondLevelOperation: '', // 二级运营
  id: '', // ID号
  detailLink: '', // 详情超链接
  time: null, // 抽检日期
  inspectionLeader: '', // 抽检组长
  inspector: '', // 抽检人
  closedIrKnowledgeId: '', // 闭环IR单的知识编号
  knowledgeCategory: '', // 知识分类
  responseNorm: [], // 答复规范&跟踪工单规范
  communicationEfficiencyNorm: [], // 沟通/效率规范
  internalProcessNorm: [], // 内部流程规范
  responsiblePerson: '', // 问题责任人名/工号
  remarks: '', // 备注
  clarificationRequired: '', // 是否澄清
  clarificationReason: '', // 澄清理由说明
  correctionPlan: '', // 整改方案
  samplingCompletion: '', // 抽检完成情况
  totalScore: '', // 工单总分
};

/**
 * 分组option
 */
export const GROUP_OPTION = [
  { label: '1组', value: '1组' },
  { label: '2组', value: '2组' },
  { label: '3组', value: '3组' },
  { label: '4组', value: '4组' },
  { label: '5组', value: '5组' },
];

/**
 * 是否澄清option
 */
export const CLARIFIED_OR_NOT_OPTION = [
  { label: '是', value: '是' },
  { label: '否', value: '否' },
];

/**
 * 严重级别option
 */
export const SEVERITY_LEVEL_OPTION = [
  { label: '致命', value: '致命' },
  { label: '严重', value: '严重' },
  { label: '一般', value: '一般' },
  { label: '提示', value: '提示' },
];

/**
 * 类型option
 */
export const TYPE_OPTION = [
  { label: 'Bug', value: 'Bug' },
  { label: '需求', value: '需求' },
  { label: '漏洞', value: '漏洞' },
  { label: '咨询', value: '咨询' },
];

/**
 * 抽检完成情况option
 */
export const INSPECTION_COMPLETION_STATUS_OPTION = [
  { label: '已抽检', value: '已抽检' },
  { label: '待抽检', value: '待抽检' },
];

/**
 * 抽检结果option
 */
export const INSPECTION_RESULTS_OPTION = [
  { label: '10分', value: '10' },
  { label: '问题工单 <10分', value: '问题工单' },
];

/**
 * 答复规范&跟踪工单规范option
 */
export const REPLY_SPECIFICATIONS_OPTION = [
  { label: '不涉及', value: '不涉及', score: 0 },
  {
    label:
      '1.1：禁止答复敏感违规信息、内部信息（包括但不仅限于工号、非对应项目信息、内部DTS/需求单号等）',
    value:
      '1.1：禁止答复敏感违规信息、内部信息（包括但不仅限于工号、非对应项目信息、内部DTS/需求单号等）',
    score: 5,
  },
  {
    label: '1.2：禁止答非所问',
    value: '1.2：禁止答非所问',
    score: 4,
  },
  {
    label:
      '1.3：禁止最终提供的解决方案为错误方案，未实际解决伙伴问题（伙伴未回复关键信息导致的自动关单不算问题项）',
    value:
      '1.3：禁止最终提供的解决方案为错误方案，未实际解决伙伴问题（伙伴未回复关键信息导致的自动关单不算问题项）',
    score: 10,
  },
  {
    label: '1.4：禁止解决方案表述不清，解答逻辑模糊',
    value: '1.4：禁止解决方案表述不清，解答逻辑模糊',
    score: 4,
  },
  {
    label: '1.5：禁止回复出现 “原生”、“纯血”、“单框架”、“双框架”等字样',
    value: '1.5：禁止回复出现 “原生”、“纯血”、“单框架”、“双框架”等字样',
    score: 5,
  },
  {
    label: '1.6：禁止L1对外答复的内容未经过知识库沉淀；答复后可以将对应知识链接粘贴在内部留言当中',
    value: '1.6：禁止L1对外答复的内容未经过知识库沉淀；答复后可以将对应知识链接粘贴在内部留言当中',
    score: 5,
  },
  {
    label:
      '4.1：禁止未及时回复伙伴消息而导致自动关单（关单后伙伴再次提问的不算问题项，可引导伙伴再次提单）',
    value:
      '4.1：禁止未及时回复伙伴消息而导致自动关单（关单后伙伴再次提问的不算问题项，可引导伙伴再次提单）',
    score: 10,
  },
  {
    label: '4.2：禁止伙伴提出升级为需求单的工单未及时进行跟踪处理而导致工单关闭',
    value: '4.2：禁止伙伴提出升级为需求单的工单未及时进行跟踪处理而导致工单关闭',
    score: 5,
  },
];

/**
 * 沟通/效率规范option
 */
export const COMMUNICATION_REGULATIONS_OPTION = [
  { label: '不涉及', value: '不涉及', score: 0 },
  {
    label: '2.1：禁止询问伙伴已在工单中提前告知的工单相关信息',
    value: '2.1：禁止询问伙伴已在工单中提前告知的工单相关信息',
    score: 5,
  },
  {
    label: '1.2：禁止答非所问',
    value: '1.2：禁止答非所问',
    score: 5,
  },
  {
    label: '2.2：禁止对可一次性询问清的问题信息进行反复询问',
    value: '2.2：禁止对可一次性询问清的问题信息进行反复询问',
    score: 4,
  },
  {
    label: '2.3：禁止询问与解决工单无关联的问题',
    value: '2.3：禁止询问与解决工单无关联的问题',
    score: 5,
  },
  {
    label: '2.4：禁止不需要demo复现的场景询问提供复现demo',
    value: '2.4：禁止不需要demo复现的场景询问提供复现demo',
    score: 10,
  },
  {
    label: '3.2：禁止直接回复伙伴不提供或者无该类demo',
    value: '3.2：禁止直接回复伙伴不提供或者无该类demo',
    score: 5,
  },
];

/**
 * 内部流程规范option
 */
export const INTERNAL_PROCESS_SPECIFICATIONS_OPTION = [
  { label: '不涉及', value: '不涉及', score: 0 },
  {
    label:
      '5.1：禁止需求单和BUG单在与三线确认定级（优先级、严重级别）后，未回复到内部评论区并更新工单状态',
    value:
      '5.1：禁止需求单和BUG单在与三线确认定级（优先级、严重级别）后，未回复到内部评论区并更新工单状态',
    score: 5,
  },
  {
    label: '5.2：禁止未处理直接透传到下一级：必须在内部留言区发言记录分析过程',
    value: '5.2：禁止未处理直接透传到下一级：必须在内部留言区发言记录分析过程',
    score: 5,
  },
  {
    label: '5.3：禁止外部沟通线上会议输出的会议纪要未同步到外部评论区',
    value: '5.3：禁止外部沟通线上会议输出的会议纪要未同步到外部评论区',
    score: 5,
  },
  {
    label: '5.4：避免在一个工单中跟踪多个问题，特别是咨询问题和需求/Bug一起跟踪',
    value: '5.4：避免在一个工单中跟踪多个问题，特别是咨询问题和需求/Bug一起跟踪',
    score: 4,
  },
  {
    label:
      '5.5：禁止未输出【问题根因分析】与【解决方案】导致的系统自动关单（例外情况：伙伴不提供关键信息导致的自动关单）（注：目前优先提高工单回复质量，按知识库答复标准流程处理，此条暂不执行，由运营人员推动解决长期未闭环工单）',
    value:
      '5.5：禁止未输出【问题根因分析】与【解决方案】导致的系统自动关单（例外情况：伙伴不提供关键信息导致的自动关单）（注：目前优先提高工单回复质量，按知识库答复标准流程处理，此条暂不执行，由运营人员推动解决长期未闭环工单）',
    score: 5,
  },
];

/**
 * 下载导入模板
 */
export const downloadTemplateService = () => {
  return service({
    url: '/IRSampling/downloadExcel ',
    method: 'get',
    responseType: 'blob',
  });
};

/**
 * 导入
 */
export const importData = (data) => {
  return service({
    url: '/IRSampling/importExcel',
    method: 'post',
    data,
  });
};

/**
 * 查询
 */
export const serachList = (data) => {
  return service({
    url: '/IRSampling/queryByCondition',
    method: 'post',
    data,
  });
};

/**
 * 编辑
 */
export const editService = (data) => {
  return service({
    url: '/IRSampling/edit',
    method: 'post',
    data,
  });
};

/**
 * 删除
 */
export const deleteService = (data) => {
  return service({
    url: '/IRSampling/delete',
    method: 'post',
    data,
  });
};

/**
 * 导出
 */
export const exportData = (data) => {
  return service({
    url: '/IRSampling/export',
    method: 'post',
    data,
    responseType: 'blob',
  });
};
