import { ref, h } from 'vue';
import { NTag, NProgress, NIcon, NTooltip } from 'naive-ui';
import {
  PersonOutline,
  TrendingUpOutline,
  CheckmarkCircleOutline,
  TimeOutline,
} from '@vicons/ionicons5';

// 状态颜色配置
export const statusColors = {
  toBeLocated: '#ff6b6b', // 待定界 - 红色
  toBeLocked: '#ffa726', // 待锁定 - 橙色
  toBeRepaired: '#42a5f5', // 待审核 - 蓝色
  toBeArchived: '#ab47bc', // 待归档 - 紫色
  toBeReturned: '#26c6da', // 待回归 - 青色
  closed: '#66bb6a', // 关闭 - 绿色
};

// 格式化数字显示
const formatNumber = (value: number) => {
  if (value === null || value === undefined) return '-';
  return value.toLocaleString();
};

// 格式化百分比显示
const formatPercentage = (value: number) => {
  if (value === null || value === undefined) return '-';
  return `${(value * 100).toFixed(1)}%`;
};

// 渲染状态标签
const renderStatusTag = (value: number, status: string, color: string) => {
  if (value === null || value === undefined || value === 0) {
    return h(NTag, { size: 'small', type: 'default' }, { default: () => '0' });
  }

  return h(
    NTooltip,
    { trigger: 'hover' },
    {
      trigger: () =>
        h(
          NTag,
          {
            size: 'small',
            color: { color, textColor: '#fff' },
            style: { fontWeight: '500', cursor: 'pointer' },
          },
          { default: () => formatNumber(value) }
        ),
      default: () => `${status}: ${formatNumber(value)}个工单`,
    }
  );
};

// 渲染进度条
const renderProgress = (value: number, type: 'success' | 'warning' | 'error' = 'success') => {
  if (value === null || value === undefined) return '-';

  const percentage = Math.round(value * 100);
  let status: 'success' | 'warning' | 'error' = 'success';

  if (percentage < 60) status = 'error';
  else if (percentage < 80) status = 'warning';

  return h(
    'div',
    { style: { display: 'flex', alignItems: 'center', gap: '8px', justifyContent: 'center' } },
    [
      h(NProgress, {
        type: 'line',
        percentage,
        status,
        height: 8,
        style: { flex: 1, minWidth: '60px' },
      }),
      h('span', { style: { fontSize: '12px', color: '#666', minWidth: '40px' } }, `${percentage}%`),
    ]
  );
};

// 工单解决状态分布表格列配置
export const solveStateColumns = [
  {
    title: () =>
      h(
        'div',
        { style: { display: 'flex', alignItems: 'center', gap: '6px', justifyContent: 'center' } },
        [
          h(NIcon, { size: 16, color: '#1890ff' }, { default: () => h(PersonOutline) }),
          h(
            NTooltip,
            { trigger: 'hover' },
            {
              trigger: () =>
                h('div', { style: { display: 'flex', alignItems: 'center' } }, [
                  'EWP责任人',
                  h(
                    'span',
                    { style: { marginLeft: '4px', cursor: 'pointer', color: '#1890ff' } },
                    '?'
                  ),
                ]),
              default: () => '以当前名下负责的应用为准，不考虑转单的情况',
            }
          ),
        ]
      ),
    key: 'name',
    width: 120,
    fixed: 'left',
    render: (row: any) =>
      h(
        'div',
        { style: { fontWeight: '500', color: '#333', textAlign: 'center' } },
        row.name || '-'
      ),
  },
  {
    title: () =>
      h(
        'div',
        { style: { display: 'flex', alignItems: 'center', gap: '6px', justifyContent: 'center' } },
        [
          h(NIcon, { size: 16, color: '#52c41a' }, { default: () => h(TrendingUpOutline) }),
          h(
            NTooltip,
            { trigger: 'hover' },
            {
              trigger: () =>
                h('div', { style: { display: 'flex', alignItems: 'center' } }, [
                  'DTS单总数',
                  h(
                    'span',
                    { style: { marginLeft: '4px', cursor: 'pointer', color: '#1890ff' } },
                    '?'
                  ),
                ]),
              default: () => '筛选时间段内所有定界DTS单数量',
            }
          ),
        ]
      ),
    key: 'total',
    width: 100,
    sorter: true,
    render: (row: any) =>
      h(
        'div',
        { style: { fontWeight: '600', color: '#1890ff', fontSize: '16px', textAlign: 'center' } },
        formatNumber(row.total)
      ),
  },
  {
    title: () =>
      h(
        'div',
        { style: { display: 'flex', alignItems: 'center', gap: '6px', justifyContent: 'center' } },
        [
          h(
            NTooltip,
            { trigger: 'hover' },
            {
              trigger: () =>
                h('div', { style: { display: 'flex', alignItems: 'center' } }, [
                  'SLA达成率',
                  h(
                    'span',
                    { style: { marginLeft: '4px', cursor: 'pointer', color: '#1890ff' } },
                    '?'
                  ),
                ]),
              default: () => 'SLA达成率=时间段内未超期的DTS单数/时间段内新增的DTS单总数',
            }
          ),
        ]
      ),
    key: 'slaRate',
    width: 120,
    sorter: true,
    render: (row: any) => renderProgress(row.slaRate),
  },
  {
    title: () =>
      h(
        'div',
        { style: { display: 'flex', alignItems: 'center', gap: '6px', justifyContent: 'center' } },
        [
          h(
            NTooltip,
            { trigger: 'hover' },
            {
              trigger: () =>
                h('div', { style: { display: 'flex', alignItems: 'center' } }, [
                  'L1自闭环率',
                  h(
                    'span',
                    { style: { marginLeft: '4px', cursor: 'pointer', color: '#1890ff' } },
                    '?'
                  ),
                ]),
              default: () =>
                'L1自闭环率=时间段内L1直接闭环的单子/时间段内完成定界的DTS单总数（只计算2025/6/1后定界的）',
            }
          ),
        ]
      ),
    key: 'l1SelfCloseRate',
    width: 120,
    sorter: true,
    align: 'center',
    render: (row: any) => renderProgress(row.l1SelfCloseRate),
  },
  {
    title: () =>
      h(
        'div',
        { style: { display: 'flex', alignItems: 'center', gap: '6px', justifyContent: 'center' } },
        [
          h(
            NTooltip,
            { trigger: 'hover' },
            {
              trigger: () =>
                h('div', { style: { display: 'flex', alignItems: 'center' } }, [
                  '新增工单',
                  h(
                    'span',
                    { style: { marginLeft: '4px', cursor: 'pointer', color: '#1890ff' } },
                    '?'
                  ),
                ]),
              default: () => '时间段内新创建的DTS单总数',
            }
          ),
        ]
      ),
    key: 'dailyNew',
    width: 90,
    sorter: true,
    render: (row: any) =>
      h(
        'div',
        { style: { color: '#fa8c16', fontWeight: '500', textAlign: 'center' } },
        formatNumber(row.dailyNew)
      ),
  },
  {
    title: () =>
      h(
        'div',
        { style: { display: 'flex', alignItems: 'center', gap: '6px', justifyContent: 'center' } },
        [
          h(
            NTooltip,
            { trigger: 'hover' },
            {
              trigger: () =>
                h('div', { style: { display: 'flex', alignItems: 'center' } }, [
                  '超期工单',
                  h(
                    'span',
                    { style: { marginLeft: '4px', cursor: 'pointer', color: '#1890ff' } },
                    '?'
                  ),
                ]),
              default: () => '时间段内创建的DTS单中，有多少已经超期',
            }
          ),
        ]
      ),
    key: 'dailyOverdue',
    width: 90,
    sorter: true,
    render: (row: any) =>
      h(
        'div',
        {
          style: {
            color: row.dailyOverdue > 0 ? '#ff4d4f' : '#52c41a',
            fontWeight: '500',
            textAlign: 'center',
          },
        },
        formatNumber(row.dailyOverdue)
      ),
  },

  {
    title: () =>
      h(
        'div',
        { style: { display: 'flex', alignItems: 'center', gap: '6px', justifyContent: 'center' } },
        [
          h(
            NTooltip,
            { trigger: 'hover' },
            {
              trigger: () =>
                h('div', { style: { display: 'flex', alignItems: 'center' } }, [
                  '锁定闭环率',
                  h(
                    'span',
                    { style: { marginLeft: '4px', cursor: 'pointer', color: '#1890ff' } },
                    '?'
                  ),
                ]),
              default: () => '锁定闭环率=时间段内锁定+闭环的单数/时间段内创建的DTS单总数',
            }
          ),
        ]
      ),
    key: 'lockAndCloseRate',
    width: 80,
    sorter: true,
    align: 'center',
    render: (row: any) => {
      return row.lockAndCloseRate;
    },
  },
  {
    title: '待定界',
    key: 'toBeLocated',
    width: 80,
    sorter: true,
    align: 'center',
    render: (row: any) => renderStatusTag(row.toBeLocated, '待定界', statusColors.toBeLocated),
  },
  {
    title: () =>
      h(
        'div',
        { style: { display: 'flex', alignItems: 'center', gap: '6px', justifyContent: 'center' } },
        [
          h(
            NTooltip,
            { trigger: 'hover' },
            {
              trigger: () =>
                h('div', { style: { display: 'flex', alignItems: 'center' } }, [
                  '待锁定',
                  h(
                    'span',
                    { style: { marginLeft: '4px', cursor: 'pointer', color: '#1890ff' } },
                    '?'
                  ),
                ]),
              default: () => '当前名下待锁定的单子数',
            }
          ),
        ]
      ),
    key: 'toBeLocked',
    width: 80,
    sorter: true,
    align: 'center',
    render: (row: any) => renderStatusTag(row.toBeLocked, '待锁定', statusColors.toBeLocked),
  },
  {
    title: () =>
      h(
        'div',
        { style: { display: 'flex', alignItems: 'center', gap: '6px', justifyContent: 'center' } },
        [
          h(
            NTooltip,
            { trigger: 'hover' },
            {
              trigger: () =>
                h('div', { style: { display: 'flex', alignItems: 'center' } }, [
                  '待审核',
                  h(
                    'span',
                    { style: { marginLeft: '4px', cursor: 'pointer', color: '#1890ff' } },
                    '?'
                  ),
                ]),
              default: () => '当前名下待审核的单子数',
            }
          ),
        ]
      ),
    key: 'toBeRepaired',
    width: 80,
    sorter: true,
    align: 'center',
    render: (row: any) => renderStatusTag(row.toBeRepaired, '待审核', statusColors.toBeRepaired),
  },
  {
    title: () =>
      h(
        'div',
        { style: { display: 'flex', alignItems: 'center', gap: '6px', justifyContent: 'center' } },
        [
          h(
            NTooltip,
            { trigger: 'hover' },
            {
              trigger: () =>
                h('div', { style: { display: 'flex', alignItems: 'center' } }, [
                  '待归档',
                  h(
                    'span',
                    { style: { marginLeft: '4px', cursor: 'pointer', color: '#1890ff' } },
                    '?'
                  ),
                ]),
              default: () => '当前名下待归档的单子数',
            }
          ),
        ]
      ),
    key: 'toBeArchived',
    width: 80,
    sorter: true,
    align: 'center',
    render: (row: any) => renderStatusTag(row.toBeArchived, '待归档', statusColors.toBeArchived),
  },
  {
    title: () =>
      h(
        'div',
        { style: { display: 'flex', alignItems: 'center', gap: '6px', justifyContent: 'center' } },
        [
          h(
            NTooltip,
            { trigger: 'hover' },
            {
              trigger: () =>
                h('div', { style: { display: 'flex', alignItems: 'center' } }, [
                  '待回归',
                  h(
                    'span',
                    { style: { marginLeft: '4px', cursor: 'pointer', color: '#1890ff' } },
                    '?'
                  ),
                ]),
              default: () => '当前名下待回归的单子数',
            }
          ),
        ]
      ),
    key: 'toBeReturned',
    width: 80,
    sorter: true,
    align: 'center',
    render: (row: any) => renderStatusTag(row.toBeReturned, '待回归', statusColors.toBeReturned),
  },
  {
    title: () =>
      h(
        'div',
        { style: { display: 'flex', alignItems: 'center', gap: '6px', justifyContent: 'center' } },
        [
          h(
            NTooltip,
            { trigger: 'hover' },
            {
              trigger: () =>
                h('div', { style: { display: 'flex', alignItems: 'center' } }, [
                  '已闭环',
                  h(
                    'span',
                    { style: { marginLeft: '4px', cursor: 'pointer', color: '#1890ff' } },
                    '?'
                  ),
                ]),
              default: () => '当前名下已闭环的单子数',
            }
          ),
        ]
      ),
    key: 'closed',
    width: 80,
    sorter: true,
    align: 'center',
    render: (row: any) => renderStatusTag(row.closed, '已闭环', statusColors.closed),
  },
];

// 查询表单配置 - 应用类型和日期
export const searchDTSResolveStates = ref([
  {
    field: 'createTime',
    label: '日期',
    component: 'DateRangePicker',
  },
  {
    field: 'conditionAppType',
    label: '应用类型',
    component: 'Select',
    componentProps: {
      options: [
        { label: '全量', value: '全量' },
        { label: 'TOP2000', value: 'TOP2000' },
        { label: '非TOP2000', value: '非TOP2000' },
      ],
    },
  },
]);

// 查询表单配置 - 小组选择
export const searchTeamResolveStates = ref([
  {
    field: 'conditionTeam',
    label: '小组',
    component: 'Select',
    componentProps: {
      options: [
        { label: '一组', value: 1 },
        { label: '二组', value: 2 },
        { label: '三组', value: 3 },
        { label: '四组', value: 4 },
        { label: '五组', value: 5 },
        { label: '自有+od', value: 8 },
      ],
    },
  },
]);

// 应用类型选项配置
export const appTypeOptions = [
  { label: '全量', value: '全量' },
  { label: 'TOP2000', value: 'TOP2000' },
  { label: '非TOP2000', value: '非TOP2000' },
];

// 小组选项配置
export const teamOptions = [
  { label: '一组', value: 1 },
  { label: '二组', value: 2 },
  { label: '三组', value: 3 },
  { label: '四组', value: 4 },
  { label: '五组', value: 5 },
  { label: '自有+od', value: 8 },
];

export const levelOptions = [
  { label: 'L1', value: 'L1' },
  { label: 'L2', value: 'L2' },
  { label: 'L1+L2', value: 'L1+L2' },
  { label: '全部', value: '全部' },
];

// 导出小组选项配置
export const groupOptions = [
  {
    label: '1组',
    value: 1,
  },
  {
    label: '2组',
    value: 2,
  },
  {
    label: '3组',
    value: 3,
  },
  {
    label: '4组',
    value: 4,
  },
  {
    label: '5组',
    value: 5,
  },
];

// 默认搜索表单数据
export const defaultSearchForm = {
  createTime: ['2025-03-30', new Date(Date.now())],
  conditionAppType: '全量',
  conditionTeam: [8,1, 2, 3, 4, 5],
  conditionLevel: 'L1+L2',
};

// 默认排序状态
export const defaultSortState = {
  sortField: 'toBeLocated',
  sortOrder: 'desc',
};

// API 配置
export const API_CONFIG = {
  DTSSolveStateLink: 'overview/view',
  exportWeeklyGroupDataUrl: '/ewp/management/overview/exportWeeklyGroupData',
  exportTotalGroupDataUrl: '/ewp/management/overview/exportTotalGroupData',
  exportDailyGroupDataUrl: '/ewp/management/overview/exportDailyOverdueData',
  exportQuickGroupDataUrl: '/ewp/management/overview/exportAboutOverdueData',
};

// 导出文件名配置
export const EXPORT_FILE_NAMES = {
  weeklyReport: '工单解决状态分布.xls',
  historyData: '小组历史数据.xls',
  dailyDataExport: '过去24h内超期的工单.xls',
  quickDataExport: '即将超期的工单.xls',
};

// 全选小组的值
export const ALL_GROUP_VALUES = [1, 2, 3, 4, 5];
export const ALL_TEAM_VALUES = [8,1, 2, 3, 4, 5];
export const ALL_LEVEL_VALUES = ['L1', 'L2'];

// 表单重置逻辑
export const resetFormFields = (searchForm: any) => {
  Object.keys(searchForm).forEach((key) => {
    if (key === 'conditionAppType') {
      searchForm[key] = '全量';
    } else if (key === 'conditionTeam') {
      searchForm[key] = [8, 1, 2, 3, 4, 5];
    } else {
      searchForm[key] = '';
    }
  });
};

// 排序状态重置逻辑
export const resetSortState = (sortState: any) => {
  sortState.value.sortField = 'toBeLocated';
  sortState.value.sortOrder = 'desc';
};
