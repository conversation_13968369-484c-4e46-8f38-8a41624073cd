import { ewpService as http } from '@/utils/axios';

export interface QueryParams {
  pageNo: number | undefined;
  pageSize: number | undefined;
  nameOrEmployeeId?: string;
  staffGroupId?: number;
  tagIds?: string;
}

export function getStaffList(data: QueryParams) {
  return http.request({
    url: '/management/staff/users',
    method: 'POST',
    data,
  });
}

export function addStaff(data: any) {
  return http.request({
    url: `/management/staff/user`,
    method: 'POST',
    data,
  });
}

export function deleteStaff(id: number) {
  return http.request({
    url: `/management/staff/user/${id}`,
    method: 'DELETE',
  });
}

export function getTag(data: any) {
  return http.request({
    url: '/management/staff/tags',
    method: 'POST',
    data,
  });
}

export function addTag(data: any) {
  return http.request({
    url: '/management/staff/tag',
    method: 'POST',
    data,
  });
}

export function deleteTag(id: any) {
  return http.request({
    url: `/management/staff/tag/${id}`,
    method: 'DELETE',
  });
}

export function getGroups(data: any) {
  return http.request({
    url: '/management/staff/groups',
    method: 'POST',
    data,
  });
}

export function addGroup(data: any) {
  return http.request({
    url: '/management/staff/group',
    method: 'POST',
    data,
  });
}

export function deleteGroup(id: any) {
  return http.request({
    url: `/management/staff/group/${id}`,
    method: 'DELETE',
  });
}
