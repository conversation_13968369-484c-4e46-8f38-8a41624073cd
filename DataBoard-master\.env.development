# 只在开发模式中被载入
VITE_PORT = 40000

# 网站根目录
VITE_PUBLIC_PATH = /

# 是否开启 mock
VITE_USE_MOCK = true

# 是否开启控制台打印 mock 请求信息
VITE_LOGGER_MOCK = true

# 是否删除console
VITE_DROP_CONSOLE = true

# 跨域代理，可以配置多个，请注意不要换行
#VITE_PROXY = [["/appApi","http://localhost:8001"],["/upload","http://localhost:8001/upload"]]
# VITE_PROXY=[["/rest","http://***********:3000"]]

# API 接口地址
VITE_GLOB_API_URL =

# 接口前缀
VITE_GLOB_API_URL_PREFIX = /api

# 文件上传地址
VITE_GLOB_UPLOAD_URL=

# 文件前缀地址
VITE_GLOB_FILE_URL=

# API 接口地址
VITE_APP_BASE_API = /board
#VITE_APP_BASE_API = /ewp
# 知识库
VITE_APP_BASE_KNOWLEDGE_API = /knowledge-admin