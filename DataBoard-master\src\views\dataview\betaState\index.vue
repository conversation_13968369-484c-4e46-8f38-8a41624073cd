<template>
  <div>
    <!-- 表单查询部分 -->
    <n-card>
      <n-form
        :model="searchForm"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        @submit.prevent="handleSearch"
      >
        <n-grid :cols="24" :x-gap="24">
          <n-grid-item v-for="item in visibleSearchFormItems" :key="item.field" :span="6">
            <n-form-item :label="item.label" :path="item.field">
              <n-input
                clearable
                v-if="item.component === 'Input'"
                v-model:value="searchForm[item.field]"
                @keyup.enter="handleSearch"
              />
              <n-select
                clearable
                v-else-if="item.component === 'Select'"
                v-model:value="searchForm[item.field]"
                :options="item.componentProps.options"
                :multiple="item.field === 'status' || item.field === 'severity'"
              />
              <n-date-picker
                clearable
                v-else-if="item.component === 'Date'"
                v-model:value="searchForm[item.field]"
                type="daterange"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        <div class="form-actions">
          <n-space>
            <n-button @click="resetForm">重置</n-button>
            <n-button type="primary" attr-type="submit">查询</n-button>
            <n-button @click="toggleExpandForm" v-if="searchFormItems.length > 4">
              {{ isExpanded ? '收起' : '展开' }}
              <template #icon>
                <n-icon>
                  <chevron-down v-if="!isExpanded" />
                  <chevron-up v-else />
                </n-icon>
              </template>
            </n-button>
          </n-space>
        </div>
      </n-form>
    </n-card>

    <!-- 表格 -->
    <n-card style="margin-top: 24px">
      <n-space align="center" style="margin-bottom: 16px">
        <n-space>
          <n-button @click="onRelateDTS"> 关联DTS单 </n-button>
          <n-upload accept=".xlsx,.xls" :custom-request="handleFileUpload" :show-file-list="false">
            <n-button secondary :loading="isImporting">
              {{ isImporting ? '导入中...' : '导入Excel' }}
            </n-button>
          </n-upload>
        </n-space>
      </n-space>
      <n-data-table
        remote
        striped
        :single-line="false"
        :columns="columns"
        :data="tableData"
        :pagination="paginationReactive"
        :loading="loadingRef"
        :row-key="rowKey"
        :checked-row-keys="checkedRowKeys"
        @update:checked-row-keys="handleCheckedRowKeysChange"
        :scroll-x="3000"
        :max-height="tableHeight"
      />
    </n-card>
    <!-- 关联DTS单 -->
    <n-modal v-model:show="showRelateModal">
      <n-card
        style="width: 600px"
        title="请输入DTS单号"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-input v-model:value="relateDTS" />
        <template #footer>
          <n-space justify="end">
            <n-button type="primary" @click="onRelateClick" :disabled="!relateDTS">保存</n-button>
            <n-button @click="showRelateModal = false">取消</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ChevronDown, ChevronUp } from '@vicons/ionicons5';
  import { ref, reactive, onMounted, computed } from 'vue';
  import { NForm, NFormItem, NInput, NIcon, useMessage } from 'naive-ui';
  import { creatColumns, searchFormItems } from './columns';
  import { ewpService as service } from '@/utils/axios';
  import { filterObjectValues } from '@/utils';
  import { getWorkOrderList, importExcel, updateWorkOrder } from '@/api/dataview/appState';
  import { isImageDom } from '@/utils/is';
  import { formatToDate } from '@/utils/dateUtil';

  const tableHeight = ref(460);
  const loadingRef = ref(false);
  const isImporting = ref(false);
  const message = useMessage();
  const searchForm = reactive({
    orderId: '',
    status: null,
    severity: null,
    dtsOrder: '',
    clusterTime: null,
    //dtsStatus: null,
  });
  const isExpanded = ref(false);
  const visibleSearchFormItems = computed(() => {
    return isExpanded.value ? searchFormItems : searchFormItems.slice(0, 4);
  });
  const tableData = ref([
    {
      id: '',
      orderId: '',
      description: '',
      status: '',
      severity: '',
      dtsOrder: '',
      //dtsStatus: '',
      submitter: '',
      submitTime: '',
    },
  ]);

  const rowKey = (row) => row.id;

  // 关联DTS单
  const relateDTS = ref('');

  const paginationReactive = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 50],
    prefix({ itemCount }) {
      return `共计：${itemCount}`;
    },
    onChange: (page: number) => {
      paginationReactive.page = page;
      fetchData();
    },
    onUpdatePageSize: (pageSize: number) => {
      paginationReactive.pageSize = pageSize;
      paginationReactive.page = 1;
      fetchData();
    },
  });

  /**
   * beta单接口入参数据
   */
  interface WorkOrderQueryParams {
    orderId?: string;
    dtsOrder?: string;
    status?: string | null;
    severity?: string | null;
    //dtsStatus?: string | null;
    pageNo: number;
    pageSize: number;
  }

  const betaLink = 'betaClubOrder';

  /**
   *
   */

  const columns = creatColumns();
  // 在组件挂载时获取数据
  onMounted(() => {
    fetchData();
  });

  /**
   * 请求数据
   * @param link 请求的链接
   * @param params 入参
   */
  async function getWorkOrderList(link, params: WorkOrderQueryParams): Promise<any> {
    try {
      const response = await service.post(`/management/${link}/query`, params);
      console.log('response:', response);

      return response;
    } catch (error) {
      console.error('Error fetching work order list:', error);
      throw error;
    }
  }

  // 表格选择框
  const selectedRows = ref<Record<string, any>[]>([]);
  const checkedRowKeys = ref<(string | number)[]>([]);
  const handleCheckedRowKeysChange = (keys: (string | number)[]) => {
    const newSelectedRows = keys
      .map((key) => {
        const existingRow = selectedRows.value.find((row) => row.id === key);
        if (existingRow) {
          return existingRow;
        }
        return tableData.value.find((row) => row.id === key);
      })
      .filter(Boolean) as ListData[];

    selectedRows.value = newSelectedRows;
    checkedRowKeys.value = keys;
  };

  // 关联DTS单模态框
  const showRelateModal = ref(false);

  /**
   * 入参和数据处理
   */
  const fetchData = async () => {
    loadingRef.value = true;
    let isNull = false;
    if (searchForm.orderId === ' ') {
      isNull = true;
      searchForm.orderId = 'empty';
    }
    const params = {};
    Object.assign(params, searchForm);
    //处理时间
    if (searchForm.clusterTime) {
      params.beginClusterTime = formatToDate(searchForm.clusterTime[0], 'yyyy-MM-dd');
      params.endClusterTime = formatToDate(searchForm.clusterTime[1], 'yyyy-MM-dd');
    }
    try {
      const queryParams = {
        ...(isNull ? params : filterObjectValues(params)),
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
      };
      const { total, records } = await getWorkOrderList(betaLink, queryParams);
      tableData.value = records;
      paginationReactive.itemCount = total;
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loadingRef.value = false;
    }
  };

  const batchSave = async (data) => {
    return service.post('/management/betaClubOrder/batchUpdate', data);
  };

  /**
   * 重置事件
   */
  const resetForm = () => {
    Object.keys(searchForm).forEach((key) => {
      searchForm[key] = '';
    });
    fetchData();
  };

  /**
   * 查询事件
   */
  const handleSearch = () => {
    paginationReactive.page = 1;
    fetchData();
    // 这里可以编写查询逻辑，更新 tableData
  };

  /**
   * 展开收起事件
   */
  const toggleExpandForm = () => {
    isExpanded.value = !isExpanded.value;
    if (isExpanded.value) {
      tableHeight.value = 400;
    } else {
      tableHeight.value = 460;
    }
  };

  // 点击关联DTS单
  const onRelateDTS = () => {
    if (selectedRows.value.length === 0) {
      return;
    }
    showRelateModal.value = true;
  };

  // 关联DTS单保存
  const onRelateClick = async () => {
    try {
      const data: Record<string, any> = [];
      selectedRows.value.forEach((item) => {
        item.dtsOrder = relateDTS.value;
        data.push(item);
      });
      await batchSave(data);
      message.success('关联成功');
      showRelateModal.value = false;
      fetchData();
      // 重置选择状态
      checkedRowKeys.value = [];
      selectedRows.value = [];
    } catch (error) {
      message.error('关联失败');
      console.log(error);
    }
  };

  // 导入数据
  const handleFileUpload = async ({ file }) => {
    isImporting.value = true;
    try {
      const formData = new FormData();
      formData.append('file', file.file);

      const response = await service.post('/management/betaClubOrder/importData', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      message.success('应用数据导入成功', response);
      fetchData(); // Refresh the table data
    } catch (error) {
      console.error('Failed to import Excel file:', error);
      message.error('Excel 文件导入失败');
    } finally {
      isImporting.value = false;
    }
  };
</script>

<style scoped>
  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 12px; /* 减小上边距 */
  }
</style>
