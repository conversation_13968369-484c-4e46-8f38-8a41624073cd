import service from "@/utils/axios";
import { CodeDemoReviewStatusEnum } from "@/enums/CodeDemoReviewStatusEnum";

export interface CodeBaseInfo {
  // demo名称
  demoName: string;
  // demo描述
  demoDescription: string;
  // demo链接
  demoLink: string;
  // demo外溢链接
  externalLink: string;
  // demo行数
  lineNumber: number;
}
export interface CodeReviewInfo {
  // demo当前处理人
  currentHandler: string;
  // demo评审意见
  reviewSuggestions: string;
  // demo评审状态(待评审)
  reviewStatus: CodeDemoReviewStatusEnum | null;
  // 评审时间
  reviewTime: string;
}
export interface CodeDemoDto extends CodeBaseInfo, CodeReviewInfo {
  // demo的id
  id: number;
  // demo的外发次数
  demoCount: number;
  // demo提交人(工号/姓名)
  demoSubmitter: string;
  // demo创建时间
  createTime?: string;
  // demo更新时间
  updateTime?: string;
  // 是否删除，初始化默认是否
  deleted?: 0 | 1;
  // demo的唯一id
  demoId: string;
  // demo的api版本
  apiVersion: 'API 12' | 'API 13' | 'API 14';
  // demo的分类(ArkTs)
  type: 'ArkTS' | 'C++';
  // demo的分类-具体分类
  selectValue: string;
  // demo示例
  samples: string[];
  // demo的标签
  tags: string[];
  // 是否原创
  isOriginality?: boolean;
}
interface BaseRsp {
  message: string;
  status: string;
}
export interface AddDemoReq {
  demoName: string;
  demoDescription: string;
  demoLink?: string;
  demoSubmitter: string;
  lineNumber: number;
  demoId: string;
  apiVersion?: string;
  type: string;
  selectValue?: string;
  samples?: string[];
  tags?: string[];
  externalLink?: string;
  // 关联心愿单
  customizedDemoId?: number;
}
export interface AddDemoRsp extends BaseRsp {
  // demoId
  data: string;
}
export function addDemo(params: AddDemoReq): Promise<AddDemoRsp> {
  return service({
    url: '/demo/add',
    method: 'post',
    data: params,
  });
}
export interface DeleteDemoReq {
  deletePerson: string;
  deleteReason: string;
  deleteDemoIdList: string[];
}
export function deleteDemo(params: DeleteDemoReq) {
  return service({
    url: '/demo/delete',
    method: 'post',
    data: params,
  });
}
export interface UpdateDemoReq {
  demoId: string;
  demoName?: string;
  demoDescription?: string;
  demoLink?: string;
  demoSubmitter?: string;
  lineNumber?: number;
  currentHandler?: string;
  reviewSuggestions?: string;
  reviewStatus?: CodeDemoReviewStatusEnum | null;
  selectValue?: string;
  samples?: string[];
  tags?: string[];
  externalLink?: string;
}
export function updateDemo(params: UpdateDemoReq) {
  return service({
    url: '/demo/update',
    method: 'post',
    data: params,
  });
}
export interface QueryDemoListReq {
  pageNum: number;
  pageSize: number;
  demo: QueryDemoParams;
}
export interface QueryDemoParams {
  demoId?: string;
  demoName?: string;
  apiVersionList?: string[];
  demoSubmitter?: string;
  reviewStatusList?: CodeDemoReviewStatusEnum[] | null;
  selectValueList?: string[];
}
export interface QueryDemoListRsp extends BaseRsp {
  data: {
    data: CodeDemoDto[];
    pageInfo: { total: number };
  };
}
export function queryDemoList(params: QueryDemoListReq): Promise<QueryDemoListRsp> {
  return service({
    url: '/demo/query',
    method: 'post',
    data: params,
  });
}
export interface OutgoingRecordsReq {
  demoId: string;
  pageNum: number;
  pageSize: number;
}
export interface QueryOutgoingRecordsRsp extends BaseRsp {
  data: {
    data: OutgoingRecordsDto[];
    pageInfo: { total: number };
  };
}
export interface OutgoingRecordsDto {
  id: number;
  demoId: string;
  sendMode: string;
  codeLines: number;
  applicationName: string;
  demoSender: string;
  senderEmail: string;
  sendTime: string | null;
  receivedEmail: string;
  industry: string;
  createTime: string | null;
  updateTime: string | null;
}
export function queryOutgoingRecords(params: OutgoingRecordsReq): Promise<QueryOutgoingRecordsRsp> {
  return service({
    url: '/demo/send_record/query',
    method: 'get',
    params,
  });
}
export interface AddSampleImageReq {
  demoId: string;
  imgUrl: string;
}
export function addSampleImage(params: AddSampleImageReq) {
  return service({
    url: '/demo/sample/add',
    method: 'post',
    data: params,
  });
}
export function deleteSampleImage(demoId: number) {
  return service({
    url: '/demo/sample/delete',
    method: 'delete',
    params: {
      id: demoId
    },
  });
}
