<template>
  <n-card>
    <n-space vertical>
      <n-flex justify="start" align="center">
        <n-button secondary strong type="warning" @click="search()">
          <template #icon>
            <n-icon>
              <Refresh />
            </n-icon>
          </template>
          刷新
        </n-button>
        <n-button
          secondary
          strong
          type="primary"
          @click="
            showModal = true;
            modelLoading = true;
          "
        >
          <template #icon>
            <n-icon>
              <Add />
            </n-icon>
          </template>
          新增出差申请
        </n-button>
        <n-button
          v-if="isAdmin"
          secondary
          strong
          type="primary"
          :disabled="!tableData.filter((item) => item.status === '已发送').length"
          @click="uptateStatus(true)"
        >
          一键登记
        </n-button>
        <n-button
          v-if="isAdmin"
          secondary
          strong
          type="primary"
          :disabled="!selectRows.length"
          @click="uptateStatus(false)"
        >
          批量登记
        </n-button>
        <n-button
          v-if="isAdmin"
          secondary
          strong
          type="primary"
          :disabled="!tableData.filter((item) => item.status === '待审批').length"
          @click="createEmlDialog()"
        >
          一键生成邮件
        </n-button>
      </n-flex>
      <n-data-table
        :loading="loading"
        ref="tableRef"
        :pagination="pagination"
        :columns="columns"
        :data="tableData"
        scroll-x
        :row-key="(row) => row.id"
        :checked-row-keys="selectRows"
        @update:checked-row-keys="handleCheck"
      />
    </n-space>

    <n-modal
      v-model:show="showModal"
      :on-after-leave="handleAfterLeave"
      :on-after-enter="handleAfterEnter"
    >
      <n-card
        :style="{ width: '800px' }"
        :title="isAdd ? '新增' : '编辑'"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-spin :show="modelLoading">
          <template #header-extra> </template>
          <n-form
            ref="formRef"
            :model="model"
            :rules="rules"
            label-placement="left"
            label-width="110"
            require-mark-placement="right-hanging"
            size="medium"
          >
            <n-grid :cols="2" :x-gap="24">
              <n-form-item-gi :span="1" label="邮箱" path="email">
                <n-input v-model:value="model.email" placeholder="请输入" />
              </n-form-item-gi>
              <n-form-item-gi label="主管" path="leaderName">
                <n-select
                  v-model:value="model.leader"
                  v-model:label="model.leaderName"
                  :options="leaderList"
                  filterable
                  clearable
                />
              </n-form-item-gi>
              <n-form-item-gi label="出差地点" path="travelCity">
                <n-cascader
                  v-model:value="model.travelCity"
                  check-strategy="child"
                  placeholder="请选择城市"
                  expand-trigger="hover"
                  :options="districts"
                  :show-path="true"
                  children-field="districts"
                  value-field="name"
                  label-field="name"
                  filterable
                  @update:value="handleUpdateValue"
                />
              </n-form-item-gi>
              <n-form-item-gi label="类型" path="type">
                <n-select v-model:value="model.type" :options="typeList" filterable clearable />
              </n-form-item-gi>

              <n-form-item-gi label="出差起始时间" path="travelStartDate">
                <n-date-picker
                  :style="{ width: '100%' }"
                  v-model:value="model.travelStartDate"
                  :is-date-disabled="disableTravelStartDate"
                  clearable
                />
              </n-form-item-gi>
              <n-form-item-gi label="预估结束时间" path="travelEndDate">
                <n-date-picker
                  :style="{ width: '100%' }"
                  v-model:value="model.travelEndDate"
                  :is-date-disabled="disableTravelEndDate"
                  clearable
                />
              </n-form-item-gi>
              <n-form-item-gi :span="2" label="出差目的" path="purpose">
                <n-input v-model:value="model.purpose" type="textarea" />
              </n-form-item-gi>
              <n-form-item-gi
                :show-require-mark="false"
                class="link"
                :span="2"
                label=""
                label-placement="left"
              >
                <n-checkbox v-model:checked="isAgreementSigned"> 已签署 </n-checkbox>
                <a
                  href="https://w3.huawei.com/ndas/#!ndas/signed/haveSignedAgreementList.html"
                  target="blank"
                  rel="noopener noreferrer"
                >
                  保密协议签署、
                </a>
                <a
                  href="https://ilearning.huawei.com/iexam/100000/examInfo?examId=71057"
                  target="blank"
                  rel="noopener noreferrer"
                >
                  CBG对外交流人员上岗考试、
                </a>
                <a
                  href="https://ilearning.huawei.com/iexam/100000/examInfo?examId=77806"
                  target="blank"
                  rel="noopener noreferrer"
                >
                  对外承诺管理、
                </a>
                <a
                  href="https://ilearning.huawei.com/iexam/100000/examInfo?examId=79771"
                  target="blank"
                  rel="noopener noreferrer"
                >
                  尊重与保护他人商业秘密、
                </a>
                <a
                  href="https://ilearning.huawei.com/iexam/100000/examInfo?examId=96744"
                  target="blank"
                  rel="noopener noreferrer"
                >
                  知识产权风险管理、
                </a>
                <a
                  href="https://ilearning.huawei.com/iexam/100000/examInfo?examId=1805064073298751489"
                  target="blank"
                  rel="noopener noreferrer"
                >
                  终端云鸿蒙生态信息安全自测
                </a>
              </n-form-item-gi>
            </n-grid>
          </n-form>
        </n-spin>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" @click="handleSubmint()"> 确认 </n-button>
            <n-button secondary strong type="error" @click="showModal = false"> 取消 </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </n-card>
</template>

<script lang="ts" setup>
  import { ref, onMounted, h, watch, toRefs } from 'vue';
  import {
    formatDateTime,
    getDefaultContactFilters,
    districts,
    emergencyContactModelTemplate,
    typeList,
    relationList,
    travelInformationModelTemplate,
    teamOptions,
    paginationReactive,
  } from '../index';
  import { useUserStore } from '@/store/modules/user';
  import { debounce, cloneDeep, uniq, concat } from 'lodash-es';
  import { Add, Refresh, HelpCircleOutline } from '@vicons/ionicons5';
  import { useMessage, useDialog, NButton, NFlex, NDataTable } from 'naive-ui';
  import {
    travelApplicationAdd,
    travelApplicationUpdate,
    travelApplicationUpdateStatus,
    travelApplicationDelete,
    travelApplicationQuery,
  } from '@/api/dataview/businessTravelersManage';
  import { queryUser } from '@/api/system/usermanage';
  import { useEventBus } from '@/hooks/useEventBus';
  const userInfo = useUserStore().getUserInfo;
  const pagination = ref(paginationReactive);
  const tableData = ref([]);
  const isAgreementSigned = ref(false);
  const formRef = ref();
  const tableRef = ref();
  const selectRows = ref([]);
  const loading = ref(true);
  const modelLoading = ref(true);
  const props = defineProps({
    leaderList: {
      default: () => [],
      required: true,
    },
    isAdmin: {
      default: () => false,
      required: true,
    },
    currentUser: {
      default: () => {},
      required: true,
    },
  });
  const { leaderList, isAdmin, currentUser } = toRefs(props);
  const dialog = useDialog();
  const model = ref({
    leader: currentUser.value?.leader
  });
  const rules = ref({
    email: {
      required: true,
      message: '请输入',
      trigger: ['blur', 'input'],
    },
    purpose: {
      required: true,
      message: '请输入',
      trigger: ['blur', 'input'],
    },
    travelCity: {
      required: true,
      message: '请选择',
      trigger: ['blur', 'change'],
    },
    leader: {
      required: true,
      message: '请选择',
      trigger: ['blur', 'change'],
    },
    type: {
      required: true,
      message: '请选择',
      trigger: ['blur', 'change'],
    },
    travelStartDate: {
      required: true,
      trigger: ['blur', 'change'],
      validator: (rule, value) => {
        if (!value) {
          return new Error('请选择');
        } else if (model.value.travelEndDate && value > model.value.travelEndDate) {
          return new Error('时间范围不正确');
        }
        return true;
      },
    },
    travelEndDate: {
      required: true,
      trigger: ['blur', 'change'],
      validator: (rule, value) => {
        if (!value) {
          return new Error('请选择');
        } else if (model.value.travelStartDate && value < model.value.travelStartDate) {
          return new Error('时间范围不正确');
        }
        return true;
      },
    },
  });
  const columns = ref([
    {
      type: 'selection',
      fixed: 'left',
      disabled: (row) => row.status !== '已发送' || !isAdmin.value,
    },
    {
      title: '登记时间',
      key: 'createTime',
      minWidth: 60,
      width: 110,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 600px;overflow-y: auto',
        },
      },
    },
    {
      title: '姓名',
      key: 'name',
      minWidth: 60,
      width: 80,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 600px;overflow-y: auto',
        },
      },
    },
    {
      title: '工号',
      key: 'userNo',
      minWidth: 60,
      width: 100,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 600px;overflow-y: auto',
        },
      },
    },
    {
      title: '主管',
      key: 'leader',
      minWidth: 60,
      width: 70,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 600px;overflow-y: auto',
        },
      },
    },
    {
      title: '出差地点',
      key: 'travelCity',
      minWidth: 60,
      width: 80,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 600px;overflow-y: auto',
        },
      },
    },
    {
      title: '类型',
      key: 'type',
      minWidth: 60,
      width: 60,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 600px;overflow-y: auto',
        },
      },
    },
    {
      title: '出差起始时间',
      key: 'travelStartDate',
      minWidth: 60,
      width: 110,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 600px;overflow-y: auto',
        },
      },
    },
    {
      title: '预估结束时间',
      key: 'travelEndDate',
      minWidth: 60,
      width: 110,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 600px;overflow-y: auto',
        },
      },
    },
    {
      title: '出差目的',
      key: 'purpose',
      minWidth: 60,
      width: 130,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 600px;overflow-y: auto',
        },
      },
    },
    {
      title: '状态',
      key: 'status',
      minWidth: 60,
      width: 110,
      resizable: true,
      filterOptions: [
        {
          label: '待审批',
          value: '待审批',
        },
        {
          label: '已发送',
          value: '已发送',
        },
        {
          label: '已审批',
          value: '已审批',
        },
      ],
      filter(value, row) {
        return ~row?.status?.indexOf(value);
      },
    },
    {
      title: '发送批次',
      key: 'sendDate',
      minWidth: 60,
      width: 110,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 600px;overflow-y: auto',
        },
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 130,
      fixed: 'right',
      render(row) {
        return [
          h(
            NFlex,
            { wrap: false, justify: 'start' },
            {
              default: () => [
                h(
                  NButton,
                  {
                    strong: true,
                    tertiary: true,
                    type: 'info',
                    size: 'small',
                    disabled: row.status !== '待审批',
                    onClick: async () => {
                      isAdd.value = false;
                      modelLoading.value = false;
                      showModal.value = true;
                      let rowData = cloneDeep(row);
                      const leader = leaderList.value.filter((item) => item.label === row.leader)[0].value;
                      try {
                        rowData.travelEndDate = new Date(row.travelEndDate).getTime();
                        rowData.travelStartDate = new Date(row.travelStartDate).getTime();
                      } catch (err) {}
                      isAgreementSigned.value = true;
                      model.value = rowData;
                      model.value.leader = leader;
                    },
                  },
                  { default: () => [h('div', '编辑')] }
                ),
                h(
                  NButton,
                  {
                    strong: true,
                    tertiary: true,
                    type: 'error',
                    size: 'small',
                    disabled: row.status !== '待审批',
                    onClick: () => {
                      dialog.error({
                        title: '警告',
                        content: '是否删除出差信息',
                        positiveText: '确定',
                        negativeText: '取消',
                        onPositiveClick: async () => {
                          loading.value = true;
                          try {
                            await travelApplicationDelete({
                              id: row.id,
                            });
                            message.success('删除成功');
                          } catch {
                            loading.value = false;
                            message.error('删除失败');
                          }
                          search();
                        },
                      });
                    },
                  },
                  { default: () => [h('div', '删除')] }
                ),
              ],
            }
          ),
        ];
      },
    },
  ]);
  const eventBus = useEventBus();
  const createEmlDialog = () => {
    dialog.info({
      title: '提示',
      content: `将使用所有待审批数据生成邮件并将状态将变更为已发送，是否确认`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        search(createEml);
      },
      onNegativeClick: () => {},
    });
  };

  const uptateStatus = (updateAll?, id?) => {
    dialog.info({
      title: '提示',
      content: `${
        updateAll ? '所有状态为已发送的' : '已选中'
      }数据状态将变更为已审批并生成出差记录，请确认`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          await updateStatus(
            updateAll
              ? tableData.value.filter((item) => item.status === '已发送')
              : selectRows.value,
            '已审批'
          );
          selectRows.value = [];
        } catch (err) {}
        search();
      },
      onNegativeClick: () => {},
    });
  };
  const message = useMessage();
  const isAdd = ref(true);
  const showModal = ref(false);

  const handleCheck = (rowKeys) => {
    selectRows.value = rowKeys;
  };
  const handleAfterLeave = () => {
    model.value = {
      leader: currentUser.value?.leader
    };
    isAdd.value = true;
    isAgreementSigned.value = false;
  };
  const handleAfterEnter = async () => {
    model.value.email = await getUserEmail();
    modelLoading.value = false;
  };
  const handleUpdateValue = (
    value: string | number | Array<string | number> | null,
    option,
    pathValues
  ) => {
    model.value.travelProvince = pathValues[0]['name'];
  };
  const disableTravelStartDate = (ts: number) => {
    return model.value.travelEndDate ? ts > model.value.travelEndDate : null;
  };
  const disableTravelEndDate = (ts: number) => {
    return model.value.travelStartDate ? ts < model.value.travelStartDate : null;
  };
  const createEml = () => {
    const list = tableData.value.filter((item) => item.status === '待审批');
    if (!list.length) {
      message.error('暂无待发送数据');
      return;
    }
    let tbody = list
      .map((item) => {
        return `
        <tr>
          <td>${formatDateTime(new Date(item.createTime), 'yyyy/MM/dd')}</td>
          <td>${item.name}</td>
          <td>${item.userNo}</td>
          <td>${item.travelCity}</td>
          <td>${item.type}</td>
          <td>${formatDateTime(new Date(item.travelStartDate), 'yyyy/MM/dd')}</td>
          <td>${formatDateTime(new Date(item.travelEndDate), 'yyyy/MM/dd')}</td>
          <td class="purpose">${item.purpose}</td>
          <td>已签</td>
          <td>已通过</td>
          <td>已通过</td>
          <td>已通过</td>
          <td>已通过</td>
          <td>已通过</td>
        </tr>`;
      })
      .join('');
    // 构建包含表格的 HTML 正文
    const htmlBody = `
        <html>
          <style>
            table {
              text-align: center;
            }
            table tr th {
              width: 80px;
              max-width: 200px;
              background-color: #e7e6e6;
              padding: 0 4px;
              text-align: center;
            }
            table tr td {
              padding: 2px 10px;
              text-align: center;
              font-size: 14px;
            }
            .link {
              width: 160px;
              max-width: 200px;
            }
            .city {
              width: 100px,
              max-width: 200px;
            }
            .time {
              width: 120px;
              max-width: 200px;
            }
            .purpose {
              width: 300px;
              max-width: 300px;
            }
            td.purpose {
              text-align: left;
            }
          </style>
          <body>
            <table border="1" cellspacing="0">
              <thead>
                <tr>
                  <th class="time">登记时间</th>
                  <th>姓名</th>
                  <th>工号</th>
                  <th class="city">出差地点</th>
                  <th>类型</th>
                  <th class="time">出差起始时间</th>
                  <th class="time">预估结束时间</th>
                  <th class="purpose">出差目的</th>
                  <th>
                    <a
                      href="https://w3.huawei.com/ndas/#!ndas/signed/haveSignedAgreementList.html"
                      target="blank"
                      rel="noopener noreferrer"
                    >
                      保密协议签署
                    </a>
                  </th>
                  <th class="link">
                    <a
                      href="https://ilearning.huawei.com/iexam/100000/examInfo?examId=71057"
                      target="blank"
                      rel="noopener noreferrer"
                    >
                      CBG对外交流人员上岗考试
                    </a>
                  </th>
                  <th>
                    <a
                      href="https://ilearning.huawei.com/iexam/100000/examInfo?examId=77806"
                      target="blank"
                      rel="noopener noreferrer"
                    >
                      对外承诺管理
                    </a>
                  </th>
                  <th class="link">
                    <a
                      href="https://ilearning.huawei.com/iexam/100000/examInfo?examId=79771"
                      target="blank"
                      rel="noopener noreferrer"
                    >
                      尊重与保护他人商业秘密
                    </a>
                  </th>
                  <th>
                    <a
                      href="https://ilearning.huawei.com/iexam/100000/examInfo?examId=96744"
                      target="blank"
                      rel="noopener noreferrer"
                    >
                      知识产权风险管理
                    </a>
                  </th>
                  <th class="link">
                    <a
                      href="https://ilearning.huawei.com/iexam/100000/examInfo?examId=1805064073298751489"
                      target="blank"
                      rel="noopener noreferrer"
                    >
                      终端云鸿蒙生态信息安全自测
                    </a>
                  </th>
                </tr>
              </thead>
              <tbody>
                  ${tbody}
              </tbody>
            </table>
          </body>
        </html>
      `;
    const ccLeaderList = uniq(list.map((item) => item.leader))
      .map((item) => leaderList?.value?.filter((leader) => leader.label === item)?.[0]?.email)
      ?.filter((item) => !!item);
    const ccList = uniq(
      concat(
        list.map((item) => item.email),
        ccLeaderList
      )
    );
    const to = '<EMAIL>';
    const subject = `【${
      new Date().getMonth() + 1
    }月${new Date().getDate()}号批次】技术支持xxx冲刺紧急出差申请，请春哥审批`;
    // 构建 EML 内容（注意邮件头格式）
    const emlContent = [
      `Subject: ${subject}`, // 邮件主题
      'Content-Type: text/html; charset=UTF-8', // 指定HTML格式
      `To: ${to};`,
      `Cc: ${ccList.filter((item) => item !== to).join(';')};`,
      '', // 空行分隔头部和内容
      htmlBody,
    ].join('\r\n'); // 必须使用CRLF换行

    // 生成 Blob 并触发下载
    const blob = new Blob([emlContent], { type: 'message/rfc822' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `${subject}.eml`; // 下载文件名
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    updateStatus(list, '已发送');
  };

  const updateStatus = async (rows, status) => {
    loading.value = true;
    try {
      let params = {
        idList: rows.map((item) => item?.id || item),
        status,
      };
      let res = await travelApplicationUpdateStatus(params);
      if (res.status == '200') {
        message.success('成功');
      }
    } catch (e) {}
    loading.value = false;
    search();
  };

  //提交处理应用
  const handleSubmint = debounce(async () => {
    formRef.value.validate(async (errors) => {
      if (!errors) {
        if (!isAgreementSigned.value) {
          message.error('请确认协议签署状态');
          return;
        }
        let params = cloneDeep(model.value);
        if (isAdd.value) {
          params.travelStartDate = formatDateTime(params.travelStartDate, 'yyyy-MM-dd');
          params.travelEndDate = formatDateTime(params.travelEndDate, 'yyyy-MM-dd');
          var res = await travelApplicationAdd(params);
        } else {
          var res = await travelApplicationUpdate(params);
        }
        if (res.status == '200') {
          message.success(isAdd.value ? '提交成功' : '更新成功');
          showModal.value = false;
          search();
        }
      }
    });
  }, 300);
  const getUserEmail = async (params) => {
    if (!isAdd.value) {
      return model.value.email;
    }
    let res = await queryUser({
      pageNum: 1,
      pageSize: 1,
      user: {
        account: userInfo.account,
      },
    });
    return res.data?.data?.[0]?.email || '';
  };
  const search = debounce(async (callback?) => {
    loading.value = true;
    try {
      let params = {
        status: '',
      };
      let res = await travelApplicationQuery(params);
      if (res.status === '200') {
        pagination.value.itemCount = res?.data?.length || 0;
        tableData.value = res?.data || [];
        callback && callback();
        eventBus.emit('updateMenu');
      }
    } catch (e) {
      console.log(e);
    }
    loading.value = false;
  }, 300);

  onMounted(() => {
    search();
  });
</script>

<style lang="less" scoped>
  :deep(.link)  {
    .n-form-item-feedback-wrapper {
      display: none;
    }
    .n-form-item-blank {
      display: block;
    }
    a {
      color: #4d9dff;
    }
  }
</style>
