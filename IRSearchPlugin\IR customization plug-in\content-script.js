try {
  (function () {
    if (typeof window.CustomEvent === "function") {
      return false;
    }
    var CustomEvent = function (event, params) {
      params = params || {
        bubbles: false,
        cancelable: false,
        detail: undefined,
      };
      var evt = document.createEvent("CustomEvent");
      evt.initCustomEvent(
        event,
        params.bubbles,
        params.cancelable,
        params.detail
      );
      return evt;
    };
    CustomEvent.prototype = window.Event.prototype;
    window.CustomEvent = CustomEvent;
  })();
  (function (xhr) {
    if (XMLHttpRequest.prototype.irPlug) return;
    var XHR = XMLHttpRequest.prototype;
    XHR.irPlug = true;
    var open = XHR.open;
    var send = XHR.send;
    XHR.open = function (_, url) {
      this._url = url;
      return open.apply(this, arguments);
    };

    XHR.send = function (body) {
      if (this._url.includes("/v1/delegate")) {
        this.addEventListener("load", function (xhr) {
          try {
            let requestBody = JSON.parse(body);
            let res = JSON.parse(xhr.target.response);
            let resJson = JSON.parse(res.resJson);
            let reqJson = JSON.parse(requestBody.reqJson).req;
            const detail = {
              requestBody,
              res,
              resJson,
              reqJson,
              issueInfo: window.__issueInfo__,
              currentUser: window.__currentUser__,
              issueHistoryList: window.__issueHistoryList__,
              issueInternalMsgList: window.__issueInternalMsgList__,
              L1UserInfoList: window.__L1UserInfoList__,
              L2UserInfoList: window.__L2UserInfoList__,
              L3UserInfoList: window.__L3UserInfoList__,
            }
            switch (requestBody.svc) {
              case "PartnerIssueService.Developer.getUserInfo":
                window.__currentUser__ = resJson.result;
                break;
              case "PartnerIssueService.Developer.getL1UserInfoByComponentID":
                window.__L1UserInfoList__ = resJson.resultList;
                break;
              case "PartnerIssueService.Developer.getL2UserInfoByComponentID":
                let userType = reqJson.userType;
                if (userType === 2) {
                  window.__L2UserInfoList__ = resJson.resultList;
                } else {
                  window.__L3UserInfoList__ = resJson.resultList;
                }
                break;
              case "PartnerIssueService.Developer.queryCommentsByIssue":
                window.__issueHistoryList__ = resJson.resultList;
                break;
              case "PartnerIssueService.Developer.queryIssue":
                window.__issueInfo__ = resJson.result;
                window.dispatchEvent(
                  new CustomEvent("issueInfoChange", {
                    detail: window.__issueInfo__,
                  })
                );
                break;
              case "PartnerIssueService.Developer.getInternalMsgList":
                window.__issueInternalMsgList__ = resJson.resultList;
                break;
              case "PartnerIssueService.Developer.updateIssue":
                if(window.__issueInfo__.issue.status !== 9 && reqJson?.status === '9' && window.__issueInfo__?.issue?.isSubmittedByHWTest !== 1 && resJson.code === 0) {
                  window.dispatchEvent(
                    new CustomEvent("onAssignIssue", {
                      detail,
                    })
                  );
                }
                break;
              case "PartnerIssueService.Developer.assignIssue":
                let code = resJson?.code;
                if (code !== 0 || window.__issueInfo__?.issue?.isSubmittedByHWTest === 1) {
                  return;
                }
                window.dispatchEvent(
                  new CustomEvent("onAssignIssue", {
                    detail,
                  })
                );
                break;

              // no default
            }
          } catch (error) {
            console.log(error);
          }
        });
      }
      return send.apply(this, arguments);
    };
  })(XMLHttpRequest);
} catch (error) {}
