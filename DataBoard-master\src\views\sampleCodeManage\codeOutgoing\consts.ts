import { CodeDemoReviewStatusEnum } from "@/enums/CodeDemoReviewStatusEnum";
import { SelectOption } from "naive-ui";
import { SelectBaseOption } from "naive-ui/es/select/src/interface";

// 全部评审状态
export const ALL_REVIEW_STATUS: CodeDemoReviewStatusEnum[] = [
  CodeDemoReviewStatusEnum.COMPLIANT,
  CodeDemoReviewStatusEnum.TO_BE_MODIFIED,
  CodeDemoReviewStatusEnum.TO_BE_REVIEWED,
  CodeDemoReviewStatusEnum.SPILLED,
  CodeDemoReviewStatusEnum.NOT_PASSED,
];
export const CODE_REVIEW_STATUS_MAP: Record<CodeDemoReviewStatusEnum, string> = {
  [CodeDemoReviewStatusEnum.COMPLIANT]: '已合规',
  [CodeDemoReviewStatusEnum.TO_BE_MODIFIED]: '待修改',
  [CodeDemoReviewStatusEnum.TO_BE_REVIEWED]: '待评审',
  [CodeDemoReviewStatusEnum.SPILLED]: '已外溢',
  [CodeDemoReviewStatusEnum.NOT_PASSED]: '待删除',
}
export const SEARCH_FILTER_OPTIONS: SelectOption[] = [
  {
    value: 'demoName',
    label: '名称',
  },
  {
    value: 'demoId',
    label: 'ID',
  },
];
export const INDUSTRY_OPTIONS: SelectBaseOption<string, string>[] = [
  {label: "便捷生活", value: "life"},
  {label: "出行导航", value: "navigation"},
  {label: "儿童", value: "children"},
  {label: "购物比价", value: "shopping"},
  {label: "教育", value: "education"},
  {label: "金融理财", value: "financialManagement"},
  {label: "旅游住宿", value: "touristAccommodation"},
  {label: "美食", value: "food"},
  {label: "母婴", value: "motherAndChild"},
  {label: "拍摄美化", value: "photographicBeautification"},
  {label: "汽车", value: "car"},
  {label: "商务", value: "business"},
  {label: "社交通讯", value: "socialNewsletter"},
  {label: "实用工具", value: "tools"},
  {label: "新闻阅读", value: "news"},
  {label: "行业通用", value: "common"},
  {label: "影音娱乐", value: "entertainment"},
  {label: "运动健康", value: "fitness"},
  {label: "主题个性", value: "theme"},
  {label: "其他", value: "others"},
]
export const SEND_TYPE_OPTIONS: SelectBaseOption<string, string>[] = [
  {label: "微信群聊", value: "mail"},
  {label: "邮件", value: "wechat"},
]


