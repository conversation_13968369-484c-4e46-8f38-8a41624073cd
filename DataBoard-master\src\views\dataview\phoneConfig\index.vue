<template>
  <div class="app-container">
    <div class="n-layout-page-header">
      <n-card :bordered="false" title="机型管理"> 管理设备的内部型号、外部型号及传播名信息 </n-card>
    </div>

    <div class="filter-container mt-4">
      <n-card :bordered="false">
        <n-form
          :model="searchForm"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          @submit.prevent="handleSearch"
        >
          <n-grid :cols="24" :x-gap="24">
            <n-grid-item v-for="item in searchFormItems" :key="item.field" :span="8">
              <n-form-item :label="item.label" :path="item.field">
                <n-input
                  v-if="item.component === 'NInput'"
                  v-model:value="searchForm[item.field]"
                  clearable
                  :placeholder="item.componentProps?.placeholder as string"
                  @keyup.enter="handleSearch"
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>
          <div class="form-actions">
            <n-space>
              <n-button @click="handleReset" type="default">重置</n-button>
              <n-button type="primary" attr-type="submit">查询</n-button>
            </n-space>
          </div>
        </n-form>
      </n-card>
    </div>

    <n-card :bordered="false" class="mt-4" size="small">
      <n-space align="center" style="margin-bottom: 16px">
        <n-button type="primary" @click="addRecord">
          <template #icon>
            <n-icon>
              <plus-outlined />
            </n-icon>
          </template>
          新增机型
        </n-button>
        <n-button type="info" @click="handleReanalyze">
          <template #icon>
            <n-icon>
              <sync-outlined />
            </n-icon>
          </template>
          重新分析
        </n-button>
      </n-space>
      <n-data-table
        remote
        :bordered="false"
        :single-line="false"
        striped
        :columns="columns"
        :data="tableData"
        :pagination="paginationReactive"
        :loading="loading"
        :scroll-x="1090"
        :row-key="(row) => row.id"
        max-height="272"
      />
    </n-card>

    <!-- 新增/编辑机型弹窗 -->
    <n-modal v-model:show="showModal" :show-icon="false" preset="dialog" :title="modalTitle">
      <n-form
        :model="formParams"
        ref="formRef"
        label-placement="left"
        :label-width="120"
        class="py-4"
      >
        <n-form-item label="BETA代号" path="internalType">
          <n-input placeholder="请输入BETA代号" v-model:value="formParams.internalType" />
        </n-form-item>
        <n-form-item label="产品型号" path="commercialType">
          <n-input placeholder="请输入产品型号" v-model:value="formParams.commercialType" />
        </n-form-item>
        <n-form-item label="传播名" path="name">
          <n-input placeholder="请输入传播名" v-model:value="formParams.name" />
        </n-form-item>
        <n-form-item label="机型类别" path="name">
          <n-input placeholder="请输入机型类别" v-model:value="formParams.deviceShape" />
        </n-form-item>
      </n-form>

      <template #action>
        <n-space>
          <n-button @click="() => (showModal = false)">取消</n-button>
          <n-button type="primary" :loading="formBtnLoading" @click="confirmForm">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  // 导入组件和工具函数
  import { h, reactive, ref, computed } from 'vue';
  import type { FormInst, FormRules, PaginationProps } from 'naive-ui';
  import {
    NCard,
    NButton,
    NSpace,
    NModal,
    NForm,
    NFormItem,
    NInput,
    NDataTable,
    NIcon,
    NGrid,
    NGridItem,
    useMessage,
    useDialog,
  } from 'naive-ui';
  import { PlusOutlined, DeleteOutlined, EditOutlined, SyncOutlined } from '@vicons/antd';
  import { createColumns, getSearchFormItems } from './columns';
  import { filterObjectValues } from '@/utils';
  import {
    deviceTypeDelete,
    deviceTypeQuery,
    deviceTypeSave,
    reanalyzeDeviceType,
  } from '@/api/dataview/phoneConfig';

  // 定义接口
  interface PhoneModel {
    id: string | number;
    internalType: string;
    commercialType: string;
    name: string;
    deviceShape: string;
  }

  // 模拟API接口
  const getPhoneModelList = async (params: any) => {
    // 模拟后端数据
    const mockData = {
      pageSize: params.pageSize || 10,
      pageNumber: params.pageNumber || 1,
      total: 55,
      list: Array.from({ length: 10 }).map((_, index) => ({
        id: params.pageNumber * params.pageSize + index,
        name: 'HUAWEI Pura X',
        commercialType: `VDE-AL00`,
        internalType: `DJY-AL00`,
      })),
    };

    // 模拟请求延迟
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(mockData);
      }, 300);
    });
  };

  // 消息与对话框
  const message = useMessage();
  const dialog = useDialog();

  // 搜索表单
  const searchFormItems = computed(() => getSearchFormItems());

  // 搜索表单数据
  const searchForm = reactive<Record<string, any>>({});

  // 表格数据
  const tableData = ref<PhoneModel[]>([]);
  const loading = ref(false);

  // 分页设置
  const paginationReactive = reactive<PaginationProps>({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    showSizePicker: true,
    pageSizes: [10, 20, 50],
    prefix({ itemCount }) {
      return `总计：${itemCount}`;
    },
    onChange: (page: number) => {
      paginationReactive.page = page;
      fetchData();
    },
    onUpdatePageSize: (pageSize: number) => {
      paginationReactive.pageSize = pageSize;
      paginationReactive.page = 1;
      fetchData();
    },
  });

  // 处理搜索
  const handleSearch = async () => {
    paginationReactive.page = 1;
    fetchData();
  };

  // 处理重置
  const handleReset = () => {
    Object.keys(searchForm).forEach((key) => {
      searchForm[key] = '';
    });
    fetchData();
  };

  // 获取表格数据
  const fetchData = async () => {
    loading.value = true;
    try {
      const queryParams = {
        ...filterObjectValues(searchForm),
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
      };
      const { total, records } = await deviceTypeQuery(queryParams);
      tableData.value = records;
      paginationReactive.itemCount = total;
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loading.value = false;
    }
  };

  // 表格列定义
  const columns = computed(() => {
    const baseColumns = createColumns();

    // 添加操作列
    const actionColumn = {
      title: '操作',
      key: 'actions',
      width: 200,
      fixed: 'right',
      align: 'center',
      render(row: PhoneModel) {
        return h(
          NSpace,
          { justify: 'center' },
          {
            default: () => [
              h(
                NButton,
                {
                  size: 'small',
                  type: 'primary',
                  onClick: () => handleEdit(row),
                },
                {
                  default: () => '编辑',
                  icon: () => h(NIcon, null, { default: () => h(EditOutlined) }),
                }
              ),
            ],
          }
        );
      },
    };

    return [...baseColumns, actionColumn];
  });

  // 弹窗相关
  const showModal = ref(false);
  const formBtnLoading = ref(false);
  const isEdit = ref(false);
  const modalTitle = computed(() => (isEdit.value ? '编辑机型' : '新增机型'));

  // 表单校验规则
  const rules: FormRules = {
    internalType: {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入BETA代号',
    },
    commercialType: {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入产品型号',
    },
    name: {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入传播名',
    },
  };

  // 弹窗表单数据
  const formRef = ref<FormInst | null>(null);
  const formParams = reactive<PhoneModel>({
    id: '',
    internalType: '',
    commercialType: '',
    name: '',
    deviceShape: '',
  });

  // 添加记录
  const addRecord = () => {
    isEdit.value = false;
    Object.assign(formParams, {
      id: '',
      internalType: '',
      deviceShape: '',
      commercialType: '',
      name: '',
    });
    showModal.value = true;
  };

  // 编辑记录
  const handleEdit = (record: PhoneModel) => {
    isEdit.value = true;
    Object.assign(formParams, { ...record });
    showModal.value = true;
  };

  // 删除记录
  const handleDelete = (record: PhoneModel) => {
    dialog.warning({
      title: '警告',
      content: '您确定要删除此记录吗？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        console.log('record', record);
        await deviceTypeDelete({ internalType: record.internalType });
        message.success('删除成功');
        fetchData();
      },
    });
  };

  // 确认表单提交
  const confirmForm = () => {
    if (!formRef.value) return;

    formRef.value.validate(async (errors) => {
      if (!errors) {
        formBtnLoading.value = true;

        // 模拟API调用
        await deviceTypeSave(formParams);
        const successMsg = isEdit.value ? '更新成功' : '添加成功';
        message.success(successMsg);
        showModal.value = false;
        fetchData();
        formBtnLoading.value = false;
      }
    });
  };

  // 处理重新分析
  const handleReanalyze = async () => {
    try {
      loading.value = true;
      await reanalyzeDeviceType();
      message.success('重新分析成功，未识别的工单已更新');
      fetchData();
    } catch (error) {
      console.error('重新分析失败:', error);
      message.error('重新分析失败');
    } finally {
      loading.value = false;
    }
  };

  // 页面加载时获取数据
  fetchData();
</script>

<style scoped>
  .app-container {
    padding: 12px;
  }

  .n-layout-page-header {
    margin: 0 -12px;
  }

  .mt-4 {
    margin-top: 16px;
  }

  .mr-2 {
    margin-right: 8px;
  }

  .py-2 {
    padding-top: 8px;
    padding-bottom: 8px;
  }

  .py-4 {
    padding-top: 16px;
    padding-bottom: 16px;
  }

  .filter-container {
    margin-bottom: 24px;
  }

  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }

  /* 修改表单项样式 */
  :deep(.n-form-item) {
    display: flex;
    margin-right: 0;
    margin-bottom: 18px;
  }

  :deep(.n-form-item-label) {
    width: 90px !important;
    text-align: right;
  }

  :deep(.n-form-item-blank) {
    flex: 1;
  }
</style>
