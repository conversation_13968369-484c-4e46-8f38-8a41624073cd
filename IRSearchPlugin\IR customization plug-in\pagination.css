/**
* CSS themes for simplePagination.js
* Author: <PERSON><PERSON><PERSON> - http://flaviusmatis.github.com/
* URL: https://github.com/flaviusmatis/simplePagination.js
*/

ul.simple-pagination {
  list-style: none;
}

.simple-pagination {
  display: block;
  overflow: hidden;
  padding: 0 5px 5px 0;
  margin: 0;
}

.simple-pagination ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.simple-pagination li {
  list-style: none;
  padding: 0;
  margin: 0;
  float: left;
}
span.ellipse.clickable {
  cursor: pointer;
}

.ellipse input {
  width: 3em;
}

/*------------------------------------*\
	Compact Theme Styles
\*------------------------------------*/
.compact-theme span {
  cursor: pointer;
}

.compact-theme a,
.compact-theme span {
  float: left;
  color: #333;
  font-size: 14px;
  line-height: 24px;
  font-weight: normal;
  text-align: center;
  border: 1px solid #aaa;
  border-left: none;
  min-width: 14px;
  padding: 0 7px;
  box-shadow: 2px 2px 2px rgba(0, 0, 0, 0.2);
  background: #efefef; /* Old browsers */
  background: -moz-linear-gradient(top, #ffffff 0%, #efefef 100%); /* FF3.6+ */
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(0%, #ffffff),
    color-stop(100%, #efefef)
  ); /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(
    top,
    #ffffff 0%,
    #efefef 100%
  ); /* Chrome10+,Safari5.1+ */
  background: -o-linear-gradient(
    top,
    #ffffff 0%,
    #efefef 100%
  ); /* Opera11.10+ */
  background: -ms-linear-gradient(top, #ffffff 0%, #efefef 100%); /* IE10+ */
  background: linear-gradient(top, #ffffff 0%, #efefef 100%); /* W3C */
}

.compact-theme a:hover,
.compact-theme li:not(.disabled):not(.active) span:hover {
  text-decoration: none;
  background: #efefef; /* Old browsers */
  background: -moz-linear-gradient(top, #efefef 0%, #bbbbbb 100%); /* FF3.6+ */
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(0%, #efefef),
    color-stop(100%, #bbbbbb)
  ); /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(
    top,
    #efefef 0%,
    #bbbbbb 100%
  ); /* Chrome10+,Safari5.1+ */
  background: -o-linear-gradient(
    top,
    #efefef 0%,
    #bbbbbb 100%
  ); /* Opera11.10+ */
  background: -ms-linear-gradient(top, #efefef 0%, #bbbbbb 100%); /* IE10+ */
  background: linear-gradient(top, #efefef 0%, #bbbbbb 100%); /* W3C */
}

.compact-theme li:first-child a,
.compact-theme li:first-child span {
  border-left: 1px solid #aaa;
  border-radius: 3px 0 0 3px;
}

.compact-theme li:last-child a,
.compact-theme li:last-child span {
  border-radius: 0 3px 3px 0;
}

.compact-theme .current {
  background: #bbbbbb; /* Old browsers */
  background: -moz-linear-gradient(top, #bbbbbb 0%, #efefef 100%); /* FF3.6+ */
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(0%, #bbbbbb),
    color-stop(100%, #efefef)
  ); /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(
    top,
    #bbbbbb 0%,
    #efefef 100%
  ); /* Chrome10+,Safari5.1+ */
  background: -o-linear-gradient(
    top,
    #bbbbbb 0%,
    #efefef 100%
  ); /* Opera11.10+ */
  background: -ms-linear-gradient(top, #bbbbbb 0%, #efefef 100%); /* IE10+ */
  background: linear-gradient(top, #bbbbbb 0%, #efefef 100%); /* W3C */
  cursor: default;
}

.compact-theme .ellipse {
  background: #eaeaea;
  padding: 0 10px;
  cursor: default;
}

/*------------------------------------*\
	Light Theme Styles
\*------------------------------------*/
.light-theme span {
  cursor: pointer;
}

.light-theme a,
.light-theme span {
  float: left;
  color: #666;
  font-size: 14px;
  line-height: 24px;
  font-weight: normal;
  text-align: center;
  border: 1px solid #bbb;
  min-width: 14px;
  padding: 0 7px;
  margin: 0 5px 0 0;
  border-radius: 3px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  background: #efefef; /* Old browsers */
  background: -moz-linear-gradient(top, #ffffff 0%, #efefef 100%); /* FF3.6+ */
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(0%, #ffffff),
    color-stop(100%, #efefef)
  ); /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(
    top,
    #ffffff 0%,
    #efefef 100%
  ); /* Chrome10+,Safari5.1+ */
  background: -o-linear-gradient(
    top,
    #ffffff 0%,
    #efefef 100%
  ); /* Opera11.10+ */
  background: -ms-linear-gradient(top, #ffffff 0%, #efefef 100%); /* IE10+ */
  background: linear-gradient(top, #ffffff 0%, #efefef 100%); /* W3C */
}

.light-theme a:hover,
.light-theme li:not(.disabled):not(.active) span:hover {
  text-decoration: none;
  background: #fcfcfc;
}

.light-theme .current {
  background: #666;
  color: #fff;
  border-color: #444;
  box-shadow: 0 1px 0 rgba(255, 255, 255, 1), 0 0 2px rgba(0, 0, 0, 0.3) inset;
  cursor: default;
}

.light-theme .ellipse {
  background: none;
  border: none;
  border-radius: 0;
  box-shadow: none;
  font-weight: bold;
  cursor: default;
}

/*------------------------------------*\
	Dark Theme Styles
\*------------------------------------*/
.dark-theme span {
  cursor: pointer;
}

.dark-theme a,
.dark-theme span {
  float: left;
  color: #ccc;
  font-size: 14px;
  line-height: 24px;
  font-weight: normal;
  text-align: center;
  border: 1px solid #222;
  min-width: 14px;
  padding: 0 7px;
  margin: 0 5px 0 0;
  border-radius: 3px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  background: #555; /* Old browsers */
  background: -moz-linear-gradient(top, #555 0%, #333 100%); /* FF3.6+ */
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(0%, #555),
    color-stop(100%, #333)
  ); /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(
    top,
    #555 0%,
    #333 100%
  ); /* Chrome10+,Safari5.1+ */
  background: -o-linear-gradient(top, #555 0%, #333 100%); /* Opera11.10+ */
  background: -ms-linear-gradient(top, #555 0%, #333 100%); /* IE10+ */
  background: linear-gradient(top, #555 0%, #333 100%); /* W3C */
}

.dark-theme a:hover,
.dark-theme li:not(.disabled):not(.active) span:hover {
  text-decoration: none;
  background: #444;
}

.dark-theme .current {
  background: #222;
  color: #fff;
  border-color: #000;
  box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2),
    0 0 1px 1px rgba(0, 0, 0, 0.1) inset;
  cursor: default;
}

.dark-theme .ellipse {
  background: none;
  border: none;
  border-radius: 0;
  box-shadow: none;
  font-weight: bold;
  cursor: default;
}

#costomization-plug-in-container em {
  color: #f73131;
  font-style: normal;
  pointer-events: none;
}
#costomization-plug-in-modal-detail pre {
  padding: 0 20px;
}

#costomization-plug-in-modal-detail strong {
  font-size: 14px;
  vertical-align: unset;
}

#costomization-plug-in-container button {
  cursor: pointer;
}

#costomization-plug-in-container label,
#costomization-plug-in-container-add-model-container label,
#costomization-plug-in-modal-detail label {
  font-weight: normal;
  margin: 0;
}

#costomization-plug-in-modal-detail input {
  margin-top: 0;
}

#costomization-plug-in-modal-detail code {
  white-space: pre-wrap;
  word-wrap: normal;
  flex: auto;
  overflow: hidden;
  font-size: 12px;
}

#costomization-plug-in-modal-detail .costomization-plug-in-modal-button {
  margin: 10px 20px;
  font-size: 14px;
  line-height: 1.5714285714285714;
  height: 32px;
  padding: 4px 15px;
  border-radius: 6px;
  box-shadow: none;
  color: #fff;
  border: 1px solid transparent;
  background: #1677ff;
  cursor: pointer;
}

#costomization-plug-in-modal-detail
  .costomization-plug-in-modal-button[disabled] {
  border: 1px solid #d9d9d9;
  cursor: not-allowed;
  color: rgba(0, 0, 0, 0.25);
  background: rgba(0, 0, 0, 0.04);
}
#costomization-plug-in-search-option-container {
  display: inline-flex;
  padding: 10px 0;
  align-items: center;
}
#costomization-plug-in-search-option-container
  .costomization-plug-in-search-option-container-label {
  align-items: center;
  display: inline-flex;
  gap: 4px;
  margin-right: 10px;
}
#costomization-plug-in-search-option-container-category-btn {
  float: right;
  color: #fff;
  background: #1677ff;
  font-size: 14px;
  line-height: 1.5714285714285714;
  height: 32px;
  padding: 4px 15px;
  border-radius: 6px;
  border: 1px solid transparent;
  margin: 10px;
}
#costomization-plug-in-container-add-model {
  width: 600px;
  max-height: calc(100vh - 40px);
  overflow-y: auto;
  position: fixed;
  left: 50%;
  top: 50%;
  background-color: #ffffff;
  z-index: 10006;
  padding: 20px;
  border-radius: 8px;
}
.custom-translate {
  transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
}

.costomization-plug-in-model-header {
  margin: 0;
  color: rgba(0, 0, 0, 0.88);
  font-weight: 600;
  font-size: 16px;
  line-height: 1.5;
  word-wrap: break-word;
}

.costomization-plug-in-container-add-model-form-item-radio {
  display: flex;
  justify-content: left;
  align-items: center;
  gap: 4px;
}

.costomization-plug-in-container-add-model-form-item {
  display: flex;
  flex-direction: column;
  align-items: start;
  gap: 4px;
  margin-top: 16px !important;
}

.costomization-plug-in-container-add-model-form-item input,
.costomization-plug-in-container-add-model-form-item select,
.costomization-plug-in-container-add-model-form-item textarea {
  box-sizing: border-box;
  margin: 0;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  line-height: 1.5714285714285714;
  list-style: none;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  position: relative;
  display: inline-block;
  width: 100%;
  min-width: 0;
  border-radius: 6px;
  transition: all 0.2s;
  background: #ffffff;
  border-width: 1px;
  border-style: solid;
  border-color: #d9d9d9;
  outline: none;
}
.costomization-plug-in-wish-category-container {
  width: 100%;
  position: relative;
}
.costomization-plug-in-wish-category-container:hover
  .costomization-plug-in-wish-category-reset {
  display: block;
}
.costomization-plug-in-wish-category-container:hover
  .costomization-plug-in-wish-category-reset.hide {
  display: none;
}

.costomization-plug-in-wish-category-reset {
  position: absolute;
  top: 20px;
  right: 4px;
  background-color: #fff;
  display: none;
}
.costomization-plug-in-container-footer {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px 0 0;
}
.costomization-plug-in-container-footer button {
  outline: none;
  position: relative;
  display: inline-flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
  background-image: none;
  background: transparent;
  border: 1px solid transparent;
  cursor: pointer;
  user-select: none;
  touch-action: manipulation;
  font-size: 14px;
  height: 32px;
  padding: 4px 15px;
  border-radius: 6px;
  border-color: #d9d9d9;
  background: #ffffff;
  outline: none;
}
.costomization-plug-in-container-footer button:last-child {
  color: #fff;
  background: #1677ff;
}
