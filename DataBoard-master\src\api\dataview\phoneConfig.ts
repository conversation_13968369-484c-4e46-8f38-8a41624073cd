import { ewpService as service } from '@/utils/axios';

export async function deviceTypeQuery(params: any): Promise<any> {
  try {
    const response = await service.post('/management/deviceType/query', params);

    return response;
  } catch (error) {
    throw error;
  }
}

export async function deviceTypeDelete(params: any): Promise<any> {
  try {
    const response = await service.post('/management/deviceType/delete', params);

    return response;
  } catch (error) {
    throw error;
  }
}

export async function deviceTypeSave(params: any): Promise<any> {
  try {
    const response = await service.post('/management/deviceType/save', params);

    return response;
  } catch (error) {
    throw error;
  }
}

export async function queryByNameOrType(params: any): Promise<any> {
  try {
    const response = await service.post('/management/deviceType/queryByNameOrType', params);

    return response;
  } catch (error) {
    throw error;
  }
}

export async function reanalyzeDeviceType() {
  const response = await service.get('/management/workOrder/identifyDeviceType');
  return response;
}
