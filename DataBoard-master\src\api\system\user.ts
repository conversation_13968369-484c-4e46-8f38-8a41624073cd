import service from "@/utils/axios";
import { UserDto } from "@/api/system/usermanage";

export interface LoginReq {
  account: string;
  password: string;
}
interface BaseRsp {
  status: string;
  message: string;
}
export interface LoginRsp extends BaseRsp {
  data: UserDto;
}
export const login: (data: LoginReq) => Promise<LoginRsp> = (data) => {
  return service({
    url: '/user/login',
    method: 'post',
    data,
  });
};
interface GetW3EmployeeInfoRsp extends BaseRsp {
  data: {
    cn: string;
    displayName: string;
    email: string;
    // 不带字母工号
    employeeNumber: string;
    employeeType: string;
    givenName: string;
    sn: string;
    telephoneNumber: string;
    // 带字母工号
    uid: string;
    uuid: string;
  };
}
export const getW3EmployeeInfo = (): Promise<GetW3EmployeeInfoRsp> => {
  return service({
    url: '/getUserInfo',
    method: 'get',
  });
};
