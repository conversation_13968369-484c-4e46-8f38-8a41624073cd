import { CodeWishReviewStatusEnum } from "@/enums/CodeWishReviewStatusEnum";
import service from "@/utils/axios";
import { CodeDemoReviewStatusEnum } from "@/enums/CodeDemoReviewStatusEnum";

export interface CodeWishDto {
  id: number;
  createTime: string;
  currentHandler: string;
  requirementReviewPassingTime: string;
  passingTime: string;
  demoLink: string;
  demoName: string;
  demoDescription: string;
  // 期望交付时间
  expectedTime: string;
  // demo心愿提交人
  demoSubmitter: string;
  // demo作者
  demoAuthor: string;
  // 承诺交付时间
  promisedTime: string;
  reviewStatus: CodeWishReviewStatusEnum;
  // 交付评审意见
  reviewSuggestions: string;
  // 需求评审意见
  requirementReviewSuggestions: string;
  // 需求评审人
  reviewer: string;
  reviewTime: string;
  reviewResult: string;
  updateTime: string;
  // 关联的demo的id
  linkDemoId: string;
  linkDemoName: string;
  linkDemoReviewStatus: CodeDemoReviewStatusEnum;
  // 交付评审组
  reviewers: string[];
  score: number;
  demoModificationType: string;
  imgUrlList: string[];
  // 示例图列表
  tdemoSampleTableList: SampleInfo[];
  //外发责任人
  outGoingPerson: string;
  //托管责任人
  custodian: string;
  source: string;
  field:string;
  creatTimes:string;
}
export interface SampleInfo {
  createTime: string;
  customizedDemoId: number;
  demoId: string;
  id: number;
  imgUrl: string;
  type: number;
  updateTime: string;
}
export interface AddCustomizedDemoReq {
  demoName: string;
  demoDescription: string;
  /* 期望交付时间 */
  expectedTime: string;
  demoSubmitter: string;
  currentHandler?: string;
  imgUrlList?: string[];
  source: string;
  field:string;
  reviewer:string;
  reviewers:string[]
}
export function addCustomizedDemo(params: AddCustomizedDemoReq) {
  return service({
    url: '/customized_demo/add',
    method: 'post',
    data: params,
  });
}
export interface DeleteCustomizedDemoReq {
  id: number;
}
export function deleteCustomizedDemo(params: DeleteCustomizedDemoReq) {
  return service({
    url: '/customized_demo/delete',
    method: 'delete',
    params,
  });
}
export interface UpdateCustomizedDemoReq {
  id: number;
  demoName?: string;
  demoDescription?: string;
  demoLink?: string;
  demoAuthor?: string;
  expectedTime?: string;
  promisedTime?: string;
  currentHandler?: string;
  reviewTime?: string;
  reviewResult?: string;
  reviewSuggestions?: string;
  requirementReviewSuggestions?: string;
  reviewer?: string;
  reviewStatus?: CodeWishReviewStatusEnum | null;
  linkDemoId?: string | null;
  score?: number;
  reviewers?: string[];
  imgUrlList?: string[];
  outGoingPerson?: string;
  custodian?: string;
  source: string;
  field:string;
  createTimes: string;
  createStartTime:string| null;
  createEndTime:string| null;
}
export function updateCustomizedDemo(params: UpdateCustomizedDemoReq) {
  return service({
    url: '/customized_demo/update',
    method: 'post',
    data: params,
  });
}
export interface QueryCustomizedDemoListReq {
  pageNum: number;
  pageSize: number;
  demo: QueryCustomizedDemoParams;
}
export interface QueryCustomizedDemoParams {
  id?: string;
  demoName?: string;
  demoAuthor?: string;
  demoSubmitter?: string;
  currentHandler?: string;
  reviewStatusList?: CodeWishReviewStatusEnum[] | null;
  createStartTime?: string;
  createEndTime?: string;
  createTimes?: string;
}
interface BaseRsp {
  message: string;
  status: string;
}
export interface QueryCustomizedDemoListRsp extends BaseRsp {
  data: {
    data: CodeWishDto[];
    pageInfo: { total: number };
  };
}
export function queryCustomizedDemoList(params: QueryCustomizedDemoListReq): Promise<QueryCustomizedDemoListRsp> {
  return service({
    url: '/customized_demo/query',
    method: 'post',
    data: params,
  });
}
export function exportWishList(params: QueryCustomizedDemoParams): Promise<BaseRsp> {
  return service({
    url: '/customized_demo/exportTestAppExcel',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}
interface AddSampleImageReq {
  customizedDemoId: number;
  imgUrl: string;
}
interface ADDSampleImageRsp extends BaseRsp{
  data: boolean;
}
export function addSampleImage(params: AddSampleImageReq): Promise<ADDSampleImageRsp> {
  return service({
    url: '/customized_demo/sample/add',
    method: 'post',
    data: params,
  });
}
interface DeleteSampleImageRsp extends BaseRsp{
  data: boolean;
}
export function deleteSampleImage(sampleId: number): Promise<DeleteSampleImageRsp> {
  return service({
    url: '/customized_demo/sample/delete',
    method: 'delete',
    params: {
      id: sampleId
    },
  });
}
export function queryRecord(demoId: number): Promise<DeleteSampleImageRsp> {
  return service({
    url: '/customized_demo/reviewRecord/query',
    method: 'get',
    params: {
      demoId: demoId
    },
  });
}
