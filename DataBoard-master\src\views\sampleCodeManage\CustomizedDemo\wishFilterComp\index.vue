<script setup lang="ts">
import {
  CodeWishFilterStatus,
  getAllStatusOptions,
} from '@/views/sampleCodeManage/CustomizedDemo/index';
import UserSearch from '@/views/comp/UserSearch.vue';
import { SOURCE_LIST } from '../consts';

const props = defineProps<{
  filters: CodeWishFilterStatus;
}>();
const emit = defineEmits<{
  (e: 'update', filters: CodeWishFilterStatus): void;
  (e: 'reset'): void;
}>();
const statusOptions = getAllStatusOptions();
const handleUpdateFilter = () => {
  emit('update', props.filters);
};
const handleResetFilter = () => {
  emit('reset');
};
</script>

<template>
  <div>
    <n-form label-placement="left" label-width="100px" label-align="left">
      <n-grid x-gap="20" :cols="3" style="margin-bottom: 12px">
        <n-gi>
          <n-form-item label="Demo名称">
            <n-input v-model:value="filters.wishName" @keyup.enter="handleUpdateFilter" clearable />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="DemoId">
            <n-input v-model:value="filters.wishId" @keyup.enter="handleUpdateFilter" clearable />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="问题状态">
            <n-select
              v-model:value="filters.reviewStatus"
              :options="statusOptions"
              multiple
              clearable
            />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="当前处理人">
            <UserSearch v-model:userLabel="filters.currentHandler"></UserSearch>
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="Demo提交人">
            <UserSearch v-model:userLabel="filters.wishSubmitter"></UserSearch>
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="交付责任人">
            <UserSearch v-model:userLabel="filters.demoAuthor"></UserSearch>
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="来源">
            <n-select v-model:value="filters.sources" :options="SOURCE_LIST" multiple clearable />
          </n-form-item>
        </n-gi>
        <n-gi>
          <n-form-item label="提交时间">
            <n-date-picker
              v-model:value="filters.createTimes"
              type="daterange"
              clearable
              :style="{ width: '160%' }"
              format="yyyy/MM/dd HH:mm:ss"
              start-placeholder="选择范围查找"
              end-placeholder="选择范围查找"
            />
          </n-form-item>
        </n-gi>
      </n-grid>
    </n-form>
    <n-space justify="center">
      <n-button secondary strong type="default" @click="handleResetFilter">重置</n-button>
      <n-button secondary strong type="primary" @click="handleUpdateFilter">查询</n-button>
    </n-space>
  </div>
</template>

<style scoped lang="less">
</style>
