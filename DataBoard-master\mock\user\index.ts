import Mock from 'mockjs';
import { resultSuccess } from '../_util';
import { defineMock } from '@alova/mock';
import { storage } from '@/utils/Storage';
import { CURRENT_USER } from '@/store/mutation-types';
const Random = Mock.Random;

const token = 'CAYWOBGKMLUEXBIHWNDMDHLPIHSLLBFF';

const whiteListUsers = [
  's00605429',
  'd00907547',
  'f00859831',
  'g30038840',
  'g00421808',
  'h60042662',
  'h30043518',
  'j60047007',
  'j30051933',
  'l30052277',
  'l00580611',
  'l30041608',
  'l00868078',
  'l30034961',
  'm60035587',
  'q60072691',
  'r60048208',
  's60034967',
  's30052949',
  's60068399',
  's60072702',
  't60068454',
  'w60051113',
  'wwx1258678',
  'w30055506',
  'w60035707',
  'w00534618',
  'w00512582',
  'w60061190',
  'x60034741',
  'xwx961461',
  'x00655026',
  'y30043600',
  'z00534823',
  'zwx1309341',
  'z60035156',
  'z60043849',
  'z30048185',
  'z60036308',
  'z60067871',
  'z60034296',
];

const adminInfo = {
  userId: '1',
  username: 'admin',
  realName: 'Admin',
  isAdmin: false,
  avatar: Random.image(),
  desc: 'manager',
  password: Random.string('upper', 4, 16),
  token,
  permissions: [
    {
      label: '主控台',
      value: 'dashboard_console',
    },
    {
      label: '监控页',
      value: 'dashboard_monitor',
    },
    {
      label: '工作台',
      value: 'dashboard_workplace',
    },
    {
      label: '基础列表',
      value: 'basic_list',
    },
    {
      label: '基础列表删除',
      value: 'basic_list_delete',
    },
  ],
};

export default defineMock({
  '[POST]/api/login': (info) => {
    const { params } = info.data;

    if (!/^[a-zA-Z]/.test(params.username)) {
      return alert('非法的username，用户名必须以字母开头');
    }

    if (params.username.toLowerCase() === 'admin') {
      if (params.password === '654321') {
        return resultSuccess({ token, ...params });
      } else {
        return alert('密码错误');
      }
    } else if (whiteListUsers.includes(params.username)) {
      return resultSuccess({ token, ...params });
    } else {
      return resultSuccess({ token, ...params });
    }
  },
  '/api/admin_info': () => {
    const curInfo = storage.get(CURRENT_USER);

    if (whiteListUsers.includes(curInfo?.username) || curInfo.username.toLowerCase() === 'admin') {
      return resultSuccess({
        ...adminInfo,
        isAdmin: true,
        username: curInfo?.username,
        userId: curInfo?.username,
      });
    }

    adminInfo.username = curInfo?.username;
    return resultSuccess(adminInfo);
  },
});
