import { ewpService as service } from '@/utils/axios';
interface IUser {
  cn: string;
  dn: string;
  sn: null;
  uid: string;
  displayName: string;
  uuid: string;
  displayNameCn: string;
  displayNameEn: string;
  givenName: string;
  employeeType: string;
  employeeNumber: string;
  globalUserID: string;
  email: string[];
}

/**
 * 获取控制台信息
 */
export function getUserInfo(): Promise<IUser> {
  return service.get('/management/oauth/getUserInfo');
}
