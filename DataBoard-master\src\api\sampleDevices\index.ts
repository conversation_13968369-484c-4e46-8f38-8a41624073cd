import service from '@/utils/axios';

export const deviceManagerInsert = (data) => {
  return service({
    url: `/device/managerInsert`,
    method: 'post',
    data,
  });
};

export const deviceManagerUpdate = (data) => {
  return service({
    url: `/device/managerUpdate`,
    method: 'post',
    data,
  });
};

export const deviceManagerSelect = () => {
  return service({
    url: `/device/managerSelect`,
    method: 'post',
  });
};

export const deviceManagerSelectBy = (data = {}) => {
  return service({
    url: `/device/managerSelectBy`,
    method: 'post',
    data,
  });
};

export const deviceBRInsert = (data) => {
  return service({
    url: `/device/BRInsert`,
    method: 'post',
    data,
  });
};

export const deviceBRSelectBy = (data) => {
  return service({
    url: `/device/BRSelectBy`,
    method: 'post',
    data,
  });
};

export const deviceBRSelect = () => {
  return service({
    url: `/device/BRSelect`,
    method: 'post',
  });
};

export const deviceBRUpdate = (data) => {
  return service({
    url: `/device/BRUpdate`,
    method: 'post',
    data,
  });
};

export const BRUpload = (data) => {
  return service({
    url: '/device/ImportBRExcel',
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};

export const downloadBRTemplate = () => {
  return service({
    url: '/device/DownloadBRExcel',
    method: 'post',
    responseType: 'blob',
  });
};

export const managerUpload = (data) => {
  return service({
    url: '/device/ImportManagerExcel',
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};

export const downloadManagerTemplate = () => {
  return service({
    url: '/device/DownloadManagerExcel',
    method: 'post',
    responseType: 'blob',
  });
};
