import { NInput } from 'naive-ui';
import { h } from 'vue';

const customType = ['update', 'new'];

export const getFeaturesColumns = (data) => [
  {
    title: '一级模块',
    key: 'firstLevel',
    width: 100,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      return customType.includes(row.customType)
        ? h(NInput, {
            value: row.firstLevel,
            type: 'text',
            size: 'small',
            onUpdateValue(v) {
              data.value[index].firstLevel = v;
            },
          })
        : row.firstLevel;
    },
  },
  {
    title: '二级模块',
    key: 'secondLevel',
    width: 100,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      return customType.includes(row.customType)
        ? h(NInput, {
            value: row.secondLevel,
            type: 'text',
            size: 'small',
            onUpdateValue(v) {
              data.value[index].secondLevel = v;
            },
          })
        : row.secondLevel;
    },
  },
  {
    title: '三级模块',
    key: 'thirdLevel',
    width: 200,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      return customType.includes(row.customType)
        ? h(NInput, {
            value: row.thirdLevel,
            type: 'text',
            size: 'small',
            onUpdateValue(v) {
              data.value[index].thirdLevel = v;
            },
          })
        : row.thirdLevel;
    },
  },
  {
    title: '来源',
    key: 'source',
    width: 100,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      return customType.includes(row.customType)
        ? h(NInput, {
            value: row.source,
            type: 'text',
            size: 'small',
            onUpdateValue(v) {
              data.value[index].source = v;
            },
          })
        : row.source;
    },
  },
  {
    title: '说明',
    key: 'description',
    width: 80,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      return customType.includes(row.customType)
        ? h(NInput, {
            value: row.description,
            type: 'text',
            size: 'small',
            onUpdateValue(v) {
              data.value[index].description = v;
            },
          })
        : row.description;
    },
  },
  {
    title: '类型',
    key: 'type',
    width: 80,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      return customType.includes(row.customType)
        ? h(NInput, {
            value: row.type,
            size: 'small',
            type: 'text',
            onUpdateValue(v) {
              data.value[index].type = v;
            },
          })
        : row.type;
    },
  },
  {
    title: 'GAP',
    key: 'gap',
    width: 80,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      return customType.includes(row.customType)
        ? h(NInput, {
            value: row.gap,
            type: 'text',
            size: 'small',
            onUpdateValue(v) {
              data.value[index].gap = v;
            },
          })
        : row.gap;
    },
  },
  {
    title: '验收',
    key: 'examResult',
    width: 80,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      return customType.includes(row.customType)
        ? h(NInput, {
            value: row.examResult,
            type: 'text',
            size: 'small',
            onUpdateValue(v) {
              data.value[index].examResult = v;
            },
          })
        : row.examResult;
    },
  },
  {
    title: '缺失',
    key: 'missingFunction',
    width: 120,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      return customType.includes(row.customType)
        ? h(NInput, {
            value: row.missingFunction,
            type: 'text',
            size: 'small',
            onUpdateValue(v) {
              data.value[index].missingFunction = v;
            },
          })
        : row.missingFunction;
    },
  },
  {
    title: 'BUG',
    key: 'bug',
    width: 120,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      return customType.includes(row.customType)
        ? h(NInput, {
            value: row.bug,
            type: 'text',
            size: 'small',
            onUpdateValue(v) {
              data.value[index].bug = v;
            },
          })
        : row.bug;
    },
  },
];
