import { ewpService as service } from '@/utils/axios';

interface VocQueryParams {
  appName?: string;
  pageNo: number;
  pageSize: number;
}

interface WorkOrderQueryParams {
  appName: string;
}

interface WorkOrderData {
  plan: any;
  functionEnhancementPercentage: any;
  versionPlan: any;
  developmentStatus: any;
  appName: string;
  crashNum: number;
  crashPercent: number;
  uxNum: number;
  uxPercent: number;
  criticalNum: number;
  criticalPercent: number;
  performanceNum: number;
  performancePercent: number;
  otherNum: number;
  otherPercent: number;
  opinionNum: number;
  allPercent: number;
}

export async function queryVoc(params: VocQueryParams): Promise<any> {
  try {
    const response = await service.post('/management/appInfo/queryVoc', params);
    return response;
  } catch (error) {
    console.error('Error fetching voc list:', error);
    throw error;
  }
}

export async function queryWorkOrder(params: WorkOrderQueryParams): Promise<WorkOrderData> {
  try {
    const response = await service.post('/management/workOrder/queryOpinion', params);
    return response;
  } catch (error) {
    console.error('Error fetching work order data:', error);
    throw error;
  }
}
export async function workOrderScene(params: any): Promise<any> {
  try {
    const response = await service.post('/management/workOrder/queryScene', params);
    return response;
  } catch (error) {
    console.error('Error fetching work order data:', error);
    throw error;
  }
}

export async function updateOwnerFunction(params: any): Promise<any> {
  try {
    const response = await service.post('/management/workOrder/updateOwnerFunction', params);
    return response;
  } catch (error) {
    console.error('Error fetching work order data:', error);
    throw error;
  }
}

export async function exportAppReport(params: any): Promise<any> {
  try {
    const response = await service.post('/management/workOrder/exportAppReport', params);
    return response;
  } catch (error) {
    console.error('Error fetching work order data:', error);
    throw error;
  }
}
export async function updateQualityInfo(params: any): Promise<any> {
  try {
    const response = await service.post('/management/appInfo/updateQualityInfo', params);
    return response;
  } catch (error) {
    console.error('Error fetching work order data:', error);
    throw error;
  }
}

export async function queryCustomerIssue(params: any): Promise<any> {
  try {
    const response = await service.post('/management/workOrder/queryCustomerIssue', params);
    return response;
  } catch (error) {
    console.error('Error fetching work order data:', error);
    throw error;
  }
}
