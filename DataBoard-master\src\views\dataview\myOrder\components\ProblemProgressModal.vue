<template>
  <n-modal
    :show="show"
    @update:show="emit('update:show', $event)"
    preset="card"
    style="width: 1100px; max-width: 95vw"
    :title="null as any"
    :mask-closable="false"
    transform-origin="center"
    class="problem-progress-modal"
  >
    <n-grid :cols="24" :x-gap="12">
      <n-grid-item :span="24">
        <n-card size="small" class="problem-summary-card">
          <template #header>
            <div class="problem-summary-header">
              <n-icon size="18" color="#2080f0">
                <AnalyticsOutline />
              </n-icon>
              <span>问题概览</span>
            </div>
          </template>
          <n-descriptions label-placement="left" :column="3" bordered>
            <n-descriptions-item label="应用名称">
              <n-tag type="success" size="small">{{ currentProblem?.appName || '未知应用' }}</n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="总进展记录">
              <n-tag type="info" size="small">{{ progressList.length }}</n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="最近更新">
              <n-tag type="warning" size="small">{{ latestUpdate }}</n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="问题描述" :span="3">
              {{ currentProblem?.description.split('】').pop() || '暂无问题描述' }}
            </n-descriptions-item>
          </n-descriptions>
        </n-card>
      </n-grid-item>
    </n-grid>

    <n-divider />

    <n-grid :cols="24" :x-gap="24">
      <!-- 左侧表单 -->
      <n-grid-item :span="10">
        <div class="form-container">
          <h3 class="section-title">
            <n-icon size="18" color="#18a058">
              <AddCircleOutline />
            </n-icon>
            添加进展记录
          </h3>
          <n-form
            ref="formRef"
            :model="formModel"
            :rules="rules"
            label-placement="left"
            label-width="auto"
            require-mark-placement="right-hanging"
          >
            <n-grid :cols="1" :x-gap="12">
              <n-grid-item>
                <!-- <n-form-item label="日期" path="createTime">
                  <n-date-picker
                    v-model:value="formModel.createTime"
                    type="date"
                    clearable
                    style="width: 100%"
                  />
                </n-form-item> -->
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="故障类型" path="faultType">
                  <n-select
                    v-model:value="formModel.faultType"
                    :options="typeOptions"
                    clearable
                    filterable
                    style="width: 100%"
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="问题归属" path="problemAttribute">
                  <n-select
                    v-model:value="formModel.problemAttribute"
                    :options="attributionOptions"
                    clearable
                    filterable
                    style="width: 100%"
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="问题进展" path="problemProcess">
                  <n-input
                    v-model:value="formModel.problemProcess"
                    type="textarea"
                    :autosize="{ minRows: 6, maxRows: 10 }"
                    placeholder="请输入问题进展详情..."
                  />
                </n-form-item>
              </n-grid-item>
            </n-grid>
            <div class="form-actions">
              <n-button round secondary @click="resetForm">重置</n-button>
              <n-button round type="primary" @click="handleSubmit">
                <template #icon>
                  <n-icon>
                    <SaveOutline />
                  </n-icon>
                </template>
                提交记录
              </n-button>
            </div>
          </n-form>
        </div>
      </n-grid-item>

      <!-- 右侧时间轴 -->
      <n-grid-item :span="14">
        <div class="timeline-container">
          <h3 class="section-title">
            <n-icon size="18" color="#2080f0">
              <TimeOutline />
            </n-icon>
            历史进展记录
          </h3>
          <div class="timeline-wrapper">
            <n-empty v-if="progressList.length === 0" description="暂无进展记录">
              <template #icon>
                <n-icon size="48" color="#d9d9d9">
                  <DocumentTextOutline />
                </n-icon>
              </template>
            </n-empty>
            <n-timeline v-else size="medium" class="custom-timeline">
              <n-timeline-item
                v-for="(item, index) in progressList"
                :key="index"
                type="default"
                :line-type="index === progressList.length - 1 ? 'dashed' : 'default'"
                class="timeline-item"
              >
                <template #header>
                  <n-space align="center" justify="space-between" class="timeline-header">
                    <n-space align="center" :size="8">
                      <span class="timeline-date">{{ formatDate(item.createTime) }}</span>
                      <span class="fault-type">{{ item.faultType }}</span>
                    </n-space>
                    <span v-if="item.problemAttribute" class="problem-attribute">{{
                      item.problemAttribute
                    }}</span>
                  </n-space>
                </template>
                <div class="timeline-content">
                  <div class="content-text">{{ item.problemProcess }}</div>
                </div>
              </n-timeline-item>
            </n-timeline>
          </div>
        </div>
      </n-grid-item>
    </n-grid>

    <n-divider />
    <!-- <div class="modal-footer">
      <n-grid :cols="2" :x-gap="16">
        <n-grid-item>
          <n-progress
            type="line"
            :percentage="70"
            :indicator-placement="'inside'"
            processing
            :height="20"
            color="#18a058"
          >
            <span style="font-size: 12px; color: #fff">解决进度: 70%</span>
          </n-progress>
        </n-grid-item>
      </n-grid>
    </div> -->
  </n-modal>
</template>

<script lang="ts" setup>
  import { ref, defineProps, defineEmits, watch, computed } from 'vue';
  import {
    NModal,
    NForm,
    NFormItem,
    NInput,
    NDatePicker,
    NSelect,
    NButton,
    NGrid,
    NGridItem,
    NTimeline,
    NTimelineItem,
    NSpace,
    NTag,
    NIcon,
    NDivider,
    NEmpty,
    NStatistic,
    FormInst,
    useMessage,
    NCard,
    NDescriptions,
    NDescriptionsItem,
    NProgress,
    NButtonGroup,
  } from 'naive-ui';
  import { format } from 'date-fns';
  import {
    AnalyticsOutline,
    AddCircleOutline,
    CloseCircleOutline,
    SaveOutline,
    TimeOutline,
    DocumentTextOutline,
    CalendarOutline,
    ConstructOutline,
    SearchOutline,
    CodeSlashOutline,
    CheckmarkDoneOutline,
    BugOutline,
  } from '@vicons/ionicons5';
  import { mySeriousQuery, mySeriousSave } from '@/api/dataview/mySerious';
  import { useEventBus } from '@/hooks/useEventBus';

  const props = defineProps<{
    show: boolean;
    orderId: string;
    currentProblem?: any;
    afterSubmit?:(row)=>{};
  }>();

  const emit = defineEmits(['update:show']);

  const message = useMessage();
  const formRef = ref<FormInst | null>(null);
  const currentFilter = ref('all');

  const formModel = ref({
    createTime: new Date(),
    faultType: null,
    problemAttribute: null,
    problemProcess: '',
  });

  // 重置表单
  const resetForm = () => {
    formModel.value = {
      createTime: new Date(),
      faultType: null,
      problemAttribute: null,
      problemProcess: '',
    };
  };

  const typeOptions = [
    { value: 'UX体验', label: 'UX体验' },
    { value: '兼容性', label: '兼容性' },
    { value: '功能故障', label: '功能故障' },
    { value: '稳定性', label: '稳定性' },
    { value: '性能功耗', label: '性能功耗' },
    { value: '安全隐私', label: '安全隐私' },
    { value: '功能缺失', label: '功能缺失' },
    { value: '应用缺失', label: '应用缺失' },
    { value: '其他', label: '其他' },
  ];
  const attributionOptions = [
    { label: '系统问题', value: '系统问题' },
    { label: '三方应用', value: '三方应用' },
    { label: '非问题', value: '非问题' },
  ];
  const rules = {
    // createTime: {
    //   // required: true,
    //   message: '请选择日期',
    //   trigger: ['blur', 'change'],
    // },
    faultType: {
      required: true,
      message: '请选择问题类型',
      trigger: ['blur', 'change'],
    },
    problemAttribute: {
      required: true,
      message: '请选择问题归属',
      trigger: ['blur', 'change'],
    },
    problemProcess: {
      required: true,
      message: '请输入问题进展',
      trigger: ['blur', 'input'],
    },
  };

  const progressList = ref<any[]>([]);
  const eventBus = useEventBus();

  const latestUpdate = computed(() => {
    if (progressList.value.length === 0) return '暂无记录';
    return formatDate(progressList.value[0].createTime);
  });
  // 监听 orderId 变化，加载数据
  watch(
    () => props.orderId,
    (newOrderId) => {
      if (newOrderId && props.show) {
        loadProgressData(newOrderId);
      }
    }
  );

  // 监听 show 变化，当打开弹窗时加载数据
  watch(
    () => props.show,
    (newShow) => {
      if (newShow && props.orderId) {
        loadProgressData(props.orderId);
      }
    }
  );

  // 加载进展数据
  const loadProgressData = async (orderId: string) => {
    try {
      // TODO: 这里替换为实际的 API 调用
      const data = await mySeriousQuery({ dts: orderId });
      console.log('data:', data);
      progressList.value = data;
      //如果progressList有数据，默认勾选第一条的数据
      if(data && data.length>0) {
        const itemData = data[0];
        formModel.value.faultType = itemData.faultType;
        formModel.value.problemAttribute = itemData.problemAttribute;
      }
      // 暂时使用模拟数据
      // progressList.value = [
      //   {
      //     createTime: '2024-03-20',
      //     type: '问题定界',
      //     problemProcess: `问题单 ${orderId} 的定界：已完成问题定界，确认是由于系统在高负载情况下连接池资源耗尽导致的问题。经过详细分析，发现连接未正确释放是主要原因。`,
      //   },
      //   {
      //     createTime: '2024-03-21',
      //     type: '数据丢失',
      //     problemProcess:
      //       '用户报告在应用使用过程中数据丢失，经排查属于云端存储同步问题。具体表现为当网络不稳定时，本地数据未能及时上传至云端，导致用户在其他设备登录时看不到最新数据。',
      //   },
      //   {
      //     createTime: '2024-03-22',
      //     type: '登录认证',
      //     problemProcess:
      //       '确认用户无法使用华为账号登录应用的问题。经测试，发现是应用端华为账号SDK集成方式有误，未正确处理授权回调流程，导致无法完成OAuth认证。',
      //   },
      //   {
      //     createTime: '2024-03-23',
      //     type: '方案设计',
      //     problemProcess:
      //       '设计解决方案：1. 优化网络错误处理逻辑；2. 增加本地数据自动备份机制；3. 实现断点续传功能；4. 添加数据同步状态指示，提升用户体验。',
      //   },
      // ];
    } catch (error) {
      message.error('加载进展数据失败');
    }
  };

  const handleSubmit = async () => {
    try {
      await formRef.value?.validate();
      await mySeriousSave({
        dts: props.orderId,
        createTime: format(formModel.value.createTime, 'yyyy-MM-dd'),
        faultType: formModel.value.faultType!,
        problemAttribute: formModel.value.problemAttribute!,
        problemProcess: formModel.value.problemProcess,
      });
      message.success('提交成功');
      if (props.afterSubmit){
        await props.afterSubmit(formModel.value);
      }
      resetForm();
      loadProgressData(props.orderId);
      // 更新待办数量
      eventBus.emit('updateMenu');
    } catch (err) {
      // 表单验证失败
    }
  };

  const formatDate = (createTime: string) => {
    return format(new Date(createTime), 'yyyy-MM-dd');
  };

  const getTimelineType = (type: string) => {
    const typeMap: Record<string, 'default' | 'info' | 'success' | 'warning' | 'error'> = {
      UX体验: 'info',
      兼容性: 'warning',
      功能故障: 'error',
      稳定性: 'warning',
      性能功耗: 'success',
      安全隐私: 'info',
      功能缺失: 'warning',
      应用缺失: 'error',
      其他: 'warning',
    };
    return typeMap[type] || 'default';
  };

  const getTagType = (type: string) => {
    return getTimelineType(type);
  };

  const getIconColor = (type: string) => {
    const colorMap: Record<string, string> = {
      UX体验: '#2080f0',
      兼容性: '#f0a020',
      功能故障: '#d03050',
      稳定性: '#f0a020',
      性能功耗: '#18a058',
      安全隐私: '#2080f0',
      功能缺失: '#f0a020',
      应用缺失: '#d03050',
      其他: '#f0a020',
    };
    return colorMap[type] || '#909399';
  };

  const getTimelineIcon = (type: string) => {
    const iconMap: Record<string, any> = {
      UX体验: SearchOutline,
      兼容性: ConstructOutline,
      功能故障: CodeSlashOutline,
      稳定性: BugOutline,
      性能功耗: CheckmarkDoneOutline,
      // 可以根据不同类型选择合适的图标
      安全隐私: DocumentTextOutline,
      功能缺失: SearchOutline,
      应用缺失: BugOutline,
      其他: SearchOutline,
    };
    return iconMap[type] || AnalyticsOutline;
  };

  const setFilter = (filter: string) => {
    currentFilter.value = filter;
    // 这里可以实现筛选逻辑
  };
</script>

<style scoped>
  .problem-progress-modal {
    border-radius: 8px;
  }

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 4px;
  }

  .title-container {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .title-icon {
    margin-right: 4px;
  }

  .modal-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #333;
  }

  .order-id {
    color: #2080f0;
    font-weight: 500;
  }

  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    margin-bottom: 16px;
    color: #333;
  }

  .form-container,
  .timeline-container {
    padding: 8px 4px;
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
  }

  .timeline-wrapper {
    height: 450px;
    overflow-y: auto;
    padding: 8px 12px;
    background-color: #f9f9f9;
    border-radius: 8px;
    scrollbar-width: thin;
    scrollbar-color: #d9d9d9 #f5f5f5;
  }

  .timeline-wrapper::-webkit-scrollbar {
    width: 6px;
  }

  .timeline-wrapper::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 3px;
  }

  .timeline-wrapper::-webkit-scrollbar-thumb {
    background: #d9d9d9;
    border-radius: 3px;
  }

  .timeline-header {
    width: 100%;
    padding: 2px 0;
  }

  .timeline-date {
    font-weight: 500;
    color: #606266;
    font-size: 13px;
  }

  .fault-type {
    font-size: 13px;
    color: #606266;
  }

  .problem-attribute {
    font-size: 13px;
    color: #8c8c8c;
  }

  .timeline-content {
    margin-top: 4px;
    line-height: 1.5;
    background-color: #fff;
    padding: 10px;
    border-radius: 6px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
  }

  .timeline-content:hover {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  }

  .content-text {
    color: #333;
    font-size: 13px;
    white-space: pre-wrap;
  }

  .timeline-item {
    padding-bottom: 12px;
    position: relative;
  }

  .timeline-icon {
    background-color: #fff;
    padding: 6px;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .timeline-item:hover .timeline-icon {
    transform: scale(1.1);
  }

  .custom-timeline {
    padding: 8px 0;
  }

  .custom-timeline :deep(.n-timeline-item-content) {
    margin-top: 8px;
  }

  .custom-timeline :deep(.n-timeline-item-line) {
    background-color: #e4e7ed;
  }

  .custom-timeline :deep(.n-timeline-item-dot) {
    border-color: #e4e7ed;
  }

  .modal-footer {
    padding: 8px 16px;
  }

  .statistic {
    text-align: center;
  }

  .status-card {
    width: 100px;
    text-align: center;
    border-radius: 8px;
    transition: all 0.3s ease;
  }

  .status-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }

  .status-count {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 4px;
  }

  .status-label {
    font-size: 14px;
    color: #606266;
  }

  .problem-summary-card {
    margin-bottom: 16px;
  }

  .problem-summary-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
  }

  .timeline-container {
    padding: 8px 4px;
  }

  .timeline-filter {
    display: flex;
    margin-bottom: 16px;
    padding: 8px 12px;
    background-color: #f5f7fa;
    border-radius: 8px;
  }

  .filter-label {
    font-size: 14px;
    color: #606266;
    margin-right: 8px;
  }
</style>
