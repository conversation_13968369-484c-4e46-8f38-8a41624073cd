<template>
  <div>
    <n-card style="margin-bottom: 12px">
      <wish-filter-comp
        :filters="filterStatus"
        @update="handleFiltersChange"
        @reset="handleFiltersReset"
      ></wish-filter-comp>
    </n-card>
    <div class="layout-page-header">
      <n-space align="center">
        <n-button secondary strong type="primary" @click="handleAddWishListClick">
          <template #icon>
            <n-icon>
              <HeartOutline />
            </n-icon>
          </template>
          添加demo
        </n-button>
        <!-- <n-button
            secondary
            strong
            type="primary"
            @click="handleAddCodeClick"
        >
          <template #icon>
            <n-icon>
              <CodeWorking/>
            </n-icon>
          </template>
          贡献demo
        </n-button> -->
        <n-button type="error" secondary :loading="exportLoading" @click="handleExport">
          导出Excel
        </n-button>
        <n-button secondary strong type="warning" @click="getWishList()">
          <template #icon>
            <n-icon>
              <Refresh />
            </n-icon>
          </template>
          刷新
        </n-button>
        <n-checkbox id="onlyISubmitted" v-model:checked="filterStatus.onlyISubmitted">
          只看我提交的
        </n-checkbox>
        <n-checkbox id="onlyIDelivered" v-model:checked="filterStatus.onlyIDelivered">
          只看我交付的
        </n-checkbox>
        <n-checkbox id="onlyMyTodo" v-model:checked="filterStatus.onlyMyTodo">
          只看我的待办
        </n-checkbox>
      </n-space>
    </div>
    <n-data-table
      :columns="columns"
      :data="data"
      @update:sorter="handleSorterChange"
      :pagination="pagination"
      :loading="isLoading"
      scroll-x
      :max-height="2000"
      remote
      style="margin-top: 20px"
    />
    <!--基本信息弹窗-->
    <n-modal v-model:show="showBasicInfoModal" :on-after-leave="initModel">
      <n-card
        style="width: 600px"
        :title="isAddWishList ? '添加样例代码需求' : '编辑样例代码需求信息'"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form
          ref="wishListFormRef"
          :model="wishInfo"
          :rules="formRules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="medium"
          :style="{
            maxWidth: '640px',
          }"
        >
          <n-form-item label="名称" path="demoName">
            <n-input v-model:value="wishInfo.demoName" placeholder="请输入demo名称" />
          </n-form-item>
          <n-form-item label="描述" path="demoDescription">
            <n-input
              type="textarea"
              v-model:value="wishInfo.demoDescription"
              placeholder="请输入demo描述"
            />
          </n-form-item>
          <n-form-item label="来源" path="source">
            <n-select v-model:value="wishInfo.source" :options="sourceList" />
          </n-form-item>
          <n-form-item label="领域" path="field">
            <n-select v-model:value="wishInfo.field" :options="firedList" />
          </n-form-item>
          <n-form-item label="示例">
            <n-upload
              action="https://dtse.cbg.huawei.com:8777/upload"
              list-type="image-card"
              @finish="uploadFinish"
              :default-file-list="wishInfo.imgList"
              @preview="handlePreview"
              @remove="handleRemove"
            >
            </n-upload>
            <n-modal
              v-model:show="showPreviewModal"
              preset="card"
              style="width: 400px"
              title="示例"
            >
              <img :src="previewImageUrlRef" style="width: 100%" />
            </n-modal>
          </n-form-item>
          <n-form-item label="期望交付时间" path="expectedTime">
            <n-date-picker
              v-model:value="wishInfo.expectedTime"
              type="datetime"
              :is-date-disabled="disablePreviousDate"
              clearable
            />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space justify="center">
            <n-button
              v-if="!isAddWishList"
              strong
              type="default"
              @click="handleBasicInfoSubmit(true)"
              >仅保存
            </n-button>
            <n-button strong type="primary" @click="handleBasicInfoSubmit(false)"
              >提交审核
            </n-button>
            <n-button strong type="error" @click="showBasicInfoModal = false">取消 </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
    <!--需求评审弹窗-->
    <n-modal v-model:show="showWishReviewInfoModal" :on-after-leave="initModel">
      <n-card
        style="width: 600px"
        title="样例代码需求评审"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form
          ref="reviewFormRef"
          :model="wishInfo"
          :rules="formRules"
          label-placement="left"
          label-width="120"
          label-align="right"
          require-mark-placement="right-hanging"
          size="medium"
          :style="{
            maxWidth: '640px',
          }"
        >
          <n-form-item
            v-if="wishReviewStatusOptions.length > 1"
            label="评审结果"
            path="reviewStatus"
          >
            <n-select
              v-model:value="wishInfo.reviewStatus!"
              @change="resultChange"
              :options="wishReviewStatusOptions"
              placeholder="请选择评审结果"
            />
          </n-form-item>
          <n-form-item label="评审意见" path="requirementReviewSuggestions">
            <n-input
              type="textarea"
              v-model:value="wishInfo.requirementReviewSuggestions"
              placeholder="请输入评审意见"
            />
          </n-form-item>
          <template v-if="wishInfo.reviewStatus === CodeWishReviewStatusEnum.UNDER_DEVELOPMENT">
            <n-form-item label="分值" path="score">
              <n-input
                :allow-input="onlyNumber"
                v-model:value="wishInfo.score"
                placeholder="请输入分值，1-100之间的正整数"
              />
            </n-form-item>
            <n-form-item label="指派给" path="demoAuthor">
              <UserSearch @userSelect="onUserSelect"></UserSearch>
            </n-form-item>
            <n-form-item label="承诺交付时间" path="promisedTime">
              <n-date-picker
                v-model:value="wishInfo.promisedTime"
                type="datetime"
                :is-date-disabled="disablePreviousDate"
                clearable
              />
            </n-form-item>
          </template>
          <n-form-item label="评审时间" path="reviewTime">
            <n-date-picker v-model:value="wishInfo.reviewTime" type="date" clearable />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" @click="handleReviewInfoSubmit">
              确认
            </n-button>
            <n-button secondary strong type="error" @click="showWishReviewInfoModal = false">
              取消
            </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
    <!--评审规则弹窗-->
    <n-modal
      preset="card"
      :closable="false"
      title="评审规则"
      style="width: 500px"
      v-model:show="showReviewRulesModal"
    >
      <div v-for="(rule, index) in REVIEW_RULES">
        <span>{{ `${index + 1}.` }}</span>
        <span v-html="rule.replace(/(\n\r|\n|\r)/g, '<br>&nbsp;&nbsp;')"></span>
      </div>
      <template #footer>
        <n-space justify="center">
          <n-button secondary strong type="default" @click="showReviewRulesModal = false">
            关闭</n-button
          >
        </n-space>
      </template>
    </n-modal>
    <!--关联样例代码弹窗-->
    <n-modal v-model:show="showAssociatingSampleCodeModal" :on-after-leave="initModel">
      <n-card
        style="width: 600px"
        title="关联样例代码"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form
          ref="deliverFormRef"
          :model="wishInfo"
          :rules="formRules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="medium"
          :style="{
            maxWidth: '640px',
          }"
        >
          <n-form-item label="样例代码ID" path="linkDemoId">
            <n-input v-model:value="wishInfo.linkDemoId" placeholder="请输入样例代码ID" />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" @click="handleAssociatingSampleCode">
              关联
            </n-button>
            <n-button secondary strong type="error" @click="showAssociatingSampleCodeModal = false">
              取消
            </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
    <!--交付评审弹窗-->
    <n-modal v-model:show="showCodeReviewInfoModal" :on-after-leave="initModel">
      <n-card
        style="width: 600px"
        title="样例代码评审"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form
          ref="acceptFormRef"
          :model="wishInfo"
          :rules="formRules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="medium"
          :style="{
            maxWidth: '640px',
          }"
        >
          <n-form-item label="评审结果" path="reviewStatus">
            <n-select
              v-model:value="wishInfo.reviewStatus!"
              :options="wishReviewStatusOptions"
              placeholder="请选择验收结果"
              @change="resultChange"
            />
          </n-form-item>
          <n-form-item
            v-if="wishInfo.reviewStatus === CodeWishReviewStatusEnum.PASSED"
            label="最终分值"
            path="score"
          >
            <n-input
              :allow-input="onlyNumber"
              v-model:value="wishInfo.score"
              placeholder="请输入分值，1-100之间的正整数"
            />
          </n-form-item>
          <n-form-item label="评审意见" path="reviewSuggestions">
            <n-input
              type="textarea"
              v-model:value="wishInfo.reviewSuggestions"
              placeholder="请输入评审意见"
            />
          </n-form-item>
          <n-form-item label="评审组" path="reviewers">
            <n-select
              v-model:value="wishInfo.reviewers"
              :options="demoReviewerList"
              multiple
              placeholder="请选择参与评审人员"
            />
          </n-form-item>
          <n-form-item label="评审时间" path="reviewTime">
            <n-date-picker v-model:value="wishInfo.reviewTime" type="date" />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" @click="handleCodeReviewSubmit">
              确认
            </n-button>
            <n-button secondary strong type="error" @click="showCodeReviewInfoModal = false">
              取消
            </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
    <!-- 评审记录弹窗 -->
    <n-modal v-model:show="showHistoryModal">
      <n-card
        style="width: 1000px"
        title=""
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <span style="font-size: 18px; color: #303133; font-weight: bold"> 需求评审记录</span>
        <n-data-table
          :columns="columnsHistory"
          :data="tableDataHistoryRev"
          style="margin-top: 12px"
          :single-line="false"
          max-height="700px"
        >
        </n-data-table>
        <div style="margin-top: 10px">
          <span style="font-size: 18px; color: #303133; font-weight: bold">交付评审记录</span>
          <n-data-table
            :columns="columnsHistory"
            :data="tableDataHistoryAcp"
            style="margin-top: 12px"
            :single-line="false"
            max-height="700px"
          >
          </n-data-table>
        </div>
      </n-card>
    </n-modal>
    <!--选择评审人弹窗-->
    <n-modal v-model:show="selectHandlerModal" :on-after-leave="initModel">
      <n-card
        style="width: 600px"
        title="选择评审人"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form
          ref="selectHandlerFormRef"
          :model="wishInfo"
          :rules="formRules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="medium"
          :style="{
            maxWidth: '640px',
          }"
        >
          <n-form-item label="评审人" path="currentHandler">
            <n-select
              v-model:value="wishInfo.currentHandler"
              :options="reviewerOptions"
              placeholder="请选择评审人"
            />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" @click="handleSelectHandlerSubmit">
              确认
            </n-button>
            <n-button secondary strong type="error" @click="selectHandlerModal = false">
              取消
            </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
    <!--demo信息弹窗-->
    <sample-code-info
      v-model:show="showCodeInfoDrawer"
      v-model:mode="codeDrawerMode"
      :is-admin="isAdmin"
      :wish-info="wishInfo"
      @close="onCodeDrawerClose"
    >
    </sample-code-info>
    <!--交付弹窗-->
    <n-modal
      v-model:show="showDeliverModal"
      preset="card"
      style="width: 600px"
      title="交付确认"
      size="huge"
      :bordered="false"
      :on-after-leave="(selectedDeliverTips = [])"
    >
      <n-checkbox-group v-model:value="selectedDeliverTips">
        <n-grid :cols="1" :y-gap="8">
          <n-gi v-for="tip in DELIVER_TIPS">
            <n-checkbox :value="tip" :label="tip" />
          </n-gi>
        </n-grid>
      </n-checkbox-group>
      <template #footer>
        <n-space>
          <n-button
            secondary
            strong
            type="primary"
            :disabled="selectedDeliverTips.length < DELIVER_TIPS.length"
            @click="handleDeliverSubmit"
          >
            确认
          </n-button>
          <n-button secondary strong type="error" @click="showDeliverModal = false">
            取消
          </n-button>
        </n-space>
      </template>
    </n-modal>
    <!--指定外发人弹窗-->
    <n-modal
      v-model:show="showDesignedOutgoingPersonModal"
      preset="card"
      style="width: 600px"
      title="指定外发人"
      size="huge"
      :bordered="false"
      :on-after-leave="initModel"
    >
      <n-form
        ref="designedOutgoingPersonFormRef"
        :model="wishInfo"
        :rules="formRules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        size="medium"
        :style="{
          maxWidth: '640px',
        }"
      >
        <n-form-item label="外发责任人" path="outGoingPerson">
          <UserSearch
            v-model:userLabel="wishInfo.outGoingPerson"
            placeholder="请选择代码外发责任人"
          ></UserSearch>
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space>
          <n-button secondary strong type="primary" @click="handleDesignatedOutgoingPersonSubmit">
            确认
          </n-button>
          <n-button secondary strong type="error" @click="showDesignedOutgoingPersonModal = false">
            取消
          </n-button>
        </n-space>
      </template>
    </n-modal>
    <!--外发邮件-->
    <n-modal
      v-model:show="showEmailModal"
      preset="card"
      style="width: 500px"
      title="外发信息"
      size="huge"
      :bordered="false"
      :on-after-leave="initModel"
    >
      <n-form
        ref="hostingFormRef"
        :model="wishInfo"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        size="medium"
        :style="{
          maxWidth: '640px',
        }"
      >
        <n-form-item label="demo名称：" path="demoName">
          {{ wishInfo.demoName }}
        </n-form-item>
        <n-form-item label="内部链接：" path="demoLink">
          <a :href="wishInfo.demoLink" target="blank">{{ wishInfo.demoLink }}</a>
        </n-form-item>
        <n-form-item label="行数：" path="codeLines">
          {{ wishInfo.codeLines || '--' }}
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space>
          <n-button strong type="primary" @click="createEmlDialog()"> 一键生成邮件 </n-button>
        </n-space>
      </template>
    </n-modal>
    <!--托管弹窗-->
    <n-modal
      v-model:show="showHostingModal"
      preset="card"
      style="width: 600px"
      title="托管确认"
      size="huge"
      :bordered="false"
      :on-after-leave="initModel"
    >
      <n-form
        ref="hostingFormRef"
        :model="wishInfo"
        :rules="formRules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        size="medium"
        :style="{
          maxWidth: '640px',
        }"
      >
        <n-form-item label="托管链接" path="externalLink">
          <n-input clearable placeholder="请输入托管链接" v-model:value="wishInfo.externalLink" />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space>
          <n-button secondary strong type="primary" @click="handleHostingSubmit"> 确认 </n-button>
          <n-button secondary strong type="error" @click="showHostingModal = false">
            取消
          </n-button>
        </n-space>
      </template>
    </n-modal>
    <!--详情 -->
    <n-drawer v-model:show="isShowDetail" :width="1100">
      <n-drawer-content closable>
        <demo-detail
          :formData="formData"
          :columnsHistory="columnsHistory"
          :tableDataHistoryAcp="tableDataHistoryAcp"
          :tableDataHistoryRev="tableDataHistoryRev"
          :isAdmin="isAdmin"
          :curPermissionMap="curPermissionMap"
          @handleSubmit="handleSubmit"
          @handleDele="handleDele"
          @handleEdit="handleEdit"
          @handleSampleCode="handleSampleCode"
          @handleClaimin="handleClaimin"
          @handleRev="handleRev"
          @handleReviewClick="handleReviewClick"
          @showLinkDemo="showLinkDemo"
          @handleOut="handleOut"
          @handleUpdtate="handleUpdtate"
          @handleHosting="handleHosting"
        ></demo-detail>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script lang="ts" setup>
import { CodeWorking, HeartOutline, Refresh } from '@vicons/ionicons5';
import {
  DataTableColumns,
  FormRules,
  NButton,
  NIcon,
  SelectOption,
  UploadFileInfo,
  useDialog,
  useMessage,
  NEllipsis,
} from 'naive-ui';
import { h, reactive, ref, VNode, watch, onMounted } from 'vue';
import {
  addCustomizedDemo,
  addSampleImage,
  deleteCustomizedDemo,
  deleteSampleImage,
  exportWishList,
  queryRecord,
  queryCustomizedDemoList,
  QueryCustomizedDemoParams,
  updateCustomizedDemo,
  UpdateCustomizedDemoReq,
} from '@/api/system/customizedDemo';
import {
  CodeWishFormModel,
  CodeWishVo,
  CodeWishFilterStatus,
  getCodeReviewParams,
  getAddCustomizedDemoParams,
  getClaimingParams,
  getCodeWishFormModel,
  getCodeWishVo,
  getDefaultCodeWishFormModel,
  getDefaultFilterStatus,
  getDeliverParams,
  getSubmitReviewParams,
  getUpdateBasicInfoParams,
  getWishReviewParams,
  getWishReviewOptions,
  isUserAdmin,
  getOutgoingParams,
  getDesignatedOutGoingPersonParams,
  getHostingParams,
  formatTime,
} from './index';
import { queryUser, UserDto } from '@/api/system/usermanage';
import { CodeWishReviewStatusEnum } from '@/enums/CodeWishReviewStatusEnum';
import { getUserLabel, useUserStore } from '@/store/modules/user';
import { COLUMNSTABLE } from '@/views/sampleCodeManage/CustomizedDemo/columns';
import UserSearch from '@/views/comp/UserSearch.vue';
import demoDetail from './demoDetail.vue';
import {
  CODE_WISH_LIST_FORM_RULES,
  CodeSampleDrawerMode,
  DELIVER_TIPS,
  SOURCE_LIST,
  SOURCE_LIST_SAMPLE_OUTGOING,
  FIRED_LIST_MAP,
} from './consts';
import { REVIEW_RULES, getFilterOptions, ALL_DEMO_TYPES } from '../consts';
import SampleCodeInfo from '@/views/sampleCodeManage/CustomizedDemo/sampleCodeInfo/index.vue';
import WishFilterComp from '@/views/sampleCodeManage/CustomizedDemo/wishFilterComp/index.vue';
import { UserRoleEnum } from '@/enums/UserRoleEnum';
import { updateDemo } from '@/api/system/code';
import { useRoute } from 'vue-router';
import { log } from 'console';
const route = useRoute();
enum ModelEnum {
  REVIEW,
  ACCEPT,
}
const previewImageUrlRef = ref('');
const formData = ref({});
const curPermissionMap = ref({});
const showPreviewModal = ref(false);
const showBasicInfoModal = ref(false);
const selectHandlerModal = ref(false);
const showWishReviewInfoModal = ref(false);
const showCodeReviewInfoModal = ref(false);
const showAssociatingSampleCodeModal = ref(false);
const showCodeInfoDrawer = ref(false);
const showReviewRulesModal = ref(false);
const showDeliverModal = ref(false);
const showDesignedOutgoingPersonModal = ref(false);
const showEmailModal = ref(false);
const showHostingModal = ref(false);
const isAddWishList = ref(false);
const codeDrawerMode = ref<CodeSampleDrawerMode>(CodeSampleDrawerMode.SHOW_ONLY);
const userInfo = useUserStore().getUserInfo;
const isAdmin = isUserAdmin() || userInfo.isSuperAdmin;
const dialog = useDialog();
const wishListFormRef = ref();
const reviewFormRef = ref();
const deliverFormRef = ref();
const acceptFormRef = ref();
const selectHandlerFormRef = ref();
const tableDataHistoryRev = ref([]);
const tableDataHistoryAcp = ref([]);
const designedOutgoingPersonFormRef = ref();
const hostingFormRef = ref();
const message = useMessage();
const isLoading = ref(false);
const filterStatus = ref<CodeWishFilterStatus>(getDefaultFilterStatus());
const data = ref<CodeWishVo[]>([]);
const wishInfo = ref<CodeWishFormModel>(getDefaultCodeWishFormModel());
const reviewStatus = ref(null);
const originalStatus = ref(Number);
const wishReviewStatusOptions = ref<SelectOption[]>([]);
const reviewerOptions = ref<SelectOption[]>([]);
const isShowDetail = ref(false);
const formRules: FormRules = CODE_WISH_LIST_FORM_RULES;
const onlyNumber = (val: string) => /^\d*$/.test(val);
const typeOptions = getFilterOptions(ALL_DEMO_TYPES);
const demoReviewerList = ref<SelectOption[]>([]);
const selectedDeliverTips = ref<string[]>([]);
const exportLoading = ref(false);
const sourceList = userInfo.roles.includes(UserRoleEnum.SAMPLE_OUTGOING_ADMIN)
  ? SOURCE_LIST.concat(SOURCE_LIST_SAMPLE_OUTGOING)
  : SOURCE_LIST;
const firedList = Object.keys(FIRED_LIST_MAP).map((key: string) => {
  return { label: key, value: key };
});
const columnsHistory = ref([
  {
    title: '评审结果',
    key: 'reviewResult',
    width: 120,
  },
  {
    title: '评审意见',
    key: 'reviewSuggestion',
    ellipsis: {
      tooltip: true,
      lineClamp: 2,
    },
    width: 180,
  },
  {
    title: '评审组',
    key: 'reviewers',
  },
  {
    title: '评审时间',
    key: 'reviewTime',
  },
]);

watch(
  filterStatus,
  (filterStatus, prev) => {
    pagination.page = 1;
    getWishList();
  },
  {
    deep: true,
  }
);
function onCodeDrawerClose(needRefresh: boolean): void {
  showCodeInfoDrawer.value = false;
  needRefresh && getWishList();
}
// 更新样式代码需求评审状态
function updateReviewInfo(params: UpdateCustomizedDemoReq) {
  updateCustomizedDemo(params)
    .then((res) => {
      if (res) {
        message.success('状态更新成功！');
        showWishReviewInfoModal.value = false;
        getWishList();
      } else {
        message.error('状态更新失败！');
      }
    })
    .catch((e) => {
      message.error(`状态更新失败，原因：${e?.response?.data?.error}`);
    });
}
function handleReviewClick(row: CodeWishVo, model: ModelEnum) {
  wishInfo.value = getCodeWishFormModel(row);
  model === ModelEnum.REVIEW && (showCodeReviewInfoModal.value = true);
  model === ModelEnum.ACCEPT && (showWishReviewInfoModal.value = true);
  const result = getWishReviewOptions(row.reviewStatus!);
  wishReviewStatusOptions.value = result.reviewOptions;
  wishInfo.value.reviewStatus = result.reviewStatus;
  wishInfo.value.reviewTime = new Date().getTime(); // 需求评审-评审时间回填当前时间
  isShowDetail.value = false;
}
// demo关联样式代码需求单
function associatingSampleCode(linkDemoId: string) {
  if (!wishInfo.value.id) {
    message.error('demo需求单状态异常！');
    return;
  }
  updateCustomizedDemo({
    id: wishInfo.value.id,
    linkDemoId,
  })
    .then((res) => {
      if (res) {
        message.success('demo需求单关联样例代码成功！');
        getWishList();
      } else {
        message.error('demo需求单关联样例代码失败！');
      }
    })
    .catch((e) => {
      message.error(`demo需求单关联样例代码失败，原因：${e?.response?.data?.error}`);
    });
}
const handleSubmit = (row) => {
  wishInfo.value = getCodeWishFormModel(row);
  updateReviewInfo(getSubmitReviewParams(wishInfo.value));
  getWishList();
  isShowDetail.value = false;
};
const handleDele = (row) => {
  dialog.warning({
    title: '警告',
    content: `确定删除demo需求：${row.demoName}吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      handleDeleteSubmit(row);
    },
    onNegativeClick: () => {},
  });
  isShowDetail.value = false;
};

const handleEdit = (row) => {
  isAddWishList.value = false;
  showBasicInfoModal.value = true;
  wishInfo.value = getCodeWishFormModel(row);
  isShowDetail.value = false;
};
const handleSampleCode = (row) => {
  wishInfo.value = getCodeWishFormModel(row);
  if (row.linkDemoId) {
    showDeliverModal.value = true;
  } else {
    dialog.warning({
      title: '提示',
      content: `该demo暂未关联样例代码，请选择新增或关联现有样例代码`,
      positiveText: '去新增',
      negativeText: '去关联',
      onPositiveClick: () => {
        codeDrawerMode.value = CodeSampleDrawerMode.ADD;
        showCodeInfoDrawer.value = true;

        isShowDetail.value = false;
      },
      onNegativeClick: () => {
        showAssociatingSampleCodeModal.value = true;
      },
    });
  }
};
const handleClaimin = (row) => {
  dialog.warning({
    title: '提示',
    content: `确定认领demo需求：${row.demoName}吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      updateReviewInfo(getClaimingParams(row.id));
      isShowDetail.value = false;
    },
    onNegativeClick: () => {},
  });
};
// 交付评审
const handleRev = async (row) => {
  // 获取交付评审人
  const reviewerConfig = await getReviewList([UserRoleEnum.SAMPLE_CODE_REVIEWER]);
  demoReviewerList.value = reviewerConfig.reviewerOptions
  // wishInfo.reviewers.value = [reviewerConfig.singleReviewer]
  handleReviewClick(row, ModelEnum.REVIEW);
};
// 指定外发人
const handleOut = (row) => {
  wishInfo.value = getCodeWishFormModel(row);
  showDesignedOutgoingPersonModal.value = true;
};
// 外发
const handleUpdtate = (row) => {
  dialog.warning({
    title: '提示',
    content: `已经外发：${row.linkDemoName}了吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      updateReviewInfo(getOutgoingParams(row.id));
    },
    onNegativeClick: () => {},
  });
};
// 托管
const handleHosting = (row) => {
  wishInfo.value = getCodeWishFormModel(row);
  showHostingModal.value = true;
};

const queryPermission = (row) => {
  const permissionMap = {
    isSubmitter: userInfo.label === row.demoSubmitter,
    isCurrentHandler: userInfo.label === row.currentHandler,
    isDemoAuthor: userInfo.label === row.demoAuthor,
    isToBeSubmitted: row.reviewStatus === CodeWishReviewStatusEnum.TO_BE_SUBMITTED,
    isToBeAccepted: row.reviewStatus === CodeWishReviewStatusEnum.TO_BE_ACCEPTED,
    isToBeDelivered: row.reviewStatus === CodeWishReviewStatusEnum.UNDER_DEVELOPMENT,
  };
  return permissionMap;
};
const showLinkDemo = (row) => {
  wishInfo.value = getCodeWishFormModel(row);
  codeDrawerMode.value = CodeSampleDrawerMode.SHOW_ONLY;
  showCodeInfoDrawer.value = true;
};
// 获取操作按钮
function getOperations(row: CodeWishVo): VNode[] {
  const permissionMap = queryPermission(row);
  let result: VNode[] = [];
  // 显示编辑和提交评审按钮：用户是提交人且状态为待提交
  if (permissionMap['isSubmitter'] && permissionMap['isToBeSubmitted']) {
    result.push(
      h(
        NButton,
        {
          strong: true,
          tertiary: true,
          type: 'info',
          size: 'medium',
          text: true,
          style: 'margin-right:20px',
          onClick: () => {
            handleEdit(row);
          },
        },
        [h('div', '编辑')]
      )
    );
    result.push(
      h(
        NButton,
        {
          strong: true,
          tertiary: true,
          type: 'info',
          size: 'medium',
          style: 'margin-right:20px',
          text: true,
          onClick: () => {
            dialog.warning({
              title: '提示',
              content: `确定提交评审吗？`,
              positiveText: '确定',
              negativeText: '取消',
              onPositiveClick: () => {
                handleSubmit(row);
              },
              onNegativeClick: () => {},
            });
          },
        },
        [h('div', '提交')]
      )
    );
  }
  // 显示需求评审按钮：用户是管理员但不是提交人，且状态为待接纳
  if (
    !permissionMap['isSubmitter'] &&
    (isAdmin || (!!row.reviewer && row.reviewer === userInfo.label)) &&
    permissionMap['isToBeAccepted']
  ) {
    result.push(
      h(
        NButton,
        {
          strong: true,
          tertiary: true,
          type: 'info',
          size: 'medium',
          text: true,
          style: 'margin-right:20px',
          onClick: () => {
            handleReviewClick(row, ModelEnum.ACCEPT);
          },
        },
        [h('div', '需求评审')]
      )
    );
  }
  // 显示修改按钮：待交付状态，用户是管理员但不是提交人，且没有demoAuthor
  // if (row.reviewStatus === CodeWishReviewStatusEnum.UNDER_DEVELOPMENT && !permissionMap['isSubmitter']&& isAdmin && !row.demoAuthor) {
  //   result.push(h(
  //       NButton,
  //       {
  //         strong: true,
  //         tertiary: true,
  //         type: 'info',
  //         size: 'medium',
  //         text: true,
  //         style: 'margin-right:20px',
  //         onClick: () => {
  //           wishInfo.value = getCodeWishFormModel(row);
  //           showWishReviewInfoModal.value = true;
  //         },
  //       },
  //       [h('div', '修改')]
  //   ))
  // }
  // 显示交付按钮：用户是交付责任人，且状态为开发中
  if (permissionMap['isDemoAuthor'] && permissionMap['isToBeDelivered']) {
    result.push(
      h(
        NButton,
        {
          strong: true,
          tertiary: true,
          type: 'info',
          size: 'medium',
          text: true,
          style: 'margin-right:20px',
          onClick: () => {
            handleSampleCode(row);
          },
        },
        [h('div', '交付')]
      )
    );
  }
  // 显示认领按钮：demoAuthor不存在且状态为待认领或开发中
  if (
    !row.demoAuthor &&
    [CodeWishReviewStatusEnum.TO_BE_CLAIMED, CodeWishReviewStatusEnum.UNDER_DEVELOPMENT].includes(
      row.reviewStatus
    )
  ) {
    result.push(
      h(
        NButton,
        {
          strong: true,
          tertiary: true,
          type: 'info',
          size: 'medium',
          text: true,
          style: 'margin-right:20px',
          onClick: () => {
            handleClaimin(row);
          },
        },
        [h('div', '认领')]
      )
    );
  }
  // 显示交付评审按钮：用户是当前处理人，且状态为待评审
  if (
    ((!!row.reviewers?.length && row.reviewers.includes(userInfo.label)) || isAdmin) &&
    row.reviewStatus === CodeWishReviewStatusEnum.TO_BE_REVIEWED
  ) {
    result.push(
      h(
        NButton,
        {
          strong: true,
          tertiary: true,
          type: 'info',
          size: 'medium',
          text: true,
          style: 'margin-right:20px',
          onClick: () => {
            handleRev(row);
          },
        },
        [h('div', '交付评审')]
      )
    );
  }
  // 显示指定外发人按钮：用户是外发管理员且状态为已通过或待外发
  if (
    userInfo.roles.includes(UserRoleEnum.SAMPLE_OUTGOING_ADMIN) &&
    [CodeWishReviewStatusEnum.PASSED, CodeWishReviewStatusEnum.TO_BE_SENT_OUT].includes(
      row.reviewStatus
    )
  ) {
    result.push(
      h(
        NButton,
        {
          strong: true,
          tertiary: true,
          type: 'info',
          size: 'medium',
          text: true,
          style: 'margin-right:20px',
          onClick: () => {
            handleOut(row);
          },
        },
        [
          h(
            'div',
            row.reviewStatus === CodeWishReviewStatusEnum.PASSED ? '指定外发人' : '更换外发人'
          ),
        ]
      )
    );
  }
  // 显示外发按钮：用户是外发责任人或外发管理人员，且状态为待外发
  if (   
    (userInfo.label === row.outGoingPerson ||
      userInfo.roles.includes(UserRoleEnum.SAMPLE_OUTGOING_ADMIN)) &&
    row.reviewStatus === CodeWishReviewStatusEnum.TO_BE_SENT_OUT
  ) {
    result.push(
      h(
        NButton,
        {
          strong: true,
          tertiary: true,
          type: 'info',
          size: 'medium',
          text: true,
          style: 'margin-right:20px',
          onClick: () => {
            handleUpdtate(row);
          },
        },
        [h('div', '外发')]
      ),
      h(
        NButton,
        {
          strong: true,
          tertiary: true,
          type: 'info',
          size: 'medium',
          text: true,
          style: 'margin-right:20px',
          onClick: () => {
            wishInfo.value = row;
            showEmailModal.value = true;
          },
        },
        [h('div', '外发邮件')]
      )
    );
  }
  // 显示托管按钮：用户是托管管理员人且状态为待托管
  if (
    !!row.custodian &&
    row.custodian.includes(userInfo.label) &&
    row.reviewStatus === CodeWishReviewStatusEnum.TO_BE_HOSTED
  ) {
    result.push(
      h(
        NButton,
        {
          strong: true,
          tertiary: true,
          type: 'info',
          size: 'medium',
          text: true,
          style: 'margin-right:20px',
          onClick: () => {
            handleHosting(row);
          },
        },
        [h('div', '托管')]
      )
    );
  }
  // 显示删除按钮：用户是提交人且状态为待提交，或用户是管理员
  if ((permissionMap['isSubmitter'] && permissionMap['isToBeSubmitted']) || isAdmin) {
    result.push(
      h(
        NButton,
        {
          strong: true,
          tertiary: true,
          type: 'error',
          size: 'medium',
          text: true,
          onClick: () => {
            handleDele(row);
          },
        },
        [h('div', '删除')]
      )
    );
  }
  return result;
}
const resultChange = (param) => {
  const list = wishReviewStatusOptions.value.find((item) => {
    return item.value === param;
  });
  wishInfo.value.reviewResult = list?.label;
};
const columns: DataTableColumns<CodeWishVo> = COLUMNSTABLE.map((item) => {
  if (item.key === 'operation') {
    item.render = (row: CodeWishVo) => {
      return getOperations(row);
    };
  }
  if (item.key === 'demoName') {
    item.render = (row: CodeWishVo) => {
      return [
        h(
          'div',
          {
            style: 'color:#1890ff',
            onClick: async () => {
              isShowDetail.value = true;
              queryHistory(row.id);
              formData.value = row;
              curPermissionMap.value = queryPermission(formData.value);
            },
          },
          [h(NEllipsis, row.demoName), h('div', row.id)]
        ),
      ];
    };
  }
  return item;
});
const pagination = reactive({
  page: 1,
  pageSize: 5,
  showSizePicker: true,
  pageSizes: [5, 10, 20],
  itemCount: 0,
  prefix({ itemCount }) {
    return `总数：${itemCount}`;
  },
  onChange: (page: number) => {
    pagination.page = page;
    getWishList();
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    getWishList();
  },
});
// 获取demo需求单列表
const getWishList = async () => {
  isLoading.value = true;
  data.value = [];

  const response = await queryCustomizedDemoList({
    pageNum: pagination.page,
    pageSize: pagination.pageSize,
    demo: getQueryWishListParams(),
    userAccount: userInfo.account,
  });
  if (response?.data) {
    data.value = response.data.data?.map((item) => getCodeWishVo(item));
    pagination.itemCount = response.data.pageInfo?.total;
  }
  isLoading.value = false;
};
const getQueryWishListParams = (): QueryCustomizedDemoParams => {
  let demo: QueryCustomizedDemoParams = {};
  if (filterStatus.value.wishId) {
    demo.id = filterStatus.value.wishId;
  }
  if (filterStatus.value.wishName) {
    demo.demoName = filterStatus.value.wishName;
  }
  if (filterStatus.value.currentHandler) {
    demo.currentHandler = filterStatus.value.currentHandler;
  }
  if (filterStatus.value.reviewStatus?.length) {
    demo.reviewStatusList = filterStatus.value.reviewStatus;
  }
  if (filterStatus.value.onlyISubmitted) {
    filterStatus.value.wishSubmitter = '';
    demo.demoSubmitter = userInfo.label;
  } else if (filterStatus.value.wishSubmitter) {
    demo.demoSubmitter = filterStatus.value.wishSubmitter;
  }
  if (filterStatus.value.onlyIDelivered) {
    filterStatus.value.demoAuthor = '';
    demo.demoAuthor = userInfo.label;
  } else if (filterStatus.value.demoAuthor) {
    demo.demoAuthor = filterStatus.value.demoAuthor;
  }
  if (filterStatus.value.onlyMyTodo) {
    filterStatus.value.currentHandler = '';
    demo.currentHandler = userInfo.label;
  } else if (filterStatus.value.currentHandler) {
    demo.currentHandler = filterStatus.value.currentHandler;
  }
  if (filterStatus.value.sources?.length) {
    demo.sources = filterStatus.value.sources;
  }
  if (filterStatus.value.createTimes) {
    demo.createStartTime = formatTime(filterStatus.value?.createTimes[0]) || '';
    demo.createEndTime = formatTime(filterStatus.value?.createTimes[1]) || '';
    delete demo.createTimes;
  }
  if (filterStatus.value.orderBy) {
    demo.orderBy = filterStatus.value.orderBy;
    demo.orderByParam = filterStatus.value.orderByParam;
  }
  return demo;
};
function initModel() {
  wishInfo.value = getDefaultCodeWishFormModel();
}
async function uploadFinish(options: { file: UploadFileInfo; event: ProgressEvent }) {
  if (options.file.status === 'finished') {
    const response = (options.event?.target as XMLHttpRequest).response;
    let url = JSON.parse(response)?.url;
    // demo需求单编辑场景才需要调用增加示例图接口
    if (wishInfo.value.id) {
      const rsp = await addSampleImage({
        customizedDemoId: wishInfo.value.id,
        imgUrl: url,
      });
      if (!rsp.data) {
        return undefined;
      }
      // todo：解决新增的image的id不是从服务端获取的id，导致无法删除问题
      return options.file;
    } else {
      wishInfo.value.imgList.push({
        url: url,
        name: url + '.png',
        id: url,
        status: 'finished',
      });
      return options.file;
    }
  }
  return undefined;
}
async function handleRemove(options: {
  file: UploadFileInfo;
  fileList: UploadFileInfo[];
  index: number;
}) {
  // demo需求单编辑场景才需要调用删除示例图接口
  if (wishInfo.value.id) {
    const rsp = await deleteSampleImage(Number(options.file.id));
    if (rsp.data) {
      wishInfo.value.imgList = wishInfo.value.imgList.filter(
        (item) => item.id !== options.file.id
      );
    }
    return rsp.data;
  }
  return undefined;
}
const sortState = ref({
  orderBy: 'riskScore',
  orderByParam: 'desc',
});
const createEmlDialog = () => {
  dialog.info({
    title: '提示',
    content: `确认一键生成邮件吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      createEml();
    },
    onNegativeClick: () => {},
  });
};
// 8、如果按照期望时间排序就传参数 orderBy=expected_time , 升序传orderByParam=esc， 降序传orderByParam=desc
const handleSorterChange = (sorter) => {
  if (sorter) {
    const { columnKey, order } = sorter;
    filterStatus.value.orderBy = order ? 'expected_time' : '';
    filterStatus.value.orderByParam = order ? (order === 'ascend' ? 'asc' : 'desc') : '';
  } else {
    filterStatus.value.orderBy = '';
    filterStatus.value.orderByParam = '';
  }
  pagination.page = 1; // 重置到第一页
  // getWishList();
};
async function queryHistory(demoId) {
  tableDataHistoryRev.value = [];
  tableDataHistoryAcp.value = [];
  const rsp = await queryRecord(Number(demoId));
  if (rsp?.data) {
    rsp.data.forEach((item) => {
      if (item.originalStatus == 3) {
        // 需求评审
        tableDataHistoryRev.value.push(item);
      } else if (item.originalStatus == 4) {
        // 交付评审
        tableDataHistoryAcp.value.push(item);
      }
    });
  }
}
function handlePreview(file: UploadFileInfo) {
  const { url } = file;
  previewImageUrlRef.value = url!;
  showPreviewModal.value = true;
}
const onUserSelect = (userInfo: UserDto) => {
  wishInfo.value.demoAuthor = getUserLabel(userInfo);
};
const handleDeleteSubmit = async (wishInfo: CodeWishVo) => {
  deleteCustomizedDemo({
    id: wishInfo.id,
  })
    .then((res) => {
      if (res?.data) {
        message.success('删除成功！');
        // todo: 自动生成的demo需求单同时删除demo
        getWishList();
      } else {
        message.error('删除失败！');
      }
    })
    .catch((e) => {
      message.error(`删除失败，原因：${e?.response?.data?.error}`);
    });
};
// 获取评审人列表
const getReviewList = async (roles) => {
  const reviewersConfig = {
    singleReviewer: '',
    reviewerOptions: []
  }
  return new Promise((resolve, reject) => {
    queryUser({
      user: {
        roles,
      },
      pageNum: 1,
      pageSize: 20,
    }).then((res) => {
      if (res?.data) {
        const data = res.data?.data as UserDto[];
        const fileList: Array<string> = []
        let allReviewers = data.map((item) => {
          let userLabel = getUserLabel(item);
          fileList.push(userLabel)
          return {
            label: userLabel,
            value: userLabel,
          };
        });

        const randomNumber = getRandomInt(1, fileList.length) - 1;
        // reviewerOptions.value = allReviewers.filter(item => !userInfo?.account || !item.label.includes(userInfo.account));
        reviewersConfig.singleReviewer = fileList[randomNumber]
        reviewersConfig.reviewerOptions = allReviewers
        resolve(reviewersConfig)
      }
    });
  })
};
// 新增或编辑
const handleBasicInfoSubmit = async (draft = false) => {
  
  const reviewerConfig = await getReviewList([UserRoleEnum.SAMPLE_WISH_REVIEWER]);
  wishInfo.value.reviewer = reviewerConfig.singleReviewer

  wishListFormRef.value.validate((errors) => {
    if (!errors) {
      if (isAddWishList.value) {
        addCustomizedDemo(getAddCustomizedDemoParams(wishInfo.value))
          .then((res) => {
            if (res?.data) {
              message.success('添加样例代码需求成功！');
              showBasicInfoModal.value = false;
              getWishList();
            } else {
              message.error('添加样例代码需求失败！');
            }
          })
          .catch((e) => {
            message.error(`添加样例代码需求失败，原因：${e?.response?.data?.error}`);
          });
      } else {
        updateCustomizedDemo(getUpdateBasicInfoParams(wishInfo.value, draft))
          .then((res) => {
            if (res) {
              message.success('修改样例代码需求成功！');
              showBasicInfoModal.value = false;
              getWishList();
            } else {
              message.error('修改样例代码需求失败！');
            }
          })
          .catch((e) => {
            message.error(`修改样例代码需求失败，原因：${e?.response?.data?.error}`);
          });
      }
    } else {
      console.log(errors);
    }
  });
};
// 选择评审人提交评审
const handleSelectHandlerSubmit = () => {
  selectHandlerFormRef.value.validate((errors) => {
    if (!errors) {
      if (!errors) {
        updateReviewInfo(getSubmitReviewParams(wishInfo.value));
        selectHandlerModal.value = false;
        getWishList();
      } else {
        console.log(errors);
      }
    } else {
      console.log(errors);
    }
  });
};
// 需求评审
const handleReviewInfoSubmit = () => {
  reviewFormRef.value.validate((errors) => {
    if (!errors) {
      updateReviewInfo(getWishReviewParams(wishInfo.value));
      showWishReviewInfoModal.value = false;
      getWishList();
    } else {
      console.log(errors);
    }
  });
};
// 交付
function handleDeliverSubmit() {
  updateReviewInfo(getDeliverParams(wishInfo.value));
  showDeliverModal.value = false;
}
// 指定外发人
function handleDesignatedOutgoingPersonSubmit() {
  designedOutgoingPersonFormRef.value.validate(async (errors) => {
    if (!errors) {
      updateReviewInfo(getDesignatedOutGoingPersonParams(wishInfo.value));
      showDesignedOutgoingPersonModal.value = false;
    } else {
      console.log(errors);
    }
  });
}
function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

const fieldChange = (key) => {
  wishInfo.value.reviewers = [];
  //  既是提交人也是对应领域的专家，评委需要走给另外两名评委
  const fileList = FIRED_LIST_MAP[key].filter((item) => {
    return item !== userInfo.label;
  });
  const randomNumber = getRandomInt(1, fileList.length) - 1;
  wishInfo.value.reviewer = fileList[randomNumber];
  wishInfo.value.reviewers.push(FIRED_LIST_MAP[key][randomNumber]);
};
// 托管
function handleHostingSubmit() {
  hostingFormRef.value.validate(async (errors) => {
    if (!errors) {
      updateDemo({
        demoId: wishInfo.value.linkDemoId,
        externalLink: wishInfo.value.externalLink,
      })
        .then((res) => {
          console.log(res);
          if (res) {
            updateReviewInfo(getHostingParams(wishInfo.value.id));
          } else {
            message.error('操作失败！');
          }
        })
        .catch((e) => {
          message.error(`操作失败，原因：${e?.response?.data?.error}`);
        })
        .finally(() => {
          showHostingModal.value = false;
        });
    } else {
      console.log(errors);
    }
  });
}
// 关联样例代码
const handleAssociatingSampleCode = async () => {
  deliverFormRef.value.validate(async (errors) => {
    if (!errors) {
      associatingSampleCode(wishInfo.value.linkDemoId);
      showAssociatingSampleCodeModal.value = false;
    } else {
      console.log(errors);
    }
  });
};
// 交付评审
const handleCodeReviewSubmit = async () => {
  acceptFormRef.value.validate(async (errors) => {
    if (!errors) {
      (wishInfo.value.reviewStatus =
        wishInfo.value.reviewResult === '通过' ? 9 : wishInfo.value.reviewStatus),
        updateReviewInfo(getCodeReviewParams(wishInfo.value));
      showCodeReviewInfoModal.value = false;
    } else {
      console.log(errors);
    }
  });
};
const createEml = () => {
  // 构建包含表格的 HTML 正文
  const htmlBody = `<html> 
  <body>
  <p>鸿蒙生态拓展需求，为满足千帆会战作战目标需求，支撑DTSE快速找到高频通用场景功能demo，提高伙伴开发效率，将DEMO代码外发至一线技术支持同事外部邮箱，后由各一线DTSE按需转发至鸿蒙应用开发伙伴。所外发DEMO均经过质量及信息安全评审，不涉及商用及三方信息，完成脱敏，并合入生态资产代码仓。</p>
  <p>----------------------------------------------</p>
  <p>注：生态工程，非内部敏感资料，核心代码行数不超过5000行，其余为工程构建文件。</p>
  </body>
  </html>`;
  const to = '<EMAIL>';
  const subject = `${wishInfo.value.demoName}【新增】，【优化】`;
  // 构建 EML 内容（注意邮件头格式）
  const emlContent = [
    `Subject: ${subject}`, // 邮件主题
    'Content-Type: text/html; charset=UTF-8', // 指定HTML格式
    `To: ${to};`,
    `Cc: caojianhua <<EMAIL>>; wangfan (P) <<EMAIL>>; caiqiuyi <<EMAIL>>; Zhouruguang <EMAIL>`,
    '', // 空行分隔头部和内容
    htmlBody,
  ].join('\r\n'); // 必须使用CRLF换行

  // 生成 Blob 并触发下载
  const blob = new Blob([emlContent], { type: 'message/rfc822' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = `${subject}.eml`; // 下载文件名
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 查询条件变化
const handleFiltersChange = (filters: CodeWishFilterStatus) => {
  filterStatus.value = filters;
};
// 重置查询条件
const handleFiltersReset = () => {
  filterStatus.value = getDefaultFilterStatus();
  filterStatus.value.createTimes = null;
};
const handleAddWishListClick = () => {
  isAddWishList.value = true;
  initModel();
  showBasicInfoModal.value = true;
};
const handleAddCodeClick = () => {
  wishInfo.value = getDefaultCodeWishFormModel();
  codeDrawerMode.value = CodeSampleDrawerMode.ADD;
  showCodeInfoDrawer.value = true;
};
const disablePreviousDate = (ts: number) => {
  return ts < Date.now();
};
const handleExport = () => {
  if (!data.value.length) {
    message.warning('暂无数据导出！');
    return;
  }
  exportLoading.value = true;
  exportWishList(getQueryWishListParams())
    .then((res) => {
      message.success('导出成功！');
    })
    .catch((e) => {
      message.success(`导出失败！错误信息: ${e}`);
      console.log(e);
    })
    .finally(() => {
      exportLoading.value = false;
    });
};
getWishList();
</script>

<style lang="less" scoped>
  .layout-page-header {
    margin-top: 20px;
  }
</style>
