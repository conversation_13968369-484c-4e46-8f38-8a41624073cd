<template>
  <div>
    <n-card class="search-container">
      <n-form
        ref="formRef"
        :model="searchModel"
        label-placement="left"
        require-mark-placement="right-hanging"
        size="medium"
      >
        <n-grid :cols="24" :x-gap="24">
          <n-form-item-gi :span="8" label="设备渠道" path="deviceChannel">
            <n-select v-model:value="searchModel.deviceChannel" :options="channelList" clearable />
          </n-form-item-gi>
          <n-form-item-gi :span="8" label="设备状态" path="deviceStatus">
            <n-select v-model:value="searchModel.deviceStatus" :options="statusList" clearable />
          </n-form-item-gi>
        </n-grid>
      </n-form>
      <n-space justify="center">
        <n-button secondary strong type="primary" @click="searchData()"> 查询 </n-button>
        <n-button secondary strong type="default" @click="refreshSearch()"> 重置 </n-button>
      </n-space>
    </n-card>
    <n-card>
      <n-space>
        <n-button secondary strong type="warning" @click="refreshData()">
          <template #icon>
            <n-icon>
              <Refresh />
            </n-icon>
          </template>
          刷新
        </n-button>
        <n-button
          :disabled="!hasPermission"
          secondary
          strong
          type="primary"
          @click="showListAddModal = true"
        >
          导入
        </n-button>
        <n-button
          secondary
          strong
          type="primary"
          :disabled="!hasPermission"
          @click="
            showModal = true;
            isAdd = true;
          "
        >
          <template #icon>
            <n-icon>
              <Add />
            </n-icon>
          </template>
          添加
        </n-button>
      </n-space>
      <n-data-table
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="pagination"
        style="margin-top: 20px"
      />
    </n-card>
    <n-modal v-model:show="showListAddModal">
      <n-card
        style="width: 600px"
        title="批量导入"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-button text @click="handleDownload" style="margin-bottom: 20px"
          >点击下载导入模板</n-button
        >
        <n-upload
          action="#"
          :custom-request="customRequest"
          :multiple="true"
          :default-file-list="fileList"
          accept=".xls,.xlsx,"
          :max="1"
          directory-dnd
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <Add />
              </n-icon>
            </div>
            <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
          </n-upload-dragger>
        </n-upload>
      </n-card>
    </n-modal>
    <n-modal v-model:show="showModal" :on-after-leave="handleAfterLeave">
      <n-card
        :style="{ width: '1200px' }"
        :title="isAdd ? '新增' : '编辑'"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <template #header-extra> </template>
        <n-form
          ref="formRef"
          :model="model"
          :rules="rules"
          label-placement="left"
          label-width="100"
          require-mark-placement="right-hanging"
          size="medium"
        >
          <n-grid :cols="24" :x-gap="24">
            <n-form-item-gi :span="12" label="设备渠道" path="deviceChannel">
              <n-select v-model:value="model.deviceChannel" :options="channelList" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="地域" path="region">
              <n-select v-model:value="model.region" :options="regionList" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="入库时间" path="storageTime">
              <n-date-picker
                v-model:value="model.storageTime"
                type="date"
                :style="{ width: '100%' }"
              />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="验收责任人" path="storageTime">
              <n-input v-model:value="model.acceptanceResponsiblePerson" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="应用名称" path="applicationName">
              <n-input v-model:value="model.applicationName" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="设备品类" path="deviceCategory">
              <n-input v-model:value="model.deviceCategory" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="设备型号" path="deviceModel">
              <n-input v-model:value="model.deviceModel" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="设备编号" path="deviceNumber">
              <n-input v-model:value="model.deviceNumber" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="设备状态" path="deviceStatus">
              <n-select v-model:value="model.deviceStatus" :options="statusList" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="库房领取人" path="warehouseRecipient">
              <n-input v-model:value="model.warehouseRecipient" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="附注" path="notes">
              <n-input v-model:value="model.notes" type="textarea" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="是否可用" path="availability">
              <n-select v-model:value="model.availability" :options="availabilityList" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="试用备注" path="trialNotes">
              <n-input v-model:value="model.trialNotes" type="textarea" />
            </n-form-item-gi>
          </n-grid>
        </n-form>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" @click="handleSubmint()"> 确认 </n-button>
            <n-button secondary strong type="error" @click="showModal = false"> 取消 </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { Add, Refresh, Close, GridOutline } from '@vicons/ionicons5';
  import { NIcon, useMessage, NButton, NFlex } from 'naive-ui';
  import { ref, reactive, onMounted, h } from 'vue';
  import { cloneDeep, debounce } from 'lodash-es';
  import {
    format,
    defaultSearchModel,
    defaultModel,
    paginationReactive,
    channelList,
    regionList,
    statusList,
    availabilityList,
  } from './index.ts';
  import {
    deviceManagerInsert,
    deviceManagerSelect,
    deviceManagerSelectBy,
    deviceManagerUpdate,
    managerUpload,
    downloadManagerTemplate,
  } from '@/api/sampleDevices/index';
  import { useUserStore } from '@/store/modules/user';
  import { UserRoleEnum } from "@/enums/UserRoleEnum";
  const userInfo = useUserStore().getUserInfo;
  const hasPermission = ref((userInfo.roles || []).includes(UserRoleEnum.DEVICE_MANAGER));

  let showModal = ref(false);
  let isAdd = ref(true);
  const loading = ref(true);
  const formRef = ref();
  let modelReactive = reactive(cloneDeep(defaultModel));
  const model = ref(modelReactive);
  let searchModelReactive = reactive(cloneDeep(defaultSearchModel));
  const searchModel = ref(searchModelReactive);
  const rules = ref({
    region: {
      required: true,
      message: '请选择地域',
      trigger: 'blur',
    },
    deviceChannel: {
      required: true,
      message: '请选择设备渠道',
      trigger: 'blur',
    },
    applicationName: {
      required: true,
      message: '请输入应用名称',
      trigger: 'blur',
    },
    deviceCategory: {
      required: true,
      message: '请输入设备品类',
      trigger: 'blur',
    },
  });
  const showListAddModal = ref(false);
  const fileList = ref([]);
  const data = ref();
  const isSearch = ref(false);
  const searchParam = ref({});

  const sampleDevicesRecordColumn = [
    {
      title: '序号',
      key: 'serialNumber',
      minWidth: 60,
      resizable: true,
    },
    {
      title: '设备渠道',
      key: 'deviceChannel',
      minWidth: 100,
      resizable: true,
    },
    {
      title: '地域',
      key: 'region',
      minWidth: 100,
      resizable: true,
    },
    {
      title: '入库时间',
      key: 'storageTime',
      minWidth: 150,
      render: (row) => {
        return format(row.storageTime);
      },
       resizable: true,
    },
    {
      title: '验收责任人',
      key: 'acceptanceResponsiblePerson',
      minWidth: 100,
      resizable: true,
    },
    {
      title: '应用名称',
      key: 'applicationName',
      minWidth: 100,
      resizable: true,
    },
    {
      title: '设备品类',
      key: 'deviceCategory',
      minWidth: 100,
      resizable: true,
    },
    {
      title: '设备型号',
      key: 'deviceModel',
      minWidth: 100,
      resizable: true,
    },
    {
      title: '设备编号',
      key: 'deviceNumber',
      minWidth: 100,
      resizable: true,
    },
    {
      title: '设备状态',
      key: 'deviceStatus',
      minWidth: 100,
      resizable: true,
    },
    {
      title: '库房领取人',
      key: 'warehouseRecipient',
      minWidth: 100,
      resizable: true,
    },
    {
      title: '附注',
      key: 'notes',
      minWidth: 100,
      resizable: true,
    },
    {
      title: '是否可用',
      key: 'availability',
      minWidth: 100,
      resizable: true,
    },
    {
      title: '试用备注',
      key: 'trialNotes',
      minWidth: 100,
      resizable: true,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      minWidth: 100,
      render: (row) => {
        return h(
          NButton,
          {
            strong: true,
            tertiary: true,
            type: 'info',
            size: 'medium',
            disabled: !hasPermission.value,
            onClick: () => {
              model.value = cloneDeep(row);
              isAdd.value = false;
              showModal.value = true;
            },
          },
          () => '编辑'
        );
      },
    },
  ];

  const columns = ref(sampleDevicesRecordColumn);
  const message = useMessage();

  const handleAfterLeave = () => {
    modelReactive = reactive(cloneDeep(defaultModel));
    model.value = modelReactive;
  };

  const pagination = ref(paginationReactive);

  const search = debounce(async () => {
    loading.value = true;
    let res;
    if (isSearch.value) {
      res = await deviceManagerSelectBy(searchParam.value);
    } else {
      res = await deviceManagerSelect();
    }
    if (res.status === '200') {
      pagination.value.itemCount = res?.data?.count || 0;
      data.value = res?.data?.list || [];
    }
    loading.value = false;
  }, 300);

  const searchData = () => {
    searchParam.value = searchModel.value;
    isSearch.value = true;
    search();
  };

  const refreshSearch = () => {
    searchModelReactive = reactive(cloneDeep(defaultSearchModel));
    searchModel.value = searchModelReactive;
    isSearch.value = false;
    search();
  };

  const refreshData = () => {
    search();
  };

  const handleSubmint = debounce(async () => {
    formRef.value.validate(async (errors) => {
      if (!errors) {
        let data = JSON.parse(JSON.stringify(model.value));
        try {
          var res = isAdd.value ? await deviceManagerInsert(data) : await deviceManagerUpdate(data);
          if (res.status == '200') {
            message.success('提交成功');
            showModal.value = false;
            search();
          }
        } catch (err) {
          message.error(err.message);
        }
      }
    });
  }, 300);

  onMounted(() => {
    search();
  });

  //上传
  const customRequest = async ({ file, onError }: UploadCustomRequestOptions) => {
    const formData = new FormData();
    formData.append('file', file.file as File);
    //上传接口
    try {
      let res = await managerUpload(formData);
      if (res.status == '200') {
        showListAddModal.value = false;
        search();
        message.success('导入成功');
      } else {
        fileList.value = [];
      }
    } catch (err) {
      fileList.value = [];
      onError();
    }
  };
  //下载
  const handleDownload = async () => {
    downloadManagerTemplate()
      .then((res) => {
        if (!res) {
          message.error('下载失败！');
        }
      })
      .catch((e) => {
        message.error(`下载失败，原因：${e?.response?.data?.error}`);
      });
  };
</script>

<style lang="less" scoped>
  .search-container {
    margin-bottom: 10px;
  }
</style>
