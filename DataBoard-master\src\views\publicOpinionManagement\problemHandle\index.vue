<template>
  <div>
    <n-card style="margin-bottom: 12px">
      <n-form label-placement="left" label-width="100px" label-align="left">
        <n-grid x-gap="20" :cols="4" style="margin-bottom: 12px">
          <n-gi>
            <n-form-item label="应用名称">
              <n-input
                v-model:value="filters.appName"
                @keyup.enter="handleUpdateFilter"
                clearable
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="DTS单号">
              <n-input v-model:value="filters.dtsNum" @keyup.enter="handleUpdateFilter" clearable />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="问题状态">
              <n-select
                v-model:value="filters.statusList"
                :options="statusList"
                multiple
                clearable
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="舆情等级">
              <n-select v-model:value="filters.levelList" :options="levelList" multiple clearable />
            </n-form-item>
          </n-gi>
        </n-grid>
        <div v-if="collapse">
          <n-grid x-gap="20" :cols="4" style="margin-bottom: 12px">
            <n-gi>
              <n-form-item label="预警时间">
                <n-date-picker v-model:value="filters.warnTime" type="daterange" clearable />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="DTS状态">
                <n-select
                  v-model:value="filters.dtsStatusList"
                  :options="dtsStatusList"
                  multiple
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="优先级">
                <n-select
                  v-model:value="filters.applicationLvList"
                  :options="applicationLvList"
                  multiple
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="清单来源">
                <n-select
                  v-model:value="filters.problemSourceList"
                  :options="problemSourceList"
                  multiple
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="保障责任人" style="margin-right: 20px">
                <n-select
                  v-model:value="filters.guarantor"
                  :options="ewpUserList"
                  filterable
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="责任人" style="margin-right: 20px">
                <n-select v-model:value="filters.owner" :options="userList" filterable clearable />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="是否超期" style="margin-right: 20px">
                <n-select
                  v-model:value="filters.isOverdue"
                  :options="isOverdueList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="超期类型" style="margin-right: 20px">
                <n-select
                  v-model:value="filters.overdueType"
                  :options="overdueTypeList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="计划上线时间">
                <n-date-picker v-model:value="filters.planTime" type="daterange" clearable />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="闭环时间">
                <n-date-picker v-model:value="filters.closeTime" type="daterange" clearable />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="计划锁定时间">
                <n-date-picker v-model:value="filters.planLockTime" type="daterange" clearable />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="领域" style="margin-right: 20px">
                <n-select
                  v-model:value="filters.module"
                  :options="appBelongList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
          </n-grid>
        </div>

        <!--      <n-grid x-gap="20" y-gap="12" cols="2 1000:3 1400:4" v-if="collapse">-->
        <!--      <n-gi label="领域" path="module">-->
        <!--        <n-input v-model:value="filters.module" />-->
        <!--      </n-gi>-->
        <!--      <n-gi label="舆情声量" path="opinionVolume">-->
        <!--        <n-input v-model:value="filters.opinionVolume" />-->
        <!--      </n-gi>-->
        <!--      <n-gi label="领域责任人" path="moduleOwner">-->
        <!--        <n-select v-model:value="filters.moduleOwner" :options="userList" filterable />-->
        <!--      </n-gi>-->
        <!--      <n-gi label="产品" path="product">-->
        <!--        <n-input v-model:value="filters.product" />-->
        <!--      </n-gi>-->
        <!--      <n-gi label="通报人" path="informer">-->
        <!--        <n-select v-model:value="filters.informer" :options="userList" filterable />-->
        <!--      </n-gi>-->
        <!--      </n-grid>-->
      </n-form>
      <n-space justify="center">
        <n-button secondary strong type="default" @click="handleResetFilter">重置 </n-button>
        <n-button secondary strong type="primary" @click="handleUpdateFilter">查询 </n-button>
        <n-button type="primary" icon-placement="right" @click="unfoldToggle">
          <template #icon>
            <n-icon size="14" class="unfold-icon" v-if="collapse">
              <UpOutlined />
            </n-icon>
            <n-icon size="14" class="unfold-icon" v-else>
              <DownOutlined />
            </n-icon>
          </template>
          {{ collapse ? '收起高级搜索' : '展开高级搜索' }}
        </n-button>
      </n-space>
    </n-card>
    <n-card>
      <div>
        <div class="layout-page-header">
          <n-space justify="space-between">
            <n-space>
              <n-button secondary strong type="warning" @click="handleGetOIList()">
                <template #icon>
                  <n-icon>
                    <Refresh />
                  </n-icon>
                </template>
                刷新
              </n-button>
              <n-button
                secondary
                strong
                type="primary"
                v-if="isAdmin"
                @click="
                  showModal = true;
                  isAdd = true;
                "
                >添加
              </n-button>
              <n-button secondary strong type="error" @click="handleExportProblem()"
                >导出EXCEL
              </n-button>
              <n-button
                v-if="isAdmin || /^[a-zA-Z](300|00)/.test(currentUser.account)"
                secondary
                strong
                type="error"
                @click="handleBatchDelete()"
                >批量删除
              </n-button>
            </n-space>
            <n-space>
              <n-button text @click="showSetColumnsModal = true">
                <template #icon>
                  <n-icon>
                    <Settings />
                  </n-icon>
                </template>
              </n-button>
            </n-space>
            <!-- <n-input
            v-model:value="irNumber"
            placeholder="输入IR单号按回车键筛选"
            style="width: 200px"
            @keypress="handleIrNumber"
          /> -->
            <!-- <n-select
            v-model:value="filterUserListValue"
            multiple
            filterable
            placeholder="选择当前处理人"
            :options="filterUserList"
          >
          </n-select>
          <n-button secondary strong type="warning" @click="handleFilterPeople()"> 确认 </n-button> -->
          </n-space>
        </div>
        <n-data-table
          v-model:checked-row-keys="checkedRowKeys"
          :columns="columns"
          :data="tableData"
          :pagination="pagination"
          :scroll-x="3700"
          :max-height="440"
          virtual-scroll
          :loading="loading"
          :on-update:checked-row-keys="onUpdateChecked"
          remote
          :on-update:sorter="handleSorter"
          style="margin-top: 20px"
          :sticky-header="true"
        />
        <n-modal v-model:show="showModal" :on-after-leave="initModel">
          <n-card
            :style="{ width: '1200px' }"
            :title="isAdd ? '新增舆情问题' : '编辑舆情问题'"
            :bordered="false"
            size="huge"
            role="dialog"
            aria-modal="true"
          >
            <template #header-extra> </template>
            <n-form
              ref="formRef"
              :model="model"
              :rules="rules"
              label-placement="left"
              label-width="auto"
              require-mark-placement="right-hanging"
              size="medium"
            >
              <n-grid :cols="24" :x-gap="24">
                <n-form-item-gi :span="12" label="预警时间" path="warnTime">
                  <n-date-picker v-model:value="model.warnTime" type="date" clearable />
                </n-form-item-gi>
                <!--                <n-form-item-gi :span="12" label="领域" path="module">-->
                <!--                  <n-select v-model:value="model.module" :options="moduleList" clearable />-->
                <!--                </n-form-item-gi>-->
                <n-form-item-gi :span="12" label="问题来源" path="source">
                  <n-select v-model:value="model.source" :options="sourceList" clearable />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="问题类型" path="issueType">
                  <n-select
                    v-model:value="model.issueType"
                    :options="ISSUE_TYPE_OPTIONS"
                    clearable
                  />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="应用名称" path="appName">
                  <n-input v-model:value="model.appName" clearable />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="问题描述" path="desc">
                  <n-input v-model:value="model.desc" clearable />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="问题状态" path="status">
                  <n-select v-model:value="model.status" :options="statusList" clearable />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="舆情等级" path="level">
                  <n-select v-model:value="model.level" :options="levelList" clearable />
                </n-form-item-gi>

                <n-form-item-gi :span="12" label="计划上线时间" path="planTime">
                  <n-date-picker v-model:value="model.planTime" type="date" clearable />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="保障责任人" path="guarantor">
                  <n-select
                    v-model:value="model.guarantor"
                    :options="ewpUserList"
                    filterable
                    clearable
                  />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="责任人" path="owner">
                  <n-select v-model:value="model.owner" :options="userList" filterable clearable />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="领域责任人" path="moduleOwner">
                  <n-select
                    v-model:value="model.moduleOwner"
                    :options="userList"
                    filterable
                    clearable
                  />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="DTS单号" path="dtsNum">
                  <n-input v-model:value="model.dtsNum" clearable />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="产品" path="product">
                  <n-input v-model:value="model.product" clearable />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="通报人" path="informer">
                  <n-select
                    v-model:value="model.informer"
                    :options="ewpUserList"
                    filterable
                    clearable
                  />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="是否超期" path="isOverdue">
                  <n-select
                    v-model:value="model.isOverdue"
                    :options="isOverdueList"
                    filterable
                    clearable
                    @update-value="model.overdueType = ''"
                  />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="超期类型" path="overdueType">
                  <n-select
                    :disabled="model.isOverdue === false"
                    v-model:value="model.overdueType"
                    :options="overdueTypeList"
                    filterable
                    clearable
                  />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="闭环时间" path="closeTime">
                  <n-date-picker v-model:value="model.closeTime" type="date" clearable />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="知识ID" path="knowledgeId">
                  <n-input v-model:value="model.knowledgeId" clearable />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="优先级" path="applicationLv">
                  <n-select
                    v-model:value="model.applicationLv"
                    :options="applicationLvList"
                    filterable
                    clearable
                  />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="清单来源" path="problemSource">
                  <n-select
                    v-model:value="model.problemSource"
                    :options="problemSourceList"
                    filterable
                    clearable
                  />
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="知识链接" path="knowledgeLink">
                  <n-input v-model:value="model.knowledgeLink" clearable />
                </n-form-item-gi>
                <!--                <n-form-item-gi :span="12" label="领域">-->
                <!--                  <n-input v-model:value="model.module" clearable />-->
                <!--                </n-form-item-gi>-->
                <n-form-item-gi :span="12" label="进展&措施" path="progress">
                  <n-input placeholder="请在表格内双击【进展&措施】框填写" type="textarea" clearable disabled/>
                </n-form-item-gi>
                <n-form-item-gi :span="12" label="备注" path="textarea">
                  <n-input v-model:value="model.textarea" type="textarea" clearable />
                </n-form-item-gi>
              </n-grid>
            </n-form>
            <template #footer>
              <n-space>
                <n-button secondary strong type="primary" @click="handleSubmint()"> 确认 </n-button>
                <n-button secondary strong type="error" @click="showModal = false"> 取消 </n-button>
              </n-space>
            </template>
          </n-card>
        </n-modal>
        <!--    列选择弹窗    -->
        <n-modal v-model:show="showSetColumnsModal" :on-after-leave="getColumnsSettings">
          <n-card
            style="width: 600px"
            title="表格设置"
            :bordered="false"
            size="huge"
            role="dialog"
            aria-modal="true"
          >
            <n-checkbox-group v-model:value="selectedBaseColumnsKeys">
              <n-space item-style="display: flex;">
                <n-checkbox v-for="c in columnOptions" :value="c.value" :label="c.label" />
              </n-space>
            </n-checkbox-group>
            <template #footer>
              <n-space justify="end">
                <n-button secondary strong type="primary" @click="handleSetColumns()">
                  保存
                </n-button>
                <n-button secondary strong type="error" @click="showSetColumnsModal = false">
                  取消
                </n-button>
              </n-space>
            </template>
          </n-card>
        </n-modal>
        <!-- <n-modal v-model:show="showListAddModal">
        <n-card
          style="width: 600px"
          title="批量导入"
          :bordered="false"
          size="huge"
          role="dialog"
          aria-modal="true"
        >
          <n-button text @click="handleDownload" style="margin-bottom: 20px"
            >点击下载导入模板</n-button
          >
          <n-upload
            action="#"
            :custom-request="customRequest"
            :multiple="true"
            :default-file-list="fileList"
            accept=".xls,.xlsx,"
            :max="1"
            directory-dnd
          >
            <n-upload-dragger>
              <div style="margin-bottom: 12px">
                <n-icon size="48" :depth="3">
                  <ArchiveIcon />
                </n-icon>
              </div>
              <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
            </n-upload-dragger>
          </n-upload>
        </n-card>
      </n-modal>
      <n-modal v-model:show="showTransferAModal">
        <n-card
          style="width: 600px"
          title="批量转移"
          :bordered="false"
          size="huge"
          role="dialog"
          aria-modal="true"
        >
          <n-form-item-gi :span="12" label="责任人" path="currentHandler" required>
            <n-select v-model:value="batchTransferUser" :options="batchTransferUserList" />
          </n-form-item-gi>
          <template #footer>
            <n-space>
              <n-button secondary strong type="primary" @click="handleSubmintBatchTransfer">
                确认
              </n-button>
              <n-button secondary strong type="error" @click="showTransferAModal = false">
                取消
              </n-button>
            </n-space>
          </template>
        </n-card>
      </n-modal> -->
      </div>
    </n-card>
    <ProblemProgressModal
      v-model:show="showProblemModal"
      :order-id="currentProblem?.orderId || ''"
      :current-problem="currentProblem"
      :after-submit="afterSubmit"
    />
  </div>
</template>

<script lang="ts" setup>
  import { Add, Refresh, Close, GridOutline, Settings } from '@vicons/ionicons5';
  import {
    NIcon,
    NButton,
    NTag,
    useDialog,
    UploadCustomRequestOptions,
    useMessage,
    DataTableColumns,
    DataTableSelectionColumn,
    DataTableBaseColumn,
    NSpace,
    NSwitch, NModal,
  } from 'naive-ui';
  import {h, ref, reactive, onBeforeMount, watch, onMounted, onUnmounted} from 'vue';
  import { useRouter } from 'vue-router';
  import {
    formatDateTime,
    getDefaultProblemHandleFilters,
    modelTemplate,
    ProblemHandleFilters,
    statusList,
    moduleList,
    sourceList,
    isOverdueList,
    overdueTypeList,
    ProblemDto,
    getColumnsSelectOptions,
    setHiddenColumns,
    getShownColumns,
    ISSUE_TYPE_OPTIONS,
    applicationLvList,
    problemSourceList,
    dtsStatusList,
    getProblemStatus,
    appBelongList,
  } from './index';
  import { useUserStore } from '@/store/modules/user';
  import { getEmployeeList, queryUserList } from '@/api/system/usermanage';
  import {
    getProblemList,
    addProblem,
    updateProblem,
    exportProblem,
    QueryProblemListReq,
    deleteProblem,
    exportPublicSentiment,
  } from '@/api/dataview/problemHandle';
  import { DownOutlined, UpOutlined } from '@vicons/antd';
  import { BASE_COLUMNS } from '@/views/publicOpinionManagement/problemHandle/columns';
  import { ColumnKey, TableSelectionColumn } from 'naive-ui/es/data-table/src/interface';
  import dayjs from "dayjs";
  import {updateEcologicalData} from "@/views/dataview/ecologicalTracking/api";
  import ProblemProgressModal from "@/views/dataview/myOrder/components/ProblemProgressModal.vue";
  import {useEventBus} from "@/hooks/useEventBus";
  import {getPersonOptionsOnlyName} from "@/views/dataview/ecologicalTracking/api.mock";
  import {DTS_HANDLE_TAG} from "@/views/dataview/personManage/tagContant";
  const eventBus = useEventBus();

  const userStore = useUserStore();
  const filterUserListValue = ref(
    JSON.parse(localStorage.getItem('filterUserListValue') || JSON.stringify([]))
  );

  const router = useRouter();
  const currentUser = userStore.getUserInfo;
  const isAdmin = currentUser.roles.includes('5_admin') || currentUser.isSuperAdmin;

  const filters = ref<ProblemHandleFilters>(getDefaultProblemHandleFilters());
  const collapse = ref(false); // 默认折叠表单高级搜索部分
  const dialog = useDialog();
  const message = useMessage();
  const formRef = ref();
  const fileList = ref([]);
  const userList = ref([]);
  const ewpUserList = ref([]);
  const isAllData = ref(true);
  const levelList = [
    {
      label: 'A',
      value: 'A',
    },
    {
      label: 'B',
      value: 'B',
    },
    {
      label: 'C',
      value: 'C',
    },
    {
      label: 'D',
      value: 'D',
    },
    //取消该处等级选项
    /*{
      label: '—',
      value: '',
    },*/
  ];
  let sortList = ['level', 'sla_time'];
  let moduleList = ['others'];
  const showModal = ref(false);
  const showSetColumnsModal = ref(false);
  const columnOptions = getColumnsSelectOptions();
  const selectedBaseColumnsKeys = ref<ColumnKey[]>(getShownColumns());
  const showListAddModal = ref(false);
  const showTransferAModal = ref(false);
  const isAdd = ref(false);
  const isTransfer = ref(false);
  const tableData = ref([]);
  const model = ref({ ...modelTemplate });
  const rules = ref({
    warnTime: {
      required: true,
    },
    appName: {
      required: true,
    },
    desc: {
      required: true,
    },
    level: {
      required: true,
    },
    status: {
      required: true,
    },
    issueType: {
      required: true,
    },
  });
  const loading = ref(false);
  const columns = ref<DataTableColumns<ProblemDto>>(getColumns());
  const checkedRowKeys = ref<Array<string | number>>([]);
  const pagination = reactive({
    page: 1,
    pageSize: 8,
    showSizePicker: true,
    pageSizes: [8, 10, 20, 50],
    itemCount: 0,
    prefix({ itemCount }) {
      return `总条数 ${itemCount}`;
    },
    onChange: (page: number) => {
      pagination.page = page;
      handleGetOIList();
    },
    onUpdatePageSize: (pageSize: number) => {
      pagination.pageSize = pageSize;
      pagination.page = 1;
      handleGetOIList();
    },
  });
  const initModel = () => {
    model.value = { ...modelTemplate };
  };

  function getColumns(): DataTableColumns<ProblemDto> {
    const selectionColumn: DataTableSelectionColumn<ProblemDto> = {
      type: 'selection',
    };
    const operationColumn: DataTableBaseColumn<ProblemDto> = {
      title: '操作',
      key: 'action',
      width: 260,
      fixed: 'right',
      render(row) {
        return [
          h(
            NButton,
            {
              strong: true,
              tertiary: true,
              type: 'info',
              size: 'medium',
              style: 'margin-right:20px',
              text: true,
              disabled: !isAdmin,
              onClick: () => {
                isAdd.value = false;
                showModal.value = true;
                model.value = JSON.parse(JSON.stringify(row));
              },
            },
            [h('div', '编辑')]
          ),
          h(
            NButton,
            {
              strong: true,
              tertiary: true,
              style: 'margin-right:20px',
              type: 'error',
              size: 'medium',
              text: true,
              disabled: !isAdmin || !/^[a-zA-Z](300|00)/.test(currentUser.account),
              onClick: () => {
                dialog.warning({
                  title: '警告',
                  content: '请先确认问题单，是否删除',
                  positiveText: '确定',
                  negativeText: '取消',

                  onPositiveClick: async () => {
                    try {
                      await deleteProblem({
                        id: row.id,
                      });
                      message.success('删除成功');
                      await handleGetOIList();
                    } catch {
                      message.error('删除失败');
                    }
                  },
                });
              },
            },
            [h('div', '删除')]
          ),
          h(
            NButton,
            {
              strong: true,
              tertiary: true,
              type: 'info',
              size: 'medium',
              style: 'display: inline;margin-right:20px',
              text: true,
              onClick: () => {
                router.push({
                  name: 'ProblemHandleDetail',
                  query: {
                    desc: row.desc,
                    dtsNum: row.dtsNum,
                  },
                });
              },
            },
            [h('div', '查看详情')]
          ),
          h(
            NButton,
            {
              strong: true,
              tertiary: true,
              type: 'info',
              size: 'medium',
              text: true,
              style: 'display: inline',
              onClick: async () => {
                await exportPublicSentiment({
                  dtsOrderId: row.dtsNum,
                });
              },
            },
            [h('div', '导出通报')]
          ),
        ];
      },
    };
    return [
      selectionColumn,
      ...BASE_COLUMNS.filter((item) => selectedBaseColumnsKeys.value.includes(item.key)),
      operationColumn,
    ];
  }
  // 设置表格要展示的列
  const handleSetColumns = () => {
    columns.value = getColumns();
    setHiddenColumns(selectedBaseColumnsKeys.value);
    showSetColumnsModal.value = false;
  };
  const getColumnsSettings = () => {
    selectedBaseColumnsKeys.value = getShownColumns();
  };
  //获取oi单列表
  const handleGetOIList = async () => {
    loading.value = true;
    const today = new Date();
    const tomorrow = new Date(today); // 克隆当前日期
    tomorrow.setDate(tomorrow.getDate() + 1); // 日期加1天
    let params: QueryProblemListReq = {
      pageNum: pagination.page - 1,
      pageSize: pagination.pageSize,
      // warnTimeStart:  isAllData.value ? formatDateTime(new Date(2025,2,31), 'yyyy-MM-dd HH:mm:ss') : undefined,
      // warnTimeEnd: isAllData.value ? formatDateTime(tomorrow, 'yyyy-MM-dd HH:mm:ss') : undefined,
      ...getProblemStatus(filters.value, sortList, moduleList),
    };

    let res = await getProblemList(params);
    if (res.status == '200') {
      message.success('获取成功');
      loading.value = false;
      pagination.itemCount = res.data.pageInfo.total;
      tableData.value = res.data.data.map((item, index) => {
        return { ...item, key: item.id };
      });
    }
  };
  //获取全量oi单列表
  const handleGetAllOIList = async () => {
    loading.value = true;
    let params: QueryProblemListReq = {
      pageNum: pagination.page - 1,
      pageSize: pagination.pageSize,
      ...getProblemStatus(filters.value, sortList),
    };

    let res = await getProblemList(params);
    if (res.status == '200') {
      message.success('获取成功');
      loading.value = false;
      pagination.itemCount = res.data.pageInfo.total;
      tableData.value = res.data.data.map((item, index) => {
        return { ...item, key: item.id };
      });
    }
  };
  // 多选逻辑
  const onUpdateChecked = (
    keys: Array<string | number>,
    rows: object[],
    meta: { row: object | undefined; action: 'check' | 'uncheck' | 'checkAll' | 'uncheckAll' }
  ) => {
    checkedRowKeys.value = keys;
  };
  const handleBatchDelete = () => {
    dialog.warning({
      title: '警告',
      content: '请先确认问题单，是否删除',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          checkedRowKeys.value.forEach(async (item) => {
            await deleteProblem({
              id: item,
            });
          });
          message.success('删除成功');

          await handleGetOIList();
          checkedRowKeys.value = [];
        } catch {
          message.error('删除失败');
        }
      },
    });
  };
  //提交处理工单
  const handleSubmint = async () => {
    // if (model.value.irNumber === 1 || model.value.irNumber === 3)
    formRef.value.validate(async (errors) => {
      if (!errors) {
        let data = JSON.parse(JSON.stringify(model.value));
        data.warnTime = formatDateTime(data.warnTime, 'yyyy-MM-dd HH:mm:ss');
        data.planTime = formatDateTime(data.planTime, 'yyyy-MM-dd HH:mm:ss');
        data.planTime = data.planTime === '1970-01-01 08:00:00' ? null : data.planTime;
        data.closeTime = data.closeTime
          ? formatDateTime(data.closeTime, 'yyyy-MM-dd HH:mm:ss')
          : null;
        data.isOverdue = data.isOverdue === 'NA' ? null : data.isOverdue;
        delete data.planLockTime;
        if (!isAdd.value) {
          var res = await updateProblem(data);
        } else {
          var res = await addProblem(data);
        }

        if (res.status == '200') {
          message.success('提交成功');
          showModal.value = false;
          await handleGetOIList();
        }
      }
    });
  };
  const handleExportProblem = () => {
    exportProblem(getProblemStatus(filters.value, sortList));
  };
  const handlequeryUserList = async () => {
    let { data } = await getEmployeeList();
    userList.value = data.map((item) => {
      return {
        label: item.userName + '/' + item.account,
        value: item.userName + '/' + item.account,
      };
    });
  };
  const handleGetewpUserList = async () => {
    let { data } = await queryUserList({
      pageSize: 1000,
      roles: '5_admin',
    });
    ewpUserList.value = data.data.map((item) => {
      return {
        label: item.userName + '/' + item.account,
        value: item.userName + '/' + item.account,
      };
    });
  };
  const handleUpdateFilter = () => {
    pagination.page = 1;
    handleGetOIList();
  };
  const handleResetFilter = () => {
    filters.value = getDefaultProblemHandleFilters();
    handleGetOIList();
  };
  // 切换高级搜索的展开/收起状态
  const unfoldToggle = () => {
    collapse.value = !collapse.value;
  };

  const handleSorter = (options) => {
    pagination.page = 1;
    let str1 = '';
    let str2 = '';
    if (options.columnKey === 'slaTime') {
      str1 = 'sla_time';
    } else if (options.columnKey === 'opinionVolume') {
      str1 = 'opinion_volume';
    } else {
      str1 = options.columnKey;
    }
    if (options.order === 'descend') {
      str2 = ' desc';
    } else if (options.order === false) {
      sortList = [];
      return handleGetOIList();
    }
    sortList = [str1 + str2];
    handleGetOIList();
  };
  handlequeryUserList();
  handleGetOIList();
  handleGetewpUserList();
  // 监听isAllData，重新渲染表格
  watch(
    () => isAllData.value,
    () => {
      console.log(isAllData.value, 'isAll');
      handleGetOIList();
    }
  );

  //问题进展
  const progressModel = () =>{
    const showProblemModal = ref(false);
    const currentProblem = ref({
      orderId: null,
      appName: '',
      description: ''
    })
    const selectData = ref({});
    const afterSubmit = async (data) =>{
      //更新最新进展
      selectData.value.progress = dayjs(data.createTime).format('MM/DD')+"："+data.problemProcess;
      delete selectData.value.planLockTime;
      await updateProblem(selectData.value);
    }
    const showModel = (row) =>{
      selectData.value = row;
      currentProblem.value.orderId = 'problem'+row.id;
      currentProblem.value.appName = row.appName;
      currentProblem.value.description = row.desc;
      showProblemModal.value = true;
    }


    return {
      showProblemModal,
      currentProblem,
      selectData,
      afterSubmit,
      showModel
    }
  }

  const {showProblemModal,
    currentProblem,
    selectData,
    afterSubmit,
    showModel} = progressModel();

  onMounted(async () => {
    eventBus.on('showModel', showModel);
  });

  onUnmounted(() => {
    eventBus.off('showModel', showModel);
  });

</script>

<style lang="less" scoped>
  .layout-page-header {
    margin-top: 20px;
    .n-select {
      min-width: 250px;
    }
  }
  .unfold-icon {
    display: flex;
    align-items: center;
    height: 100%;
    margin-left: -3px;
  }
</style>
