import { ObjectDirective } from 'vue';
import { useUserStore } from '@/store/modules/user';

export const permission: ObjectDirective = {
  mounted(el: HTMLElement, binding) {
    const { value } = binding;
    const userStore = useUserStore();
    const roles = userStore.roles;

    if (value && value instanceof Array) {
      if (value.length > 0) {
        const permissionRoles = value;
        const hasPermission = roles.some((role) => permissionRoles.includes(role));

        if (!hasPermission) {
          el.parentNode && el.parentNode.removeChild(el);
        }
      }
    } else {
      throw new Error("need roles! Like v-permission=\"['superadmin']\"'");
    }
  },
};
