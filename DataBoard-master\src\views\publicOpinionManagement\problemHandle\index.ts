import { BASE_COLUMNS } from '@/views/publicOpinionManagement/problemHandle/columns';
import { SelectOption } from 'naive-ui';
import { SelectBaseOption } from 'naive-ui/es/select/src/interface';
import { ColumnKey } from 'naive-ui/es/data-table/src/interface';
import { ProblemStatus, QueryProblemListReq } from "@/api/dataview/problemHandle";

export const statusList = [
  {
    label: '定界中',
    value: '0',
  },
  {
    label: '待锁定',
    value: '1',
  },
  {
    label: '已锁定',
    value: '2',
  },
  {
    label: '已闭环',
    value: '3',
  },
  {
    label: '观察声量',
    value: '4',
  },
  {
    label: '225未闭环',
    value: '5',
  },
];
export enum IssueTypeEnum {
  /** 应用缺失 */
  APP_MISSING = 0,
  /** 功能缺失 */
  FUNC_MISSING = 1,
  /** 功能问题 */
  FUNC_PROBLEM = 2,
}
export const ISSUE_TYPE_OPTIONS: SelectBaseOption<IssueTypeEnum, string>[] = [
  {
    label: '应用缺失',
    value: IssueTypeEnum.APP_MISSING,
  },
  {
    label: '功能缺失',
    value: IssueTypeEnum.FUNC_MISSING,
  },
  {
    label: '功能问题',
    value: IssueTypeEnum.FUNC_PROBLEM,
  },
];
export interface ProblemHandleFilters {
  id: number | null;
  dtsNum: string;
  appName: string;
  status: string;
  owner: string;
  guarantor: string;
  level: string;
  dtsStatus: string;
  warnTime: [number, number] | null;
  planTime: [number, number] | null;
  levelList: string[];
  statusList: string[];
  module: string[];
  isOverdue: Array<boolean | 'NA'>;
  overdueType: [];
  closeTime: null | string[];
  dtsStatusList: string;
  planLockTime: [number, number] | null;
  applicationLvList: [];
  problemSourceList: [];
}
export interface ProblemDto {
  isOverduePlanLock: string | boolean;
  id: number;
  warnTime: string;
  module: string;
  appName: string;
  desc: string;
  level: string;
  opinionVolume: number;
  status: string;
  progress: string;
  source: string;
  planTime: string;
  guarantorAccount: string;
  guarantor: string;
  ownerAccount: string;
  owner: string;
  moduleOwnerAccount: string;
  moduleOwner: string;
  dtsNum: string;
  dtsStatus: string;
  product: string;
  informer: string;
  createTime: string;
  slaTime: string;
  isOverdue: boolean;
  overdueType: string;
  closeTime: string;
  locateTime: string;
  planLockTime: string;
  knowledgeId: string;
  knowledgeLink: string;
  issueType: IssueTypeEnum;
}
export const moduleList = [
  {
    label: '4796',
    value: '4796',
  },
  {
    label: '垂域专精',
    value: '垂域专精',
  },
  {
    label: '长尾',
    value: '长尾',
  },
];
export const sourceList = [
  {
    label: 'FUT',
    value: '0',
  },
  {
    label: 'BetaClub',
    value: '1',
  },
  {
    label: '热线',
    value: '2',
  },
  {
    label: 'VOC',
    value: '3',
  },
  {
    label: 'VIP',
    value: '4',
  },
];
export const modelTemplate = {
  warnTime: new Date(),
  module: '',
  appName: '',
  desc: '',
  level: '',
  opinionVolume: '',
  status: '',
  progress: '',
  reviewResult: '',
  irStatus: '',
  planTime: null,
  guarantor: '',
  owner: '',
  moduleOwner: '',
  product: '',
  dtsNum: null,
  informer: '',
  source: '',
  isOverdue: null,
  overdueType: '',
  closeTime: null,
  knowledgeId: '',
  knowledgeLink: '',
  issueType: null,
  textarea: '',
  applicationLv: '',
  problemSource: '',
};
export function formatDateTime(date, format) {
  date = new Date(date);
  const o = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12, // 小时
    'H+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
    a: date.getHours() < 12 ? '上午' : '下午', // 上午/下午
    A: date.getHours() < 12 ? 'AM' : 'PM', // AM/PM
  };
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      );
    }
  }
  return format;
}
export function getDefaultProblemHandleFilters(): ProblemHandleFilters {
  return {
    id: null,
    dtsNum: '',
    appName: '',
    status: '',
    owner: '',
    guarantor: '',
    level: '',
    dtsStatus: '',
    module: [],
    warnTime: [formatDateTime(new Date(2025,2,31), 'yyyy-MM-dd HH:mm:ss'),new Date()],
    planTime: null,
    statusList: [],
    levelList: [],
    isOverdue: [],
    overdueType: [],
    closeTime: null,
    planLockTime: null,
    applicationLvList: [],
    problemSourceList: [],
    dtsStatusList: '',
  };
}

export const isOverdueList = [
  {
    label: 'NA',
    value: 'NA',
  },
  {
    label: '否',
    value: false,
  },
  {
    label: '是',
    value: true,
  },
];
export const overdueTypeList = [
  {
    label: '定界超期',
    value: '定界超期',
  },
  {
    label: '计划锁定超期',
    value: '计划锁定超期',
  },
  {
    label: '闭环超期',
    value: '闭环超期',
  },
];
export const appBelongList = [
  {
    label: '225',
    value: '225',
  },
  {
    label: '4796',
    value: 'others',
  },
];
export const dtsStatusList = [
  { label: 'CCB方案审核', value: 'CCB方案审核' },
  { label: '测试人员回归测试', value: '测试人员回归测试' },
  { label: 'CMO归档', value: 'CMO归档' },
  { label: '关闭', value: '关闭' },
  { label: '审核人员审核修改', value: '审核人员审核修改' },
  { label: '测试经理组织测试', value: '测试经理组织测试' },
  { label: '开发人员实施修改', value: '开发人员实施修改' },
  { label: '测试（项目）经理审核', value: '测试（项目）经理审核' },
  { label: '问题提交人填写', value: '问题提交人填写' },
  { label: '挂起', value: '挂起' },
  { label: '撤销', value: '撤销' },
];
export const applicationLvList = [
  { label: 'lv1', value: 'lv1' },
  { label: 'lv2', value: 'lv2' },
  { label: 'lv3', value: 'lv3' },
  { label: 'lv4', value: 'lv4' },
  { label: 'lv5', value: 'lv5' },
  { label: 'lv6', value: 'lv6' },
];
export const problemSourceList = [
  { label: '代表处', value: '代表处' },
  { label: '互联网', value: '互联网' },
  { label: '行业系统部', value: '行业系统部' },
];

export function getColumnsSelectOptions(): SelectOption[] {
  const result: SelectOption[] = [];
  BASE_COLUMNS.forEach((item) => {
    if (item.title && item.key) {
      result.push({
        value: item.key,
        label: item.title as string,
      });
    }
  });
  return result;
}
// 保存要隐藏的表格列
export function setHiddenColumns(selectedColumnsKeys: ColumnKey[]): void {
  localStorage.setItem(
    'publicOpinionHiddenColumns',
    BASE_COLUMNS.map((item) => item.key)
      .filter((item) => !selectedColumnsKeys.includes(item))
      .join(',')
  );
}
// 获取要显示的表格列
export function getShownColumns(): ColumnKey[] {
  const hiddenColumns: ColumnKey[] =
    localStorage.getItem('publicOpinionHiddenColumns')?.split(',') ?? [];
  return BASE_COLUMNS.map((item) => item.key).filter((item) => !hiddenColumns.includes(item));
}
export function getProblemStatus(filters: ProblemHandleFilters, sortList: string[], moduleList: string[]): ProblemStatus {
  let params: ProblemStatus = {
    descField: sortList,
  };
  for (let key in filters) {
    if (key === 'isOverdue') {
      params.isOverdue = filters.isOverdue.map((item) => (item === 'NA' ? null : item));
    } else if (key === 'warnTime') {
      if (filters.warnTime?.[0] && filters.warnTime?.[1]) {
        [params.warnTimeStart, params.warnTimeEnd] = filters.warnTime;
        params.warnTimeStart = formatDateTime(params.warnTimeStart, 'yyyy-MM-dd HH:mm:ss');
        params.warnTimeEnd = formatDateTime(params.warnTimeEnd + 86400000, 'yyyy-MM-dd HH:mm:ss');
      }
    } else if (key === 'planTime') {
      if (filters.planTime?.[0] && filters.planTime?.[1]) {
        [params.planTimeStart, params.planTimeEnd] = filters.planTime;
        params.planTimeStart = formatDateTime(params.planTimeStart, 'yyyy-MM-dd HH:mm:ss');
        params.planTimeEnd = formatDateTime(params.planTimeEnd + 86400000, 'yyyy-MM-dd HH:mm:ss');
      }
    } else if (key === 'closeTime') {
      if (filters.closeTime) {
        params['closeTime'] = filters.closeTime;
        params['closeTime'][1] += 86400000;
      }
    } else if (key === 'planLockTime') {
      if (filters.planLockTime) {
        [params.planLockTimeStart, params.planLockTimeEnd] = filters.planLockTime;
        params.planLockTimeStart = formatDateTime(
          params.planLockTimeStart,
          'yyyy-MM-dd HH:mm:ss'
        );
        params.planLockTimeEnd = formatDateTime(
          params.planLockTimeEnd + 86400000,
          'yyyy-MM-dd HH:mm:ss'
        );
      }
    } else if (key === 'module') {
      if (filters.module.length === 0) {
        params.module = ['others'];
      }else {
        params.module = filters.module;
      }
    } else if (filters[key]) {
      params[key] = filters[key];
    }
  }
  return params;
}
