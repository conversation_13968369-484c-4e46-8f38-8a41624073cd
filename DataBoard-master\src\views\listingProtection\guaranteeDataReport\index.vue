<template>
  <div class="fut-container">
    <div class="chart-container">
      <div class="chart-box flex-box">
        <div class="chart-item">
          <LineChart :title="lineChart.title" :x-data="lineChart.xData" :y-data="lineChart.yData"></LineChart>
        </div>
        <div class="chart-item">
          <BarChart :title="barChart.title" :x-data="barChart.xData" :y-data="barChart.yData"></BarChart>
        </div>
      </div>

      <div class="chart-box flex-box" style="margin-top: 10px;">
        <div class="chart-item">
          <LineBarChart></LineBarChart>
        </div>
        <div class="chart-item">
          <twoLineChart></twoLineChart>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import LineChart from './charts/LineChart.vue';
import BarChart from './charts/BarChart.vue';
import LineBarChart from './charts/LineBarChart.vue'
import twoLineChart from './charts/TwoLineChart.vue'

const lineChart = {
  title: '应用使用样例代码提升效率(人/天)',
  xData: [
    "23-10.6",
    "23-10.13",
    "23-10.20",
    "23-10.27",
    "23-11.03",
  ],
  yData: [
    1179,
    1182,
    1190,
    1200,
    1205
  ],
}

const barChart = {
  title: '样例代码贡献数量(前十)',
  xData: ['平平', '王校宏', '张伟隆', '魏素芳', '朱维俊', '施博洋', '葛颖森', '李承芳', '邵宝', '房智睿'],
  yData: [41, 34, 22, 21, 20, 17, 12, 12, 12, 11]
}

</script>

<style scoped>
.flex-box {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

}

.fut-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
  width: 100%;
  justify-content: space-between;
}

.chart-container {
  width: 100%;
  height: 100%;
}

.chart-box {
  width: 100%;
  /* height: 100%; */

}

.chart-item {
  width: calc(50% - 5px);
  height: 400px;
  background-color: white;
}
</style>
