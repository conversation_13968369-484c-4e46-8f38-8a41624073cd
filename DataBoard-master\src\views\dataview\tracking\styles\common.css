.tracking-container {
  padding: 24px;
  background-color: #f5f7fa;
  min-height: 100%;
}

.tracking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  margin-bottom: 24px;
}

.tracking-header:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.header-left {
  display: flex;
  flex-direction: column;
}

.header-left h2 {
  font-size: 2rem;
  font-weight: 600;
  margin: 0;
  background: linear-gradient(45deg, #1a1a1a, #4a4a4a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.5px;
}

.subtitle {
  font-size: 1rem;
  color: #666;
  margin-top: 8px;
  font-weight: 500;
  letter-spacing: 0.5px;
  opacity: 0.8;
}

.header-right {
  display: flex;
  gap: 40px;
}

.header-right :deep(.n-statistic) {
  text-align: center;
  padding: 16px 28px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.header-right :deep(.n-statistic:hover) {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.06);
}

.header-right :deep(.n-statistic-label) {
  font-size: 0.875rem;
  color: #666;
  font-weight: 500;
  margin-bottom: 6px;
  letter-spacing: 0.5px;
}

.header-right :deep(.n-statistic-value) {
  font-size: 1.75rem;
  font-weight: 600;
}

.statistics-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 24px;
}

.statistics-row .n-card {
  border-radius: 12px;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.statistics-row .n-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.trend-card {
  padding: 16px;
}

.trend-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.trend-header span {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
}

.trend-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  color: #666;
  font-size: 0.875rem;
}

.metric-card {
  text-align: center;
  padding: 20px;
}

.metric-title {
  color: #333;
  font-weight: 500;
  margin-bottom: 16px;
}

.metric-value {
  font-size: 2.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 12px 0;
  line-height: 1;
}

.unit {
  font-size: 1rem;
  margin-left: 4px;
  color: #666;
  font-weight: 500;
}

.metric-footer {
  margin-top: 12px;
  font-size: 0.875rem;
  font-weight: 500;
}

.charts-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 24px;
}

.chart-container {
  height: 320px;
  padding: 16px;
}

.n-data-table {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
}

:deep(.n-data-table-th) {
  background-color: #f8fafc !important;
  font-weight: 600;
  color: #333;
}

:deep(.n-tag) {
  font-weight: 500;
}

:deep(.n-progress) {
  margin: 8px 0;
}

:deep(.n-button) {
  font-weight: 500;
}

:deep(.n-card-header) {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
}
