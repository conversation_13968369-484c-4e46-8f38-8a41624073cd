import { RouteRecordRaw } from 'vue-router';
import { Layout } from '@/router/constant';
import { SettingOutlined } from '@vicons/antd';
import { renderIcon } from '@/utils';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/setting',
    name: 'Setting',
    redirect: '/setting/account',
    component: Layout,
    meta: {
      title: '设置页面',
      icon: renderIcon(SettingOutlined),
      sort: 5,
      hidden: true
    },
    children: [
      {
        path: 'account',
        name: 'setting-account',
        meta: {
          title: '个人信息',
        },
        component: () => import('@/views/setting/account/account.vue'),
      },
      // {
      //   path: 'system',
      //   name: 'setting-system',
      //   meta: {
      //     title: '系统设置',
      //   },
      //   component: () => import('@/views/setting/system/system.vue'),
      // },
      {
        path: 'knowledgeSetting',
        name: 'knowledgeSetting',
        meta: {
          title: '知识库设置',
        },
        component: () => import('@/views/kowledgeBase/knowledgeIframe/index.vue'),
      },
    ],
  },
];

export default routes;
