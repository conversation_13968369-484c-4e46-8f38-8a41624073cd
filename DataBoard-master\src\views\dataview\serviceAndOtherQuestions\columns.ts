import {
  getPersonOptionsNameAccount,
  getPersonOptionsOnlyName,
} from '@/views/dataview/personManage/staffCommonUtils';
import { DTS_HANDLE_TAG } from '@/views/dataview/personManage/tagContant';

interface ComponentStyle {
  width: string;
  textAlign: string;
}

interface ComponentProps {
  style: ComponentStyle;
  clearable?: boolean;
  placeholder?: string;
  options?: Array<{ label: string; value: any }>;
  filterable?: boolean;
}

export interface SearchFormItem {
  field: string;
  label: string;
  component: 'Input' | 'Select' | 'DateRangePicker';
  componentProps?: ComponentProps;
}

// 创建一个通用的样式对象
const commonStyle: ComponentProps = {
  style: {
    width: '300px',
    textAlign: 'left',
  },
  clearable: true,
  placeholder: '请输入',
};

// 为 Select 组件创建特定样式
const selectStyle: ComponentProps = {
  ...commonStyle,
  style: {
    ...commonStyle.style,
  },
  filterable: true,
  placeholder: '请选择',
};

export const sourceOptions = [
  { label: 'VOC预警', value: 'VOC预警' },
  { label: '预录单', value: '预录单' },
  { label: '热线/互联网', value: '热线/互联网' },
  { label: '门店反馈', value: '门店反馈' },
  { label: '巡店', value: '巡店' },
  { label: 'SVIP/VIP', value: 'SVIP/VIP' },
  { label: 'BetaClub/反馈助手', value: 'BetaClub/反馈助手' },
  { label: '代表处', value: '代表处' },
  { label: 'UEF', value: 'UEF' },
  { label: '其它(电话/微信/mail/eSapce等)', value: '其它(电话/微信/mail/eSapce等)' },
  { label: 'AG', value: 'AG' },
];

export const getSearchFormItems = (): SearchFormItem[] => [
  {
    field: 'appName',
    label: '应用名称',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'orderDescription',
    label: '问题描述',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'orderStatus',
    label: '状态',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: dtsStatusOptions,
    },
  },
  {
    field: 'issueTypeList',
    label: '问题类型',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: typeOptions,
    },
  },
  {
    field: 'source',
    label: '来源',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: sourceOptions,
    },
  },
  {
    field: 'orderId',
    label: '服务单号',
    component: 'Input',
    componentProps: commonStyle,
  },
  // {
  //   field: 'appVersion',
  //   label: '应用版本',
  //   component: 'Input',
  //   componentProps: commonStyle,
  // },
  // {
  //   field: 'sceneName',
  //   label: '场景名称',
  //   component: 'Input',
  //   componentProps: commonStyle,
  // },
  {
    field: 'dtsOrderId',
    label: 'DTS单号',
    component: 'Input',
    componentProps: commonStyle,
  },
  // {
  //   field: 'severity',
  //   label: '问题级别',
  //   component: 'Select',
  //   componentProps: {
  //     ...selectStyle,
  //     options: questionLevelOptions,
  //   },
  // },
  // {
  //   field: 'progress',
  //   label: '问题进展',
  //   component: 'Input',
  //   componentProps: commonStyle,
  // },
  {
    field: 'deviceList',
    label: '产品（可搜索）',
    component: 'Input',
    componentProps: {
      ...selectStyle,
      options: deviceOptions,
    },
  },
  // {
  //   field: 'osVersion',
  //   label: '系统版本',
  //   component: 'Input',
  //   componentProps: commonStyle,
  // },
  {
    field: 'ewpOwner',
    label: 'EWP责任人',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: employeesNameOptions,
    },
  },
  // {
  //   field: 'domainOwner',
  //   label: '领域责任人',
  //   component: 'Input',
  //   componentProps: commonStyle,
  // },
  {
    field: 'feedbackSource',
    label: '反馈人/响应群名',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'sourceClosed',
    label: '来源是否关闭',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: sourceClosedOption,
    },
  },
  {
    field: 'sourceOwner',
    label: '来源登记人',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'top',
    label: '是否top2000',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: trueFalseOption,
    },
  },
  {
    field: 'userSubmissionTime',
    label: '用户提单时间',
    component: 'DateRangePicker',
    componentProps: commonStyle,
  },
];

export const questionLevelOptions = [
  { label: '一般', value: '一般' },
  { label: '严重', value: '严重' },
  { label: '提示', value: '提示' },
  { label: '致命', value: '致命' },
];

export const dtsStatusOptions = [
  { label: '待分单', value: '待分单' },
  { label: '待定界', value: '待定界' },
  { label: '待锁定', value: '待锁定' },
  { label: '待审核', value: '待审核' },
  { label: '待归档', value: '待归档' },
  { label: '待回归', value: '待回归' },
  // { label: '已锁定', value: '已锁定' },
  { label: '已闭环', value: '已闭环' },
];

export const trueFalseOption = [
  { label: '是', value: '是' },
  { label: '否', value: '否' },
];

export const sourceClosedOption = [
  { label: '是', value: '是' },
  { label: '否', value: '否' },
  { label: '无需闭环', value: '无需闭环' },
];

export const typeOptions = [
  { label: '重大舆情', value: '重大舆情' },
  { label: '风险问题', value: '风险问题' },
  { label: 'Purax问题', value: 'Purax问题' },
  { label: '需走单', value: '需走单' },
  { label: '非ewp问题单', value: '非ewp问题单' },
  { label: '未更新', value: '未更新' },
  { label: '需降级', value: '需降级' },
  { label: '新单待定界', value: '新单待定界' },
  { label: '已更新', value: '已更新' },
  { label: '挂起', value: '挂起' },
];

export const deviceOptions = [
  { label: 'Pura X', value: 'Pura X' },
  { label: 'nova 14', value: 'nova 14' },
  { label: 'nova 14 Pro', value: 'nova 14 Pro' },
  { label: 'nova 14 Ultra', value: 'nova 14 Ultra' },
  { label: 'Mate XT', value: 'Mate XT' },
  { label: 'Mate X6', value: 'Mate X6' },
  { label: 'Mate X5', value: 'Mate X5' },
  { label: 'mate 60', value: 'mate 60' },
  { label: 'mate 60 pro', value: 'mate 60 pro' },
  { label: 'mate 70', value: 'mate 70' },
  { label: 'Pura 70 Pro', value: 'Pura 70 Pro' },
  { label: 'Pura 70 Pro+', value: 'Pura 70 Pro+' },
  { label: 'MatePad Pro', value: 'MatePad Pro' },
  { label: 'Mate 70 Pro+', value: 'Mate 70 Pro+' },
  { label: 'Pocket 2', value: 'Pocket 2' },
  { label: 'nova 13 Pro', value: 'nova 13 Pro' },
  { label: 'Pura 70 Ultra', value: 'Pura 70 Ultra' },
  { label: 'Mate 60 RS', value: 'Mate 60 RS' },
  { label: 'MatePad Air', value: 'MatePad Air' },
  { label: 'nova 12 Ultra', value: 'nova 12 Ultra' },
  { label: 'nova 13 Pro', value: 'nova 13 Pro' },
  { label: 'all', value: 'all' },
];

//获取用户信息
export const employeesOptions = await getPersonOptionsNameAccount(DTS_HANDLE_TAG);
export const employeesNameOptions = await getPersonOptionsOnlyName(DTS_HANDLE_TAG);
