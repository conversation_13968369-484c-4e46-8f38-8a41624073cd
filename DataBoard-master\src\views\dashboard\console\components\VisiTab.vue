<template>
  <div class="mt-4">
    <NRow :gutter="24">
      <NCol :span="24">
        <n-card content-style="padding: 0;" :bordered="false">
          <n-tabs type="line" size="large" :tabs-padding="20" pane-style="padding: 20px;">
            <n-tab-pane name="关键问题">
              <div class="issue-list">
                <n-data-table
                  :columns="columns"
                  :data="props.keyDtsList"
                  :pagination="false"
                  :bordered="false"
                  :max-height="400"
                  size="small"
                />
              </div>
            </n-tab-pane>
            <n-tab-pane name="TOP2000问题">
              <div class="issue-list">
                <n-data-table
                  @update:filters="handleFiltersChange"
                  remote
                  :columns="columns"
                  :data="top1000DtsList"
                  :pagination="paginationReactive"
                  :bordered="false"
                  :max-height="400"
                  size="small"
                />
              </div>
            </n-tab-pane>
          </n-tabs>
        </n-card>
      </NCol>
    </NRow>
  </div>
</template>

<script lang="ts" setup>
  import { h, onMounted, reactive, ref } from 'vue';
  import { NButton, NTag, useDialog, useMessage } from 'naive-ui';
  import { getWorkOrderList, updateWorkOrder } from '@/api/dataview/appState';
  import { useUserStore } from '@/store/modules/user';
  import { filterObjectValues } from '@/utils';
  const userStore = useUserStore();
  const userInfo: any = userStore.getUserInfo || {};
  interface DtsList {
    id: number;
    orderId: string;
    appName: string;
    description: string;
    status: string;
    // ... other fields can be added as needed
  }
  const paginationReactive = reactive({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    prefix({ itemCount }) {
      return `Total：${itemCount}`;
    },
    onChange: (page: number) => {
      paginationReactive.page = page;
      fetchData();
    },
    onUpdatePageSize: (pageSize: number) => {
      paginationReactive.pageSize = pageSize;
      paginationReactive.page = 1;
      fetchData();
    },
  });
  const searchForm = reactive({});
  const props = defineProps<{
    keyDtsList: DtsList[];
  }>();
  const top1000DtsList = ref([]);
  const handleFiltersChange = (filters: any) => {
    // 合并筛选条件到搜索表单
    Object.assign(searchForm, filters);
    // 重置到第一页
    paginationReactive.page = 1;
    // 重新获取数据
    fetchData();
  };
  const fetchData = async () => {
    try {
      const queryParams = {
        ...filterObjectValues(searchForm),
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
        sortField: 'riskScore',
        sortOrder: 'desc',
        ewpOwner: userInfo.userName,
        top: 'TOP2000',
      };
      const { total, records } = await getWorkOrderList(queryParams);
      top1000DtsList.value = records;
      paginationReactive.itemCount = total;
    } catch (error) {
      console.error('Failed to load table data:', error);
    }
  };
  const columns = [
    {
      title: '问题单号',
      key: 'orderId',
      width: 200,
      render: (row) => {
        return h(
          NTag,
          {
            type: 'info',
            bordered: false,
            onClick: async () => {
              const { orderId } = row;
              // 根据orderId跳转到对应的详情页面
              window.open(
                `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${orderId}`,
                '_blank'
              );
            },
          },
          {
            default: () => row.orderId,
          }
        );
      },
    },
    {
      title: '应用名称',
      key: 'appName',
      width: 120,
      render: (row) => {
        return h(
          NTag,
          {
            bordered: false,
            style: {
              cursor: 'pointer',
            },
            // onClick: () => handleAppNameClick(row.appName),
          },
          {
            default: () => row.appName,
          }
        );
      },
    },
    {
      title: '问题描述',
      key: 'description',
      ellipsis: {
        tooltip: true,
      },
      show: true,
      render: (row) => {
        const description = true
          ? row.description.split('】').pop() || row.description
          : row.description;
        return h('span', {}, description);
      },
    },
    {
      title: 'DTS单状态',
      key: 'status',
      width: 120,
      filterOptions: [
        { label: '问题提交人填写', value: '问题提交人填写' },
        { label: '测试（项目）经理审核', value: '测试（项目）经理审核' },
        { label: '开发人员实施修改', value: '开发人员实施修改' },
        { label: 'CCB方案审核', value: 'CCB方案审核' },
        { label: '审核人员审核修改', value: '审核人员审核修改' },
        { label: 'CMO归档', value: 'CMO归档' },
        { label: '测试经理组织测试', value: '测试经理组织测试' },
        { label: '测试人员回归测试', value: '测试人员回归测试' },
        { label: '关闭', value: '关闭' },
        { label: '挂起', value: '挂起' },
      ],
      filter: true,
      multiple: true,
      render(row) {
        const statusMap = {
          CCB方案审核: { text: '处理中', type: 'warning' },
          开发人员实施修改: { text: '处理中', type: 'info' },
          已解决: { text: '已解决', type: 'success' },
        };
        const status = statusMap[row.status] || { text: '待处理', type: 'error' };
        return h(
          NTag,
          {
            type: status.type,
            size: 'small',
          },
          { default: () => row.status }
        );
      },
    },
    {
      title: 'EWP状态',
      key: 'ewpStatus',
      ellipsis: {
        tooltip: true,
      },
      show: true,
      filter: true,
      filterOptions: [
        { label: '待分单', value: '待分单' },
        { label: '待定界', value: '待定界' },
        { label: '待锁定', value: '待锁定' },
        { label: '待审核', value: '待审核' },
        { label: '待归档', value: '待归档' },
        { label: '待回归', value: '待回归' },
        // { label: '已锁定', value: '已锁定' },
        { label: '已闭环', value: '已闭环' },
      ],
      multiple: true,
    },
    {
      title: '问题进展',
      key: 'progress',
      fixed: 'right',
      ellipsis: {
        tooltip: true,
      },
      show: true,
    },
    {
      title: '操作',
      key: 'actions',
      fixed: 'right',
      width: 200,
      render: (row) => {
        return h('div', { style: { display: 'flex', gap: '8px' } }, [
          h(
            NButton,
            {
              size: 'small',
              onClick: () => handleRemark?.(row),
            },
            { default: () => '问题进展' }
          ),
        ]);
      },
    },
  ];
  const message = useMessage();
  const dialog = useDialog();
  const emit = defineEmits(['refresh']);
  // 处理问题进展
  const handleRemark = (row: any) => {
    const tempRemark = ref(row.progress || '');

    dialog.create({
      title: '问题进展',
      content: () =>
        h('div', [
          h('textarea', {
            rows: 4,
            type: 'error', // 添加红样式
            value: tempRemark.value,
            style: {
              width: '100%',
              padding: '8px',
              marginTop: '8px',
              border: '1px solid #ccc',
              borderRadius: '4px',
            },
            onInput: (e: Event) => {
              tempRemark.value = (e.target as HTMLTextAreaElement).value;
            },
          }),
        ]),
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          await updateWorkOrder({
            ...row,
            progress: tempRemark.value,
          });
          message?.success('更新成功');
          emit('refresh');
        } catch (error) {
          message?.error('更新失败');
        }
      },
    });
  };

  onMounted(() => {
    fetchData();
  });
</script>

<style scoped>
  /* .issue-list {
    height: 400px;
  } */

  :deep(.n-data-table .n-data-table-tr) {
    height: 47px;
  }
</style>
