<template>
  <div>
    <n-card style="margin-bottom: 10px">
      <n-form
        :model="searchForm"
        label-placement="left"
        label-width="90"
        require-mark-placement="right-hanging"
        size="medium"
        v-if="collapse"
      >
        <n-grid :cols="3" :x-gap="24">
          <n-form-item-gi label="TicketNo" path="ticketNo">
            <n-input placeholder="enter keywords for search" v-model:value="searchForm.ticketNo" />
          </n-form-item-gi>
        </n-grid>
      </n-form>
      <n-space justify="center">
        <n-button secondary strong type="primary" @click="formSearch()"> Query </n-button>
        <n-button secondary strong type="default" @click="refreshSearch()"> Reset </n-button>
        <n-button type="primary" icon-placement="right" @click="collapse = !collapse">
          <template #icon>
            <n-icon size="14" class="unfold-icon" v-if="collapse">
              <UpOutlined />
            </n-icon>
            <n-icon size="14" class="unfold-icon" v-else>
              <DownOutlined />
            </n-icon>
          </template>
          {{ collapse ? 'Collapse' : 'Expand' }}
        </n-button>
      </n-space>
    </n-card>
    <n-card>
      <n-space>
        <n-button secondary strong type="warning" @click="refreshData()">
          <template #icon>
            <n-icon>
              <Refresh />
            </n-icon>
          </template>
          Refresh
        </n-button>
        <n-button secondary strong type="primary" @click="onExportClick" :loading="exportLoading">
          <template #icon>
            <n-icon>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                <path fill="currentColor" d="M13 10h5l-6-6-6 6h5v8h2v-8M4 19v-7h2v7h12v-7h2v7H4Z" />
              </svg>
            </n-icon>
          </template>
          Export
        </n-button>
        <n-button secondary strong type="error" @click="handleBatchDelete()">
          <template #icon>
            <n-icon>
              <Trash />
            </n-icon>
          </template>
          Batch Delete
        </n-button>
      </n-space>
      <n-data-table
        :columns="columns"
        :data="data"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        :checked-row-keys="selectRows"
        @update:checked-row-keys="handleRowCheck"
        style="margin-top: 20px"
      >
        <template #empty>
          <n-empty description="No Data"></n-empty>
        </template>
      </n-data-table>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
  import { Refresh, Trash } from '@vicons/ionicons5';
  import { DownOutlined, UpOutlined } from '@vicons/antd';
  import { useMessage } from 'naive-ui';
  import { ref, reactive, onMounted, h } from 'vue';
  import { cloneDeep, debounce } from 'lodash-es';
  import { query, exportExcel, batchDelete } from '@/api/wiseoperKnowledge/index';
  import { defaultSearchForm } from './index';
  const loading = ref(true);
  const exportLoading = ref(false);
  const data = ref([]);
  const collapse = ref(true);
  const searchForm = ref(cloneDeep(defaultSearchForm));
  const selectRows = ref([]);

  const sampleDevicesRecordColumn = [
    {
      type: 'selection',
    },
    {
      title: 'TicketNo',
      key: 'ticketNo',
      minWidth: 60,
      resizable: true,
      render: (row) => {
        return h(
          'a',
          {
            href: row.url,
            target: '_blank',
            rel: 'noopener noreferrer',
            style: {
              color: '#1288ff',
              textDecoration: 'none',
              cursor: 'pointer',
            },
          },
          { default: () => row.ticketNo }
        );
      },
    },
    {
      title: 'DefectId',
      key: 'defectId',
      minWidth: 100,
      resizable: true,
    },
    {
      title: 'IsKnowledgeExist',
      key: 'isKnowledgeExist',
      minWidth: 100,
      resizable: true,
      render(row) {
        return row.isKnowledgeExist ? 'Yes' : 'No';
      },
    },
  ];
  const columns = ref(sampleDevicesRecordColumn);
  const message = useMessage();
  const pagination = ref(
    reactive({
      page: 1,
      pageSize: 10,
      itemCount: 10,
      showSizePicker: true,
      pageSizes: [
        {
          label: '10/page',
          value: 10,
        },
        {
          label: '20/page',
          value: 20,
        },
        {
          label: '50/page',
          value: 50,
        },
      ],
      prefix({ itemCount }) {
        return `Total：${itemCount}`;
      },
      onChange: (page: number) => {
        pagination.value.page = page;
        search();
      },
      onUpdatePageSize: (pageSize: number) => {
        pagination.value.pageSize = pageSize;
        pagination.value.page = 1;
        search();
      },
    })
  );

  const search = debounce(async () => {
    loading.value = true;
    let res;
    try {
      res = await query({
        ticketNo: searchForm.value.ticketNo,
        pageNum: pagination.value.page,
        pageSize: pagination.value.pageSize,
      });
      if (res?.status === '200') {
        pagination.value.itemCount = res?.data?.pageInfo?.count || 0;
        data.value = res?.data?.wiseoperTickets || [];
      }
    } catch (e) {}
    loading.value = false;
  }, 300);

  const refreshData = () => {
    search();
  };

  const formSearch = () => {
    pagination.value.page = 1;
    search();
  };

  const refreshSearch = () => {
    searchForm.value = cloneDeep(defaultSearchForm);
    search();
  };

  const onExportClick = async () => {
    loading.value = true;
    try {
      await exportExcel();
    } catch (e) {
      message.warning('Export failed！');
    } finally {
      loading.value = false;
    }
  };

  const handleRowCheck = (rowKeys) => {
    selectRows.value = rowKeys;
  };

  const handleBatchDelete = async () => {
    if (!selectRows.value.length) {
      message.warning('Please select the data to be deleted！');
      return;
    }
    try {
      const res = await batchDelete(selectRows.value);
      if (res?.status === '200') {
        message.success('Deleted successfully！');
        pagination.value.page = 1;
        search();
      }
    } catch (e) {
      message.error('Deletion failed！');
    }
  };

  onMounted(() => {
    search();
  });
</script>

<style lang="less" scoped>
  .search-container {
    margin-bottom: 10px;
  }
</style>
