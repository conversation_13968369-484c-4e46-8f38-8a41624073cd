<template>
  <div>
    <n-card :bordered="false" class="mt-4">
      <!-- 分类导航区域 -->
      <div class="category-nav">
        <!-- 一级分类 -->
        <div class="category-section">
          <div class="category-title">一级分类:</div>
          <div class="category-content">
            <n-space>
              <n-button
                v-for="item in categoryLevel1Options"
                :key="item.value"
                :type="selectedCategories.includes(item.value) ? 'primary' : 'default'"
                @click="handleCategoryClick(item.value, 0)"
                class="category-btn"
              >
                {{ item.label }}
              </n-button>
            </n-space>
          </div>
        </div>

        <!-- 二级分类 -->
        <div class="category-section" v-if="level2Options.length">
          <div class="category-title">二级分类:</div>
          <div class="category-content">
            <n-space>
              <n-button
                v-for="item in level2Options"
                :key="item.value"
                :type="selectedCategories.includes(item.value) ? 'primary' : 'default'"
                @click="handleCategoryClick(item.value, 1)"
                size="small"
                class="category-btn"
                ghost
              >
                {{ item.label }}
              </n-button>
            </n-space>
          </div>
        </div>

        <!-- 三级分类 -->
        <div class="category-section" v-if="level3Options.length">
          <div class="category-title">三级分类:</div>
          <div class="category-content">
            <n-space>
              <n-button
                v-for="item in level3Options"
                :key="item.value"
                :type="selectedCategories.includes(item.value) ? 'primary' : 'default'"
                @click="handleCategoryClick(item.value, 2)"
                size="small"
                class="category-btn"
                ghost
              >
                {{ item.label }}
              </n-button>
            </n-space>
          </div>
        </div>

        <!-- 已选分类展示 -->
        <div class="selected-section" v-if="selectedCategories.length">
          <div class="category-title">已选条件:</div>
          <div class="selected-content">
            <n-space align="center" wrap-item>
              <n-tag
                v-for="(categoryId, index) in selectedCategories"
                :key="categoryId"
                :type="'primary'"
                closable
                class="selected-tag"
                @close="removeCategory(index)"
              >
                {{ getCategoryFullPath(categoryId) }}
              </n-tag>
              <n-button text type="primary" @click="clearCategories" class="clear-btn">
                清空全部
              </n-button>
            </n-space>
          </div>
        </div>
      </div>

      <!-- 搜索区域 -->
      <div class="search-area mb-4">
        <n-input-group>
          <n-input
            v-model:value.enter="searchForm.keyword"
            placeholder="支持表格全字段搜索，请输入故障描述/故障原因/故障规则关键词"
            clearable
            style="width: 350px"
          />
          <n-button type="primary" @click="handleSearch">
            <template #icon>
              <n-icon><SearchOutlined /></n-icon>
            </template>
            搜索
          </n-button>
          <n-button @click="resetSearchForm">重置</n-button>
        </n-input-group>

        <!-- 表格操作按钮 -->
        <n-button type="primary" @click="handleAdd">新增场景</n-button>
      </div>

      <!-- 树形表格 -->
      <n-data-table
        remote
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        :scroll-x="2000"
        :bordered="false"
        :single-line="false"
        striped
      />
    </n-card>

    <!-- 新增/编辑弹窗 -->
    <n-modal
      v-model:show="showModal"
      :title="modalTitle"
      preset="dialog"
      :positive-text="modalType === 'add' ? '确认新增' : '确认修改'"
      :negative-text="'取消'"
      @positive-click="handleModalConfirm"
      @negative-click="closeModal"
    >
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="一级分类" path="levelone">
          <n-select
            v-model:value="formData.levelone"
            :options="categoryLevel1Options"
            placeholder="请选择一级分类"
            clearable
            @update:value="handleModalCategoryChange($event, 0)"
          />
        </n-form-item>
        <n-form-item v-if="formData.levelone" label="二级分类" path="leveltwo">
          <n-select
            v-model:value="formData.leveltwo"
            :options="level2Options"
            clearable
            placeholder="请选择二级分类"
            @update:value="handleModalCategoryChange($event, 1)"
          />
        </n-form-item>
        <n-form-item v-if="formData.leveltwo" label="三级分类" path="levelthree">
          <n-select
            v-model:value="formData.levelthree"
            :options="level3Options"
            clearable
            placeholder="请选择三级分类"
          />
        </n-form-item>
        <n-form-item label="故障描述" path="description">
          <n-input
            v-model:value="formData.description"
            type="textarea"
            placeholder="请输入故障描述"
          />
        </n-form-item>
        <n-form-item label="故障原因" path="reasion">
          <n-input v-model:value="formData.reasion" type="textarea" placeholder="请输入故障原因" />
        </n-form-item>
        <n-form-item label="故障规则" path="rule">
          <n-input v-model:value="formData.rule" type="textarea" placeholder="请输入故障规则" />
        </n-form-item>
      </n-form>
    </n-modal>
  </div>
</template>
<script lang="ts">
  export default {
    name: 'TreeList',
  };
</script>
<script lang="ts" setup>
  import { ref, reactive, onMounted, h, computed } from 'vue';
  import { NButton, useDialog, useMessage } from 'naive-ui';
  import { PlusOutlined, SearchOutlined } from '@vicons/antd';
  import {
    getCategoryTreeApi,
    type Category,
    createFaultTree,
    updateFaultTree,
    deleteFaultTree,
    getFaultTreeList,
    searchFaultTreeDetail,
    markFaultTreeUseful,
  } from '@/api/dataview/faultScenes';
  import { UserInfoType, useUserStore } from '@/store/modules/user';
  const message = useMessage();

  // 搜索表单
  const searchForm = reactive({
    keyword: '', // 搜索关键词
  });

  // 表单校验规则
  const rules = {
    levelone: {
      required: true,
      message: '请输入场景名称',
      trigger: 'blur',
    },
    leveltwo: {
      required: true,
      message: '请选择故障类型',
      trigger: ['blur', 'change'],
    },
    levelthree: {
      required: true,
      message: '请选择影响程度',
      trigger: ['blur', 'change'],
    },
  };

  // 表格列定义
  const columns = [
    {
      title: '一级分类',
      key: 'leveloneName',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '二级分类',
      key: 'leveltwoName',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
    },

    {
      title: '三级分类',
      key: 'levelthreeName',
      width: 130,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '故障描述',
      key: 'description',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '故障原因',
      key: 'reasion',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '故障规则',
      key: 'rule',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '作者',
      key: 'author',
      width: 120,
    },
    {
      title: '有用',
      key: 'useful',
      fixed: 'right',
      width: 70,
    },
    {
      title: '操作',
      key: 'actions',
      width: 280,
      fixed: 'right',
      render(row) {
        return h(
          'div',
          {
            style: {
              display: 'flex',
              gap: '8px',
            },
          },
          [
            h(
              NButton,
              {
                strong: true,
                tertiary: true,
                size: 'small',
                onClick: () => handleEdit(row),
              },
              '编辑'
            ),
            h(
              NButton,
              {
                size: 'small',
                type: 'primary',
                strong: true,
                onClick: () => handleDelete(row),
              },
              '删除'
            ),
            h(
              NButton,
              {
                size: 'small',
                type: 'success',
                strong: true,
                onClick: () => handleCopyConclusion(row),
              },
              '复制结论'
            ),
          ]
        );
      },
    },
  ];

  // 分页配置
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 30, 40],
    onChange: (page: number) => {
      pagination.page = page;
      handleSearch();
    },
    onUpdatePageSize: (pageSize: number) => {
      pagination.pageSize = pageSize;
      pagination.page = 1;
      handleSearch();
    },
  });

  // 加载表格数据
  const loading = ref(false);
  const tableData = ref<any>([]);

  async function loadTableData(params = {}) {
    loading.value = true;
    try {
      const res = await getFaultTreeList();
      tableData.value = res;
    } catch (error) {
      message.error('获取数据失败');
    } finally {
      loading.value = false;
    }
  }
  const userStore = useUserStore();
  const userInfo: UserInfoType = userStore.getUserInfo || {};
  // 弹窗相关
  const showModal = ref(false);
  const modalType = ref<'add' | 'edit'>('add');
  const modalTitle = computed(() => (modalType.value === 'add' ? '新增场景' : '编辑场景'));
  const formRef = ref(null);
  const formData = reactive({
    levelone: null,
    leveltwo: null,
    levelthree: null,
    description: '',
    reasion: '',
    rule: '',
    author: '',
  });

  // 重置搜索表单
  function resetSearchForm() {
    searchForm.keyword = '';
    handleSearch();
  }

  // 处理搜索
  async function handleSearch() {
    try {
      loading.value = true;
      // 构建搜索参数
      const searchParams: any = {};

      // 添加分类ID搜索条件
      if (selectedCategories.value.length > 0) {
        const [levelone, leveltwo, levelthree] = selectedCategories.value;
        if (levelone) searchParams.levelone = levelone;
        if (leveltwo) searchParams.leveltwo = leveltwo;
        if (levelthree) searchParams.levelthree = levelthree;
      }

      // 添加关键词搜索
      if (searchForm.keyword) {
        // 关键词搜索应用到description、reasion和rule字段
        searchParams.keyword = searchForm.keyword.trim();
      }

      // 添加分页参数
      searchParams.page = pagination.page;
      searchParams.pageSize = pagination.pageSize;

      const res = await searchFaultTreeDetail(searchParams);
      tableData.value = res.items;
      pagination.itemCount = res.total;
    } catch (error) {
      message.error('搜索失败');
    } finally {
      loading.value = false;
    }
  }

  // 新增场景
  async function handleAdd() {
    modalType.value = 'add';
    Object.assign(formData, {
      levelone: null,
      leveltwo: null,
      levelthree: null,
      description: '',
      reasion: '',
      rule: '',
    });
    showModal.value = true;
  }

  // 编辑场景
  async function handleEdit(row: any) {
    modalType.value = 'edit';
    Object.assign(formData, row);
    showModal.value = true;
  }

  const dialog = useDialog();
  // 删除场景
  async function handleDelete(row: any) {
    dialog.warning({
      title: '确认删除',
      content: '是否确认删除该场景？删除后将无法恢复',
      positiveText: '确认',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          await deleteFaultTree(row._id);
          message.success('删除成功');
          handleSearch();
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  }
  // 确认弹窗
  async function handleModalConfirm() {
    await formRef.value?.validate();
    formData.author = userInfo.account;
    try {
      if (modalType.value === 'add') {
        await createFaultTree(formData);
        message.success('新增成功');
      } else {
        await updateFaultTree(formData);
        message.success('更新成功');
      }
      closeModal();
      handleSearch();
    } catch (error) {
      message.error(modalType.value === 'add' ? '新增失败' : '更新失败');
    }
  }

  // 关闭弹窗
  function closeModal() {
    showModal.value = false;
  }

  onMounted(() => {
    handleSearch();
  });

  // 分类相关的状态
  const categoryData = ref<Category[]>([]); // 原始分类数据
  const selectedCategories = ref<string[]>([]); // 选中的分类ID

  // 计算属性：一级分类选项
  const categoryLevel1Options = computed(() =>
    categoryData.value.map((item) => ({
      label: item.name,
      value: item._id,
    }))
  );

  // 修改 level2Options 计算属性
  const level2Options = computed(() => {
    if (showModal.value) {
      // 弹窗中使用 formData
      if (!formData.levelone) return [];
      const firstCategory = categoryData.value.find((item) => item._id === formData.levelone);
      return (
        firstCategory?.children?.map((item) => ({
          label: item.name,
          value: item._id,
        })) || []
      );
    } else {
      // 导航区域使用 selectedCategories
      if (!selectedCategories.value[0]) return [];
      const firstCategory = categoryData.value.find(
        (item) => item._id === selectedCategories.value[0]
      );
      return (
        firstCategory?.children?.map((item) => ({
          label: item.name,
          value: item._id,
        })) || []
      );
    }
  });

  // 修改 level3Options 计算属性
  const level3Options = computed(() => {
    if (showModal.value) {
      // 弹窗中使用 formData
      if (!formData.levelone || !formData.leveltwo) return [];
      const firstCategory = categoryData.value.find((item) => item._id === formData.levelone);
      const secondCategory = firstCategory?.children?.find(
        (item) => item._id === formData.leveltwo
      );
      return (
        secondCategory?.children?.map((item) => ({
          label: item.name,
          value: item._id,
        })) || []
      );
    } else {
      // 导航区域使用 selectedCategories
      if (!selectedCategories.value[0] || !selectedCategories.value[1]) return [];
      const firstCategory = categoryData.value.find(
        (item) => item._id === selectedCategories.value[0]
      );
      const secondCategory = firstCategory?.children?.find(
        (item) => item._id === selectedCategories.value[1]
      );
      return (
        secondCategory?.children?.map((item) => ({
          label: item.name,
          value: item._id,
        })) || []
      );
    }
  });

  // 获取分类名称
  function getCategoryLabel(categoryId: string): string {
    // 在一级分类中查找
    const level1 = categoryData.value.find((item) => item._id === categoryId);
    if (level1) return level1.name;

    // 在二级分类中查找
    for (const level1 of categoryData.value) {
      const level2 = level1.children?.find((item) => item._id === categoryId);
      if (level2) return level2.name;

      // 在三级分类中查找
      for (const level2 of level1.children || []) {
        const level3 = level2.children?.find((item) => item._id === categoryId);
        if (level3) return level3.name;
      }
    }
    return '';
  }

  // 获取分类层级标题
  function getCategoryTitle(level: number): string {
    if (level === 1 && selectedCategories.value[0]) {
      return getCategoryLabel(selectedCategories.value[0]);
    }
    if (level === 2 && selectedCategories.value[1]) {
      return getCategoryLabel(selectedCategories.value[1]);
    }
    return '';
  }

  // 获取完整的分类路径
  function getCategoryFullPath(categoryId: string): string {
    const path: string[] = [];

    // 查找分类路径
    function findPath(categories: Category[], targetId: string): boolean {
      for (const category of categories) {
        if (category._id === targetId) {
          path.push(category.name);
          return true;
        }
        if (category.children) {
          if (findPath(category.children, targetId)) {
            path.unshift(category.name);
            return true;
          }
        }
      }
      return false;
    }

    findPath(categoryData.value, categoryId);
    return path.join(' / ');
  }

  // 处理分类点击
  function handleCategoryClick(categoryId: string, level: number) {
    if (selectedCategories.value[level] === categoryId) {
      // 如果点击已选中的分类，则取消选择该层级及其后续层级
      selectedCategories.value = selectedCategories.value.slice(0, level);
    } else {
      // 选择新分类，保留之前层级，更新当前层级
      selectedCategories.value = [...selectedCategories.value.slice(0, level), categoryId];
    }
    // 移除条件判断，每次点击都触发搜索
    handleSearch();
  }

  // 移除选中的分类
  function removeCategory(index: number) {
    selectedCategories.value = selectedCategories.value.slice(0, index);
    handleSearch();
  }

  // 清空所有选中的分类
  function clearCategories() {
    selectedCategories.value = [];
    handleSearch();
  }

  // 加载分类数据
  async function loadCategories() {
    try {
      const data = await getCategoryTreeApi();
      categoryData.value = data;
    } catch (error) {
      message.error('获取分类数据失败');
    }
  }

  // 在组件挂载时加载分类数据
  onMounted(() => {
    loadCategories();
    handleSearch();
  });

  // 修改弹窗中的分类选择处理方法
  function handleModalCategoryChange(value: string | null, level: number) {
    if (level === 0) {
      // 选择一级分类时，清空二三级分类
      formData.leveltwo = null;
      formData.levelthree = null;
    } else if (level === 1) {
      // 选择二级分类时，清空三级分类
      formData.levelthree = null;
    }
  }

  // 添加复制结论的处理函数
  async function handleCopyConclusion(row: any) {
    try {
      // 复制内容到剪贴板
      const conclusion = `原因分析：${row.reasion}\n修改建议：${row.rule}`;

      // 创建text area
      let textArea = document.createElement('textarea');
      textArea.value = conclusion;
      // 使text area不在viewport，同时设置不可见
      textArea.style.position = 'absolute';
      textArea.style.opacity = '0';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      return new Promise(async (res, rej) => {
        // 执行复制命令并移除文本框
        document.execCommand('copy') ? res(null) : rej();
        textArea.remove();

        // 调用 useful API
        await markFaultTreeUseful(row._id);

        // 刷新列表
        await handleSearch();

        message.success('复制成功并已标记为有用');
      });
    } catch (error) {
      message.error('操作失败');
    }
  }
</script>

<style lang="less" scoped>
  .n-breadcrumb-item {
    cursor: pointer;
    &:hover {
      color: var(--primary-color);
    }
  }

  .n-tag {
    min-width: 80px;
    text-align: center;

    &:hover {
      opacity: 0.8;
    }
  }

  .category-tags {
    margin: 16px 0;

    .category-group {
      margin-bottom: 8px;

      .category-label {
        margin-right: 8px;
        color: #666;
      }
    }
  }

  .category-nav {
    padding-top: 16px;
    padding-bottom: 16px;
    background-color: #fff;
    border-radius: 4px;

    .category-section {
      display: flex;
      align-items: flex-start;
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .category-title {
      flex-shrink: 0;
      width: 100px;
      padding-top: 4px;
      color: #606266;
      font-weight: 500;
    }

    .category-content {
      flex: 1;

      .category-btn {
        margin-right: 12px;
        margin-bottom: 8px;

        &:last-child {
          margin-right: 0;
        }
      }
    }

    .selected-section {
      margin-top: 24px;
      padding-top: 16px;
      border-top: 1px dashed #e4e7ed;

      .selected-content {
        flex: 1;
      }

      .selected-tag {
        margin-right: 8px;
        margin-bottom: 8px;
        padding: 4px 8px;

        &:hover {
          opacity: 0.9;
        }
      }

      .clear-btn {
        font-size: 13px;

        &:hover {
          color: var(--primary-color-hover);
        }
      }
    }
  }

  // 表格样式优化
  .n-data-table {
    margin-top: 16px;
  }

  // 隐藏原搜索表单
  .hidden {
    display: none;
  }

  .mb-4 {
    margin-bottom: 16px;
  }

  // 添加搜索区域样式
  .search-area {
    display: flex;
    justify-content: center;
    padding: 16px 0;
    border-top: 1px solid #f0f0f0;
  }
</style>
