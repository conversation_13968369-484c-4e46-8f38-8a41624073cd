<template>
  <n-card>
    <template #header>
      <n-space justify="space-between">
        <n-space>
          <n-button type="primary" @click="handleAdd" v-if="isManageRef">
            <template #icon>
              <n-icon>
                <PersonAddOutline />
              </n-icon>
            </template>
            新增人员
          </n-button>
          <n-button @click="handleRefresh">
            <template #icon>
              <n-icon>
                <Refresh />
              </n-icon>
            </template>
            刷新
          </n-button>
          <n-button @click="showGroupModal = true" v-if="isManageRef">
            <template #icon>
              <n-icon>
                <PeopleOutline />
              </n-icon>
            </template>
            管理分组
          </n-button>
          <n-button @click="showTagModal = true" v-if="isManageRef">
            <template #icon>
              <n-icon>
                <PricetagOutline />
              </n-icon>
            </template>
            管理标签
          </n-button>
        </n-space>
        <n-space align="center">
          <n-select
            v-model:value="selectedGroup"
            placeholder="选择分组"
            clearable
            :options="groupOptions"
            style="width: 150px"
            @update:value="handleGroupChange"
          />
          <n-select
            v-model:value="selectedTags"
            placeholder="选择标签"
            clearable
            multiple
            :options="tagOptions"
            style="width: 200px"
            @update:value="handleTagChange"
          />
          <n-input
            v-model:value="searchText"
            placeholder="请输入姓名/工号搜索"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #suffix>
              <n-icon :component="Search" />
            </template>
          </n-input>
        </n-space>
      </n-space>
    </template>

    <n-data-table
      remote
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :pagination="paginationReactive"
      @update:page="handlePageChange"
      @update:page-size="handlePageSizeChange"
    />

    <!-- 新增/编辑弹窗 -->
    <n-modal v-model:show="showModal" :title="modalTitle" preset="card" :style="{ width: '600px' }">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="100"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="姓名" path="name">
          <n-input v-model:value="formData.name" placeholder="请输入姓名" />
        </n-form-item>
        <n-form-item label="工号" path="id">
          <n-input v-model:value="formData.id" placeholder="请输入工号" :disabled="isEditing" />
        </n-form-item>
        <n-form-item label="职位" path="position">
          <n-input v-model:value="formData.position" placeholder="请输入职位" />
        </n-form-item>
        <n-form-item label="分组" path="groupId">
          <n-select
            v-model:value="formData.groupId"
            :options="groupOptions"
            placeholder="请选择分组"
            clearable
          />
        </n-form-item>
        <n-form-item label="标签" path="tags">
          <n-select
            v-model:value="formData.tags"
            :options="tagOptions"
            placeholder="请选择标签"
            multiple
          />
        </n-form-item>
        <n-form-item label="状态" path="status">
          <n-switch v-model:value="formData.status">
            <template #checked>启用</template>
            <template #unchecked>禁用</template>
          </n-switch>
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showModal = false">取消</n-button>
          <n-button type="primary" :loading="submitLoading" @click="handleSubmit">确定</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 分组管理弹窗 -->
    <n-modal
      v-model:show="showGroupModal"
      title="分组管理"
      preset="card"
      :style="{ width: '500px' }"
    >
      <n-space vertical>
        <n-space>
          <n-input v-model:value="newGroupName" placeholder="请输入分组名称" />
          <n-button type="primary" @click="handleAddGroup">添加分组</n-button>
        </n-space>
        <n-data-table :columns="groupColumns" :data="groups" :bordered="false" />
      </n-space>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showGroupModal = false">关闭</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 标签管理弹窗 -->
    <n-modal v-model:show="showTagModal" title="标签管理" preset="card" :style="{ width: '500px' }">
      <n-space vertical>
        <n-space>
          <n-input v-model:value="newTagName" placeholder="请输入标签名称" />
          <n-color-picker
            :style="{ width: '100px' }"
            v-model:value="newTagColor"
            :modes="['hex']"
          />
          <n-button type="primary" @click="handleAddTag">添加标签</n-button>
        </n-space>
        <n-data-table :columns="tagColumns" :data="tags" :bordered="false" />
      </n-space>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showTagModal = false">关闭</n-button>
        </n-space>
      </template>
    </n-modal>

    <n-modal v-model:show="showEditGroupModal" preset="card" title="编辑分组" style="width: 400px">
      <n-form
        :model="editingGroup"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="分组名" path="groupName">
          <n-input v-model:value="editingGroup.groupName" />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showEditGroupModal = false">取消</n-button>
          <n-button type="primary" @click="saveEditGroup">保存</n-button>
        </n-space>
      </template>
    </n-modal>

    <n-modal v-model:show="showEditTagModal" preset="card" title="编辑标签" style="width: 400px">
      <n-form
        :model="editingTag"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="标签名称" path="tagName">
          <n-input v-model:value="editingTag.name" />
        </n-form-item>
        <n-form-item label="标签颜色" path="tagColor">
          <n-color-picker
            :style="{ width: '100px' }"
            v-model:value="editingTag.color"
            :modes="['hex']"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showEditTagModal = false">取消</n-button>
          <n-button type="primary" @click="saveEditTag">保存</n-button>
        </n-space>
      </template>
    </n-modal>
  </n-card>
</template>

<script setup lang="ts">
  import { h, ref, onMounted, computed, reactive } from 'vue';
  import {
    useMessage,
    useDialog,
    type PaginationProps,
    NInput,
    NModal,
    NSelect,
    NFormItem,
    NSpace,
    NButton,
    NForm,
  } from 'naive-ui';
  import {
    Search,
    PersonAddOutline,
    Refresh,
    PeopleOutline,
    PricetagOutline,
  } from '@vicons/ionicons5';

  import { getMaxIntNumber } from '@/utils/commonUtils';

  import {
    addGroup,
    addStaff,
    addTag,
    deleteGroup,
    deleteStaff,
    deleteTag,
    getGroups,
    getStaffList,
    getTag,
  } from './staff';

  import { useUserStore } from '@/store/modules/user';
  import { getPersonNamesFromTag } from '@/views/dataview/personManage/staffCommonUtils';
  import { MANAGE_TAG } from '@/views/dataview/personManage/tagContant';

  //人员权限相关
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo || {};
  const isManageRef = ref(false);
  //判断人员是否为管理员
  const judgeManagePerson = async () => {
    const list = await getPersonNamesFromTag(MANAGE_TAG);
    isManageRef.value = list.includes(userInfo.userName);
  };

  const isEditing = ref(false);

  const message = useMessage();
  const dialog = useDialog();

  // 表格相关
  const loading = ref(false);
  const tableData = ref<any[]>([]);
  const paginationReactive = reactive<PaginationProps>({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 50, 100],
    prefix({ itemCount }) {
      return `Total：${itemCount}`;
    },
    onChange: (page: number) => {
      paginationReactive.page = page;
      fetchData();
    },
    onUpdatePageSize: (pageSize: number) => {
      paginationReactive.pageSize = pageSize;
      paginationReactive.page = 1;
      fetchData();
    },
  });

  // 分组相关
  const groups = ref<{ id: number; name: string }[]>([]);
  const newGroupName = ref('');
  const selectedGroup = ref(null);

  const groupOptions = computed(() => {
    return groups.value.map((group) => ({
      label: group.name,
      value: group.id,
    }));
  });

  const groupColumns = [
    {
      title: '分组名称',
      key: 'name',
    },
    {
      title: '操作',
      key: 'actions',
      render(row) {
        return h('div', [
          h(
            'a',
            {
              style: {
                marginRight: '10px',
                cursor: 'pointer',
              },
              onClick: () => handleEditGroup(row),
            },
            '编辑'
          ),
          h(
            'a',
            {
              style: {
                color: '#d03050',
                cursor: 'pointer',
              },
              onClick: () => handleDeleteGroup(row),
            },
            '删除'
          ),
        ]);
      },
    },
  ];

  // 分组管理方法
  const handleAddGroup = async () => {
    try {
      if (!newGroupName.value.trim()) {
        message.warning('分组名称不能为空');
        return;
      }
      const param = { name: newGroupName.value };
      await addGroup(param);
      newGroupName.value = '';
      message.success('添加分组成功');
      await fetchGroups();
    } catch (error) {
      message.error('添加分组失败');
    }
  };

  const handleEditGroup = (row) => {
    showEditGroupModal.value = true;
    editingGroup.value.groupName = row.name;
    editingGroup.value.id = row.id;
  };

  const saveEditGroup = async () => {
    try {
      const param = {
        id: editingGroup.value.id,
        name: editingGroup.value.groupName,
      };
      await addGroup(param);
      await fetchGroups();
      message.success('修改成功');
      await fetchData();
      showEditGroupModal.value = false;
    } catch (error) {
      message.error('修改失败');
    }
  };

  // 标签相关
  const tags = ref<{ id: number; name: string; color: string }[]>([]);
  const newTagName = ref('');
  const newTagColor = ref('#2080f0');
  const selectedTags = ref<number[]>([]);
  const showTagModal = ref(false);
  const showGroupModal = ref(false);

  const tagOptions = computed(() => {
    return tags.value.map((tag) => ({
      label: tag.name,
      value: tag.id,
      style: {
        color: tag.color,
      },
    }));
  });

  const tagColumns = [
    {
      title: '标签名称',
      key: 'name',
      render(row) {
        return h(
          'span',
          {
            style: {
              color: row.color,
              fontWeight: 'bold',
            },
          },
          row.name
        );
      },
    },
    {
      title: '颜色',
      key: 'color',
      render(row) {
        return h('div', {
          style: {
            width: '20px',
            height: '20px',
            borderRadius: '4px',
            backgroundColor: row.color,
          },
        });
      },
    },
    {
      title: '操作',
      key: 'actions',
      render(row) {
        return h('div', [
          h(
            'a',
            {
              style: {
                marginRight: '10px',
                cursor: 'pointer',
              },
              onClick: () => handleEditTag(row),
            },
            '编辑'
          ),
          h(
            'a',
            {
              style: {
                color: '#d03050',
                cursor: 'pointer',
              },
              onClick: () => handleDeleteTag(row),
            },
            '删除'
          ),
        ]);
      },
    },
  ];

  const columns = ref([]);

  const createColumns = (appName: string) => {
    // 表格列定义
    const columnOrigin = [
      {
        title: '姓名',
        key: 'name',
        show: true,
      },
      {
        title: '工号',
        key: 'id',
        show: true,
      },
      {
        title: '分组',
        key: 'staffGroupId',
        show: true,
        render(row) {
          if (!row.staffGroupId) return '未分组';
          const group = groups.value.find((g) => g.id === row.staffGroupId);
          return group ? group.name : '未分组';
        },
      },
      {
        title: '标签',
        key: 'tags',
        show: true,
        render(row) {
          if (!row.tags || row.tags.length === 0) return '无';

          return h(
            'div',
            {
              style: {
                display: 'flex',
                gap: '4px',
                flexWrap: 'wrap',
              },
            },
            row.tags.map((tag) => {
              // const tag = tags.value.find((t) => t.id === tagId);
              if (!tag) return null;

              return h(
                'div',
                {
                  style: {
                    padding: '2px 6px',
                    borderRadius: '4px',
                    fontSize: '12px',
                    color: tag.color,
                    border: `1px solid ${tag.color}`,
                    whiteSpace: 'nowrap',
                  },
                },
                tag.name
              );
            })
          );
        },
      },
      {
        title: '状态',
        key: 'status',
        show: true,
        render(row) {
          return h(
            'div',
            {
              style: {
                color: row.status === '启用' ? '#18a058' : '#d03050',
              },
            },
            row.status
          );
        },
      },
      {
        title: '操作',
        key: 'actions',
        show: isManageRef.value,
        render(row) {
          return h('div', [
            h(
              'a',
              {
                style: {
                  marginRight: '10px',
                  cursor: 'pointer',
                },
                onClick: () => handleEdit(row),
              },
              '编辑'
            ),
            h(
              'a',
              {
                style: {
                  color: '#d03050',
                  marginRight: '10px',
                  cursor: 'pointer',
                },
                onClick: () => handleDelete(row),
              },
              '删除'
            ),
          ]);
        },
      },
    ];
    columns.value = columnOrigin.filter((item) => item.show);
  };

  // 表单相关
  const showModal = ref(false);
  const modalTitle = ref('新增人员');
  const formRef = ref(null);
  const submitLoading = ref(false);

  const formData = ref({
    id: null,
    name: '',
    position: '',
    status: true,
    groupId: null,
    tags: [] as number[],
  });

  // 表单校验规则
  const rules = {
    name: {
      required: true,
      message: '请输入姓名',
      trigger: ['blur', 'input'],
    },
    id: {
      required: true,
      message: '请输入工号',
      trigger: ['blur', 'input'],
    },
    groupId: {
      type: 'number',
      required: true,
      message: '请输入分组',
      trigger: ['blur', 'change'],
    },
  };

  // 搜索相关
  const searchText = ref('');

  // 方法定义
  const fetchData = async () => {
    loading.value = true;
    try {
      // TODO: 调用后端API获取数据
      const res = await getStaffList({
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
        nameOrEmployeeId: searchText.value,
        staffGroupId: selectedGroup.value ? selectedGroup.value : null,
        tagIds: selectedTags.value.join(','),
      });
      tableData.value = res.records;
      paginationReactive.itemCount = res.total;
    } catch (error) {
      message.error('获取数据失败');
    } finally {
      loading.value = false;
    }
  };

  //查询标签
  const fetchTags = async () => {
    try {
      // TODO: 调用后端API获取数据
      const res = await getTag({
        pageNo: 1,
        pageSize: getMaxIntNumber(),
      });
      tags.value = res.records;
    } catch (error) {
      message.error('获取数据失败');
    } finally {
      loading.value = false;
    }
  };

  //查询组
  const fetchGroups = async () => {
    try {
      // TODO: 调用后端API获取数据
      const res = await getGroups({
        pageNo: 1,
        pageSize: getMaxIntNumber(),
      });
      groups.value = res.records;
    } catch (error) {
      message.error('获取数据失败');
    } finally {
      loading.value = false;
    }
  };

  const handlePageChange = (page: number) => {
    paginationReactive.page = page;
    fetchData();
  };

  const handlePageSizeChange = (pageSize: number) => {
    paginationReactive.pageSize = pageSize;
    paginationReactive.page = 1;
    fetchData();
  };

  const handleSearch = () => {
    paginationReactive.page = 1;
    fetchData();
  };

  const handleRefresh = () => {
    searchText.value = '';
    selectedGroup.value = null;
    selectedTags.value = [];
    fetchData();
  };

  const handleGroupChange = () => {
    paginationReactive.page = 1;
    fetchData();
  };

  const handleTagChange = () => {
    paginationReactive.page = 1;
    fetchData();
  };

  const resetForm = () => {
    formData.value = {
      id: null,
      name: '',
      position: '',
      status: true,
      groupId: null,
      tags: [],
    };
  };

  const handleAdd = () => {
    modalTitle.value = '新增人员';
    resetForm();
    isEditing.value = false;
    showModal.value = true;
  };

  const handleEdit = (row) => {
    modalTitle.value = '编辑人员';
    isEditing.value = true;
    const tags = row.tags ? row.tags.map((item) => item.id) : null;
    const group = groups.value.find((g) => g.id === row.staffGroupId);
    formData.value = {
      id: row.id,
      name: row.name,
      position: row.job,
      status: row.status === '启用',
      groupId: group ? group.id : null,
      tags,
    };
    // formData.value = {...row};
    showModal.value = true;
  };

  const handleDelete = async (row) => {
    try {
      await dialog.warning({
        title: '确认删除',
        content: '确定要删除该人员吗？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: async () => {
          // TODO: 调用后端API删除数据
          await deleteStaff(row.id);
          message.success('删除成功');
          fetchData();
        },
      });
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleSubmit = async () => {
    try {
      await formRef.value?.validate();
      submitLoading.value = true;
      const param = {
        id: formData.value.id,
        name: formData.value.name,
        job: formData.value.position,
        staffGroupId: formData.value.groupId,
        tagIds: formData.value.tags?.join(','),
        status: formData.value.status ? '启用' : '禁用',
      };
      await addStaff(param);
      message.success(formData.value.id ? '修改成功' : '新增成功');
      showModal.value = false;
      await fetchData();
    } catch (error) {
      console.error('表单校验失败:', error);
      message.error(formData.value.id ? '修改失败' : '新增失败');
    } finally {
      submitLoading.value = false;
    }
  };

  const handleDeleteGroup = (row) => {
    dialog.warning({
      title: '确认删除',
      content: '确定要删除该分组吗？删除后，属于该分组的人员将变为未分组状态。',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          const index = groups.value.findIndex((g) => g.id === row.id);
          if (index !== -1) {
            await deleteGroup(row.id);
            await fetchGroups();
            // 更新已分配该分组的人员
            tableData.value.forEach((person) => {
              if (person.groupId === row.id) {
                person.groupId = null;
              }
            });
            message.success('删除分组成功');
          }
        } catch (error) {
          message.error('删除分组失败');
        }
      },
    });
  };

  // 标签管理方法
  const handleAddTag = async () => {
    try {
      if (!newTagName.value.trim()) {
        message.warning('标签名称不能为空');
        return;
      }
      const param = {
        name: newTagName.value.trim(),
        color: newTagColor.value,
      };
      await addTag(param);
      newTagName.value = '';
      await fetchTags();
      await fetchData();
      message.success('添加标签成功');
    } catch (e) {
      message.error('添加标签失败');
    }
  };

  const handleEditTag = (row) => {
    showEditTagModal.value = true;
    editingTag.value.name = row.name;
    editingTag.value.color = row.color;
    editingTag.value.id = row.id;
  };

  const handleDeleteTag = (row) => {
    dialog.warning({
      title: '确认删除',
      content: '确定要删除该标签吗？删除后，已添加该标签的人员将移除此标签。',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          await deleteTag(row.id);
          await fetchTags();
          await fetchData();
          message.success('删除标签成功');
        } catch (error) {
          message.error('删除标签失败');
        }
      },
    });
  };
  //修改弹窗
  const showEditTagModal = ref(false);
  const showEditGroupModal = ref(false);
  const editingGroup = ref({});
  const editingTag = ref({});
  const saveEditTag = async () => {
    try {
      const param = {
        id: editingTag.value.id,
        name: editingTag.value.name,
        color: editingTag.value.color,
      };
      await addTag(param);
      await fetchTags();
      await fetchData();
      message.success('修改成功');
      showEditTagModal.value = false;
    } catch (error) {
      message.success('修改失败');
    }
  };
  onMounted(() => {
    judgeManagePerson().then(() => {
      createColumns();
    });
    fetchTags();
    fetchGroups();
    fetchData();
  });
</script>

<style scoped>
  .n-card {
    margin: 16px;
  }
</style>
