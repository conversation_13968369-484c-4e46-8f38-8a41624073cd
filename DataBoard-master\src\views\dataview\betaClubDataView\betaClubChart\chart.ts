import { ewpService as service } from '@/utils/axios';

const stateMap = {
  unhandled: '待处理',
  dtsTrackingNum: 'DTS跟踪',
  finished: '已闭环',
  pendingNum: '已挂起',
};

export const getData = async () => {
  return service.post('/management/betaClubOrder/overview', { afterDayCnt: '15' });
};

/**
 * 把 rowData 整理成 echarts 需要的格式
 * @param rowData
 */
export const getEchartOptions = (rowData) => {
  const { series, xAxisData } = getSeriesAndXAxis(rowData);
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
      },
    },
    legend: {
      data: Object.values(stateMap),
      selectedMode: true,
      top: 10,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '15%',
      containLabel: true,
    },
    yAxis: {
      type: 'value',
      minInterval: 1,
      splitLine: {
        lineStyle: {
          type: 'dashed',
        },
      },
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      boundaryGap: false,
    },
    series,
  };
};

const getSeriesAndXAxis = (rawData) => {
  const series: echarts.LineSeriesOption[] = [];
  const xAxisData: string[] = [];
  const dateKeys = Object.keys(rawData).sort(
    (a, b) => new Date(a).getTime() - new Date(b).getTime()
  );

  // 初始化所有系列
  Object.entries(stateMap).forEach(([key, name], index) => {
    series[index] = {
      name,
      type: 'line',
      data: [],
      symbol: 'circle',
      symbolSize: 6,
      label: {
        show: true,
        position: 'top',
      },
      lineStyle: {
        width: 2,
      },
      connectNulls: true, // 连接空数据点
    };
  });

  // 填充数据
  dateKeys.forEach((date) => {
    xAxisData.push(date.slice(5));
    Object.entries(stateMap).forEach(([key, name], index) => {
      const value = rawData[date][key] || 0;
      series[index].data!.push(value);
    });
  });

  return { series, xAxisData };
};
