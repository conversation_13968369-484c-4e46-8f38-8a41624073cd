import service from '@/utils/axios';

const baseUrl = '/industry_base';
// 查询
export const queryImported = (data) => {
  return service({
    url: '/industry_base/queryImported',
    method: 'post',
    data,
  });
};
// 导入
export const importIndustry = (data) => {
  return service({
    url: `${baseUrl}/importExcel`,
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};
// 批量删除
export const delIndustryList = (data) => {
  return service({
    url: `${baseUrl}/deleteByIndustry`,
    method: 'DELETE',
    data,
  });
};
export const exportData = (params) => {
  return service({
    url: `/industry_base/export`,
    method: 'get',
    params,
    responseType :'blob'
  });
};