import { h } from 'vue';
import { NAvatar, NTag } from 'naive-ui';
import { BasicColumn } from '@/components/Table';
export interface ListData {
  id: number;
  name: string;
  sex: string;
  avatar: string;
  email: string;
  city: string;
  status: string;
  type: string;
  createDate: string;
}

const sexMap = {
  male: '男',
  female: '女',
  unknown: '未知',
};

const statusMap = {
  close: '已取消',
  refuse: '已拒绝',
  pass: '已通过',
};

export const columns: BasicColumn<ListData>[] = [
  {
    title: 'id',
    key: 'id',
  },
  {
    title: '名称',
    key: 'name',
    editComponent: 'NInput',
    // 默认必填校验
    editRule: true,
    edit: true,
  },
  {
    title: '头像',
    key: 'avatar',
    render(record) {
      return h(NAvatar, {
        size: 50,
        src: record.avatar,
      });
    },
  },
  {
    title: '性别',
    key: 'sex',
    render(record) {
      return h(
        NTag,
        {
          type: record.sex === 'male' ? 'info' : 'error',
        },
        {
          default: () => sexMap[record.sex],
        }
      );
    },
  },
  {
    title: '邮箱',
    key: 'email',
    width: 220,
  },
  {
    title: '城市',
    key: 'city',
    editComponent: 'NSelect',
    editComponentProps: {
      options: [
        {
          label: '深圳市',
          value: 1,
        },
        {
          label: '广州市',
          value: 2,
        },
      ],
    },
    edit: true,
    width: 220,
  },
  {
    title: '状态',
    key: 'status',
    render(record) {
      return h(
        NTag,
        {
          type:
            record.status === 'close'
              ? 'default'
              : record.status === 'refuse'
              ? 'error'
              : 'success',
        },
        {
          default: () => statusMap[record.status],
        }
      );
    },
  },
  {
    title: '创建时间',
    key: 'createDate',
  },
];
