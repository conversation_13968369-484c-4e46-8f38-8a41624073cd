import {h, ref} from 'vue';
import {formatDateTime} from '@/utils';
import {allUserType} from '@/api/feedback';

interface ComponentStyle {
  width: string;
  textAlign: string;
}

interface ComponentProps {
  style: ComponentStyle;
  clearable?: boolean;
  placeholder?: string;
  options?: Array<{ label: string; value: any }>;
  filterable?: boolean;
}

interface SearchFormItem {
  field: string;
  label: string;
  component: 'Input' | 'Select' | 'DateRangePicker';
  componentProps?: ComponentProps;
}

// 修改 createColumns 的返回类型
export const createColumns = (handleAppNameClick?: (row: any) => void): any => {
  const baseColumns = [
    {
      title: '应用名称',
      key: 'appName',
      resizable: true,
      width: 120,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '发布时间',
      key: 'publishDate',
      resizable: true,
      width: 120,
      render(row) {
        return formatDateTime(row.publishDate);
      },
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '标题',
      key: 'title',
      resizable: true,
      width: 120,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '摘要',
      key: 'summary',
      resizable: true,
      width: 200,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '文章地址',
      key: 'articleUrl',
      resizable: true,
      width: 200,
      ellipsis: {
        tooltip: true,
      },
      render: (row) => {
        return h(
          'a',
          {
            href: '#',
            onClick: (e) => {
              const { articleUrl } = row;
              window.open(
                articleUrl,
                '_blank'
              );
            },
            style: {
              color: '#1288ff',
              textDecoration: 'none',
              cursor: 'pointer',
            },
          },
          row.articleUrl
        );
      },
    },
    {
      title: '细分问题',
      key: 'subdivideProblem',
      resizable: true,
      width: 200,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '问题原因',
      key: 'reason',
      resizable: true,
      width: 200,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '优化计划',
      key: 'plan',
      resizable: true,
      width: 200,
      ellipsis: {
        tooltip: true,
      },

    },
    {
      title: '问题分类',
      key: 'kind',
      resizable: true,
      width: 200,
      ellipsis: {
        tooltip: true,
      },

    }
  ];

  return baseColumns;
};

// 创建一个通用的样式对象
const commonStyle: ComponentProps = {
  style: {
    width: '300px',
    textAlign: 'left',
  },
  clearable: true,
  placeholder: '请输入',
};

// 为 Select 组件创建特定样式
const selectStyle: ComponentProps = {
  ...commonStyle,
  style: {
    ...commonStyle.style,
  },
  filterable: true,
  placeholder: '请选择',
};

const allUserTypeOptions = ref<Array<{ label: string; value: string }>>([]);
const getUserIssueType = async () => {
  try {
    const response = await allUserType();
    if (response && Array.isArray(response)) {
      allUserTypeOptions.value = response.map((item) => ({
        label: item,
        value: item,
      }));
    } else {
      allUserTypeOptions.value = [];
    }
  } catch (error) {
    allUserTypeOptions.value = [];
  }
};

export const getSearchFormItems = (): SearchFormItem[] => [
  {
    field: 'appName',
    label: '应用名称',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'summary',
    label: '摘要',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'kind',
    label: '问题分类',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: [
        { label: '未上架', value: '未上架' },
        { label: '功能缺失', value: '功能缺失' },
        { label: '功能bug', value: '功能bug' },
        { label: '性能', value: '性能' },
        { label: '稳定性', value: '稳定性' },
        { label: '适配问题', value: '适配问题' },
      ],
    },
  }
];
