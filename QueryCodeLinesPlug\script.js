window.onload = async () => {
  const src = chrome.runtime.getURL("./settings.js");
  const { SETTINGS } = await import(src);
  const accountsList = SETTINGS.accounts
    .replaceAll(/[^a-zA-Z\d]+/g, ",")
    .split(",")
    .filter((item) => item);
  const startDate = SETTINGS.startDate;
  const endDate = SETTINGS.endDate;

  chrome.runtime.sendMessage(
    {
      action: "getCookie",
      domain: ".rnd.huawei.com",
    },
    (res) => {
      customDownload(res.csrfToken);
    }
  );

  let customDownload = async (csrfToken) => {
    try {
      function exportToExcel(jsonData, filename) {
        let workbook = XLSX.utils.book_new();
        let worksheet = XLSX.utils.json_to_sheet(jsonData);
        var colInfo = [
          { wch: 20 },
          {
            style: {
              alignment: {
                wrapText: true,
              },
            },
            wch: 100,
          },
          { wch: 20 },
        ];
        worksheet["!cols"] = colInfo;

        XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

        let wbout = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
        let blob = new Blob([wbout], { type: "application/octet-stream" });
        let a = document.createElement("a");
        a.download = filename + ".xlsx";
        a.href = URL.createObjectURL(blob);
        a.click();
      }
      let customRequest = async (user) => {
        return fetch(
          `https://scmt.rnd.huawei.com/CompassWeb/v1/compass/v1/person/commits?userName=${user}&startDate=${startDate}&endDate=${endDate}&type=code&pageNo=1&pageSize=3000&_=${Date.now()}`,
          {
            method: "get",
            credentials: "include",
            headers: {
              "content-type": "application/json;charset=UTF-8",
              accept: "application/json, text/plain, */*",
              cftk: csrfToken,
              "x-language": "en",
              "x-requested-with": "XMLHttpRequest",
            },
            body: null,
          }
        );
      };
      let requestList = accountsList.map(async (item) => {
        return (await customRequest(item)).json();
      });
      let res = await Promise.all(requestList);
      let jsonData = res.map((item, index) => {
        let account = accountsList[index];
        let list = item?.result?.list || [];
        let projectLinesMap = {};
        list.forEach((item) => {
          if (projectLinesMap[item.repName]) {
            projectLinesMap[item.repName] =
              projectLinesMap[item.repName] + item?.commitSize || 0;
          } else {
            projectLinesMap[item.repName] = item?.commitSize || 0;
          }
        });
        let lines = item?.result?.statistics?.allCommitSize || 0;
        let projects = Object.keys(projectLinesMap).map((item) => {
          return `${item}: ${projectLinesMap[item]}`;
        });
        let projectLines = Object.keys(projectLinesMap)?.length
          ? projects.join(";\n")
          : "";
        return {
          工号: account,
          按项目统计: projectLines,
          总代码量: lines,
        };
      });
      exportToExcel(jsonData, `代码量统计：${startDate}-${endDate}`);
    } catch (e) {
      console.log(e);
    }
  };
};
