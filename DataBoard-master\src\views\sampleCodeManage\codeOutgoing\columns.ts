import { DataTableColumns, NButton } from "naive-ui";
import { CodeDemoDto, OutgoingRecordsDto } from "@/api/system/code";
import { h } from "vue";
import {
  getAllStatusOptions,
  getIndustryLabel,
  getSendTypeLabel,
} from "@/views/sampleCodeManage/codeOutgoing/index";
import {
  CODE_REVIEW_STATUS_MAP,
} from "./consts";
import { ALL_API_VERSION, ALL_DEMO_TYPES, getFilterOptions } from "@/views/sampleCodeManage/consts";

export const CODE_DEMO_COLUMNS: DataTableColumns<CodeDemoDto> = [
  {
    title: '名称/ID',
    key: 'demoName',
    width: '150px',
    resizable: true,
    minWidth: '100px',
    ellipsis: {
      tooltip: true,
    },
    render(row: CodeDemoDto) {
      return [
        h(
          NButton,
          {
            type: 'info',
            size: 'medium',
            text: true,
            tag: 'a',
            href: row.demoLink,
            target: 'blank',
          },
          [h('div', row.demoName)]
        ),
        h(
          'div',
          {},
          [h('span', row.demoId)]
        )
      ]
    },
  },
  {
    title: '描述',
    key: 'demoDescription',
    width: '150px',
    resizable: true,
    minWidth: '100px',
    ellipsis: {
      tooltip: true,
      lineClamp: 2,
    },
  },
  {
    title: 'API版本',
    key: 'apiVersion',
    width: '100px',
    resizable: true,
    minWidth: '50px',
    ellipsis: {
      tooltip: true
    },
    filterMultiple: true,
    filterOptions: getFilterOptions(ALL_API_VERSION),
    filter(value, row) {
      return true;
    }
  },
  {
    title: '分类',
    key: 'selectValue',
    width: '100px',
    resizable: true,
    minWidth: '50px',
    ellipsis: {
      tooltip: true
    },
    filterMultiple: true,
    filterOptions: getFilterOptions(ALL_DEMO_TYPES),
    filter(value, row) {
      return true;
    }
  },
  {
    title: '提交人',
    key: 'demoSubmitter',
    width: '150px',
    resizable: true,
    minWidth: '50px',
    ellipsis: {
      tooltip: true
    },
  },
  {
    title: '行数',
    key: 'lineNumber',
    width: '80px',
    resizable: true,
    minWidth: '50px',
    ellipsis: {
      tooltip: true
    },
  },
  {
    title: '状态',
    key: 'reviewStatus',
    width: '100px',
    resizable: true,
    minWidth: '50px',
    render(row: CodeDemoDto) {
      return CODE_REVIEW_STATUS_MAP[row.reviewStatus!];
    },
    filterMultiple: true,
    filterOptions: getAllStatusOptions(),
    filter(value, row) {
      return true;
    }
  }
];
export const OUTGOING_RECORDS_COLUMNS: DataTableColumns<OutgoingRecordsDto> = [
  {
    title: '应用名称',
    key: 'applicationName',
    width: '10%',
    ellipsis: {
      tooltip: true
    },
  },
  {
    title: '行业',
    key: 'industry',
    width: '10%',
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      return getIndustryLabel(row.industry);
    }
  },
  {
    title: '发送模式',
    key: 'sendMode',
    width: '10%',
    render(row) {
      return getSendTypeLabel(row.sendMode);
    }
  },
  {
    title: '发送人',
    key: 'demoSender',
    width: '10%',
    ellipsis: {
      tooltip: true
    },
  },
  {
    title: '发送邮箱',
    key: 'senderEmail',
    width: '10%',
    ellipsis: {
      tooltip: true
    },
  },
  {
    title: '发送时间',
    key: 'sendTime',
    width: '10%',
    ellipsis: {
      tooltip: true
    },
  },
  {
    title: '接收邮箱',
    key: 'receivedEmail',
    width: '10%',
    ellipsis: {
      tooltip: true
    },
  },
  {
    title: '代码行数',
    key: 'codeLines',
    width: '10%',
    ellipsis: {
      tooltip: true
    },
  }
];
