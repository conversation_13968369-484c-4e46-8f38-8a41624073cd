<template>
  <div class="app-container">
    <!-- Add statistics cards after filter container -->
    <div class="statistics-container">
      <n-grid :cols="6" :x-gap="12" :y-gap="12">
        <n-grid-item>
          <n-card size="small" class="stat-card">
            <div class="stat-content">
              <div class="stat-label">待分配</div>
              <div class="stat-value">{{ knowledgeStats.preDistributeNum }}</div>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card size="small" class="stat-card">
            <div class="stat-content">
              <div class="stat-label">待输出</div>
              <div class="stat-value">{{ knowledgeStats.preWriteNum }}</div>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card size="small" class="stat-card">
            <div class="stat-content">
              <div class="stat-label">待修改</div>
              <div class="stat-value">{{ knowledgeStats.preModifyNum }}</div>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card size="small" class="stat-card">
            <div class="stat-content">
              <div class="stat-label">已上线</div>
              <div class="stat-value">{{ knowledgeStats.onlineNum }}</div>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card size="small" class="stat-card">
            <div class="stat-content">
              <div class="stat-label">待下线</div>
              <div class="stat-value">{{ knowledgeStats.preOfflineNum }}</div>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card size="small" class="stat-card">
            <div class="stat-content">
              <div class="stat-label">已下线</div>
              <div class="stat-value">{{ knowledgeStats.offlineNum }}</div>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card size="small" class="stat-card">
            <div class="stat-content">
              <div class="stat-label">应用缺失</div>
              <div class="stat-value">{{ knowledgeTypes.appLackTypeNum }}</div>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card size="small" class="stat-card">
            <div class="stat-content">
              <div class="stat-label">功能缺失</div>
              <div class="stat-value">{{ knowledgeTypes.functionLackTypeNum }}</div>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card size="small" class="stat-card">
            <div class="stat-content">
              <div class="stat-label">功能故障</div>
              <div class="stat-value">{{ knowledgeTypes.functionFailureTypeNum }}</div>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card size="small" class="stat-card">
            <div class="stat-content">
              <div class="stat-label">特殊场景</div>
              <div class="stat-value">{{ knowledgeTypes.specialSceneTypeNum }}</div>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item>
          <n-card size="small" class="stat-card">
            <div class="stat-content">
              <div class="stat-label">应用全场景</div>
              <div class="stat-value">{{ knowledgeTypes.appAllSceneTypeNum }}</div>
            </div>
          </n-card>
        </n-grid-item>
        <n-grid-item />
      </n-grid>
    </div>
    <div class="filter-container">
      <n-card>
        <n-form
          :model="searchForm"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          @submit.prevent="handleSearch"
        >
          <n-grid :cols="24" :x-gap="24">
            <n-grid-item v-for="item in visibleSearchFormItems" :key="item.field" :span="6">
              <n-form-item :label="item.label" :path="item.field">
                <n-input
                  clearable
                  v-if="item.component === 'Input'"
                  v-model:value="searchForm[item.field]"
                  @keyup.enter="handleSearch"
                />
                <n-select
                  clearable
                  v-else-if="item.component === 'Select'"
                  v-model:value="searchForm[item.field]"
                  :filterable="item.componentProps.filterable"
                  :options="item.componentProps.options"
                  :multiple="
                    item.field === 'opinionLevel' ||
                    item.field === 'opinionStatus' ||
                    item.field === 'type' ||
                    item.field === 'status' ||
                    item.field === 'dtsStatus'
                  "
                />
                <n-date-picker
                  v-else-if="item.component === 'DateRangePicker'"
                  v-model:value="searchForm[item.field]"
                  type="daterange"
                  clearable
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>
          <div class="form-actions">
            <n-space>
              <n-button @click="resetForm">重置</n-button>
              <n-button type="primary" attr-type="submit">查询</n-button>
              <n-button @click="toggleExpandForm">
                {{ isExpanded ? '收起' : '展开' }}
                <template #icon>
                  <n-icon>
                    <chevron-down v-if="!isExpanded" />
                    <chevron-up v-else />
                  </n-icon>
                </template>
              </n-button>
            </n-space>
          </div>
        </n-form>
      </n-card>
    </div>

    <n-card style="margin-top: 24px">
      <n-space align="center" style="margin-bottom: 16px">
        <n-button type="primary" @click="showAddModal = true">新增</n-button>
      </n-space>
      <n-data-table
        remote
        :bordered="false"
        :single-line="false"
        striped
        @update:sorter="handleSorterChange"
        @update:filters="handleFiltersChange"
        :columns="columns"
        :data="tableData"
        :pagination="paginationReactive"
        :loading="loading"
        :scroll-x="3500"
        :row-key="(row) => row.id"
        :checked-row-keys="checkedRowKeys"
        @update:checked-row-keys="handleCheckedRowKeysChange"
      />
    </n-card>

    <n-modal v-model:show="showAddModal" preset="card" title="新增知识" style="width: 600px">
      <n-form
        ref="formRef"
        :model="newKnow"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="应用名称" path="appName">
          <n-input v-model:value="newKnow.appName" @change="selectUsername" />
        </n-form-item>
        <n-form-item label="DTS单号" path="dtsId">
          <n-input v-model:value="newKnow.dtsId" />
        </n-form-item>
        <n-form-item label="问题描述" path="desc">
          <n-input v-model:value="newKnow.desc" />
        </n-form-item>
        <n-form-item label="应用责任人" path="ewpOwner">
          <n-select
            filterable
            v-model:value="newKnow.ewpOwner"
            :options="addAccountOptions"
          />
        </n-form-item>
        <n-form-item label="知识类型" path="type">
          <n-select
            v-model:value="newKnow.type"
            :options="
              searchFormItems.find((item) => item.field === 'type')?.componentProps.options
            "
          />
        </n-form-item>
        <n-form-item label="计划解决时间" path="weKnowSolvePlanTime">
          <n-input v-model:value="newKnow.weKnowSolvePlanTime" />
        </n-form-item>
        <n-form-item label="规避方案" path="tempResolveScheme">
          <n-input v-model:value="newKnow.tempResolveScheme" type="textarea" />
        </n-form-item>
        <n-form-item label="反馈口径" path="feedbackWay">
          <n-input v-model:value="newKnow.feedbackWay" type="textarea" />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showAddModal = false">取消</n-button>
          <n-button type="primary" @click="saveKnowledge">确认</n-button>
        </n-space>
      </template>
    </n-modal>

    <n-modal v-model:show="showEditModal" preset="card" title="编辑工单" style="width: 600px">
      <n-form
        :model="editingWorkOrder"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="知识类型" path="type">
          <n-select
            v-model:value="editingWorkOrder.type"
            :options="searchFormItems.find((item) => item.field === 'type')?.componentProps.options"
          />
        </n-form-item>
        <n-form-item label="知识状态" path="status">
          <n-select
            v-model:value="editingWorkOrder.status"
            :options="
              searchFormItems.find((item) => item.field === 'status')?.componentProps.options
            "
          />
        </n-form-item>
        <n-form-item label="知识解决计划" path="weknowSolvePlanTime">
          <n-input v-model:value="editingWorkOrder.weknowSolvePlanTime" />
        </n-form-item>
        <n-form-item label="规避方案" path="tempResolveScheme">
          <n-input v-model:value="editingWorkOrder.tempResolveScheme" type="textarea" />
        </n-form-item>
        <n-form-item label="反馈口径" path="feedbackWay">
          <n-input v-model:value="editingWorkOrder.feedbackWay" type="textarea" />
        </n-form-item>
        <n-form-item label="知识id" path="weknowId">
          <n-input v-model:value="editingWorkOrder.weknowId" />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showEditModal = false">取消</n-button>
          <n-button type="primary" @click="saveEdit">保存</n-button>
        </n-space>
      </template>
    </n-modal>
    <n-modal v-model:show="showknowAuthorModal" preset="card" title="分配作者" style="width: 600px">
      <n-form label-placement="left" label-width="auto" require-mark-placement="right-hanging">
        <n-form-item label="知识作者" path="type">
          <n-select v-model:value="knowAuthor" :options="knowAuthorOptions" clearable />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showknowAuthorModal = false">取消</n-button>
          <n-button type="primary" @click="saveknowAuthorEdit">保存</n-button>
        </n-space>
      </template>
    </n-modal>
    <n-modal v-model:show="showTableSeetingModal">
      <n-card
        style="width: 600px"
        title="表格设置"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form inline :label-width="80" :model="editingTableSetting">
          <n-checkbox-group v-model:value="showColumnListRef">
            <n-grid x-gap="12" :cols="4">
              <n-gi
                v-for="item in allColumnList"
                :key="item"
                :style="!item || item === '操作' ? 'display: none;' : ''"
              >
                <n-checkbox :value="item" :label="item" />
              </n-gi>
            </n-grid>
          </n-checkbox-group>
        </n-form>
        <template #footer>
          <n-space justify="end">
            <n-button type="primary" @click="saveTableSetting">保存</n-button>
            <n-button @click="showTableSeetingModal = false">取消</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>

    <n-modal
      v-model:show="showHitDelimit"
      preset="card"
      title="生成知识"
      style="width: 600px"
      :on-close="clearForm"
    >
      <n-form label-placement="left" label-width="auto" require-mark-placement="right-hanging">
        <n-form-item>
          <div v-html="reslute" contenteditable> </div>
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="center">
          <n-button type="success" @click="handleCopy">一键复制</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'Dtsstate',
  };
</script>

<script lang="ts" setup>
  import { ref, reactive, onMounted, computed, h } from 'vue';
  import {
    NCard,
    NForm,
    NFormItem,
    NInput,
    NSelect,
    NButton,
    NSpace,
    NGrid,
    NGridItem,
    NIcon,
    NUpload,
    useMessage,
    NModal,
    NSwitch, FormRules,
  } from 'naive-ui';
  import { ChevronDown, ChevronUp, Settings } from '@vicons/ionicons5';
  import { employeesOptions } from '../appState/index.vue';
  import {
    createColumns,
    ListData,
    getSearchFormItems,
    allColumnList,
    columnKeyMap, serviceKnowEmployee
  } from './columns';
  import {
    getWeKnowList,
    updateWeKnow,
    distributeAuthor,
    getKnowledgeStatus,
    queryBasicMsg,
    getKnowledgeType,
    insertKnowledge,
  } from '@/api/knowledge/index';
  import { filterObjectValues } from '@/utils';
  import { useRoute } from 'vue-router';
  import { cloneDeep } from 'lodash-es';
  import { UserInfoType, useUserStore } from '@/store/modules/user';
  import { batchCreateIR } from '@/api/dataview/irManagement';
  import * as XLSX from 'xlsx-js-style';
  import { useDialog, TreeOption, FormItemRule } from 'naive-ui';
  import { fetchData } from '../futDateView/statisticsTable/statisticsTable';
  import {getStaffList} from "@/views/dataview/personManage/staff";
  import {getMaxIntNumber} from "@/utils/commonUtils";
  import {DTS_HANDLE_TAG} from "@/views/dataview/personManage/tagContant";
  import {getUsernameFromAppName} from "@/api/dataview/serviceAndOtherQuestions";

  const userStore = useUserStore();
  const userInfo: UserInfoType = userStore.getUserInfo || {};
  const route = useRoute();
  const searchForm = reactive({});
  const sortState = ref({
    sortField: 'opinionLevel',
    sortOrder: 'desc',
  });
  const tableData = ref<any[]>([]);
  const loading = ref(false);
  const showknowAuthorModal = ref(false);
  const showHitDelimit = ref(false);
  const knowAuthor = ref('');
  const reslute = ref('');
  const curId = ref('');
  const knowAuthorOptions = ref(serviceKnowEmployee);

  const addAccountOptions = ref([]);

  const rules: FormRules = {
    appName: [
      {
        required: true,
        message: '请输入应用名称',
      },
    ],
    dtsId: [
      {
        required: true,
        message: '请输入DTS单号',
      },
    ],
    desc: [
      {
        required: true,
        message: '请输入问题描述',
      },
    ],
    ewpOwner: [
      {
        required: true,
        message: '请输入应用责任人',
      },
    ],
    type: [
      {
        required: true,
        message: '请选择知识类型',
      },
    ],
  };

  const distributeLoading = ref(false);
  const CCBLoading = ref(false);
  const paginationReactive = reactive({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    prefix({ itemCount }) {
      return `Total：${itemCount}`;
    },
    onChange: (page: number) => {
      paginationReactive.page = page;
      fetchData();
    },
    onUpdatePageSize: (pageSize: number) => {
      paginationReactive.pageSize = pageSize;
      paginationReactive.page = 1;
      fetchData();
    },
  });

  const isExpanded = ref(false);
  const visibleSearchFormItems = computed(() => {
    return isExpanded.value ? searchFormItems.value : searchFormItems.value.slice(0, 4);
  });

  const handleSearch = () => {
    console.log('Search form data:', searchForm);
    paginationReactive.page = 1;
    fetchData();
  };

  const resetForm = () => {
    Object.keys(searchForm).forEach((key) => {
      searchForm[key] = '';
    });
    sortState.value.sortField = 'opinionLevel';
    sortState.value.sortOrder = 'desc';
    fetchData();
    // 重置选中状态
    checkedRowKeys.value = [];
    selectedRows.value = [];
  };

  const toggleExpandForm = () => {
    isExpanded.value = !isExpanded.value;
  };

  const fetchData = async () => {
    loading.value = true;

    try {
      const queryParams = {
        ...filterObjectValues(searchForm),
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
        sortField: sortState.value.sortField,
        sortOrder: sortState.value.sortOrder,
      };
      const { total, records } = await getWeKnowList(queryParams);
      tableData.value = records;
      paginationReactive.itemCount = total;
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loading.value = false;
      // 更新选中行的数据
      selectedRows.value = selectedRows.value.map((selectedRow) => {
        const updatedRow = tableData.value.find((row) => row.id === selectedRow.id);
        return updatedRow ? cloneDeep(updatedRow) : selectedRow;
      });
    }
  };

  const showAddModal = ref(false);

  const newKnow = ref({});

  const formRef = ref(null);

  const saveKnowledge = async () => {
    try {
      await formRef.value?.validate();
      await insertKnowledge(newKnow.value);
      message.success('知识创建成功');
      showAddModal.value = false;
      newKnow.value = {};
      fetchData();
    } catch (error: any) {
      if (error?.response?.status === 400) {
        message.error(error.response.data.message);
      } else {
        message.error(error?.message || '知识创建失败');
      }
    }
  };

  const message = useMessage();

  const moduleOptions = [
    { value: '4796-UX体验', label: '4796-UX体验' },
    { value: '4796-一多适配', label: '4796-一多适配' },
    { value: '4796-功能故障', label: '4796-功能故障' },
    { value: '4796-功能故障-解锁登录', label: '4796-功能故障-解锁登录' },
    { value: '4796-功能完善', label: '4796-功能完善' },
    { value: '4796-稳定性', label: '4796-稳定性' },
    { value: '4796-性能功耗', label: '4796-性能功耗' },
    { value: '4796-生态丰富', label: '4796-生态丰富' },
    { value: '4796-其他', label: '4796-其他' },
  ];

  const showEditModal = ref(false);
  const editingWorkOrder = ref<Partial<ListData>>({});

  const handleEdit = (row: any) => {
    editingWorkOrder.value = { ...row };
    editingWorkOrder.value.oldWeKnowId = editingWorkOrder.value.weknowId;
    showEditModal.value = true;
  };
  const saveknowAuthorEdit = async () => {
    let res = await distributeAuthor({
      author: knowAuthor.value || '',
      id: curId.value,
    });
    showknowAuthorModal.value = false;

    fetchData();
  };

  const saveEdit = async () => {
    try {
      let res = { ...editingWorkOrder.value };
      res.newWeKnowId = editingWorkOrder.value.weknowId;
      await updateWeKnow(res);
      message.success('更新成功');
      showEditModal.value = false;
      fetchData();
    } catch (error) {
      console.error('Failed to update work order:', error);
      message.error('更新失败');
    }
  };
  const handleAppNameClick = (appName: string) => {
    searchForm.appName = appName;
    // 触发搜索
    handleSearch();
  };
  const handleAuthor = (row) => {
    showknowAuthorModal.value = true;
    knowAuthor.value = row.author || '';
    curId.value = row.id || '';
  };
  const handleGeneratingknowledge = async (row) => {
    let res = await queryBasicMsg(row.id);
    console.log('res:', res);
    if (res.content) {
      showHitDelimit.value = true;
      reslute.value = res.content;
    }
  };
  const handleCopy = async () => {
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard.writeText(reslute.value).then(() => {
        message.success('模板已生成并复制到剪贴板');
        // 重置选择状态
        checkedRowKeys.value = [];
        selectedRows.value = [];
      });
    } else {
      // 创建text area
      let textArea = document.createElement('textarea');
      textArea.value = reslute.value;
      // 使text area不在viewport，同时设置不可见
      textArea.style.position = 'absolute';
      textArea.style.opacity = '0';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      return new Promise((res, rej) => {
        // 执行复制命令并移除文本框
        document.execCommand('copy') ? res(null) : rej();
        textArea.remove();
        message.success('复制到剪贴板');
        // 重置选择状态
        checkedRowKeys.value = [];
        selectedRows.value = [];
      });
    }
  };
  const isCleanMode = ref(true);

  const handleFiltersChange = (filters: any) => {
    // 合并筛选条件到搜索表单
    Object.assign(searchForm, filters);
    // 重置到第一页
    paginationReactive.page = 1;
    // 重新获取数据
    fetchData();
  };

  const columns = computed(() =>
    createColumns(handleEdit, handleAppNameClick, handleAuthor, handleGeneratingknowledge)
  );

  const handleSorterChange = (sorter) => {
    if (sorter) {
      const { columnKey, order } = sorter;
      sortState.value.sortField = columnKey;
      sortState.value.sortOrder = order === 'ascend' ? 'asc' : 'desc';
    } else {
      sortState.value.sortField = 'opinionLevel';
      sortState.value.sortOrder = 'desc';
    }
    paginationReactive.page = 1; // 重置到第一页
    fetchData();
  };
  const fetchSelectOptions = async () => {
    const res = await getStaffList({
      pageNo: 1,
      pageSize: getMaxIntNumber(),
      tagIds: DTS_HANDLE_TAG,
    });
    const accounts = [];
    res.records.forEach((item) => {
      const option = {
        label: item.name,
        value: item.name+"/"+item.pinyin[0]+item.id,
      };
      accounts.push(option);
    });
    addAccountOptions.value = accounts;
  };

  const selectUsername = async () => {
    const username = await getUsernameFromAppName(newKnow.value.appName);
    if (username) {
      const findOption =  addAccountOptions.value.find(item=>item.label===username);
      newKnow.value.ewpOwner = findOption.value;
    }
  };

  onMounted(() => {
    fetchData();
    fetchKnowledgeStats();
    fetchKnowledgeTypes();
    fetchSelectOptions();
    // 从localStorage获取表格设置
    const columnsSetting = JSON.parse(
      window.localStorage.getItem('__4796_admin_dts_columns_setting') || '{}'
    );

    // 初始化显示的列
    showColumnListRef.value = allColumnList.filter((item) => {
      if (item === '操作') return false;
      const key = columnKeyMap[item];
      return !columnsSetting[key] || columnsSetting[key].show !== 'false';
    });
  });

  const selectedRows = ref<any[]>([]);
  const checkedRowKeys = ref<(string | number)[]>([]);
  const handleCheckedRowKeysChange = (keys: (string | number)[]) => {
    const newSelectedRows = keys
      .map((key) => {
        const existingRow = selectedRows.value.find((row) => row.id === key);
        if (existingRow) {
          return existingRow;
        }
        return tableData.value.find((row) => row.id === key);
      })
      .filter(Boolean) as ListData[];

    selectedRows.value = newSelectedRows;
    checkedRowKeys.value = keys;
  };

  const searchFormItems = computed(() => getSearchFormItems());
  const showTableSeetingModal = ref(false);
  const editingTableSetting = ref([]);
  const showColumnListRef = ref<string[]>([]);

  const onTableSettingClick = () => {
    // 从localStorage获取最新设置
    const columnsSetting = JSON.parse(
      window.localStorage.getItem('__4796_admin_dts_columns_setting') || '{}'
    );

    // 更新显示的列，确保操作列始终存在
    showColumnListRef.value = allColumnList.filter((item) => {
      // 操作列始终显示
      if (item === '操作') return true;
      const key = columnKeyMap[item];
      return !columnsSetting[key] || columnsSetting[key].show !== 'false';
    });

    showTableSeetingModal.value = true;
  };

  const saveTableSetting = () => {
    const tableSetting = {};
    allColumnList.forEach((item) => {
      if (item === '操作') return;
      const key = columnKeyMap[item];
      // 如果列不在选中列表中，则设置为不显示
      if (!showColumnListRef.value.includes(item)) {
        tableSetting[key] = { show: 'false' };
      } else {
        tableSetting[key] = { show: 'true' };
      }
    });

    window.localStorage.setItem('__4796_admin_dts_columns_setting', JSON.stringify(tableSetting));
    message.success('保存成功');
    window.location.reload();
  };

  // Add new reactive reference for knowledge stats
  const knowledgeStats = ref({
    preDistributeNum: 0,
    preWriteNum: 0,
    preModifyNum: 0,
    onlineNum: 0,
    preOfflineNum: 0,
    offlineNum: 0,
  });

  // Add function to fetch knowledge stats
  const fetchKnowledgeStats = async () => {
    try {
      const data = await getKnowledgeStatus();
      knowledgeStats.value = data;
    } catch (error) {
      console.error('Failed to fetch knowledge stats:', error);
    }
  };

  // Add new reactive reference for knowledge types
  const knowledgeTypes = ref({
    appLackTypeNum: 0,
    functionLackTypeNum: 0,
    functionFailureTypeNum: 0,
    specialSceneTypeNum: 0,
    appAllSceneTypeNum: 0,
  });

  // Add function to fetch knowledge types
  const fetchKnowledgeTypes = async () => {
    try {
      const data = await getKnowledgeType();
      console.log('data:', data);
      knowledgeTypes.value = data;
    } catch (error) {
      console.error('Failed to fetch knowledge types:', error);
    }
  };
</script>

<style>
  .filter-container {
    margin-bottom: 24px;
  }

  /* 修改表单项样式 */
  :deep(.n-form-item) {
    display: flex;
    margin-right: 0;
    margin-bottom: 18px;
  }

  :deep(.n-form-item-label) {
    width: 90px !important;
    text-align: right;
  }

  :deep(.n-form-item-blank) {
    flex: 1;
  }

  /* 统一输入框和选择框的宽度和对齐方式 */
  :deep(.n-input),
  :deep(.n-select) {
    /* width: 300px !important; */
  }

  /* 确保输入内容左对齐 */
  :deep(.n-input__input-el),
  :deep(.n-select-option__content) {
    text-align: left !important;
  }

  /* 确保选择框的内容左对齐 */
  :deep(.n-base-selection-label) {
    text-align: left !important;
  }

  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }

  .setting-icon {
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
    color: #666;
  }

  .setting-icon:hover {
    background-color: #f5f5f5;
    color: #2080f0;
    transform: rotate(30deg);
  }

  .bold-font {
    font-weight: 700;
  }

  .gradient-text {
    font-size: 14px;
    background: -webkit-linear-gradient(90deg, red 0%, green 50%, blue 100%); /* Chrome, Safari */
    background: linear-gradient(90deg, red 0%, green 50%, blue 100%); /* 标准语法 */
    -webkit-background-clip: text; /* Chrome, Safari */
    background-clip: text;
    -webkit-text-fill-color: transparent; /* Chrome, Safari */
    color: transparent;
  }

  .statistics-container {
    margin: 24px 0;
  }

  .stat-card {
    background-color: #fff;
    transition: all 0.3s;
    height: 100%;
  }

  .stat-card:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .stat-content {
    text-align: center;
  }

  .stat-label {
    color: #666;
    font-size: 14px;
    margin-bottom: 8px;
  }

  .stat-value {
    color: #2080f0;
    font-size: 24px;
    font-weight: bold;
  }
</style>
