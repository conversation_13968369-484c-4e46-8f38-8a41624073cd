import type { BasicColumn } from '@/components/Table';
import { ewpService as service } from '@/utils/axios';

export const columns: BasicColumn[] = [
  {
    title: '日期',
    key: 'date',
  },
  {
    title: '总量',
    key: 'total',
  },
  {
    title: '组织者评审',
    key: 'organizerReviewNum',
  },
  {
    title: '特性负责人评审',
    key: 'featureOwnerReviewNum',
  },
  {
    title: '研发系统跟踪(DTS)',
    key: 'dtsTrackingNum',
  },
  {
    title: '提单人处理',
    key: 'billLadderHandleNum',
  },
  {
    title: '挂起',
    key: 'pendingNum',
  },
  {
    title: '关闭',
    key: 'closedNum',
  },
  {
    title: '已分析',
    key: 'analyzedNum',
  },
  {
    title: '分析覆盖率',
    key: 'analysisCoverage',
  },
  {
    title: '挂起率',
    key: 'pendingCoverage',
  }
];

export const fetchData = async () => {
  const titleMap = getTitleMap()
  const tableData: Record<string, string>[] = []
  const rawData = await service.post('/management/betaClubOrder/overview', { afterDayCnt: '15' });
  const dateKeys = Object.keys(rawData).sort(function(a, b) {
      return new Date(a) - new Date(b);
  })
  dateKeys.forEach((dateKey) => {
    const currentRowData = {
      date: dateKey
    }
    Object.keys(rawData[dateKey]).forEach((stateKey) => {
      currentRowData[stateKey] = rawData[dateKey][stateKey]
    })
    tableData.push(currentRowData)
  })
  return tableData
};

const getTitleMap = () => {
  const map = {}
  columns.forEach(titleObj => {
    map[titleObj.key as string] = titleObj.title
  })
  return map
}
