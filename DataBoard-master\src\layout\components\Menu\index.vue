<template>
  <NMenu
    :options="menus"
    :inverted="inverted"
    :mode="mode"
    :collapsed="collapsed"
    :collapsed-width="64"
    :collapsed-icon-size="20"
    :indent="24"
    :expanded-keys="openKeys"
    :value="getSelectedKeys"
    @update:value="clickMenuItem"
    @update:expanded-keys="menuExpanded"
  />
</template>

<script lang="ts">
  import { defineComponent, ref, onMounted, onUnmounted, reactive, computed, watch, toRefs, unref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useAsyncRouteStore } from '@/store/modules/asyncRoute';
  import { generatorMenu, generatorMenuMix } from '@/utils';
  import { useProjectSettingStore } from '@/store/modules/projectSetting';
  import { useProjectSetting } from '@/hooks/setting/useProjectSetting';
  import { queryWorkplaceInfo } from '@/api/workplace/workplace';
  import { getTotalCount } from '@/api/knowledgeBase/knowledgeBase';
  import { travelApplicationQuery } from '@/api/dataview/businessTravelersManage';
  import { useEventBus } from '@/hooks/useEventBus';
  import { queryToBeApproved } from '@/api/dataview/statisticalOverview';
  import { getToDoCount } from '@/api/dataview/myOrder';
  import { useUserStore } from '@/store/modules/user';

  export default defineComponent({
    name: 'AppMenu',
    components: {},
    props: {
      mode: {
        // 菜单模式
        type: String,
        default: 'vertical',
      },
      collapsed: {
        // 侧边栏菜单是否收起
        type: Boolean,
      },
      //位置
      location: {
        type: String,
        default: 'left',
      },
    },
    emits: ['update:collapsed', 'clickMenuItem'],
    setup(props, { emit }) {
      // 当前路由
      const currentRoute = useRoute();
      const router = useRouter();
      const asyncRouteStore = useAsyncRouteStore();
      const settingStore = useProjectSettingStore();
      const menus = ref<any[]>([]);
      const selectedKeys = ref<string>(currentRoute.name as string);
      const headerMenuSelectKey = ref<string>('');

      const { navMode } = useProjectSetting();

      // 获取当前打开的子菜单
      const matched = currentRoute.matched;

      const getOpenKeys = matched && matched.length ? matched.map((item) => item.name) : [];

      const state = reactive({
        openKeys: getOpenKeys,
      });

      const inverted = computed(() => {
        return ['dark', 'header-dark'].includes(settingStore.navTheme);
      });

      const getSelectedKeys = computed(() => {
        let location = props.location;
        return location === 'left' || (location === 'header' && unref(navMode) === 'horizontal')
          ? unref(selectedKeys)
          : unref(headerMenuSelectKey);
      });
      // 用户信息
      const userStore = useUserStore();
      const userInfo: any = userStore.getUserInfo || {};
      // 知识库角标，解决知识库接口挂掉影响菜单问题
      const knowledgeBaseBadgeArr = reactive([]);

      const eventBus = useEventBus();
      // 监听分割菜单
      watch(
        () => settingStore.menuSetting.mixMenu,
        () => {
          updateMenu();
          if (props.collapsed) {
            emit('update:collapsed', !props.collapsed);
          }
        }
      );

      // 监听菜单收缩状态
      // watch(
      //   () => props.collapsed,
      //   (newVal) => {
      //   }
      // );

      // 跟随页面路由变化，切换菜单选中状态
      watch(
        () => currentRoute.fullPath,
        () => {
          updateMenu();
        }
      );

      // 监听知识库角标变化更新菜单栏
      watch(
        () => knowledgeBaseBadgeArr,
        () => {
          updateMenu();
        },
        { deep: true }
      );

      function updateSelectedKeys() {
        const matched = currentRoute.matched;
        state.openKeys = matched.map((item) => item.name);
        const activeMenu: string = (currentRoute.meta?.activeMenu as string) || '';
        selectedKeys.value = activeMenu ? (activeMenu as string) : (currentRoute.name as string);
      }

      async function updateMenu() {
        const badgeArr = [];
        try {
          // Demo创作待处理数量
          const { data } = await queryWorkplaceInfo();
          const demoData = data.userItemCustomizedDemoData;
          const workplaceInfoCount =
            demoData.waitHandleDemoNum +
            demoData.waitReviewDemoNum +
            demoData.waitOutGoingDemoNum +
            demoData.waitGiteeDemoNum;
          badgeArr.push({
            name: 'SampleCodeManage',
            value:workplaceInfoCount
          },
          {
            name:'CustomizedDemo',
            value: workplaceInfoCount,
          });
          // 部门
          // 交付件统计
          const toBeApprovedRes = await queryToBeApproved();
          // 差旅管理
          let travelApplicationRes = await travelApplicationQuery({
            status: '待审批',
          });
          const departmentOKRCount =
            (toBeApprovedRes?.data?.length || 0) + (travelApplicationRes?.data?.length || 0);
          badgeArr.push(
            {
              name: 'departmentOKR',
              value: departmentOKRCount,
            },
            {
              name: 'StatisticalOverview',
              value: toBeApprovedRes?.data?.length || 0,
            },
            {
              name: 'BusinessTravelersManage',
              value: travelApplicationRes?.data?.length || 0,
            }
          );
          // 舆情保障
          // 我的待办
          const toDoCount = await getToDoCount({
            currentHandler: userInfo.account.replace(/[a-z]/gi, ''),
          });
          badgeArr.push(
            {
              name: 'ListingProtection',
              value: toDoCount,
            },
            {
              name: 'MyTodo',
              value: toDoCount,
            }
          );
          // 知识库-我的待办数量
          badgeArr.push(...knowledgeBaseBadgeArr);
        } catch (error) {}
        if (!settingStore.menuSetting.mixMenu) {
          menus.value = generatorMenu(asyncRouteStore.getMenus, badgeArr);
        } else {
          //混合菜单
          const firstRouteName: string = (currentRoute.matched[0].name as string) || '';
          menus.value = generatorMenuMix(
            asyncRouteStore.getMenus,
            firstRouteName,
            props.location,
            badgeArr
          );
          const activeMenu: string = currentRoute?.matched[0].meta?.activeMenu as string;
          headerMenuSelectKey.value = (activeMenu ? activeMenu : firstRouteName) || '';
        }
        updateSelectedKeys();
      }

      // 点击菜单
      function clickMenuItem(key: string) {
        if (/http(s)?:/.test(key)) {
          window.open(key);
        } else {
          router.push({ name: key });
        }
        emit('clickMenuItem' as any, key);
        getKnowledgeBaseToDoCount(); // 请求知识库待办数量
      }

      //展开菜单
      function menuExpanded(openKeys: string[]) {
        if (!openKeys) return;
        const latestOpenKey = openKeys.find((key) => state.openKeys.indexOf(key) === -1);
        const isExistChildren = findChildrenLen(latestOpenKey as string);
        state.openKeys = isExistChildren ? (latestOpenKey ? [latestOpenKey] : []) : openKeys;
      }

      //查找是否存在子路由
      function findChildrenLen(key: string) {
        if (!key) return false;
        const subRouteChildren: string[] = [];
        for (const { children, key } of unref(menus)) {
          if (children && children.length) {
            subRouteChildren.push(key as string);
          }
        }
        return subRouteChildren.includes(key);
      }

      // 请求知识库待办数量
      const getKnowledgeBaseToDoCount = async () => {
        try {
          const todoCountres = await getTotalCount();
          if (!knowledgeBaseBadgeArr.length) {
            knowledgeBaseBadgeArr.push(
              {
                name: 'KnowledgeBase',
                value: todoCountres.data.totalCount,
              },
              {
                name: 'knowledgeTodos',
                value: todoCountres.data.totalCount,
              }
            );
          } else {
            knowledgeBaseBadgeArr[0].value = todoCountres.data.totalCount;
            knowledgeBaseBadgeArr[1].value = todoCountres.data.totalCount;
          }
        } catch (error) {}
      };

      onMounted(() => {
        updateMenu();
        eventBus.on('updateMenu', updateMenu);
        getKnowledgeBaseToDoCount();
      });

      onUnmounted(() => {
        eventBus.off('updateMenu', updateMenu);
      });

      return {
        ...toRefs(state),
        inverted,
        menus,
        selectedKeys,
        headerMenuSelectKey,
        getSelectedKeys,
        clickMenuItem,
        menuExpanded,
      };
    },
  });
</script>
