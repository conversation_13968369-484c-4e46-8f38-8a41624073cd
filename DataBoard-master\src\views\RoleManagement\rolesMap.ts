import { UserRoleEnum, UserRoleCNameEnum } from "@/enums/UserRoleEnum"

class RoleManager {
  map: Record<string, string> = {}
  static instance

  constructor() {
    this.init()
  }

  getInstance() {
    if (!RoleManager.instance) {
      RoleManager.instance = new RoleManager()
    }
    return RoleManager.instance
  }

  init() {
    Object.keys(UserRoleEnum).forEach(permissionName => {
      const permissionValue = UserRoleEnum[permissionName]
      this.map[permissionValue] = UserRoleCNameEnum[permissionName]
    })
  }

}
const roleManager = new RoleManager()
export default roleManager
