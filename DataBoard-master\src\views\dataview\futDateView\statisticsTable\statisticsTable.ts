import type { BasicColumn } from '@/components/Table';
import { ewpService as service } from '@/utils/axios';

export const columns: BasicColumn[] = [
  {
    title: '日期',
    key: 'date',
  },
  {
    title: '总量',
    key: 'total',
  },
  {
    title: '待分析',
    key: 'init',
  },
  {
    title: '待提单',
    key: 'analyzedDts',
  },
  {
    title: '已挂起',
    key: 'analyzedPending',
  },
  {
    title: '问题定界',
    key: 'open',
  },
  {
    title: '待锁定',
    key: 'unlocked',
  },
  {
    title: '待修复',
    key: 'tracking',
  },
  {
    title: '待回归',
    key: 'regression',
  },
  {
    title: '已闭环',
    key: 'closed',
  },
];

export const fetchData = async () => {
  const titleMap = getTitleMap()
  const tableData: Record<string, string>[] = []
  const rawData = await service.post('/management/futOrder/overview', { afterDayCnt: '15' });
  const dateKeys = Object.keys(rawData).sort(function(a, b) {
      return new Date(a) - new Date(b);
  })
  dateKeys.forEach((dateKey) => {
    const currentRowData = {
      date: dateKey
    }
    Object.keys(rawData[dateKey]).forEach((stateKey) => {
      currentRowData[stateKey] = rawData[dateKey][stateKey]
    })
    tableData.push(currentRowData)
  })
  return tableData
};

const getTitleMap = () => {
  const map = {}
  columns.forEach(titleObj => {
    map[titleObj.key as string] = titleObj.title
  })
  return map
}
