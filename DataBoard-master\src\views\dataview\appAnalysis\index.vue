<template>
  <div class="filter-container">
      <n-card>
        <n-form
          :model="searchForm"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          @submit.prevent="handleSearch"
        >
          <n-grid :cols="24" :x-gap="24">
            <n-grid-item v-for="item in searchFormItems" :key="item.field" :span="6">
              <n-form-item :label="item.label" :path="item.field">
                <n-input
                  v-if="item.component === 'Input'"
                  v-model:value="searchForm[item.field]"
                  @keyup.enter="handleSearch"
                  clearable
                />
                <n-date-picker
                  v-if="item.component === 'DateRangePicker'"
                  v-model:value="searchForm[item.field]"
                  type="daterange"
                  clearable
                />
              </n-form-item>
            </n-grid-item>
            <n-grid-item v-for="item in channelList" :key="item.field" :span="2">
              <n-form-item :label="item.label" :path="item.field">
                <n-checkbox
                  v-if="item.component === 'CheckBox'"
                  v-model:checked="searchForm[item.field]"
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>
          <div class="form-actions">
            <n-space>
              <n-button @click="resetForm">重置</n-button>
              <n-button type="primary" attr-type="submit">查询</n-button>
            </n-space>
          </div>
        </n-form>
      </n-card>
  </div>
  <div class="app-analysis-container">
    <!-- 左侧列表 -->
    <div class="left-panel">
      <n-card title="应用列表" :bordered="false" size="small">
        <n-data-table
          remote
          :columns="leftColumns"
          :data="leftTableData"
          :bordered="false"
          size="small"
          :pagination="paginationReactive"
          :loading="loading"
          :single-line="false"
          striped
          :row-props="getDetail"
          :scroll-x="500"
          :max-height="tableMaxHeight"
        />
      </n-card>
    </div>

    <!-- 右侧内容 -->
    <div class="right-panel">
      <!-- 上方列表 -->
      <div class="top-list">
        <n-card :bordered="false" size="small">
          <n-data-table
            :columns="topColumns"
            :data="topTableData"
            :bordered="false"
            size="small"
            :pagination="false"
            :scroll-x="1000"
            :single-line="false"
            striped
          />
        </n-card>
      </div>

      <!-- 下方Tab内容 -->
      <div class="bottom-tabs">
        <n-card :bordered="false" size="small">
          <n-tabs type="line" animated class="custom-tabs" @update:value="tabChange">
            <template #prefix>
              <div style="width: 12px"></div>
            </template>
            <template #suffix>
              <n-button
                secondary
                type="primary"
                size="small"
                class="export-button"
                @click="handleExport"
                :loading="exportLoading"
              >
                <template #icon>
                  <n-icon>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M13 10h5l-6-6-6 6h5v8h2v-8M4 19v-7h2v7h12v-7h2v7H4Z"
                      />
                    </svg>
                  </n-icon>
                </template>
                导出
              </n-button>
            </template>
            <n-tab-pane v-for="tab in tabs" :key="tab.key" :name="tab.key" :tab="tab.title">
              <n-data-table
                remote
                :columns="tabColumns"
                :data="tabTableData"
                :bordered="false"
                :loading="dtsDetailloading"
                size="small"
                :pagination="paginationMissing"
                :single-line="false"
                striped
                min-height="500px"
              />
            </n-tab-pane>
          </n-tabs>
        </n-card>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, h, reactive, onMounted, nextTick, watch, onUnmounted, defineComponent } from 'vue';
  import {
    NDataTable,
    NTabs,
    NTabPane,
    NCard,
    NTag,
    NInput,
    NButton,
    NInputGroup,
    NIcon,
    NTreeSelect,
  } from 'naive-ui';
  import { SearchOutline } from '@vicons/ionicons5';
  import {
    queryVoc,
    queryWorkOrder,
    workOrderScene,
    updateOwnerFunction,
  } from '@/api/dataview/appAnalysis';
  import { query as queryFeatures } from '@/api/dataview/featuresList';
  // import WordCloud from 'wordcloud2';
  function toPercent(point) {
    var str = Number(point * 100).toFixed(2);
    return str;
  }

  const ShowOrEdit = defineComponent({
    props: {
      value: [String, Number],
      onUpdateValue: [Function, Array],
    },
    setup(props) {
      const isEdit = ref(false);
      const selectRef = ref(null);
      const selectValue = ref(props.value);
      function handleOnClick() {
        isEdit.value = true;
        nextTick(() => {
          selectRef.value.focusInput();
        });
      }
      function handleChange() {
        props.onUpdateValue(selectValue.value);
        isEdit.value = false;
      }
      return () =>
        h(
          'div',
          {
            style: 'min-height: 22px',
            onClick: handleOnClick,
          },
          isEdit.value
            ? h(NTreeSelect, {
                ref: selectRef,
                cascade: true,
                filterable: true,
                clearable: true,
                defaultShow: true,
                checkStrategy: 'all',
                defaultValue: props.value,
                style: {
                  textAlign: 'left',
                  width: '100%',
                },
                menuProps: {
                  style: { minWidth: '250px' },
                },
                options: featuresOptions.value,
                onUpdateValue: (v) => {
                  selectValue.value = v;
                },
                onChange: handleChange,
                onBlur: handleChange,
              })
            : props.value
        );
    },
  });

  // 初始化查询
  const searchFormItems = [
  {
    field: 'searchKeyword',
    label: '应用名称',
    component: 'Input',
  },
  {
    field: 'createTime',
    label: '创建时间',
    component: 'DateRangePicker',
  },
];

// 渠道分类查询列表
const channelList = [
  {
    field: 'fut',
    label: 'fut',
    component: 'CheckBox',
  },
  {
    field: 'beta',
    label: 'beta',
    component: 'CheckBox',
  },
  {
    field: 'nss',
    label: 'nss',
    component: 'CheckBox',
  },
  {
    field: 'ag',
    label: 'ag',
    component: 'CheckBox',
  },
];

  // 左侧表格列定义
  const leftColumns = [
    {
      title: '序号',
      key: 'index',
      width: 60,
      align: 'center',
      render: (row, index) => index + 1,
    },
    {
      title: '应用名称',
      key: 'appName',
      align: 'center',
      render: (row) => {
        return h(
          'div',
          {
            class: 'app-name-cell',
          },
          [
            h('img', {
              src: row.icon,
              class: 'app-icon',
            }),
            h('span', row.appName),
          ]
        );
      },
    },
    {
      title: '提及声量',
      align: 'center',
      children: [
        {
          title: '全部',
          key: 'opinionNum',
          align: 'center',
        },
        {
          title: '已提单',
          key: 'dtsOpinionNum',
          align: 'center',
        }
      ]
    },
    {
      title: '提及占比',
      key: 'opinionNum',
      align: 'center',
      children: [
        {
          title: '全部',
          key: 'percent',
          align: 'center',
          render: (row) => `${toPercent(row.percent)}%`,
        },
        {
          title: '已提单',
          key: 'dtsPercent',
          align: 'center',
          render: (row) => `${toPercent(row.dtsPercent)}%`,
        }
      ]
    },
  ];

  // 上方表格列定义
  const topColumns = [
    {
      title: '应用名称',
      key: 'appName',
      fixed: 'left',
      align: 'center',
    },
    {
      title: '质量属性分布',
      key: 'qualityAttributes',
      align: 'center',
      children: [
        {
          title: '功能',
          key: 'function',
          align: 'center',
          children: [
            {
              title: '声量',
              key: 'criticalNum',
              align: 'center',
            },
            {
              title: '占比',
              key: 'criticalPercent',
              align: 'center',
              render: (row) => `${row.criticalPercent}%`,
            },
          ],
        },
        {
          title: '性能功耗',
          key: 'performance',
          align: 'center',
          children: [
            {
              title: '声量',
              key: 'performanceNum',
              align: 'center',
            },
            {
              title: '占比',
              key: 'performancePercent',
              align: 'center',
              render: (row) => `${row.performancePercent}%`,
            },
          ],
        },
        {
          title: '稳定性',
          key: 'stability',
          align: 'center',
          children: [
            {
              title: '声量',
              key: 'crashNum',
              align: 'center',
            },
            {
              title: '占比',
              key: 'crashPercent',
              align: 'center',
              render: (row) => `${row.crashPercent}%`,
            },
          ],
        },
        {
          title: 'UX',
          key: 'ux',
          align: 'center',
          children: [
            {
              title: '声量',
              key: 'uxNum',
              align: 'center',
            },
            {
              title: '占比',
              key: 'uxPercent',
              align: 'center',
              render: (row) => `${row.uxPercent}%`,
            },
          ],
        },
        // {
        //   title: '兼容性',
        //   key: 'compatibility',
        //   align: 'center',
        //   children: [
        //     {
        //       title: '声量',
        //       key: 'compatibilityMentions',
        //       align: 'center',
        //     },
        //     {
        //       title: '占比',
        //       key: 'compatibilityPercentage',
        //       align: 'center',
        //       render: (row) => `${row.compatibilityPercentage}%`,
        //     },
        //   ],
        // },
        // {
        //   title: '功耗',
        //   key: 'power',
        //   align: 'center',
        //   children: [
        //     {
        //       title: '声量',
        //       key: 'powerMentions',
        //       align: 'center',
        //     },
        //     {
        //       title: '占比',
        //       key: 'powerPercentage',
        //       align: 'center',
        //       render: (row) => `${row.powerPercentage}%`,
        //     },
        //   ],
        // },
        // {
        //   title: '安全',
        //   key: 'security',
        //   align: 'center',
        //   children: [
        //     {
        //       title: '声量',
        //       key: 'securityMentions',
        //       align: 'center',
        //     },
        //     {
        //       title: '占比',
        //       key: 'securityPercentage',
        //       align: 'center',
        //       render: (row) => `${row.securityPercentage}%`,
        //     },
        //   ],
        // },
        {
          title: '其他',
          key: 'other',
          align: 'center',
          children: [
            {
              title: '声量',
              key: 'otherNum',
              align: 'center',
            },
            {
              title: '占比',
              key: 'otherPercent',
              align: 'center',
              render: (row) => `${row.otherPercent}%`,
            },
          ],
        },
        {
          title: '总计',
          key: 'total',
          align: 'center',
          children: [
            {
              title: '声量',
              key: 'opinionNum',
              align: 'center',
            },
            {
              title: '占比',
              key: 'allPercent',
              align: 'center',
              render: (row) => `${row.allPercent}%`,
            },
          ],
        },
      ],
    },
  ];

  // Tab表格列定义
  const tabColumns = [
    {
      title: '场景名称',
      key: 'sceneName',
      width: 200,
      align: 'center',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render: (row) => {
        const description = row.sceneName?.split('】').pop();
        return h('span', {}, description);
      },
    },
    {
      title: '归属功能',
      key: 'ownerFunction',
      align: 'center',
      resizable: true,
      render(row, index) {
        return h(ShowOrEdit, {
          value: row.ownerFunction,
          onUpdateValue: async (v) => {
            try {
              let res = await updateOwnerFunction({
                dtsOrder:
                  row.workOrderSceneDetails
                    ?.filter((item) => item?.dtsNo)
                    ?.map((item) => {
                      return item.dtsNo;
                    }) || [],
                ownerFunction: v || '',
              });
            } catch (e) {}
            loadWorkOrderScene(selectedAppName.value, curTab.value);
          },
        });
      },
    },
    {
      title: '故障分类',
      key: 'problemKind',
      align: 'center',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '提及声量',
      key: 'opinionNum',
      align: 'center',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '提及占比',
      key: 'percent',
      render: (row) => `${toPercent(row.percent)}%`,
      align: 'center',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '状态跟踪',
      key: 'workOrderSceneDetails',
      align: 'center',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render: (row) => {
        if (!row.workOrderSceneDetails?.length) return '';

        return h(
          'div',
          { class: 'table-cell-list' },
          row.workOrderSceneDetails.map((detail: any) => {
            const statusMap = {
              pending: { text: '待处理', type: 'warning' },
              processing: { text: '处理中', type: 'info' },
              completed: { text: '已完成', type: 'success' },
            };
            return h(
              'div',
              {
                class: row.workOrderSceneDetails.length > 1 ? 'cell-item' : '',
              },
              h(
                NTag,
                {
                  type: statusMap[detail.state]?.type || 'default',
                  size: 'small',
                },
                { default: () => statusMap[detail.state]?.text || detail.state }
              )
            );
          })
        );
      },
    },
    {
      title: '解决计划',
      key: 'workOrderSceneDetails',
      align: 'center',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render: (row) => {
        if (!row.workOrderSceneDetails?.length) return '';

        return h(
          'div',
          { class: 'table-cell-list' },
          row.workOrderSceneDetails.map((detail: any) =>
            h(
              'div',
              {
                class: `solution-item ${row.workOrderSceneDetails.length > 1 ? 'cell-item' : ''}`,
              },
              detail.solutionPlan
            )
          )
        );
      },
    },
    {
      title: '跟踪单号',
      key: 'workOrderSceneDetails',
      align: 'center',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      width: 150,
      render: (row) => {
        if (!row.workOrderSceneDetails?.length) return '';

        return h(
          'div',
          { class: 'table-cell-list' },
          row.workOrderSceneDetails.map((detail: any) =>
            h(
              'div',
              {
                class: row.workOrderSceneDetails.length > 1 ? 'cell-item' : '',
              },
              h(
                NTag,
                {
                  type: 'info',
                  size: 'small',
                },
                { default: () => detail.dtsNo }
              )
            )
          )
        );
      },
    },
  ];

  // Mock数据
  const leftTableData = ref();

  const topTableData = ref();

  const tabTableData = ref();

  // Tab定义
  const tabs = [
    { key: 'functionMiss', title: '功能缺失' },
    { key: 'functionFalut', title: '功能故障' },
    { key: 'performance', title: '性能功耗' },
    { key: 'stability', title: '稳定性' },
    { key: 'ux', title: 'UX体验' },
    // { key: 'security', title: '安全' },
    // { key: 'compatibility', title: '兼容性' },
  ];
  const curTab = ref(tabs[0].title);
  // 搜索相关
  const searchKeyword = ref('');

  const handleSearch = () => {
    paginationReactive.page = 1;
    fetchData();
  };
  const loading = ref(false);
  const dtsDetailloading = ref(false)
  const paginationReactive = reactive({
    page: 1,
    pageSize: 12,
    itemCount: 0,
    showSizePicker: true,
    pageSizes: [12, 20, 50, 100],
    pageSlot: 1,
    prefix({ itemCount }) {
      return `Total：${itemCount}`;
    },
    onChange: (page: number) => {
      paginationReactive.page = page;
      fetchData();
    },
    onUpdatePageSize: (pageSize: number) => {
      paginationReactive.pageSize = pageSize;
      paginationReactive.page = 1;
      fetchData();
    },
  });

  const paginationMissing = reactive({
    page: 1,
    pageSize: 8,
    itemCount: 0,
    showSizePicker: true,
    pageSizes: [8, 20, 50, 100],
    pageSlot: 5,
    prefix({ itemCount }) {
      return `Total：${itemCount}`;
    },
    onChange: (page: number) => {
      paginationMissing.page = page;
      loadWorkOrderScene(selectedAppName.value, curTab.value);
    },
    onUpdatePageSize: (pageSize: number) => {
      paginationMissing.pageSize = pageSize;
      paginationMissing.page = 1;
      loadWorkOrderScene(selectedAppName.value, curTab.value);
    },
  });

  const searchForm = reactive({});

  const resetForm = () => {
    Object.keys(searchForm).forEach((key) => {
      searchForm[key] = '';
    });
    fetchData();
  };

  const fetchData = async () => {
    loading.value = true;

    try {
      const queryParams = {
        appName: searchForm.searchKeyword,
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
      };
      if (searchForm.createTime) {
        const [start, end] = searchForm.createTime;
        queryParams.startTime = formatDate(start);
        queryParams.endTime = formatDate(end);
      }
      queryParams.fut = searchForm.fut ? searchForm.fut : '';
      queryParams.beta = searchForm.beta ? searchForm.beta : '';
      queryParams.nss = searchForm.nss ? searchForm.nss : '';
      queryParams.ag = searchForm.ag ? searchForm.ag : '';
      const { total, records } = await queryVoc(queryParams);
      leftTableData.value = records;
      if (leftTableData.value.length > 0) {
        selectedAppName.value = leftTableData.value[0].appName;
        getFeatures(leftTableData.value[0].appName);
        loadWorkOrderData(leftTableData.value[0].appName);
        loadWorkOrderScene(leftTableData.value[0].appName, curTab.value);
      }
      paginationReactive.itemCount = total;
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loading.value = false;
    }
  };

  // 添加选中行的状态
  const selectedAppName = ref('');

  const getDetail = (row: any) => {
    return {
      style: 'cursor: pointer;',
      onClick: async () => {
        selectedAppName.value = row.appName;
        await Promise.all([
          loadWorkOrderData(selectedAppName.value),
          getFeatures(selectedAppName.value),
        ]);
        await loadWorkOrderScene(selectedAppName.value, curTab.value);
      },
    };
  };

  const transformTree = (data) => {
    // 创建一个映射来存储每个父节点及其对应的子节点列表
    const parentMap = new Map();

    // 遍历输入数据
    data.forEach((item) => {
      const { firstLevel, secondLevel, thirdLevel } = item;

      // 如果父节点不存在，则创建一个新的空数组
      if (!parentMap.has(firstLevel)) {
        parentMap.set(firstLevel, []);
      }

      // 获取当前父节点对应的子节点列表
      const children = parentMap.get(firstLevel);

      // 查找是否有相同的 secondLevel
      let foundChild = null;
      for (let i = 0; i < children.length; i++) {
        if (children[i].key === secondLevel) {
          foundChild = children[i];
          break;
        }
      }

      // 如果找到了相同的 secondLevel，则更新其 thirdLevel
      if (foundChild) {
        if (!foundChild.children) {
          foundChild.children = [];
        }
        foundChild.children.push({ key: thirdLevel, label: thirdLevel });
      } else {
        // 否则，添加一个新的子节点
        children.push({
          key: secondLevel,
          label: secondLevel,
          children: thirdLevel ? [{ key: thirdLevel, label: thirdLevel, children: null }] : null,
        });
      }
    });

    // 构建最终的目标结构
    const result = Array.from(parentMap.entries()).map(([key, value]) => ({
      key,
      label: key,
      children: value,
    }));

    return result;
  };
  const featuresOptions = ref([]);
  const getFeatures = async (appName) => {
    let res = await queryFeatures({
      pageNum: 1,
      pageSize: 1000,
      appName,
    });
    featuresOptions.value = transformTree(res?.data?.data || []);
  };
  // 加载工单数据
  const loadWorkOrderData = async (appName: string) => {
    try {
      loading.value = true;

      const queryParams = { appName };
      if (searchForm.createTime) {
        const [start, end] = searchForm.createTime;
        queryParams.startTime = formatDate(start);
        queryParams.endTime = formatDate(end);
      }
      queryParams.fut = searchForm.fut ? searchForm.fut : '';
      queryParams.beta = searchForm.beta ? searchForm.beta : '';
      queryParams.nss = searchForm.nss ? searchForm.nss : '';
      queryParams.ag = searchForm.ag ? searchForm.ag : '';
      const data = await queryWorkOrder(queryParams);

      // 更新上方表格数据
      topTableData.value = [
        {
          appName: data.appName,
          crashNum: data.crashNum,
          crashPercent: toPercent(data.crashPercent),
          uxNum: data.uxNum,
          uxPercent: toPercent(data.uxPercent),
          criticalNum: data.criticalNum,
          criticalPercent: toPercent(data.criticalPercent),
          performanceNum: data.performanceNum,
          performancePercent: toPercent(data.performancePercent),
          otherNum: data.otherNum,
          otherPercent: toPercent(data.otherPercent),
          opinionNum: data.opinionNum,
          allPercent: toPercent(data.allPercent),
        },
      ];
    } catch (error) {
      console.error('Failed to load work order data:', error);
    } finally {
      loading.value = false;
    }
  };

  // 添加词云相关的ref
  const missingWordCloud = ref<HTMLCanvasElement | null>(null);
  const failureWordCloud = ref<HTMLCanvasElement | null>(null);

  // 生成词云数据的函数
  const generateWordCloudData = (tableData: any[]) => {
    return tableData.map((item) => [item.sceneName, item.mentionVolume]);
  };

  // 配置词云选项
  const wordCloudOptions = {
    gridSize: 16,
    weightFactor: 10,
    fontFamily: 'Microsoft YaHei',
    color: 'random-dark',
    rotateRatio: 0.5,
    backgroundColor: 'transparent',
  };

  // 渲染词云的函数
  // const renderWordClouds = () => {
  //   if (missingWordCloud.value && tabTableData.value?.functionMissing) {
  //     const missingData = generateWordCloudData(tabTableData.value.functionMissing);
  //     WordCloud(missingWordCloud.value, {
  //       list: [
  //         ['词云', 50],
  //         ['前端', 30],
  //         ['JavaScript', 20],
  //       ],
  //       ...wordCloudOptions,
  //       clearCanvas: true,
  //       hover: function (item: any) {
  //         if (item) {
  //           const [text, weight] = item;
  //           console.log(`${text}: ${weight}`);
  //         }
  //       },
  //     });
  //   }

  //   if (failureWordCloud.value && tabTableData.value?.functionFailure) {
  //     const failureData = generateWordCloudData(tabTableData.value.functionFailure);
  //     WordCloud(failureWordCloud.value, {
  //       list: [
  //         ['词云', 50],
  //         ['前端', 30],
  //         ['JavaScript', 20],
  //       ],
  //       ...wordCloudOptions,
  //       clearCanvas: true,
  //       hover: function (item: any) {
  //         if (item) {
  //           const [text, weight] = item;
  //           console.log(`${text}: ${weight}`);
  //         }
  //       },
  //     });
  //   }
  // };

  // 监听数据变化，更新词云
  // watch(
  //   () => tabTableData.value,
  //   () => {
  //     nextTick(() => {
  //       // renderWordClouds();
  //     });
  //   },
  //   { deep: true }
  // );

  const tabChange = (value: any) => {
    curTab.value = tabs.find((item) => item.key === value)!.title;
    loadWorkOrderScene(selectedAppName.value, curTab.value);
  };

  // 修改 formatDate 函数
  const formatDate = (date: number | Date | null): string => {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  async function loadWorkOrderScene(appName: string, type: string) {
    dtsDetailloading.value = true
    try {
      const queryParams = {
        appName,
        type,
        pageNo: paginationMissing.page,
        pageSize: paginationMissing.pageSize,
      };
      if (searchForm.createTime) {
        const [start, end] = searchForm.createTime;
        queryParams.startTime = formatDate(start);
        queryParams.endTime = formatDate(end);
      }
      queryParams.fut = searchForm.fut ? searchForm.fut : '';
      queryParams.beta = searchForm.beta ? searchForm.beta : '';
      queryParams.nss = searchForm.nss ? searchForm.nss : '';
      queryParams.ag = searchForm.ag ? searchForm.ag : '';
      const { total, records } = await workOrderScene(queryParams);
      tabTableData.value = records;
      paginationMissing.itemCount = total;
    } catch (error) {
      console.error('Failed to load work order scene data:', error);
    } finally {
      dtsDetailloading.value = false
    }
  }
  const exportLoading = ref(false);
  const handleExport = () => {
    return new Promise((resolve, reject) => {
      const req = new XMLHttpRequest();
      exportLoading.value = true;
      const queryParams = {
        appName: selectedAppName.value,
      };
      req.open(
        'POST',
        `http://${window.location.host}/ewp/management/workOrder/exportAppReport`,
        true
      );
      req.responseType = 'blob';
      req.setRequestHeader('Content-Type', 'application/json');
      req.onload = function () {
        const data = req.response;
        const blob = new Blob([data]);
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = '导出.xlsx';
        a.href = blobUrl;
        a.click();
        exportLoading.value = false;
        resolve(true);
      };
      req.send(JSON.stringify(queryParams));
    });
  };
  // 在组件挂载后初始化词云
  onMounted(() => {
    fetchData();
    // renderWordClouds();
  });

  // 添加表格最大高度计算
  const tableMaxHeight = ref(0);

  // 计算表格高度的函数
  const calculateTableHeight = () => {
    // 减去其他元素的高度(卡片padding、标题、搜索框、分页器等)
    tableMaxHeight.value = window.innerHeight - 340; // 调整减去的高度，为分页器预留空间
  };

  // 监听窗口大小变化
  onMounted(() => {
    calculateTableHeight();
    window.addEventListener('resize', calculateTableHeight);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', calculateTableHeight);
  });
</script>

<style scoped>
    .filter-container {
    margin-bottom: 12px;
  }

  /* 修改表单项样式 */
  :deep(.n-form-item) {
    display: flex;
    margin-right: 0;
    margin-bottom: 18px;
  }

  :deep(.n-form-item-label) {
    width: 90px !important;
    text-align: right;
  }

  :deep(.n-form-item-blank) {
    flex: 1;
  }

  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 24px;
  }

  .app-analysis-container {
    display: flex;
    width: 100%;
    height: calc(100vh - 118px);
    gap: 16px;
    padding: 16px;
    background-color: #f5f7fa;
    box-sizing: border-box;
    overflow: hidden; /* 防止容器出现滚动条 */
  }

  .left-panel {
    width: 40%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 防止面板出现滚动条 */
  }

  .left-panel :deep(.n-card) {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .left-panel :deep(.n-card-content) {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding-bottom: 8px; /* 为分页器增加底部间距 */
  }

  .left-panel :deep(.n-data-table) {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  /* 调整表格容器样式 */
  .left-panel :deep(.n-data-table-wrapper) {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0; /* 重要：允许内容收缩 */
  }

  /* 调整表格主体样式 */
  .left-panel :deep(.n-data-table-base-table) {
    flex: 1;
    min-height: 0; /* 重要：允许内容收缩 */
  }

  /* 确保分页器显示 */
  .left-panel :deep(.n-data-table-pagination) {
    position: relative; /* 确保分页器不会被遮挡 */
    margin-top: 12px;
    padding: 0 8px;
    flex-shrink: 0;
    background-color: #fff; /* 可选：添加背景色 */
    z-index: 1; /* 确保分页器在最上层 */
  }

  /* 美化分页器样式 */
  .left-panel :deep(.n-pagination) {
    justify-content: flex-end; /* 将分页器靠右对齐 */
  }

  .left-panel :deep(.n-pagination .n-pagination-item) {
    min-width: 28px;
    height: 28px;
    line-height: 28px;
  }

  .left-panel :deep(.n-select) {
    width: 100px; /* 调整页码选择器宽度 */
  }

  .right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: 100%;
  }

  .top-list {
    width: 100%;
  }

  .bottom-tabs {
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow-y: auto;
  }

  .bottom-tabs :deep(.n-card) {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .bottom-tabs :deep(.n-card-content) {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .bottom-tabs :deep(.n-tabs) {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .bottom-tabs :deep(.n-tab-pane) {
    flex: 1;
    overflow: auto;
  }

  .bottom-tabs :deep(.n-tabs-content) {
    flex: 1;
    overflow: hidden;
  }

  .bottom-tabs :deep(.n-data-table) {
    height: 100%;
  }

  .app-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .app-icon {
    width: 24px;
    height: 24px;
    border-radius: 4px;
  }

  .trend-up {
    color: #18a058;
  }

  .trend-down {
    color: #d03050;
  }

  .trend {
    margin-left: 8px;
    font-size: 12px;
  }

  :deep(.custom-tabs .n-tabs-nav) {
    padding: 0 16px;
  }

  :deep(.n-card) {
    transition: all 0.3s ease;
    border-radius: 8px;
  }

  :deep(.n-card:hover) {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  }

  :deep(.n-data-table .n-data-table-td) {
    padding: 12px 8px;
  }

  :deep(.n-tag) {
    padding: 0 8px;
  }

  .top-list :deep(.n-data-table-th) {
    text-align: center !important;
    background-color: #f5f7fa;
  }

  .top-list :deep(.n-data-table-td) {
    text-align: center !important;
  }

  .top-list :deep(.n-data-table) {
    --n-merged-th-color: #f5f7fa;
    --n-merged-td-color: #ffffff;
  }

  .top-list :deep(.n-data-table-th.n-data-table-th--last) {
    border-right: 1px solid var(--n-border-color);
  }

  .left-panel :deep(.n-data-table .n-data-table-tr--selected) {
    background-color: var(--n-color-hover) !important;
  }

  .function-tables {
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: 100%;
  }

  .function-tables :deep(.n-card) {
    flex: 1;
  }

  .function-tables :deep(.n-card-header) {
    padding: 12px 18px;
    font-size: 14px;
    font-weight: 500;
  }

  .function-tables :deep(.n-data-table) {
    height: calc(100% - 48px);
  }

  .mt-4 {
    margin-top: 16px;
  }

  .table-cloud-container {
    flex: 1;
  }

  .content-wrapper {
    display: flex;
    gap: 16px;
    height: 100%;
  }

  .table-wrapper {
    flex: 2;
    min-width: 0;
  }

  .word-cloud-wrapper {
    flex: 1;
    min-width: 300px;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .word-cloud-wrapper canvas {
    width: 100%;
    height: 100%;
  }

  .table-cell-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  :deep(.cell-item) {
    padding: 4px 0;
    border-bottom: 1px solid #dfdede !important;
  }

  :deep(.cell-item:last-child) {
    border-bottom: none;
  }

  .solution-item {
    padding: 4px 8px;
    background-color: #f5f7fa;
    border-radius: 4px;
    line-height: 1.5;
    font-size: 13px;
  }

  .export-button {
    margin-right: 16px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .export-button:hover {
    background-color: rgba(24, 160, 88, 0.1);
  }

  :deep(.n-tabs-nav--bar-type) {
    padding-right: 0;
  }

  :deep(.n-tabs-wrapper) {
    flex: 1;
  }

  :deep(.n-tabs-nav__suffix) {
    padding: 0;
  }

  :deep(.v-binder-follower-content) {
    min-width: 200px;
  }
</style>
