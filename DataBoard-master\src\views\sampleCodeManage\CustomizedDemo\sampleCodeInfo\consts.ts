import { FormItemRule, FormRules } from "naive-ui";

export const CODE_SAMPLE_FORM_RULES: FormRules = {
  demoName: {
    required: true,
    trigger: ["blur", "input"],
    message: "请输入名称，例如：健康生活应用",
  },
  demoDescription: {
    required: true,
    trigger: ["blur", "input"],
    message: "请输入描述，例如：介绍UIAbility和自定义组件生命周期",
  },
  selectValue: {
    required: true,
    trigger: ["blur", "change"],
    message: "请选择分类",
  },
  tags: {
    type: "array",
    required: true,
    trigger: ["blur", "change"],
    message: "请选择标签",
    validator(rule, value) {
      if (value.length > 3) {
        value.pop();
        return new Error("最多选三个标签");
      }
      if (value.length == 0) {
        return new Error("最少选一个标签");
      }
      return true;
    },
  },
  apiVersion: {
    required: true,
    trigger: "change",
    message: "请选择 API 版本",
  },
  type: {
    required: true,
    trigger: "change",
    message: "请选择开发语言",
  },
  demoLink: {
    required: true,
    trigger: ["blur", "input"],
    message: "请输入详情链接",
  },
  lineNumber: [
    {
      pattern: /^[1-9]\d*$/,
      message: "请输入正整数",
      trigger: ["input", "blur"],
    },
  ],
  reviewSuggestions: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入评审意见',
    }
  ],
  reviewStatus: [
    {
      required: true,
      trigger: ['blur', 'input'],
      validator(rule: FormItemRule, value: number) {
        if (!value) {
          return new Error('请选择评审状态');
        }
        return true;
      },
    }
  ]
};
