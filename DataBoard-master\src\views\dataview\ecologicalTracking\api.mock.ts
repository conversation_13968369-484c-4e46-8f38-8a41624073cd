import { EcologicalRecord } from './columns';
import {ewpService as http, ewpService as service} from "@/utils/axios";
import {QueryParams} from "@/views/dataview/personManage/staff";
import {getMaxIntNumber} from "@/utils/commonUtils";

// Mock data storage
let mockData: EcologicalRecord[] = [
  {
    id: '1',
    registrationDate: Date.now() - 5 * 24 * 60 * 60 * 1000, // 5 days ago
    ewpOwner: '张三',
    problemBelonging: '三方应用',
    representative: '北京代表处',
    appName: '音乐APP',
    problemDescription: '用户反馈音乐APP在Pura X上播放时有卡顿现象',
    problemLevel: '中',
    source: '热线/互联网',
    volume: 5,
    knowledgeId: 'KID202305001',
    dtsTicket: 'DTS2023050001',
    status: 'TRACKING',
    sourceCloseLoop: '否',
    issueReportTicket: 'IR2023050001',
    progress:
      '2025-05-12进展：【问题根因】应用音频解码效率低下\n【解决方案】正在与应用开发商沟通修复方案',
    currentHandler: '李四',
    domainSupervisor: '李国良 00578453',
    affectedProducts: ['Pura X'],
    planCloseLoopTime: Date.now() + 10 * 24 * 60 * 60 * 1000, // 10 days from now
    dataSource: 'auto',
    deviceModel: 'Pura X',
  },
  {
    id: '2',
    registrationDate: Date.now() - 10 * 24 * 60 * 60 * 1000, // 10 days ago
    ewpOwner: '王五',
    problemBelonging: '系统问题',
    representative: '上海代表处',
    appName: '购物APP',
    problemDescription: '购物APP在nova 14上闪退',
    problemLevel: '高',
    source: 'BetaClub/反馈助手',
    volume: 12,
    knowledgeId: 'KID202305002',
    dtsTicket: 'DTS2023050002',
    status: 'OPEN',
    sourceCloseLoop: '是',
    issueReportTicket: 'IR2023050002',
    progress:
      '2025-05-12进展：【问题根因】应用未适配nova 14系统版本\n【解决方案】等待应用开发商更新适配',
    currentHandler: '赵六',
    domainSupervisor: '李国良 00578453',
    affectedProducts: ['nova 14'],
    planCloseLoopTime: Date.now() + 5 * 24 * 60 * 60 * 1000, // 5 days from now
    dataSource: 'manual',
    deviceModel: 'nova 14',
  },
  {
    id: '3',
    registrationDate: Date.now() - 15 * 24 * 60 * 60 * 1000, // 15 days ago
    ewpOwner: '钱七',
    problemBelonging: '非问题',
    representative: '广州代表处',
    appName: '视频APP',
    problemDescription: 'Pura X用户反馈视频播放画面偶尔有花屏现象',
    problemLevel: '低',
    source: 'VOC预警',
    volume: 3,
    knowledgeId: 'KID202305003',
    dtsTicket: 'DTS2023050003',
    status: 'CLOSED',
    sourceCloseLoop: '是',
    issueReportTicket: 'IR2023050003',
    progress:
      '2025-05-12进展：【问题根因】用户网络问题导致的画质降级\n【解决方案】已告知用户在网络稳定环境下使用',
    currentHandler: '孙八',
    domainSupervisor: '李国良 00578453',
    affectedProducts: ['Pura X'],
    planCloseLoopTime: Date.now() - 2 * 24 * 60 * 60 * 1000, // 2 days ago
    dataSource: 'auto',
    deviceModel: 'Pura X',
  },
];

// Mock API implementations
export const getEcologicalData = async (params: any) => {
  try {
    const response = await service.post('/management/special-item/orders', params);
    return response;
  } catch (error) {
    console.error('Error fetching work order list:', error);
    throw error;
  }
};

export const createEcologicalData = async (data: EcologicalRecord) => {
  // Create new record
  const newRecord = {
    ...data,
  } as EcologicalRecord;

  await service.post('/management/special-item/order', newRecord);

  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000));

  return {
    success: true,
  };
};

export const updateEcologicalData = async (data: Omit<EcologicalRecord, 'id'>) => {
  const newRecord = {
    ...data,
    id: data.id,
  } as EcologicalRecord;

  await service.post('/management/special-item/order', newRecord);

  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000));

  return {
    success: true,
  };
};

export const deleteEcologicalData = async (id: string) => {
  console.log(id, 'id')
  await service.delete(`/management/special-item/order/${id}`);

  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500));
};

export const importPuraXHistoricalData = async (file: File) => {
  // In a real app, this would process and parse the file
  console.log('Importing file:', file.name);

  // Add some mock Pura X historical data
  const historicalData: EcologicalRecord[] = [
    {
      id: 'h1',
      registrationDate: Date.now() - 60 * 24 * 60 * 60 * 1000, // 60 days ago
      ewpOwner: '历史记录1',
      problemBelonging: '三方应用',
      representative: '北京代表处',
      appName: '游戏APP',
      problemDescription: 'Pura X上游戏运行卡顿',
      problemLevel: '低',
      source: '热线/互联网',
      volume: 2,
      knowledgeId: 'KID202302001',
      dtsTicket: 'DTS2023020001',
      status: 'CLOSED',
      sourceCloseLoop: '是',
      issueReportTicket: 'IR2023020001',
      progress: '【问题根因】游戏未针对设备优化\n【解决方案】与开发商合作已优化',
      currentHandler: '历史处理人1',
      domainSupervisor: '李国良 00578453',
      affectedProducts: ['Pura X'],
      planCloseLoopTime: Date.now() - 30 * 24 * 60 * 60 * 1000, // 30 days ago
      dataSource: 'manual',
      deviceModel: 'Pura X',
    },
    {
      id: 'h2',
      registrationDate: Date.now() - 45 * 24 * 60 * 60 * 1000, // 45 days ago
      ewpOwner: '历史记录2',
      problemBelonging: '三方应用',
      representative: '上海代表处',
      appName: '社交APP',
      problemDescription: 'Pura X上社交APP通知延迟',
      problemLevel: '中',
      source: 'VOC预警',
      volume: 7,
      knowledgeId: 'KID202303001',
      dtsTicket: 'DTS2023030001',
      status: 'CLOSED',
      sourceCloseLoop: '是',
      issueReportTicket: 'IR2023030001',
      progress: '【问题根因】应用后台进程被系统限制\n【解决方案】已添加到系统白名单',
      currentHandler: '历史处理人2',
      domainSupervisor: '李国良 00578453',
      affectedProducts: ['Pura X'],
      planCloseLoopTime: Date.now() - 20 * 24 * 60 * 60 * 1000, // 20 days ago
      dataSource: 'manual',
      deviceModel: 'Pura X',
    },
  ];

  // Add historical data to mock data
  mockData = [...mockData, ...historicalData];

  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000));

  return {
    success: true,
    message: '导入成功，共导入2条历史记录',
  };
};

export const syncPuraXAutoDetectedIssues = async (params:string) => {
  const response = await service.post(`/management/special-item/latest-order?device=${params}`);

  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000));

  return {
    success: true,
    message: `同步成功，发现${response.data}个新问题`,
  };
};

export async function getItemsFromAppName(appName: string): Promise<any> {
  try {
    const params = {
      pageNo: 1,
      pageSize: 10,
      appName: appName,
      sortField: 'riskScore',
      sortOrder: 'desc',
      riskScoreGe: 0,
    };
    let showArr:string[] = [];
    const response = await service.post('/management/appInfo/query', params);
    const getPM = await service.post('/management/appInfo/info', params);
    if (response.records.length > 0 && getPM.records.length > 0) {
      showArr.push(getPM.records[0].ewpOwner);
      showArr.push(response.records[0].represent);
      showArr.push(getPM.records[0].appPM);
      return showArr;
    }
    return '';
  } catch (error) {
    console.error(error);
    return '';
  }
}

export function getStaffList(data: QueryParams) {
  return http.request({
    url: '/management/staff/users',
    method: 'POST',
    data,
  });
}

export const getPersonOptionsOnlyName = async (tag: string) => {
  const res = await getStaffList({
    pageNo: 1,
    pageSize: getMaxIntNumber(),
    tagIds: tag,
  });
  const accounts = [];
  res.records.forEach((item) => {
    const option = {
      label: `${item.name} ${item.id}`,
      value: `${item.name} ${item.id}`,
    };
    accounts.push(option);
  });
  return accounts;
};
