<template>
  <div class="chart-container">
    <div id="fut-chart"></div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted } from "vue"
import * as echarts from "echarts"

import { getData, getEchartOptions } from "./chart"



onMounted(async () => {
    const rowData = await getData()
    console.log('rowData', rowData)
    const futChartOptions = getEchartOptions(rowData)
    const futChart = echarts.init(document.getElementById('fut-chart'));
    futChart.setOption(futChartOptions)
})
</script>

<style scoped>
.chart-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

#fut-chart {
    height: 300px;
    background-color: #FFFFFF;
}
</style>
