import { h, ref } from 'vue';
import { NTag } from 'naive-ui';
import { BasicColumn } from '@/components/Table';
import { getPersonOptionsOnlyName } from '@/views/dataview/personManage/staffCommonUtils';
import { DTS_HANDLE_TAG } from '@/views/dataview/personManage/tagContant';

export interface ListData {
  id: string;
  orderId: string;
  appName: string;
  ewpOwner: string;
  severity: string;
  status: string;
  currentHandler: string;
  irOrderId: string;
  irOrderStatus: string;
  description: string;
  createTime: string;
  creator: string;
  // ... 其他字段可以根据需要添加
}

// 工单解决状态分布相关配置已迁移到 components/bugStatusConfig.ts

export const DTSStatusColumns: BasicColumn<ListData>[] = [
  {
    title: '提单数',
    key: 'total',
  },
  {
    title: '待分析定界',
    key: 'toBeAnalyzed',
  },
  {
    title: '待锁定计划',
    key: 'toBeLocked',
  },
  {
    title: '已解决/已锁定计划',
    key: 'lockedOrClosed',
  },
];

// 定义查询表单项
export const searchFormItems = [
  {
    field: 'orderId',
    label: '问题单号',
    component: 'Input',
  },
  {
    field: 'appName',
    label: '应用名称',
    component: 'Input',
  },
  {
    field: 'ewpOwner',
    label: '应用责任人',
    component: 'Input',
  },
  {
    field: 'severity',
    label: '严重程度',
    component: 'Select',
    componentProps: {
      options: [
        { label: '严重', value: 'high' },
        { label: '中等', value: 'medium' },
        { label: '低', value: 'low' },
      ],
    },
  },
  {
    field: 'status',
    label: 'DTS单状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '已关闭', value: 'close' },
        { label: '处理中', value: 'processing' },
        { label: '已解决', value: 'resolved' },
      ],
    },
  },
  {
    field: 'currentHandler',
    label: '当前处理人',
    component: 'Input',
  },
  {
    field: 'irOrderStatus',
    label: 'IR单状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '已关闭', value: 'close' },
        { label: '处理中', value: 'processing' },
        { label: '已解决', value: 'resolved' },
      ],
    },
  },
];

// 定义查询表单项
export const searchFormItemsDTSResolveStates = ref([
  {
    field: 'submitTime',
    label: '时间',
    component: 'DateRangePicker',
  },
  {
    field: 'top',
    label: 'TOP2000',
    component: 'Select',
    componentProps: {
      options: [{ label: 'TOP2000', value: 'TOP2000' }],
    },
  },
]);

// 工单解决状态分布查询配置已迁移到 components/bugStatusConfig.ts

// 定义DTS单状态分查询表单项
export const searchFormItemsDTSdistribution = ref([
  {
    field: 'submitTime',
    label: '时间',
    component: 'Date',
  },
]);

// 定义DTS单状态分查询表单项
export const searchFormItemsFUT = ref([
  {
    field: 'submitTime',
    label: '时间',
    component: 'Date',
  },
]);

//获取用户信息
export const employeesOptions = await getPersonOptionsOnlyName(DTS_HANDLE_TAG);

// 定义日工作量统计查询表单项
export const searchDailyWorkStatistics = ref([
  {
    field: 'date',
    label: '日期',
    component: 'DateRangePicker',
  },
  {
    field: 'name',
    label: '姓名',
    component: 'Select',
    componentProps: {
      options: employeesOptions,
    },
  },
]);

export const futColumns: BasicColumn[] = [
  {
    title: '日期',
    key: 'date',
  },
  {
    title: '总量',
    key: 'total',
  },
  {
    title: '待分析',
    key: 'init',
  },
  {
    title: '待提单',
    key: 'analyzedDts',
  },
  {
    title: '已挂起',
    key: 'analyzedPending',
  },
  {
    title: '问题定界',
    key: 'open',
  },
  {
    title: '待锁定',
    key: 'unlocked',
  },
  {
    title: '待修复',
    key: 'tracking',
  },
  {
    title: '待回归',
    key: 'regression',
  },
  {
    title: '已闭环',
    key: 'closed',
  },
];
