import { ewpService as service } from '@/utils/axios';

export async function getQuestions(params: any): Promise<any> {
  try {
    const response = await service.post('/management/service/orders', params);

    return response;
  } catch (error) {
    throw error;
  }
}

export async function addQuestion(params: any): Promise<any> {
  try {
    const response = await service.post('/management/service/order', params);

    return response;
  } catch (error) {
    throw error;
  }
}

export async function deleteQuestion(id: number): Promise<any> {
  try {
    const response = await service.delete(`/management/service/order/${id}`);
    return response;
  } catch (error) {
    throw error;
  }
}

export async function syncQuestion(id: number): Promise<any> {
  try {
    const response = await service.post(`/management/service/order/${id}/replication`);
    return response;
  } catch (error) {
    throw error;
  }
}

export async function getUsernameFromAppName(appName: string): Promise<any> {
  try {
    const params = {
      pageNo: 1,
      pageSize: 10,
      appName: appName,
      sortField: 'riskScore',
      sortOrder: 'desc',
      riskScoreGe: 0,
    };
    const getPM = await service.post('/management/appInfo/info', params);
    if (getPM.records.length > 0) {
      return getPM.records[0];
    }
    return '';
  } catch (error) {
    console.error(error);
    return '';
  }
}

export async function getInfoFromOrderId(appName: string): Promise<any> {
  try {
    const params = {
      pageNo: 1,
      pageSize: 10,
      appName: appName,
      sortField: 'riskScore',
      sortOrder: 'desc',
      riskScoreGe: 0,
    };
    const response = await service.post('/management/appInfo/query', params);
    if (response.records.length > 0) {
      return response.records[0];
    }
    return '';
  } catch (error) {
    console.error(error);
    return '';
  }
}
