import { getModules } from '@/api/dataview/myOrder';
import { ref } from 'vue';

export interface KnowledgeItem {
  title: string;
  description: string;
  link: string;
}

export const moduleOptions = ref([]);
const fecthModules = async () => {
  const res = await getModules();
  moduleOptions.value = res.map((item) => {
    return {
      label: item,
      value: item,
    };
  });
};
fecthModules();
export const resOptions = [
  { label: '常规模板', value: '常规模板' },
  { label: '需求类', value: '需求类' },
  { label: '新版本已解决', value: '新版本已解决' },
  { label: '非问题', value: '非问题' },
  { label: '没有复现条件', value: '没有复现条件' },
  { label: '无法复现', value: '无法复现' },
  { label: '重复问题', value: '重复问题' },
  { label: '上架问题', value: '上架问题' },
];
export const kownOptions = [
  { label: '完全匹配', value: '6' },
  { label: '仅场景匹配', value: '7' },
];
export const nextOptions = [
  { label: '定界', value: '定界' },
  { label: '锁定计划', value: '锁定计划' },
];

export const attributionOptions = [
  { label: '系统问题', value: '系统问题' },
  { label: '三方应用', value: '三方应用' },
  { label: '非问题', value: '非问题' },
];

export const closeOptions = [
  { label: '问题解决关闭', value: '问题解决关闭' },
  { label: '重复问题关闭', value: '重复问题关闭' },
  { label: '非问题关闭', value: '非问题关闭' },
  { label: 'CCB评审不解决关闭', value: 'CCB评审不解决关闭' },
];

export const getProcessConfigList = (level: string) => {
  // 根据问题严重程度返回不同的流程配置
  if (level === '严重') {
    return [
      { title: '问题定界', key: 'delimit', defauleExpendedNames: [] },
      { title: '锁定计划', key: 'lock', defauleExpendedNames: [] },
      { title: '审核修改', key: 'review', defauleExpendedNames: [] },
    ];
  } else {
    return [
      { title: '问题定界', key: 'delimit', defauleExpendedNames: [] },
      { title: '锁定计划', key: 'lock', defauleExpendedNames: [] },
      { title: '审核修改', key: 'review', defauleExpendedNames: [] },
    ];
  }
};

export const getInfoConfig = (info) => {
  const res = getDefaultInfo();
  return res;
};

export const getReproductionList = (info) => {
  const reproductionList = [
    {
      label: '设备类型',
      value: 'CYB-AL00',
    },
    {
      label: '系统版本',
      value: '3.0.0.111(C00E110R4P7log)',
    },
    {
      label: '应用名称',
      value: '天天基金',
    },
    {
      label: '应用版本',
      value: '1.0.3',
    },
    {
      label: '场景名称',
      value: '放大后无法拖动进度条',
    },
    {
      label: '问题描述',
      value: '天天基金，放大后无法拖动进度条',
    },
  ];
  const res = reproductionList.map((item) => {
    return {
      label: item.label,
      value: item.value,
    };
  });
  return res;
};

export const similarIssues = [
  {
    id: 1,
    dtsId: 'DTS202403250001',
    similarity: 95,
    title: '系统登录异常',
    status: '已解决',
  },
  {
    id: 2,
    dtsId: 'DTS202403250002',
    similarity: 85,
    title: '数据加载失败',
    status: '处理中',
  },
  // ... 更多数据
];

export const mockRecommendList: KnowledgeItem[] = [
  {
    title: '常见问题解决方案',
    description: '包含常见问题的详细解决步骤和最佳实践',
    link: 'https://example.com/solutions',
  },
  {
    title: '操作指南',
    description: '系统操作的详细指导文档',
    link: 'https://example.com/guide',
  },
  // ... 其他项目
];
export function getDefaultInfo() {
  return {
    id: 42,
    warnTime: '2024-11-20 08:00:00',
    module: '4796,生态丰富度5165',
    appName: '千牛',
    desc: '千牛上架诉求',
    level: 'A',
    opinionVolume: 52,
    status: '2',
    progress:
      '2/24进展：\n【根因分析】：千牛应用未上架\n【舆情进展】：作战平台里程碑计划已锁定，当前计划331完成核心功能版本上架，伙伴正在和商家确认功能需求优先级，331上架仍存在一定风险\n【下一步计划】：定期与解决方案确认331交付进展与风险\n2/17进展：\n【根因分析】：千牛应用未上架\n【舆情进展】：作战平台里程碑计划已锁定，当前计划331完成核心功能版本上架，伙伴正在和商家确认功能需求优先级，331上架仍存在一定风险\n【下一步计划】：定期与解决方案确认331交付进展与风险\n2/13进展：作战平台KCP1已达成\n2/12进展：BD反馈开发者已经启动开发了，但还没锁定计划，作战平台上还处于启动状态中\n1/23进展：伙伴内部产品、开发已评估完成，当前正在伙伴高层进行决策，具体的决策层级伙伴未透露，应该为负责千牛产品线的总裁。预计本周内会有结论\n1/10进展：伙伴意愿已达成，工作量评估已完成，正在向上汇报确认开发优先级和排期，BD下周确认伙伴结论\n12/31进展：1月9约伙伴线下提拉，1月9号后确认\n12/27进展：还在评估中，下周再和领导汇报\n12/26进展：已和BD确认，暂无进展\n12/24进展：还未决策完成，12/27再次确认进展\n12/20进展：千牛的评估已经完成向产品总监汇报，产品总监支持，但是决策还需要更高层级，正在往上一层汇报决策中\n12/19进展：未达成合作意愿，伙伴的组织调整刚完成，启动分析，待其完成内部汇报\n11/25进展：问卷还未提供\n11/21进展：已传递用户原声给伙伴，并引导进线用户直接联系伙伴；\n11/20进展：当前四组一队正在组织与阿里高层沟通推动',
    source: '3',
    planTime: '2025-03-31 00:00:00',
    guarantorAccount: null,
    guarantor: '邵羽安/s00605429',
    ownerAccount: null,
    owner: '徐寿红/x00453611',
    moduleOwnerAccount: null,
    moduleOwner: '赵成林/z00638528',
    dtsNum: 'DTS2024111828087',
    dtsStatus: 'CMO归档',
    product: 'ALL',
    informer: '郭晶/g30038840',
    knowledgeId: 'zh-cn16025191',
    knowledgeLink:
      'https://consumer-tkb.huawei.com/weknow/index.html#!a/detail.html?contextNo=zh-cn16025191',
    issueType: 0,
    createTime: '2024-12-05 22:30:26',
    slaTime: '2024-12-04 08:00:00',
    isOverdue: true,
    overdueType: '计划锁定超期',
    closeTime: null,
    locateTime: null,
    planLockTime: '2024-12-04 08:00:00',
    textarea: null,
    isOverduePlanLock: true,
  };
}

// 添加原因分析模板配置
export const analysisTemplates = {
  常规模板: `【定界过程】
【分析结论】
【修改建议】`,
};
