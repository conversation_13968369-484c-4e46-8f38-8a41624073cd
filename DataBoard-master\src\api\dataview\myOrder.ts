import { ewpService as service } from '@/utils/axios';

export async function delimit(params: any): Promise<any> {
  try {
    const response = await service.post('/management/ewpOrder/delimit', params);

    return response;
  } catch (error) {
    throw error;
  }
}

export async function saveDelimit(params: any): Promise<any> {
  try {
    const response = await service.post('/management/ewpOrder/delimitSave', params);

    return response;
  } catch (error) {
    throw error;
  }
}

export async function lock(params: any): Promise<any> {
  try {
    const response = await service.post('/management/ewpOrder/lock', params);

    return response;
  } catch (error) {
    throw error;
  }
}

export async function delimitView(dtsOrder: any): Promise<any> {
  try {
    const response = await service.get(`/management/ewpOrder/delimitView?dtsOrder=${dtsOrder} `);

    return response;
  } catch (error) {
    throw error;
  }
}

export async function lockView(dtsOrder: any): Promise<any> {
  try {
    const response = await service.get(`/management/ewpOrder/lockView?dtsOrder=${dtsOrder} `);

    return response;
  } catch (error) {
    throw error;
  }
}
export async function getDataList(link: string, params: object): Promise<any> {
  try {
    const response = await service.post(`/management/${link}`, params);
    return response;
  } catch (error) {
    console.error('Error fetching work order list:', error);
    throw error;
  }
}
export async function addWishOrder(params: object): Promise<any> {
  try {
    const response = await service.post(`/management/ewpOrder/addWishOrder`, params);
    return response;
  } catch (error) {
    console.error('Error fetching work order list:', error);
    throw error;
  }
}

export async function getModules(): Promise<any> {
  try {
    const response = await service.get(`/management/ewpOrder/getModules`);

    return response;
  } catch (error) {
    throw error;
  }
}

export async function getProcessLog(dtsOrder): Promise<any> {
  try {
    const response = await service.get(`/management/ewpOrder/getProcessLog?dtsOrder=${dtsOrder}`);

    return response;
  } catch (error) {
    throw error;
  }
}

export function reviewOrder(data: any) {
  return service.post('/management/ewpOrder/review/process', data);
}

export function reviewView(orderId: string) {
  return service.get('/management/ewpOrder/review/query', { params: { dtsOrder: orderId } });
}

/**
 * 获取待办数量
 * @param data { currentHandler: string } 去字母的工号
 */
export function getToDoCount(data: any) {
  return service.post('/management/workOrder/getCount', data);
}
