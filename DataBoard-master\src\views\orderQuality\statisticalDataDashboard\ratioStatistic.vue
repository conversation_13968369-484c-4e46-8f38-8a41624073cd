<template>
  <div style="margin-top: 12px">
    <h1 style="font-size: 24px; color: #303133">数据分析表</h1>
    <n-card style="margin-top: 12px">
      <!-- 搜索 -->
      <div style="display: flex; align-items: center; justify-content: space-between">
        <div style="display: flex; align-items: center">
          工单闭环时间:
          <n-date-picker
            v-model:value="searchModel.createTime"
            type="daterange"
            @confirm="onConfirm"
            @clear="onClear"
            clearable
            style="margin-left: 20px"
          />
          <n-alert type="info" v-if="!searchModel.createTime" closable style="margin-left: 12px"
            >数据创建时间为2025年01月01日至今</n-alert
          >
        </div>
        <div>
          <n-button secondary strong type="primary" @click="handleExportList"> 导出 </n-button>
        </div>
      </div>
      <div class="table-wrapper" style="overflow-x: auto">
        <n-table
          :loading="loading"
          style="margin-top: 20px; width: 80%; overflow-x: auto"
          :bordered="false"
          :single-line="false"
          striped
        >
          <thead>
            <tr>
              <th>关键领域</th>
              <th>1组</th>
              <th>未提交审核比例</th>
              <th>未提交攻关比例</th>
              <th>L2.5闭环比例</th>
              <th>走单规范比例</th>
              <th>3组</th>
              <th>未提交审核比例</th>
              <th>未提交攻关比例</th>
              <th>L2.5闭环比例</th>
              <th>走单规范比例</th>
              <th>4组</th>
              <th>未提交审核比例</th>
              <th>未提交攻关比例</th>
              <th>L2.5闭环比例</th>
              <th>走单规范比例</th>
              <th>5组</th>
              <th>未提交审核比例</th>
              <th>未提交攻关比例</th>
              <th>L2.5闭环比例</th>
              <th>走单规范比例</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="item in FIRED_LIST" :key="item">
              <td style="white-space: nowrap">{{ item }}</td>
              <td style="white-space: nowrap">{{ formData[item]?.groupOne.leaderName }}</td>
              <td>{{ formData[item]?.groupOne.unReviewedRatio }}</td>
              <td>{{ formData[item]?.groupOne.unResolvedRatio }}</td>
              <td>{{ formData[item]?.groupOne.resolvedRatio }}</td>
              <td>{{ formData[item]?.groupOne.properRatio }}</td>

              <td style="white-space: nowrap">{{ formData[item]?.groupThree.leaderName }}</td>
              <td>{{ formData[item]?.groupThree.unReviewedRatio }}</td>
              <td>{{ formData[item]?.groupThree.unResolvedRatio }}</td>
              <td>{{ formData[item]?.groupThree.resolvedRatio }}</td>
              <td>{{ formData[item]?.groupThree.properRatio }}</td>

              <td style="white-space: nowrap">{{ formData[item]?.groupFour.leaderName }}</td>
              <td>{{ formData[item]?.groupFour.unReviewedRatio }}</td>
              <td>{{ formData[item]?.groupFour.unResolvedRatio }}</td>
              <td>{{ formData[item]?.groupFour.resolvedRatio }}</td>
              <td>{{ formData[item]?.groupFour.properRatio }}</td>

              <td style="white-space: nowrap">{{ formData[item]?.groupFive.leaderName }}</td>
              <td>{{ formData[item]?.groupFive.unReviewedRatio }}</td>
              <td>{{ formData[item]?.groupFive.unResolvedRatio }}</td>
              <td>{{ formData[item]?.groupFive.resolvedRatio }}</td>
              <td>{{ formData[item]?.groupFive.properRatio }}</td>
            </tr>
          </tbody>
        </n-table>
      </div>
    </n-card>
    <!-- </n-card> -->
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { FIRED_LIST, formatTime } from './index';
  import { getRatioStatistic, exportRatioStatistic } from './const';
  const formData = ref({});
  const handleExportList = () => {
    let params = {
      startDate: searchModel.value?.createTime
        ? formatTime(searchModel.value.createTime[0])
        : formatTime('2025-01-01'),
      endDate: searchModel.value?.createTime
        ? formatTime(searchModel.value.createTime[1])
        : formatTime(new Date()),
    };
    exportRatioStatistic(params);
  };
  const searchModel = ref({
    createTime: [new Date().setMonth(new Date().getMonth() - 1), new Date()],
  });
  const loading = ref(false);
  const data = ref([]);
  onMounted(() => {
    search(formatTime(new Date().setMonth(new Date().getMonth() - 1)), formatTime(new Date()));
  });
  const onConfirm = async (v: number | number[] | null) => {
    if (v) {
      search(formatTime(v[0]), formatTime(v[1]));
    } else {
      // 清空后时间默认为2025-01-01-至今
      search(formatTime('2025-01-01'), formatTime(new Date()));
    }
  };
  const onClear = async (v: number | number[] | null) => {
    search(formatTime('2025-01-01'), formatTime(new Date()));
  };
  const search = async (startDate, endDate) => {
    loading.value = true;
    try {
      let res = await getRatioStatistic({
        startDate: startDate,
        endDate: endDate,
      });

      FIRED_LIST.forEach((item) => {
        formData.value[item] = [];
      });
      const levelMAP = {
        '1组': 'groupOne',
        '3组': 'groupThree',
        '4组': 'groupFour',
        '5组': 'groupFive',
      };
      if (res.status === '200') {
        FIRED_LIST.forEach((item) => {
          formData.value[item] = {};
        });
        Object.keys(res.data).forEach((key) => {
          const name = key.split('_')[0];
          const level = key.split('_')[1];
          if (levelMAP[level]) {
            formData.value[name][levelMAP[level]] = res.data[key];
          }
        });
      }
    } catch (e) {}
    loading.value = false;
  };
</script>

<style lang="less" scoped>
  table {
    border: 1px solid rgba(250, 250, 252, 1);
  }
  table tr th {
    background-color: rgba(250, 250, 252, 1);
    height: 30px;
    padding: 0 4px;
    text-align: center;
  }
  table tr td {
    padding: 2px 10px;
    text-align: center;
    font-size: 14px;
  }
</style>
