import type { RouteRecordRaw } from 'vue-router';
import { isNavigationFailure, Router } from 'vue-router';
import { useUser } from '@/store/modules/user';
import { useAsyncRoute } from '@/store/modules/asyncRoute';
import { PageEnum } from '@/enums/pageEnum';
import { ErrorPageRoute } from '@/router/base';
import { isDevMode } from '@/utils/env';
import { UserRoleEnum } from '@/enums/UserRoleEnum';
import { storage } from '@/utils/Storage';
import { REDIRECT_PATH } from '@/store/mutation-types';

const whitePathList: PageEnum[] = [PageEnum.UNAUTHORIZED_PAGE]; // no redirect whitelist

export function createRouterGuards(router: Router) {
  const userStore = useUser();
  const asyncRouteStore = useAsyncRoute();
  router.beforeEach(async (to, from) => {
    const Loading = window['$loading'] || null;
    Loading && Loading.start();

    if ((to.path as PageEnum) === PageEnum.BASE_LOGIN) {
      return isDevMode() ? true : { path: PageEnum.BASE_HOME };
    }

    // Whitelist can be directly entered
    if (whitePathList.includes(to.path as PageEnum)) {
      return true;
    }

    const isLoggedIn = await userStore.checkIsLoggedIn();
    if (!isLoggedIn) {
      if (isDevMode()) {
        return { path: PageEnum.BASE_LOGIN };
      } else {
        userStore.w3Login();
        return false;
      }
    }

    const userInfo = await userStore.getInfo();
    if (!userInfo) {
      return { path: PageEnum.UNAUTHORIZED_PAGE };
    }

    // 登录重定向，在logout登出时存储redirectPath
    const storageRedirectPath = storage.getCookie(REDIRECT_PATH);
    const path = decodeURIComponent(storageRedirectPath);
    console.log('storageRedirectPath：', path);
    if (storageRedirectPath) {
      storage.removeCookie(REDIRECT_PATH); // 重定向完成后清空
      window.location.href = path;
      return false;
    }

    if (asyncRouteStore.getIsDynamicRouteAdded) {
      return true;
    }

    const routes = await asyncRouteStore.generateRoutes(userInfo);

    // 动态添加可访问路由表
    routes.forEach((item) => {
      router.addRoute(item as unknown as RouteRecordRaw);
    });

    //添加404
    const isErrorPage = router.getRoutes().findIndex((item) => item.name === ErrorPageRoute.name);
    if (isErrorPage === -1) {
      router.addRoute(ErrorPageRoute as unknown as RouteRecordRaw);
    }

    const redirectPath = (from.query.redirect || to.path) as string;
    const redirect = decodeURIComponent(redirectPath);
    const nextData = to.path === redirect ? { ...to, replace: true } : { path: redirect };
    asyncRouteStore.setDynamicRouteAdded(true);
    Loading && Loading.finish();

    /**
     * 如果只有【仅查看舆情角色】跳转到主控台
     */
    const isWorkplacePage = router
      .getRoutes()
      .findIndex((item) => item.path === PageEnum.BASE_HOME);
    if (userInfo.roles.includes(UserRoleEnum.ONLY_VIEW_PUBLIC_OPINION) && isWorkplacePage === -1) {
      const params: string[] = [];
      Object.keys(to.query).forEach((key) => {
        params.push(`${key}=${to.query[key]}`);
      });
      const path = `/listingProtection/myTodo?${params.join('&')}`;
      console.log(`testLog: to`, to);
      console.log(`testlog: fullPath `, to.fullPath);
      console.log(`testlog: path `, path);
      console.log('test log: params', params);
      return { path, query: to.query };
    }

    return nextData;
  });

  router.afterEach((to, _, failure) => {
    document.title = (to?.meta?.title as string) || document.title;
    if (isNavigationFailure(failure)) {
      //console.log('failed navigation', failure)
    }
    const asyncRouteStore = useAsyncRoute();
    // 在这里设置需要缓存的组件名称
    const keepAliveComponents = asyncRouteStore.keepAliveComponents;
    const currentComName: any = to.matched.find((item) => item.name == to.name)?.name;
    if (currentComName && !keepAliveComponents.includes(currentComName) && to.meta?.keepAlive) {
      // 需要缓存的组件
      keepAliveComponents.push(currentComName);
    } else if (!to.meta?.keepAlive || to.name == 'Redirect') {
      // 不需要缓存的组件
      const index = asyncRouteStore.keepAliveComponents.findIndex((name) => name == currentComName);
      if (index != -1) {
        keepAliveComponents.splice(index, 1);
      }
    }
    asyncRouteStore.setKeepAliveComponents(keepAliveComponents);
    const Loading = window['$loading'] || null;
    Loading && Loading.finish();
  });

  router.onError((error) => {
    console.log(error, '路由错误');
  });
}
