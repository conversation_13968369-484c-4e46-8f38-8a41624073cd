import axios, { ewpService as service } from '@/utils/axios';
import { SearchParams, Top38Item } from '@/views/dataview/top38/types';

enum API {
  LIST = '/management/top38/query',
  DETAIL = '/api/dataview/top38/detail',
  CREATE = '/api/dataview/top38/create',
  UPDATE = '/api/dataview/top38/update',
  DELETE = '/api/dataview/top38/delete',
}

// 获取TOP38问题列表
export const getTop38List = async (params: any) => {
  return await service.post<Top38Item[]>(API.LIST, params, {
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
    },
  });
};

// 获取TOP38问题详情
export const getTop38Detail = (id: string | number) => {
  return axios.request<Top38Item>({
    url: API.DETAIL,
    method: 'get',
    params: { id },
  });
};

// 创建TOP38问题
export const createTop38 = (data: Top38Item) => {
  return axios.request({
    url: API.CREATE,
    method: 'post',
    data,
  });
};

// 更新TOP38问题
export const updateTop38 = (data: Top38Item) => {
  return axios.request({
    url: API.UPDATE,
    method: 'put',
    data,
  });
};

// 删除TOP38问题
export const deleteTop38 = (id: string | number) => {
  return axios.request({
    url: API.DELETE,
    method: 'delete',
    params: { id },
  });
};
