<template>
  <div>
    <n-space vertical>
      <n-card>
        <n-space>
          <n-button secondary strong type="warning" @click="refreshData()">
            <template #icon>
              <n-icon>
                <Refresh />
              </n-icon>
            </template>
            刷新
          </n-button>
          <n-button secondary strong type="primary" @click="showListAddModal = true">
            导入
          </n-button>
          <n-button
            secondary
            strong
            type="primary"
            @click="
              showModal = true;
              isAdd = true;
            "
          >
            <template #icon>
              <n-icon>
                <Add />
              </n-icon>
            </template>
            添加
          </n-button>
        </n-space>
        <n-data-table
          :columns="columns"
          :data="data"
          :pagination="pagination"
          :loading="loading"
          style="margin-top: 20px"
        />
      </n-card>
    </n-space>

    <n-modal v-model:show="showListAddModal">
      <n-card
        style="width: 600px"
        title="批量导入"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-button text @click="handleDownload" style="margin-bottom: 20px"
          >点击下载导入模板</n-button
        >
        <n-upload
          action="#"
          :custom-request="customRequest"
          :multiple="true"
          :default-file-list="fileList"
          accept=".xls,.xlsx,"
          :max="1"
          directory-dnd
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <Add />
              </n-icon>
            </div>
            <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
          </n-upload-dragger>
        </n-upload>
      </n-card>
    </n-modal>

    <n-modal v-model:show="showModal" :on-after-leave="handleAfterLeave">
      <n-card
        :style="{ width: '1200px' }"
        :title="isAdd ? '新增' : '编辑'"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <template #header-extra> </template>
        <n-form
          ref="formRef"
          :model="model"
          :rules="rules"
          label-placement="left"
          label-width="100"
          require-mark-placement="right-hanging"
          size="medium"
        >
          <n-grid :cols="24" :x-gap="24">
            <n-form-item-gi :span="12" label="日期" path="date">
              <n-input :disabled="!isAdd" v-model:value="model.date" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="合格率" path="qualificationRate">
              <n-input v-model:value="model.qualificationRate" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="当期不合格工单数" path="currentPeriodDefectiveOrders">
              <n-input v-model:value="model.currentPeriodDefectiveOrders" />
            </n-form-item-gi>
          </n-grid>
        </n-form>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" @click="handleSubmint()"> 确认 </n-button>
            <n-button secondary strong type="error" @click="showModal = false"> 取消 </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>
<script lang="ts" setup>
  import { ref, reactive, h, onMounted } from 'vue';
  import { Add, Refresh, Close, GridOutline } from '@vicons/ionicons5';
  import { NIcon, useMessage, useDialog, NButton, NFlex } from 'naive-ui';
  import { paginationReactive, defaultModel } from './index';
  import {
    orderPassRateAdd,
    orderPassRateDelete,
    orderPassRateUpdate,
    orderPassRateQuery,
    orderPassRateImport,
    orderPassRateDownloadTemplate,
  } from '@/api/dataview/orderQualityManager';
  import { cloneDeep, debounce } from 'lodash-es';

  const formRef = ref();
  const isAdd = ref(true);
  const showModal = ref(false);
  const showListAddModal = ref(false);
  const fileList = ref([]);
  const loading = ref(false);
  const message = useMessage();
  const dialog = useDialog();
  const sampleDevidesUsageRegistrationColumn = [
    {
      title: '日期',
      key: 'date',
      width: 200,
      resizable: true,
      sorter: 'default',
    },
    {
      title: '当期不合格工单数',
      key: 'currentPeriodDefectiveOrders',
      width: 200,
      resizable: true,
      sorter: 'default',
    },
    {
      title: '合格率',
      key: 'qualificationRate',
      width: 200,
      resizable: true,
      sorter: 'default',
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 100,
      render: (row) => {
        return [
          h(NFlex, { wrap: false }, () => [
            h(
              NButton,
              {
                strong: true,
                tertiary: true,
                type: 'info',
                size: 'medium',
                onClick: () => {
                  model.value = cloneDeep(row);
                  isAdd.value = false;
                  showModal.value = true;
                },
              },
              {
                default: () => '编辑',
              }
            ),
            h(
              NButton,
              {
                strong: true,
                tertiary: true,
                type: 'error',
                size: 'medium',
                onClick: () => {
                  remove(row);
                },
              },
              {
                default: () => '删除',
              }
            ),
          ]),
        ];
      },
    },
  ];
  const columns = ref(sampleDevidesUsageRegistrationColumn);
  paginationReactive.onChange = (page: number) => {
    paginationReactive.page = page;
  };
  paginationReactive.onUpdatePageSize = (pageSize: number) => {
    paginationReactive.pageSize = pageSize;
    paginationReactive.page = 1;
  };
  const pagination = ref(paginationReactive);
  const rules = ref({
    date: {
      required: true,
      message: '请输入日期',
      trigger: 'blur',
    },
  });
  const data = ref([]);
  let modelReactive = reactive(cloneDeep(defaultModel));
  const model = ref(modelReactive);
  const handleAfterLeave = () => {
    modelReactive = reactive(cloneDeep(defaultModel));
    model.value = modelReactive;
  };

  const remove = (row) => {
    dialog.info({
      title: '提示',
      content: '你确定要删除吗？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        let res = await orderPassRateDelete({ date: row.date });
        if (res.status) {
          message.success('删除成功');
        }
        search();
      },
      onNegativeClick: () => {},
    });
  };

  // 查询表格数据
  const search = debounce(async () => {
    loading.value = true;
    try {
      let res = await orderPassRateQuery({});
      if (res.status === '200') {
        data.value = res?.data?.samplingQualificationRateTable || [];
        paginationReactive.itemCount = res?.data?.count || 0;
      }
    } catch (e) {}
    loading.value = false;
  }, 300);

  const refreshData = () => {
    search();
  };

  const handleSubmint = debounce(async () => {
    formRef.value.validate(async (errors) => {
      if (!errors) {
        let data = JSON.parse(JSON.stringify(model.value));
        try {
          var res = isAdd.value ? await orderPassRateAdd(data) : await orderPassRateUpdate(data);
          if (res.status == '200') {
            message.success('提交成功');
            showModal.value = false;
            search();
          }
        } catch (err) {
          message.error(err.message);
        }
      }
    });
  }, 300);
  //上传
  const customRequest = async ({ file, onError }: UploadCustomRequestOptions) => {
    const formData = new FormData();
    formData.append('file', file.file as File);
    //上传接口
    try {
      let res = await orderPassRateImport(formData);
      if (res.status == '200') {
        showListAddModal.value = false;
        search();
        message.success('导入成功');
      } else {
        fileList.value = [];
      }
    } catch (err) {
      fileList.value = [];
      onError();
    }
  };
  //下载
  const handleDownload = async () => {
    orderPassRateDownloadTemplate()
      .then((res) => {
        if (!res) {
          message.error('下载失败！');
        }
      })
      .catch((e) => {
        message.error(`下载失败，原因：${e?.response?.data?.error}`);
      });
  };
  onMounted(() => {
    refreshData();
  });
</script>
