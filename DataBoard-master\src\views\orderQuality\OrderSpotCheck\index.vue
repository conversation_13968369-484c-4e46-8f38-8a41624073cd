<template>
  <div>
    <n-card style="margin-bottom: 12px">
      <n-form label-placement="left" label-width="100px" label-align="left" v-if="collapse">
        <n-grid x-gap="20" :cols="4">
          <n-gi>
            <n-form-item label="严重级别">
              <n-select
                v-model:value="filterFormData.severityLevel"
                :options="SEVERITY_LEVEL_OPTION"
                filterable
                clearable
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="类型">
              <n-select
                v-model:value="filterFormData.type"
                :options="TYPE_OPTION"
                filterable
                clearable
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="标题">
              <n-input v-model:value="filterFormData.title" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="项目名称">
              <n-input v-model:value="filterFormData.projectName" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="分组">
              <n-select v-model:value="filterFormData.group" :options="GROUP_OPTION" clearable />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="问题分类">
              <n-input v-model:value="filterFormData.issueCategory" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="专家领域">
              <n-input v-model:value="filterFormData.field" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="最高运营级别">
              <n-input v-model:value="filterFormData.highestOperationLevel" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="一级运营">
              <n-input v-model:value="filterFormData.firstLevelOperation" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="二级运营">
              <n-input v-model:value="filterFormData.secondLevelOperation" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="ID号">
              <n-input v-model:value="filterFormData.id" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="详情超链接">
              <n-input v-model:value="filterFormData.detailLink" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="抽检日期">
              <n-date-picker v-model:value="filterFormData.time" type="daterange" clearable />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="抽检组长">
              <n-input v-model:value="filterFormData.inspectionLeader" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="抽检人">
              <n-input v-model:value="filterFormData.inspector" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="闭环IR单知识编号">
              <n-input v-model:value="filterFormData.closedIrKnowledgeId" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="知识分类">
              <n-input v-model:value="filterFormData.knowledgeCategory" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="答复规范&跟踪工单规范">
              <n-select
                v-model:value="filterFormData.responseNorm"
                :options="REPLY_SPECIFICATIONS_OPTION"
                filterable
                multiple
                clearable
                :render-option="renderOption"
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="沟通/效率规范">
              <n-select
                v-model:value="filterFormData.communicationEfficiencyNorm"
                :options="COMMUNICATION_REGULATIONS_OPTION"
                filterable
                multiple
                clearable
                :render-option="renderOption"
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="内部流程规范">
              <n-select
                v-model:value="filterFormData.internalProcessNorm"
                :options="INTERNAL_PROCESS_SPECIFICATIONS_OPTION"
                filterable
                multiple
                clearable
                :render-option="renderOption"
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="问题责任人名/工号">
              <n-input v-model:value="filterFormData.responsiblePerson" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="备注">
              <n-input v-model:value="filterFormData.remarks" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="是否澄清">
              <n-select
                v-model:value="filterFormData.clarificationRequired"
                :options="CLARIFIED_OR_NOT_OPTION"
                clearable
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="澄清理由说明">
              <n-input v-model:value="filterFormData.clarificationReason" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="整改方案">
              <n-input v-model:value="filterFormData.correctionPlan" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="抽检完成情况">
              <n-select
                v-model:value="filterFormData.samplingCompletion"
                :options="INSPECTION_COMPLETION_STATUS_OPTION"
                clearable
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="抽检结果">
              <n-select
                v-model:value="filterFormData.totalScore"
                :options="INSPECTION_RESULTS_OPTION"
                clearable
              />
            </n-form-item>
          </n-gi>
        </n-grid>
      </n-form>
      <n-space justify="center">
        <n-button secondary strong type="default" @click="resetList"> 重置 </n-button>
        <n-button secondary strong type="primary" @click="query"> 查询 </n-button>
        <n-button type="primary" icon-placement="right" @click="collapse = !collapse">
          <template #icon>
            <n-icon size="14" class="unfold-icon" v-if="collapse">
              <UpOutlined />
            </n-icon>
            <n-icon size="14" class="unfold-icon" v-else>
              <DownOutlined />
            </n-icon>
          </template>
          {{ collapse ? '收起' : '展开' }}
        </n-button>
      </n-space>
    </n-card>
    <n-card>
      <n-space>
        <n-button secondary strong type="warning" @click="queryList">
          <template #icon>
            <n-icon>
              <Refresh />
            </n-icon>
          </template>
          刷新
        </n-button>
        <n-button
          type="primary"
          secondary
          strong
          @click="showImportDialog = true"
          :disabled="!isAdmin"
        >
          导入
        </n-button>
        <n-button
          type="primary"
          secondary
          strong
          @click="exportTableData"
          :disabled="!(isAdmin || isSpotCheckLeader)"
        >
          导出
        </n-button>
        <n-button type="error" secondary strong @click="batchDelete" :disabled="!isAdmin">
          批量删除
        </n-button>
      </n-space>
      <n-data-table
        remote
        :columns="tableColumns"
        :data="tableData"
        :pagination="pagination"
        :row-key="(row) => row.serialId"
        v-model:checked-row-keys="checkedRowKeys"
        @update:checked-row-keys="handleCheck"
        style="margin-top: 12px"
        scroll-x
      />
    </n-card>
    <n-modal v-model:show="showImportDialog">
      <n-card
        style="width: 600px"
        title="批量导入"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-button text @click="downloadImportTemplate" style="margin-bottom: 20px">
          点击下载导入模板
        </n-button>
        <n-upload
          action="#"
          :custom-request="customRequest"
          accept=".xls,.xlsx,"
          :max="1"
          directory-dnd
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <Add />
              </n-icon>
            </div>
            <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
          </n-upload-dragger>
        </n-upload>
      </n-card>
    </n-modal>
    <n-modal v-model:show="showEditModal">
      <n-card
        style="width: 1200px"
        title="编辑工单信息"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form
          ref="formRef"
          :model="editableFormData"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="medium"
          label-align="right"
          :rules="rules"
        >
          <n-grid :cols="24" :x-gap="24">
            <n-form-item-gi :span="12" label="严重级别" path="severityLevel">
              <n-input v-model:value="editableFormData.severityLevel" disabled />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="类型" path="type">
              <n-input v-model:value="editableFormData.type" disabled />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="标题" path="title">
              <n-input v-model:value="editableFormData.title" disabled />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="项目名称" path="projectName">
              <n-input v-model:value="editableFormData.projectName" disabled />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="分组" path="group">
              <n-select
                v-model:value="editableFormData.group"
                :options="GROUP_OPTION"
                :disabled="!isAdmin"
                filterable
                clearable
              />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="问题分类" path="issueCategory">
              <n-input v-model:value="editableFormData.issueCategory" :disabled="!isAdmin" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="专家领域" path="field">
              <n-input v-model:value="editableFormData.field" :disabled="!isAdmin" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="最高运营级别" path="highestOperationLevel">
              <n-input v-model:value="editableFormData.highestOperationLevel" disabled />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="一级运营" path="firstLevelOperation">
              <n-input v-model:value="editableFormData.firstLevelOperation" disabled />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="二级运营" path="secondLevelOperation">
              <n-input v-model:value="editableFormData.secondLevelOperation" disabled />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="ID号" path="id">
              <n-input v-model:value="editableFormData.id" disabled />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="详情超链接" path="detailLink">
              <n-input v-model:value="editableFormData.detailLink" disabled />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="抽检日期" path="samplingDate">
              <n-input v-model:value="editableFormData.samplingDate" disabled />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="抽检组长" path="inspector">
              <n-select
                v-model:value="editableFormData.inspectionLeader"
                :options="inspectorLeaderOption"
                :disabled="!isAdmin"
                filterable
                clearable
              />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="抽检人" path="inspector">
              <n-select
                v-model:value="editableFormData.inspector"
                :options="inspectorOption"
                filterable
                clearable
              />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="闭环IR单的知识编号" path="closedIrKnowledgeId">
              <n-input v-model:value="editableFormData.closedIrKnowledgeId" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="知识分类" path="knowledgeCategory">
              <n-input v-model:value="editableFormData.knowledgeCategory" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="答复规范&跟踪工单规范" path="responseNorm">
              <n-select
                v-model:value="editableFormData.responseNorm"
                :options="REPLY_SPECIFICATIONS_OPTION"
                filterable
                multiple
                clearable
                :render-option="renderOption"
                @update:value="handleUpdateValue"
              />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="沟通/效率规范" path="communicationEfficiencyNorm">
              <n-select
                v-model:value="editableFormData.communicationEfficiencyNorm"
                :options="COMMUNICATION_REGULATIONS_OPTION"
                filterable
                multiple
                clearable
                :render-option="renderOption"
                @update:value="handleUpdateValue"
              />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="内部流程规范" path="internalProcessNorm">
              <n-select
                v-model:value="editableFormData.internalProcessNorm"
                :options="INTERNAL_PROCESS_SPECIFICATIONS_OPTION"
                filterable
                multiple
                clearable
                :render-option="renderOption"
                @update:value="handleUpdateValue"
              />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="问题责任人名/工号" path="responsiblePerson">
              <n-input v-model:value="editableFormData.responsiblePerson" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="备注" path="remarks">
              <n-input v-model:value="editableFormData.remarks" />
            </n-form-item-gi>
            <!-- 是否澄清只能这条数据的抽检人可以修改 -->
            <n-form-item-gi :span="12" label="是否澄清" path="clarificationRequired">
              <n-select
                v-model:value="editableFormData.clarificationRequired"
                :options="CLARIFIED_OR_NOT_OPTION"
                :disabled="
                  !(isAdmin || isInputEdit || currentLoginUser === editableFormData.inspector)
                "
                @update:value="handleUpdateValue"
                clearable
              />
            </n-form-item-gi>
            <!-- 这条数据的抽检人可以修改-->
            <n-form-item-gi :span="12" label="澄清理由说明" path="clarificationReason">
              <n-input
                v-model:value="editableFormData.clarificationReason"
                :disabled="
                  !(isAdmin || isInputEdit || currentLoginUser === editableFormData.inspector)
                "
                :on-input="handleUpdateValue"
              />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="整改方案" path="correctionPlan">
              <n-input
                v-model:value="editableFormData.correctionPlan"
                :disabled="!(isAdmin || isInputEdit)"
              />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="抽检完成情况" path="samplingCompletion">
              <n-input v-model:value="editableFormData.samplingCompletion" disabled />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="工单总分" path="totalScore">
              <n-input v-model:value="editableFormData.totalScore" disabled />
            </n-form-item-gi>
          </n-grid>
        </n-form>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" @click="editSubmit"> 确认 </n-button>
            <n-button secondary strong type="error" @click="showEditModal = false"> 取消 </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>
<script lang="ts" setup>
  import { Add, Refresh } from '@vicons/ionicons5';
  import { h, ref, reactive, VNode, onMounted } from 'vue';
  import {
    useMessage,
    UploadCustomRequestOptions,
    NButton,
    NTooltip,
    SelectOption,
  } from 'naive-ui';
  import { DownOutlined, UpOutlined } from '@vicons/antd';
  import { cloneDeep } from 'lodash-es';
  import {
    filterForm,
    CLARIFIED_OR_NOT_OPTION,
    GROUP_OPTION,
    REPLY_SPECIFICATIONS_OPTION,
    COMMUNICATION_REGULATIONS_OPTION,
    INTERNAL_PROCESS_SPECIFICATIONS_OPTION,
    HIGHEST_SCORE,
    SYMBOL,
    SEVERITY_LEVEL_OPTION,
    TYPE_OPTION,
    INSPECTION_RESULTS_OPTION,
    INSPECTION_COMPLETION_STATUS_OPTION,
    downloadTemplateService,
    serachList,
    importData,
    editService,
    deleteService,
    exportData,
  } from './index';
  import { useUserStore } from '@/store/modules/user';
  import { UserRoleEnum } from '@/enums/UserRoleEnum';
  import UserSearch from '@/views/comp/UserSearch.vue';
  import dayjs from 'dayjs';
  import { queryUser, QueryUserListRsp } from '@/api/system/usermanage';

  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo;
  const roles = userInfo.roles;
  // 超级管理员、工单质量管理员
  const isAdmin =
    roles.includes(UserRoleEnum.SUPER_ADMIN) || roles.includes(UserRoleEnum.ORDER_QUALITY_ADMIN);
  // 工单质量抽检组长
  const isSpotCheckLeader = roles.includes(UserRoleEnum.ORDER_QUALITY_SPOT_CHECK_LEADER);
  // 工单质量抽检人
  const isSpotCheckUser = roles.includes(UserRoleEnum.ORDER_QUALITY_SPOT_CHECK_USER);
  // 当前登录人，格式：姓名空格字母工号
  const currentLoginUser = `${userInfo.userName} ${userInfo.account}`;
  // 输入框是否可编辑
  const isInputEdit = ref(false);
  const collapse = ref(false);
  const message = useMessage();
  const showImportDialog = ref(false);
  // 表格
  const filterFormData = ref(cloneDeep(filterForm));
  const tableData = ref();
  const checkedRowKeys = ref<Array<string | number>>([]);
  const tableColumns = ref([
    {
      type: 'selection',
      fixed: 'left',
    },
    {
      title: '严重级别',
      key: 'severityLevel',
      width: 80,
    },
    {
      title: '类型',
      key: 'type',
      width: 80,
    },
    {
      title: '标题',
      key: 'title',
      width: 200,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '项目名称',
      key: 'projectName',
      width: 200,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '分组',
      key: 'group',
      width: 150,
    },
    {
      title: '问题分类',
      key: 'issueCategory',
      width: 150,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '专家领域',
      key: 'field',
      width: 150,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '最高运营级别',
      key: 'highestOperationLevel',
      width: 150,
    },
    {
      title: '一级运营',
      key: 'firstLevelOperation',
      width: 180,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '二级运营',
      key: 'secondLevelOperation',
      width: 180,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: 'ID号',
      key: 'id',
      width: 180,
      render(row) {
        return h(
          'a',
          {
            href: '#',
            onClick: (e) => {
              window.open(
                `https://issuereporter.developer.huawei.com/detail/${row.id}/comment`,
                '_blank'
              );
            },
            style: {
              color: '#1288ff',
              textDecoration: 'none',
              cursor: 'pointer',
            },
          },
          {
            default: () => row.id,
          }
        );
      },
    },
    {
      title: '详情超链接',
      key: 'detailLink',
      width: 150,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '抽检日期',
      key: 'samplingDate',
      width: 120,
      render(row) {
        return row.samplingDate ? dayjs(row.samplingDate).format('YYYY-MM-DD') : '';
      },
    },
    {
      title: '抽检组长',
      key: 'inspectionLeader',
      width: 150,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '抽检人',
      key: 'inspector',
      width: 150,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '闭环IR单的知识编号',
      key: 'closedIrKnowledgeId',
      width: 210,
    },
    {
      title: '知识分类',
      key: 'knowledgeCategory',
      width: 150,
    },
    {
      title: '答复规范&跟踪工单规范',
      key: 'responseNorm',
      width: 200,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
      render(row) {
        const arr = toArray(row.responseNorm);
        return arr.join('');
      },
    },
    {
      title: '沟通/效率规范',
      key: 'communicationEfficiencyNorm',
      width: 150,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
      render(row) {
        const arr = toArray(row.communicationEfficiencyNorm);
        return arr.join('');
      },
    },
    {
      title: '内部流程规范',
      key: 'internalProcessNorm',
      width: 150,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
      render(row) {
        const arr = toArray(row.internalProcessNorm);
        return arr.join('');
      },
    },
    {
      title: '问题责任人名/工号',
      key: 'responsiblePerson',
      width: 150,
    },
    {
      title: '备注',
      key: 'remarks',
      width: 150,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '是否澄清',
      key: 'clarificationRequired',
      width: 150,
    },
    {
      title: '澄清理由说明',
      key: 'clarificationReason',
      width: 150,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '整改方案',
      key: 'correctionPlan',
      width: 150,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '抽检完成情况',
      key: 'samplingCompletion',
      width: 110,
    },
    {
      title: '工单总分',
      key: 'totalScore',
      width: 80,
    },
    {
      title: '操作',
      key: 'serialId',
      width: 100,
      fixed: 'right',
      render(row) {
        /**
         * 编号从 抽检组长 - 整改方案 顺序排
         * 管理员：全部
         * 抽检组长：2-9、自己组(控制输入框禁用)的10-12
         * 抽检人：分配给自己的2-9(控制编辑按钮)
         */
        let isEdit = false;
        if (isAdmin || isSpotCheckLeader) {
          // 管理员、组长编辑所有
          isEdit = true;
          if (isSpotCheckLeader) {
            // 组长只能编辑自己组下的9-11
            isInputEdit.value = row.group === userInfo.team;
          }
        } else {
          // 抽检人只能编辑分配给自己的
          isEdit = row.inspector.includes(userInfo.userName);
        }
        return [
          h(
            NButton,
            {
              strong: true,
              tertiary: true,
              disabled: !isEdit,
              type: 'info',
              size: 'small',
              onClick: () => {
                showEditModal.value = true;
                const cloneRow = cloneDeep(row);
                cloneRow.samplingDate = row.samplingDate
                  ? dayjs(row.samplingDate).format('YYYY-MM-DD')
                  : ''; // 抽检日期
                cloneRow.responseNorm = toArray(cloneRow.responseNorm);
                cloneRow.communicationEfficiencyNorm = toArray(
                  cloneRow.communicationEfficiencyNorm
                );
                cloneRow.internalProcessNorm = toArray(cloneRow.internalProcessNorm);
                editableFormData.value = cloneDeep(cloneRow);
              },
            },
            [h('div', '编辑')]
          ),
        ];
      },
    },
  ]);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 50],
    itemCount: 0,
    prefix({ itemCount }) {
      return `总数：${itemCount}`;
    },
    onChange: (page: number) => {
      pagination.page = page;
      queryList();
    },
    onUpdatePageSize: (pageSize: number) => {
      pagination.pageSize = pageSize;
      pagination.page = 1;
      queryList();
    },
  });
  // 编辑
  const showEditModal = ref(false);
  const editableFormData = ref({});
  const formRef = ref();
  const rules = {
    responsiblePerson: {
      required: false,
      trigger: 'blur',
      validator(rule, value: string) {
        if (value && !/^[\u4e00-\u9fa5]{2,6} [a-z]{1,3}[0-9]{6,8}$/.test(value)) {
          return new Error('请输入姓名+空格+字母工号的格式，如：张三 a12345678');
        }
        return true;
      },
    },
    clarificationReason: {
      required: false,
      trigger: 'blur',
      validator(rule, value: string) {
        if (editableFormData.value.clarificationRequired === '是' && !value) {
          return new Error('请输入澄清理由说明');
        }
        return true;
      },
    },
  };
  const inspectorLeaderOption = ref([]); // 抽检组长
  const inspectorOption = ref([]); // 抽检人

  /**
   * 多选
   */
  const handleCheck = (rowKeys) => {
    checkedRowKeys.value = rowKeys;
  };

  /**
   * 重置
   */
  const resetList = () => {
    filterFormData.value = cloneDeep(filterForm);
    pagination.page = 1;
    queryList();
  };

  /**
   * 查询按钮
   */
  const query = () => {
    pagination.page = 1;
    queryList();
  };

  /**
   * 格式化搜索筛选项
   */
  const formatSearchData = (searchData) => {
    const data = cloneDeep(searchData);
    if (data.time) {
      data.samplingDateBegin = `${dayjs(data.time[0]).format('YYYY-MM-DD')} 00:00:00`;
      data.samplingDateEnd = `${dayjs(data.time[1]).format('YYYY-MM-DD')} 23:59:59`;
    }
    // 答复规范&跟踪工单规范  沟通/效率规范  内部流程规范  三个字段排序
    data.responseNorm = multipleSortToString(data.responseNorm);
    data.communicationEfficiencyNorm = multipleSortToString(data.communicationEfficiencyNorm);
    data.internalProcessNorm = multipleSortToString(data.internalProcessNorm);
    data.time = undefined;
    return data;
  };

  /**
   * 查询列表
   */
  const queryList = async () => {
    try {
      const searchData = formatSearchData(filterFormData.value);
      searchData.pageNumber = pagination.page;
      searchData.pageSize = pagination.pageSize;
      const { data, status } = await serachList(searchData);
      if (status === '200') {
        tableData.value = data.data;
        pagination.itemCount = data.page.total;
        checkedRowKeys.value = [];
      } else {
        tableData.value = [];
      }
    } catch (error) {
      tableData.value = [];
      Error();
    }
  };

  /**
   * 下载导入模板
   */
  const downloadImportTemplate = () => {
    downloadTemplateService()
      .then((res) => {
        if (!res) {
          message.error('下载失败！');
        }
      })
      .catch((e) => {
        message.error(`下载失败，原因：${e?.response?.data?.error}`);
      });
  };

  /**
   * 导入
   */
  const customRequest = async ({
    file,
    data,
    headers,
    withCredentials,
    action,
    onFinish,
    onError,
    onProgress,
  }: UploadCustomRequestOptions) => {
    try {
      const formData = new FormData();
      formData.append('file', file.file as File);
      const inspectorsList = inspectorOption.value.map((item) => item.userName);
      formData.append('inspectorsList', inspectorsList);
      let importRes = await importData(formData);
      showImportDialog.value = false;
      if (importRes.status == '200') {
        queryList();
        message.success('导入成功');
      } else {
        message.error('导入失败');
      }
    } catch (error) {
      showImportDialog.value = false;
      message.error(error?.response?.data?.message, { duration: 5000 });
      Error();
    }
  };

  /**
   * 导出
   */
  const exportTableData = async () => {
    const exportFilter = formatSearchData(filterFormData.value);
    try {
      const res = await exportData(exportFilter);
      if (res.status === '200') {
        message.success('导出成功');
      } else {
        message.success('导出成功');
      }
    } catch (error) {
      Error();
    }
  };

  /**
   * 批量删除
   */
  const batchDelete = async () => {
    if (!checkedRowKeys.value.length) {
      message.warning('请选择要删除的数据！');
      return;
    }
    try {
      const res = await deleteService({ serialIdList: checkedRowKeys.value });
      if (res.status === '200') {
        message.success('删除成功');
        pagination.page = 1;
        queryList();
      } else {
        message.success('删除失败');
      }
    } catch (error) {
      Error();
    }
  };

  /**
   * 编辑确认
   */
  const editSubmit = async () => {
    formRef.value.validate(async (errors) => {
      if (!errors) {
        const data = cloneDeep(editableFormData.value);
        const submitFormData = cloneDeep(editableFormData.value);
        submitFormData.samplingDate = data.samplingDate ? `${data.samplingDate} 00:00:00` : '';
        submitFormData.responseNorm = multipleSortToString(data.responseNorm);
        submitFormData.communicationEfficiencyNorm = multipleSortToString(
          data.communicationEfficiencyNorm
        );
        submitFormData.internalProcessNorm = multipleSortToString(data.internalProcessNorm);
        try {
          const res = await editService(submitFormData);
          if (res.status === '200') {
            message.success('编辑成功！');
            queryList();
            showEditModal.value = false;
          } else {
            message.error('编辑失败！');
          }
        } catch (error) {
          Error();
        }
      } else {
        Error();
      }
    });
  };

  /**
   * 下拉框Tooltip
   */
  const renderOption = ({ node, option }: { node: VNode; option: SelectOption }) =>
    h(NTooltip, null, {
      trigger: () => node,
      default: () => `${option.label}`,
    });

  /**
   * 计算工单总分
   * 每个工单满分十分，勾选【答复规范&跟踪工单规范、沟通/效率规范、内部流程规范】则扣对应项分值score，扣到0分截止，
   * 如“是否澄清”填“是”，且澄清理由说明不为空，则工单为10分
   */
  const calculatedOrderScore = (rowData) => {
    if (rowData.clarificationRequired === '是' && rowData.clarificationReason) {
      return HIGHEST_SCORE;
    }
    let highestScore = HIGHEST_SCORE;
    // 答复规范&跟踪工单规范
    REPLY_SPECIFICATIONS_OPTION.forEach((item) => {
      if ((rowData.responseNorm || '').includes(item.label)) {
        highestScore -= item.score;
      }
    });
    // 沟通/效率规范
    COMMUNICATION_REGULATIONS_OPTION.forEach((item) => {
      if ((rowData.communicationEfficiencyNorm || '').includes(item.label)) {
        highestScore -= item.score;
      }
    });
    // 内部流程规范
    INTERNAL_PROCESS_SPECIFICATIONS_OPTION.forEach((item) => {
      if ((rowData.internalProcessNorm || '').includes(item.label)) {
        highestScore -= item.score;
      }
    });
    return highestScore <= 0 ? 0 : highestScore;
  };

  /**
   * 答复规范&跟踪工单规范  沟通/效率规范  内部流程规范  三个字段排序并返回字符串
   */
  const multipleSortToString = (arr) => {
    const cloneArr = cloneDeep(arr || []);
    cloneArr.sort(); // 顺序排序
    return cloneArr.join(SYMBOL);
  };

  /**
   * 答复规范&跟踪工单规范  沟通/效率规范  内部流程规范 分隔成数组
   */
  const toArray = (str) => {
    return str && str.trim() ? str.trim().split(SYMBOL) : [];
  };

  /**
   * 编辑修改下拉选项时，改变抽检完成情况、分值
   */
  const handleUpdateValue = () => {
    editableFormData.value.totalScore = calculatedOrderScore(editableFormData.value);
    // 抽检完成情况
    if (
      (editableFormData.value.responseNorm || []).length &&
      (editableFormData.value.communicationEfficiencyNorm || []).length &&
      (editableFormData.value.internalProcessNorm || []).length
    ) {
      editableFormData.value.samplingCompletion = '已抽检';
    } else {
      editableFormData.value.samplingCompletion = '待抽检';
    }
  };

  /**
   * 查询抽检人选项
   */
  const getInspectorOption = async () => {
    try {
      const res: QueryUserListRsp = await queryUser({
        pageNum: 1,
        pageSize: 1000,
        departmentList: [],
        user: {
          roles: [
            UserRoleEnum.ORDER_QUALITY_SPOT_CHECK_LEADER,
            UserRoleEnum.ORDER_QUALITY_SPOT_CHECK_USER,
          ],
        },
      });
      if (res.status === '200') {
        const optionList = res.data.data.map((v) => ({
          ...v,
          label: `${v.userName} ${v.account}`,
          value: `${v.userName} ${v.account}`,
        }));
        inspectorLeaderOption.value = optionList.filter((v) =>
          v.roles.includes(UserRoleEnum.ORDER_QUALITY_SPOT_CHECK_LEADER)
        );
        inspectorOption.value = optionList.filter((v) =>
          v.roles.includes(UserRoleEnum.ORDER_QUALITY_SPOT_CHECK_USER)
        );
      }
    } catch (error) {
      Error();
    }
  };

  onMounted(() => {
    queryList(); // 列表请求
    getInspectorOption(); // 抽检人查询
  });
</script>
