import { h } from 'vue';
import { NB<PERSON>on, NTag } from 'naive-ui';
import { BasicColumn } from '@/components/Table';
import { NSpace } from 'naive-ui';
import { formatDateTime } from '@/utils';
import { getPersonOptionsOnlyName } from '@/views/dataview/personManage/staffCommonUtils';
import { DTS_HANDLE_TAG } from '@/views/dataview/personManage/tagContant';

export interface ListData {
  _id: string;
}
export interface TemplateItem {
  id: number | null; // 时间戳生成的唯一ID
  templateTitle: string;
  content: string; // 模板内容文本
  originalContent: string; // 初始模板内容文本
}
const severityColorMap = {
  一般: 'default',
  严重: 'error', // 将 'warning' 改为 'error'
  提示: 'info',
  致命: 'error',
};

const dtsEmployeeOptions = await getPersonOptionsOnlyName(DTS_HANDLE_TAG);

export const createColumns = (
  showAction = true,
  handleEdit?: (row: ListData) => void,
  handleAppNameClick?: (appName: string) => void,
  handleDelete?: (row: ListData) => void,
  openDialog?: (row: ListData) => void
): BasicColumn<ListData>[] => {
  const baseColumns: BasicColumn<ListData>[] = [
    {
      title: '问题单号',
      key: 'orderId',
      width: 170,
      render: (row) => {
        return h(
          NTag,
          {
            type: 'info',
            bordered: false,
            onClick: async () => {
              const { orderId } = row;
              // 根据orderId跳转到对应的详情页面
              window.open(
                `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${orderId}`,
                '_blank'
              );
            },
          },
          {
            default: () => row.orderId,
          }
        );
      },
    },
    {
      title: '应用名称',
      key: 'appName',
    },
    {
      title: 'EWP责任人',
      key: 'ewpOwner',
    },
    {
      title: '验收责任人',
      key: 'acceptanceOwner',
    },
    {
      key: 'creator',
      title: 'DTS提单人',
    },
    {
      title: 'IR单状态',
      key: 'irOrderStatus',
    },
    {
      title: '登记时间',
      key: 'irRegisTime',
      render(row) {
        return formatDateTime(row.irRegisTime);
      },
    },
    {
      title: '提单时间',
      key: 'irOverTime',
      render(row) {
        return formatDateTime(row.irOverTime);
      },
    },

    {
      title: '严重程度',
      key: 'severity',
      width: 100,
      render(record) {
        return h(
          NTag,
          {
            type: severityColorMap[record.severity] || 'default',
            bordered: false,
          },
          {
            default: () => record.severity,
          }
        );
      },
    },
  ];

  if (showAction) {
    baseColumns.push({
      title: '操作',
      key: 'actions',
      fixed: 'right',
      width: 240,
      render: (row) => {
        return h(
          NSpace,
          {},
          {
            default: () => [
              h(
                NButton,
                {
                  size: 'small',
                  onClick: () => handleEdit?.(row),
                },
                { default: () => '编辑' }
              ),
              h(
                NButton,
                {
                  size: 'small',
                  onClick: () => openDialog?.(row),
                },
                { default: () => '提IR' }
              ),
            ],
          }
        );
      },
    });
  }

  return baseColumns;
};

// 定义查询表单项
export const searchFormItems = [
  {
    field: 'appName',
    label: '应用名称',
    component: 'Input',
  },
  {
    field: 'acceptanceOwner',
    label: '验收责任人',
    component: 'Input',
  },
  {
    field: 'irOrderStatus',
    label: 'IR单状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '已提单', value: '已提单' },
        { label: '未提单', value: '未提单' },
        { label: '无需提单', value: '无需提单' },
      ],
    },
  },
  {
    field: 'severity',
    label: '严重程度',
    component: 'Select',
    componentProps: {
      options: [
        { label: '一般', value: '一般' },
        { label: '严重', value: '严重' },
        { label: '提示', value: '提示' },
        { label: '致命', value: '致命' },
      ],
    },
  },
  {
    field: 'ewpOwner',
    label: '应用责任人',
    component: 'Select',
    componentProps: {
      filterable: true,
      options: dtsEmployeeOptions,
    },
  },
  {
    field: 'irRegisTimeRange',
    label: '登记时间',
    component: 'DateRangePicker',
  },
  {
    field: 'irOverTimeRange',
    label: '提单时间',
    component: 'DateRangePicker',
  },
  {
    field: 'creator',
    label: 'DTS提单人',
    component: 'Input',
  },
];
