<template>
  <div class="app-detail">
    <n-card class="header-card">
      <div class="header-content">
        <n-button @click="goBack" class="back-button">
          <template #icon>
            <n-icon><arrow-back /></n-icon>
          </template>
          返回
        </n-button>
        <h1 class="app-title">{{ appName }} 详情</h1>
      </div>
    </n-card>
    <FunctionList :isTest="true"></FunctionList>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import FunctionList from '../../comp/functionList/functionList.vue';
import { ArrowBack } from '@vicons/ionicons5';
import { useRoute, useRouter } from 'vue-router';
const router = useRouter();
const route = useRoute();

const appName = ref(route.query.appName as string);
function goBack() {
  router.push({ name: 'ApplicationAcceptance' });
}
</script>

<style scoped>
.app-detail {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header-card {
  margin-bottom: 10px;
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-button {
  flex-shrink: 0;
}

.app-title {
  font-size: 24px;
  color: #303133;
  margin: 0;
  padding: 10px 0;
  flex-grow: 1;
}

.charts {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.chart {
  flex: 1;
}

.chart-card {
  height: 100%;
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.chart-title {
  font-size: 18px;
  color: #303133;
  font-weight: bold;
}

.chart-container {
  width: 100%;
  height: 300px;
}

@media (max-width: 768px) {
  .charts {
    flex-direction: column;
  }
}
.top-card {
  margin-bottom: 10px;
}

.table-card {
  margin-top: 10px;
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.custom-table td) {
  padding-top: 2px !important;
  padding-bottom: 2px !important;
}
:deep(.add-new-line) {
  display: none !important;
  cursor: pointer;
}
:deep(tr:hover .add-new-line) {
  display: inline-block !important;
}
:deep(tr:hover .add-new-line.disabled) {
  cursor: not-allowed;
}
</style>
