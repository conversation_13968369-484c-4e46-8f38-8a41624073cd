import { getMaxIntNumber } from '@/utils/commonUtils';
import { getStaffList } from '@/views/dataview/personManage/staff';

export const getPersonOptionsOnlyName = async (tag: string) => {
  const res = await getStaffList({
    pageNo: 1,
    pageSize: getMaxIntNumber(),
    tagIds: tag,
  });
  const accounts = [];
  res.records.forEach((item) => {
    const option = {
      label: item.name,
      value: item.name,
    };
    accounts.push(option);
  });
  accounts.push({ value: '', label: '无' })
  return accounts;
};

export const getPersonOptionsNameAccount = async (tag: string) => {
  const res = await getStaffList({
    pageNo: 1,
    pageSize: getMaxIntNumber(),
    tagIds: tag,
  });
  const accounts = [];
  res.records.forEach((item) => {
    const option = {
      label: `${item.name} ${item.id}`,
      value: `${item.pinyin} ${item.id}`,
    };
    accounts.push(option);
  });
  return accounts;
};

export const getPersonNamesFromTag = async (tag: string) => {
  const res = await getStaffList({
    pageNo: 1,
    pageSize: getMaxIntNumber(),
    tagIds: tag,
  });
  const accounts = [];
  res.records.forEach((item) => {
    accounts.push(item.id);
    accounts.push(item.name);
    accounts.push(item.pinyin[0] + item.id);
  });
  return accounts;
};
