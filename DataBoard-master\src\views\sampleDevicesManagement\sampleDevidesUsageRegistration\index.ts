import { h, reactive } from 'vue';

export const defaultSearchModel = {
  region: null,
  status: null,
  purpose: null,
  channel: null,
  applicationName: null,
};

export const defaultModel = {
  channel: null,
  region: null,
  applicationName: null,
  category: null,
  model: null,
  deviceNumber: null,
  borrower: null,
  borrowTime: null,
  expectedReturnTime: null,
  status: null,
  purpose: null,
  returner: null,
  returnTime: null,
};

export const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    paginationReactive.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    paginationReactive.pageSize = pageSize;
    paginationReactive.page = 1;
  },
  prefix: (info) => {
    return h('div', null, { default: () => `共 ${info.itemCount} 项` });
  },
});

export const statusList = reactive([
  {
    label: '已归还',
    value: '已归还',
  },
  {
    label: '使用中',
    value: '使用中',
  },
]);
export const purposeList = reactive([
  {
    label: '测试验收',
    value: '测试验收',
  },
  {
    label: '用户体验',
    value: '用户体验',
  },
]);
