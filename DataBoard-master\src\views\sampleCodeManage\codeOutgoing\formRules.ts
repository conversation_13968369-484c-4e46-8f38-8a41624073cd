import { FormItemRule, FormRules } from "naive-ui";

export const CODE_OUTGOING_FORM_RULES: FormRules = {
  demoName: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入样例代码名称',
    }
  ],
  demoDescription: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入样例代码描述',
    }
  ],
  demoLink: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入样例代码链接',
    }
  ],
  lineNumber: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入样例代码行数',
    }
  ],
  demoSubmitter: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入样例代码提交人',
    }
  ],
  currentHandler: [
    {
      required: true,
      trigger: ['blur', 'input'],
      validator(rule: FormItemRule, value: string) {
        if (!value) {
          return new Error('请输入样例代码评审人');
        }
        return true;
      },
    }
  ],
  selectValue: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请选择分类',
    }
  ],
  apiVersion: [],
  reviewSuggestions: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入评审意见',
    }
  ],
  reviewStatus: [
    {
      required: true,
      trigger: ['blur', 'input'],
      validator(rule: FormItemRule, value: number) {
        if (!value) {
          return new Error('请选择评审状态');
        }
        return true;
      },
    }
  ],
  deleteReason: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入删除原因',
    }
  ],
};
