import service from '@/utils/axios';

export const query = (data) => {
  return service({
    url: `/functionList/query`,
    method: 'post',
    data,
  });
};

export const importExcel = (data) => {
  return service({
    url: `/functionList/importExcel`,
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};

export const downloadTemplate = () => {
  return service({
    url: `/functionList/downloadTemplate`,
    method: 'get',
    responseType: 'blob',
  });
};

export const checkNew = (data) => {
  return service({
    url: `/functionList/checkNew`,
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};

export const multiUpdate = (data) => {
  return service({
    url: `/functionList/multiUpdate`,
    method: 'post',
    data,
  });
};
