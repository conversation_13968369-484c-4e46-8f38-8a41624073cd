<template>
  <div class="app-container">
    <div class="filter-container">
      <n-card>
        <n-form
          :model="searchForm"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          @submit.prevent="handleSearch"
        >
          <n-grid :cols="24" :x-gap="24">
            <n-grid-item v-for="item in visibleSearchFormItems" :key="item.field" :span="6">
              <n-form-item :label="item.label" :path="item.field">
                <template v-if="item.field === 'testOwner'">
                  <n-input-group>
                    <n-select
                      clearable
                      v-model:value="searchForm[item.field]"
                      :options="item.componentProps.options"
                      :filterable="item.componentProps.filterable"
                    />
                    <n-select
                      v-model:value="searchForm.hasTestOwner"
                      :options="[
                        { label: '有责任人', value: 'notempty' },
                        { label: '无责任人', value: 'empty' },
                      ]"
                      style="width: 100px"
                      @update:value="handleSearch"
                      clearable
                    />
                  </n-input-group>
                </template>
                <n-input
                  clearable
                  v-else-if="item.component === 'Input'"
                  v-model:value="searchForm[item.field]"
                  @keyup.enter="handleSearch"
                />
                <n-select
                  clearable
                  v-else-if="item.component === 'Select'"
                  v-model:value="searchForm[item.field]"
                  :options="item.componentProps.options"
                  :multiple="!!item.multiple"
                />
                <n-date-picker
                  clearable
                  v-else-if="item.component === 'Date'"
                  v-model:value="searchForm[item.field]"
                  type="daterange"
                />
                <n-switch
                  v-else-if="item.component === 'Switch'"
                  v-model:value="searchForm[item.field]"
                />
                <!--                <n-checkbox
                  v-if="item.component === 'CheckBox'"
                  v-model:checked="searchForm[item.field]"
                />-->
                <template v-else-if="item.component === 'Checkbox'">
                  <n-space justify="space-around" style="margin-left: 70px">
                    <n-checkbox
                      v-for="subItem in item['children']"
                      :key="subItem.field"
                      v-model:checked="searchForm[subItem.field]"
                      :label="subItem.label"
                    />
                  </n-space>
                </template>
              </n-form-item>
            </n-grid-item>
          </n-grid>
          <div class="form-actions">
            <n-space>
              <n-button @click="resetForm">重置</n-button>
              <n-button type="primary" attr-type="submit">查询</n-button>
              <n-button @click="toggleExpandForm" type="default">
                {{ isExpanded ? '收起' : '展开' }}
                <template #icon>
                  <n-icon>
                    <chevron-down v-if="!isExpanded" />
                    <chevron-up v-else />
                  </n-icon>
                </template>
              </n-button>
            </n-space>
          </div>
        </n-form>
      </n-card>
    </div>
    <n-card style="margin-top: 24px">
      <n-space justify="space-between" style="margin-bottom: 16px">
        <n-space>
          <n-button v-if="isShowRelationButton" @click="onBindParent"> 关联DTS单</n-button>
          <n-button v-if="isShowBatchButton" @click="onBatchPendingClick"> 批量处理</n-button>
          <n-button @click="onExportClick"> 导出Excel</n-button>
          <n-button @click="showUpdateFUTModal = true"> 更新FUT</n-button>
          <n-button v-if="isFUTAdmin" @click="showAutoBindDTSModal = true"> 自动绑单</n-button>
          <n-upload
            :show-file-list="false"
            :action="uploadURL"
            :on-before-upload="onBeforeUpload"
            :on-finish="onUploadFinish"
            class="upload"
          >
            <n-button :loading="isUploading">
              <slot>上传文件</slot>
            </n-button>
          </n-upload>
          <n-button
            v-if="isShowBatchProcess"
            @click="onDownloadBatchProcessTemplate"
            :loading="isDownloading"
          >
            下载批量处理模板
          </n-button>
          <n-upload
            v-if="isShowBatchProcess"
            :show-file-list="false"
            :action="uploadBatchProcessURL"
            :on-before-upload="onBeforeBatchProcess"
            :on-finish="onBatchProcessFinish"
            class="upload"
          >
            <n-button :loading="isBatchProcessUploading">
              <slot>上传批量处理文件</slot>
            </n-button>
          </n-upload>
        </n-space>
        <n-icon size="16" @click="onTableSettingClick" style="padding-top: 11px">
          <settings />
        </n-icon>
      </n-space>
      <n-data-table
        remote
        :single-line="false"
        striped
        :bordered="false"
        :scroll-x="tableWidth"
        :loading="loadingRef"
        :columns="columnsShow"
        :data="tableData"
        :pagination="paginationReactive"
        :row-key="(row) => row.id"
        :checked-row-keys="checkedRowKeys"
        @update:checked-row-keys="handleCheckedRowKeysChange"
        @update:sorter="handleSorterChange"
        :max-height="425"
      />
    </n-card>
    <!-- 编辑 -->
    <n-modal v-model:show="showEditModal" preset="card" title="编辑" style="width: 600px">
      <n-form
        :model="editingData"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="问题单号" path="orderId">
          <n-input v-model:value="editingData.orderId" disabled />
        </n-form-item>
        <n-form-item label="应用" path="appName">
          <n-input v-model:value="editingData.appName" />
        </n-form-item>
        <n-form-item label="领域" path="field">
          <n-select
            v-model:value="editingData.field"
            :options="
              searchFormItems.find((item) => item.field === 'field')?.componentProps.options
            "
          />
        </n-form-item>
        <n-form-item label="进展&措施" path="progressAndMeasure">
          <n-input v-model:value="editingData.progressAndMeasure" type="textarea" />
        </n-form-item>
        <n-form-item label="状态" path="status">
          <n-select v-model:value="editingData.status" :options="editingStatusOptions()" />
        </n-form-item>
        <n-form-item label="备注" path="pendingReason">
          <n-input v-model:value="editingData.pendingReason" />
        </n-form-item>
        <n-form-item label="责任人" path="responsiblePerson">
          <n-input v-model:value="editingData.responsiblePerson" />
        </n-form-item>
        <n-form-item label="领域责任人" path="areaResponsiblePerson">
          <n-input v-model:value="editingData.areaResponsiblePerson" />
        </n-form-item>
        <n-form-item label="计划上线时间" path="plannedLaunchTime">
          <n-date-picker v-model:value="editingData.plannedLaunchTimeShow" clearable />
        </n-form-item>
        <n-form-item label="DTS单" path="dtsUrl">
          <n-input v-model:value="editingData.dtsUrl" />
        </n-form-item>
        <n-form-item label="问题类型" path="questionType">
          <n-select
            v-model:value="editingData.questionType"
            :options="questionTypeOptions"
            clearable
          />
        </n-form-item>
        <n-form-item label="自动关联DTS单" path="autoLink">
          <n-select
            v-model:value="editingData.autoLink"
            :options="
              searchFormItems.find((item) => item.field === 'autoLink')?.componentProps.options
            "
            clearable
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button type="primary" @click="saveEdit">保存</n-button>
          <n-button @click="showEditModal = false">取消</n-button>
        </n-space>
      </template>
    </n-modal>
    <!-- 关联DTS单 -->
    <n-modal v-model:show="showRelateModal">
      <n-card
        style="width: 600px"
        title="请输入DTS单号"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-input v-model:value="relateDTS" />
        <template #footer>
          <n-space justify="end">
            <n-button type="primary" @click="onRelateClick" :disabled="!relateDTS">保存</n-button>
            <n-button @click="showRelateModal = false">取消</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
    <!-- 批量处理 -->
    <n-modal v-model:show="showBatchPendingModal">
      <n-card style="width: 400px" :bordered="false" size="huge" role="dialog" aria-modal="true">
        <n-form :label-width="80" :model="batchProcessing" label-placement="left">
          <n-form-item label="自动关联DTS单" path="autoLink">
            <n-select
              v-model:value="batchProcessing.autoLink"
              :options="
                searchFormItems.find((item) => item.field === 'autoLink')?.componentProps.options
              "
            />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space justify="end">
            <n-button type="primary" @click="batchPending">保存</n-button>
            <n-button @click="showBatchPendingModal = false">取消</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
    <!-- 表格设置 -->
    <n-modal v-model:show="showTableSeetingModal">
      <n-card
        style="width: 600px"
        title="表格设置"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form inline :label-width="80" :model="editingTableSetting">
          <n-checkbox-group v-model:value="showColumnListRef">
            <n-grid x-gap="12" :cols="4">
              <n-gi
                v-for="item in allColumnList"
                :key="item"
                :style="!item || item === '操作' ? 'display: none;' : ''"
              >
                <n-checkbox :value="item" :label="item" />
              </n-gi>
            </n-grid>
          </n-checkbox-group>
        </n-form>
        <template #footer>
          <n-space justify="end">
            <n-button type="primary" @click="saveTableSetting">保存</n-button>
            <n-button @click="showTableSeetingModal = false">取消</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
    <!-- 更新 FUT -->
    <n-modal v-model:show="showUpdateFUTModal">
      <n-card
        style="width: 600px"
        title="更新FUT"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form :label-width="80" :model="distributionAnalyzerData" label-placement="left">
          <n-form-item label="开始日期" path="date">
            <n-date-picker v-model:value="updateFUTData.date" />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space justify="end">
            <n-button type="primary" @click="onUpdateFUT">更新FUT</n-button>
            <n-button @click="showUpdateFUTModal = false">取消</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
    <!-- 自动绑单 -->
    <n-modal v-model:show="showAutoBindDTSModal">
      <n-card
        style="width: 600px"
        title="自动绑单"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form :label-width="80" :model="autoBindDTSData" label-placement="left">
          <n-form-item label="推理日期" path="date">
            <n-date-picker v-model:value="autoBindDTSData.date" />
          </n-form-item>
          <n-form-item label="推理开始日期" path="startDate">
            <n-date-picker v-model:value="autoBindDTSData.startDate" />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space justify="end">
            <n-button type="primary" @click="onAutoBindDTSDataUpload">上传</n-button>
            <n-button type="primary" @click="onAutoBindDTSDataDownload">下载</n-button>
            <n-button @click="showAutoBindDTSModal = false">取消</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
    <!-- 提示批量处理错误信息 -->
    <n-modal :show="showBatchProcessErrorModal">
      <n-card
        style="width: 700px"
        title="表格内容以下存在问题（最多显示20条），请修改"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-data-table
          :columns="[
            { title: '单号', key: 'no' },
            { title: '错误原因', key: 'reason' },
          ]"
          :data="errorReasonData"
          :pagination="false"
          :bordered="false"
          max-height="500"
        />
        <template #footer>
          <n-space justify="end">
            <n-button @click="showBatchProcessErrorModal = false">关闭</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, reactive, computed } from 'vue';
  import { ChevronDown, ChevronUp, Settings, CloudDownloadOutline } from '@vicons/ionicons5';
  import { formatToDate, formatToDateStart, formatToDateEnd } from '@/utils/dateUtil';
  import { useMessage, useDialog, NInput, NSelect, NCard } from 'naive-ui';

  import {
    getColumns,
    fetchData,
    searchFormItems,
    updateData,
    deleteData,
    isTester,
    relateParentOrder,
    batchSave,
    // allColumns,
    exportData,
    isFUTAdmin,
    updateFUT,
    autoBindDTSUpload,
    autoBindDTSDownload,
    questionTypeOptions,
  } from './questionsTable';

  // 父组件参数
  const props = defineProps<{
    source: string | undefined;
  }>();

  // 只看FUT来源
  const onlyFUTSource = props.source === 'FUT';

  // FUT问题页面隐藏【来源】过滤项
  searchFormItems.forEach((item) => {
    if (item.field === 'source') {
      item.show = !onlyFUTSource;
    }
  });

  const initialSearchForm = {
    orderId: '',
    submitTime: null,
    clusterTime: null,
    createTime: null,
    field: '',
    appName: '',
    sceneName: '',
    status: '',
    dtsUrl: '',
    problemDescription: '',
    testOwner: '',
    hasTestOwner: '',
    pendingReason: '',
    parentOnly: false,
    experienceFracture: false,
    topName: '',
    clustersRules: '',
    autoLink: '',
    kind: '',
    isIncorrectAnalysis: false,
    top: '',
    source: onlyFUTSource ? 'FUT' : '',
  };

  const searchForm = reactive(Object.assign({}, initialSearchForm));

  // 查询
  const handleSearch = () => {
    renderTable({ pageNo: 1 });
  };

  const isExpanded = ref(false);

  // 关联DTS单模态框
  const showRelateModal = ref(false);

  // 批量处理
  const showBatchPendingModal = ref(false);

  // 表格设置
  const showTableSeetingModal = ref(false);

  // 批量处理模板下载中
  const isDownloading = ref(false);

  // 上传文件中
  const isUploading = ref(false);

  // 批量处理上传中
  const isBatchProcessUploading = ref(false);

  // 上传前钩子函数
  const onBeforeUpload = () => {
    isUploading.value = true;
  };

  // 批量上传错误信息列表
  const errorReasonData = ref([]);

  // 上传完成钩子函数
  const onUploadFinish = ({ file }) => {
    if (file.status === 'finished') {
      message.success('上传成功');
    } else {
      message.error('上传失败');
    }
    isUploading.value = false;
  };

  // 上传前钩子函数
  const onBeforeBatchProcess = () => {
    isBatchProcessUploading.value = true;
  };

  // 上传完成钩子函数
  const onBatchProcessFinish = ({ file, event }) => {
    if (file.status === 'finished') {
      const response = JSON.parse(event.target.response);
      if (response.code === 200) {
        message.success('上传成功');
        renderTable({ pageNo: 1 });
      } else {
        message.error('上传失败');
        const reasons = [];
        Object.keys(response.data)
          .slice(0, 20)
          .forEach((item) => {
            reasons.push({
              no: item,
              reason: response.data[item],
            });
          });
        errorReasonData.value = reasons;
        showBatchProcessErrorModal.value = true;
      }
    } else {
      message.error('上传失败');
    }
    isBatchProcessUploading.value = false;
  };

  // 上传地址
  const uploadURL = `${window.location.origin}/ewp/management/futOrder/importRawFutData`;

  // 批量处理上传地址
  const uploadBatchProcessURL = `${window.location.origin}/ewp/management/futOrder/importUpdateData`;

  const visibleSearchFormItems = computed(() => {
    return isExpanded.value ? searchFormItems : searchFormItems.slice(0, 4);
  });

  // 重置
  const resetForm = () => {
    Object.keys(searchForm).forEach((key) => {
      searchForm[key] = initialSearchForm[key];
    });
    renderTable({ pageNo: 1 });
  };

  const toggleExpandForm = () => {
    isExpanded.value = !isExpanded.value;
  };

  const loadingRef = ref(true);

  const rowKey = (row) => row.orderId;

  const parentOnly = ref(false);

  const tableData = ref<Record<string, string>[]>([]);

  let tableSortField = '';

  let tableSortOrder = '';

  // 表格选择框
  const selectedRows = ref<Record<string, any>[]>([]);
  const checkedRowKeys = ref<(string | number)[]>([]);
  const handleCheckedRowKeysChange = (keys: (string | number)[],rows:object[]) => {
    const newSelectedRows = keys
      .map((key) => {
        const existingRow = selectedRows.value.find((row) => row.id === key);
        if (existingRow) {
          return existingRow;
        }
        return tableData.value.find((row) => row.id === key);
      })
      .filter(Boolean);

    selectedRows.value = newSelectedRows;
    checkedRowKeys.value = keys;
  };

  // 点击表头排序
  const handleSorterChange = (sorter) => {
    const { columnKey, order } = sorter;
    let sortField;
    let sortOrder;
    if (order === false) {
      sortField = tableSortField = '';
      sortOrder = tableSortOrder = order === '';
    } else {
      sortField = tableSortField = columnKey;
      sortOrder = tableSortOrder = order === 'ascend' ? 'asc' : 'desc';
    }

    // 清空TOP排序
    searchForm.topName = initialSearchForm.topName;

    renderTable({
      sortField,
      sortOrder,
      pageNo: 1,
    });
  };

  // 关联DTS单
  const relateDTS = ref('');

  // 批量处理
  const batchProcessing = ref({
    autoLink: '',
  });
  const batchStatusOptions = [{ label: '待提单', value: 'Analyzed(dts)' }];

  // 处理请求表格和导出数据的参数
  const progressextraParams = (searchForm, params) => {
    // 处理时间
    if (searchForm.submitTime) {
      params.beginSubmitTime = formatToDateStart(searchForm.submitTime[0]);
      params.endSubmitTime = formatToDateEnd(searchForm.submitTime[1]);
    }
    if (searchForm.clusterTime) {
      params.beginClusterTime = formatToDateStart(searchForm.clusterTime[0]);
      params.endClusterTime = formatToDateEnd(searchForm.clusterTime[1]);
    }
    if (searchForm.createTime) {
      params.beginCreateTime = formatToDateStart(searchForm.createTime[0]);
      params.endCreateTime = formatToDateEnd(searchForm.createTime[1]);
    }
    // 处理体验断裂
    if (searchForm.experienceFracture) {
      params.sceneName =
        (searchForm.sceneName ? `${searchForm.sceneName},` : '') +
        '闪退,卡死,定位,无声,打不开,无法打开,无法使用,解锁,登录,验证码,掉帧,性能,卡顿,发热,慢';
    }
    // 处理 TOP 排序
    if (searchForm.topName === 'topKindName') {
      params.groupSortField = 'kindNum';
      params.sortField = 'kind';
      params.sortOrder = 'desc';
    }
    if (searchForm.topName === 'topAppName') {
      params.groupSortField = 'appNum';
      params.sortField = 'appName';
      params.sortOrder = 'desc';
    }
    if (searchForm.topName === 'topSceneName') {
      params.groupSortField = 'sceneNum';
      params.sortField = 'sceneName';
      params.sortOrder = 'desc';
    }
    // 处理表格排序（优先级更高）
    if (tableSortField && tableSortOrder) {
      params.groupSortField = '';
      params.sortField = tableSortField;
      params.sortOrder = tableSortOrder;
    }
    // 处理状态
    if (Array.isArray(searchForm.status)) {
      params.status = searchForm.status.join(',');
    }
    params.pageNo = params.pageNo || paginationReactive.page;
    params.pageSize = params.pageSize || paginationReactive.pageSize;
    // 处理聚类规则
    if (searchForm.clustersRules) {
      const clustersRulesObject = {
        day: {
          beginDayVocNum: 3,
        },
        week: {
          beginWeekVocNum: 7,
        },
        month: {
          beginMonthVocNum: 15,
        },
      }[searchForm.clustersRules];
      params = Object.assign(params, clustersRulesObject);
    }
    // 处理分类
    if (searchForm.kind) {
      params.kind = String(searchForm.kind).trim();
    }
    // 处理自动关联DTS单
    if (Array.isArray(searchForm.autoLink)) {
      params.autoLink = searchForm.autoLink.join(',');
    }
    // 处理来源
    if (Array.isArray(searchForm.source)) {
      params.source = searchForm.source.join(',');
    }
    // 处理自动关联DTS单错误
    if (searchForm.experienceFracture) {
      params.experienceFracture = true;
    }
    //处理责任人
    if (!searchForm.testOwner) {
      params.testOwner = searchForm.hasTestOwner;
    }
    return params;
  };

  const renderTable = async (params) => {
    loadingRef.value = true;
    const extraParams = progressextraParams(searchForm, params);
    const { records, total, size } = await fetchData({
      ...searchForm,
      ...extraParams,
    });
    tableData.value = records;
    paginationReactive.page = params.pageNo;
    paginationReactive.itemCount = total;
    paginationReactive.pageSize = size;
    loadingRef.value = false;
  };

  const paginationReactive = reactive({
    page: 1,
    pageSize: 20,
    showSizePicker: true,
    pageSizes: [10, 20, 50, 100],
    prefix({ itemCount }) {
      return `所有条目: ${itemCount}`;
    },
    onChange: async (page: number) => {
      await renderTable({
        pageNo: page,
      });
    },
    onUpdatePageSize: async (pageSize: number) => {
      await renderTable({
        pageNo: 1,
        pageSize,
      });
    },
  });
  paginationReactive.itemCount = 2;

  const isShowRelationButton = isTester;
  const isShowBatchButton = isTester;
  const isShowBatchProcess = isTester;

  // 在组件挂载时获取数据
  onMounted(async () => {
    renderTable({
      pageNo: 1,
      pageSize: 20,
    });
  });

  const showEditModal = ref(false);
  const editingData = ref({});
  const message = useMessage();
  const dialog = useDialog();

  //点击处理
  const handleQuestion = async (row) => {
    try {
      const now = new Date();
      const param = {
        ...row,
        testProcessTime: now,
      };
      await updateData(param);
      // row.testProcessTime = now;
      await renderTable({
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
      });
      message.success('处理成功');
    } catch (error) {
      console.log(error);
      message.error('处理失败');
    }
  };

  // 点击编辑
  const handleEdit = (row) => {
    editingData.value = { ...row };
    editingData.value.plannedLaunchTimeShow = editingData.value.plannedLaunchTime
      ? Number(new Date(editingData.value.plannedLaunchTime))
      : null;
    showEditModal.value = true;
  };

  // 点击删除
  const handleDelete = async (row) => {
    dialog.warning({
      title: '提示',
      content: '确定删除此FUT问题单？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          await deleteData(row.orderId);
          message.success('删除成功');
          renderTable({
            pageNo: paginationReactive.page,
            pageSize: paginationReactive.pageSize,
          });
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  };

  // 点击搜索分类
  const handleKindClick = (kind: string) => {
    Object.keys(searchForm).forEach((key) => {
      searchForm[key] = initialSearchForm[key];
    });
    searchForm.kind = kind;
    renderTable({ pageNo: 1 });
  };

  // 点击一键关联
  const handleQuickAssociate = async (row: Record<string, any>) => {
    const rowClone = Object.assign(row);
    rowClone.dtsUrl = row.autoLinkDtsUrl;
    try {
      await updateData(rowClone);
      message.success('关联成功');
      renderTable({
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
      });
    } catch (error) {
      message.error('关联失败');
    }
  };

  // 点击保存
  const saveEdit = async () => {
    try {
      if (
        editingData.value.plannedLaunchTimeShow &&
        typeof editingData.value.plannedLaunchTimeShow === 'number'
      ) {
        editingData.value.plannedLaunchTime = formatToDate(
          editingData.value.plannedLaunchTimeShow,
          'yyyy/MM/dd'
        );
      } else if (editingData.value.plannedLaunchTimeShow === null) {
        editingData.value.plannedLaunchTime = '';
      } else {
        editingData.value.plannedLaunchTime = editingData.value.plannedLaunchTimeShow;
      }
      await updateData(editingData.value);
      message.success('更新成功');
      showEditModal.value = false;
      renderTable({
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
      });
    } catch (error) {
      message.error('更新失败');
    }
  };

  // 点击关联DTS单
  const onBindParent = () => {
    if (selectedRows.value.length === 0) {
      return;
    }
    if (selectedRows.value.some((item) => item.parentOrderId === '0')) {
      message.error('父单不能关联！');
      return;
    }
    showRelateModal.value = true;
  };

  // 关联DTS单保存
  const onRelateClick = async () => {
    try {
      const data: Record<string, any> = [];
      selectedRows.value.forEach((item) => {
        if (item.autoLink === 'Y') {
          item.autoLink = 'C';
        }
        item.dtsUrl = relateDTS.value;
        const now = Date.now();
        if (item.testProcessTime){
          item.associateDtsTime = new Date(now);
        }else{
          item.testProcessTime = now;
          item.associateDtsTime = new Date(now+5000);
        }
        data.push(item);
      });
      await batchSave(data);
      message.success('保存成功');
      showRelateModal.value = false;
      renderTable({
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
      });
      // 重置选择状态
      checkedRowKeys.value = [];
      selectedRows.value = [];
    } catch (error) {
      message.error('保存失败');
      console.log(error);
    }
  };

  // 点击批量处理按钮
  const onBatchPendingClick = () => {
    if (selectedRows.value.length === 0) {
      return;
    }
    // 清空内容
    batchProcessing.value = {};
    showBatchPendingModal.value = true;
  };

  // 批量处理
  const batchPending = async () => {
    try {
      const data: Record<string, any> = [];
      // 获取非空字段的 key
      const TruthValueList = Object.keys(batchProcessing.value).filter(
        (item) => batchProcessing.value[item]
      );
      const hasParentOrderId = selectedRows.value.some((item) => {
        return Boolean(item.parentOrderId);
      });
      selectedRows.value.forEach((row) => {
        TruthValueList.forEach((key) => {
          row[key] = batchProcessing.value[key];
        });
        data.push(row);
      });
      await batchSave(data);
      message.success('保存成功');
      showBatchPendingModal.value = false;
      renderTable({
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
      });
      // 重置选择状态
      checkedRowKeys.value = [];
      selectedRows.value = [];
    } catch (error) {
      message.error('保存失败');
      console.log(error);
    }
  };

  const editingTableSetting = ref([]);

  // 点击表格设置
  const onTableSettingClick = () => {
    showTableSeetingModal.value = true;
  };

  // 保存表格设置
  const saveTableSetting = () => {
    const tableSetting = {};
    allColumns.value.forEach((item) => {
      if (item.type === 'selection' || item.key === 'action') {
        return;
      }
      if (!showColumnListRef.value.some((item2) => item2 === item.title)) {
        tableSetting[item.key] = { show: 'false' };
      }
    });
    window.localStorage.setItem('__4796_admin_fut_columns_setting', JSON.stringify(tableSetting));
    message.success('保存成功');
    window.location.reload();
  };

  const allColumns = computed(()=>getColumns(
    handleQuestion,
    handleEdit,
    handleDelete,
    handleKindClick,
    handleQuickAssociate
  ));
  const columnsShow = allColumns.value.filter((item) => item.show);

  const tableWidth = computed(() => {
    let width = 0;
    allColumns.value.forEach((item) => {
      if (item.show && item.width) {
        width = width + item.width + 20;
      }
    });
    return width;
  });

  const showColumnListRef = ref([]);

  // 表格设置绑定对象
  const allColumnList = ref([]);
  allColumns.value.forEach((item) => {
    allColumnList.value.push(item.title);
    if (item.show) {
      showColumnListRef.value.push(item.title);
    }
  });

  // 导出Excel
  const onExportClick = async () => {
    try {
      loadingRef.value = true;
      const extraParams = progressextraParams(searchForm, {
        pageNo: 1,
        pageSize: paginationReactive.itemCount,
      });
      await exportData(
        {
          url: `http://${window.location.host}/ewp/management/futOrder/exportData`,
          fileName: '问题导出.xlsx',
        },
        { ...searchForm, ...extraParams }
      );
      loadingRef.value = false;
    } catch (e) {
      message.error('导出失败');
    }
  };

  // 下载批量处理模板
  const onDownloadBatchProcessTemplate = async () => {
    try {
      isDownloading.value = true;
      const downloadURL = `http://${window.location.host}/ewp/management/futOrder/uploadTemplate`;
      await exportData({ url: downloadURL, fileName: '批量处理模板.xlsx' });
    } catch (e) {
      message.error('下载失败');
    }
    isDownloading.value = false;
  };

  // 自动绑单
  const showAutoBindDTSModal = ref(false);

  // 批量处理报错
  const showBatchProcessErrorModal = ref(false);

  // 更新 FUT
  const showUpdateFUTModal = ref(false);

  const distributionAnalyzerData = ref({
    analyzer: [],
    date: null,
    endDate: null,
  });

  const autoBindDTSData = ref({
    data: null,
    startDate: null,
  });

  const updateFUTData = ref({
    date: null,
  });

  // 自动绑单上传
  const onAutoBindDTSDataUpload = async () => {
    try {
      const params = {
        param: formatToDate(autoBindDTSData.value.date, 'yyyyMMdd'),
        submitTime: formatToDate(autoBindDTSData.value.startDate, 'yyyy-MM-dd'),
      };
      await autoBindDTSUpload(params);
      message.success('上传成功');
      showAutoBindDTSModal.value = false;
    } catch (error) {
      message.error('上传失败');
      console.log(error);
    }
  };

  // 自动绑单下载
  const onAutoBindDTSDataDownload = async () => {
    try {
      const params = {
        param: formatToDate(autoBindDTSData.value.date, 'yyyyMMdd'),
      };
      await autoBindDTSDownload(params);
      message.success('下载成功');
      showAutoBindDTSModal.value = false;
    } catch (error) {
      message.error('下载失败');
      console.log(error);
    }
  };

  // 更新FUT
  const onUpdateFUT = async () => {
    try {
      const date = formatToDate(updateFUTData.value.date, 'yyyy-MM-dd');
      await updateFUT(date);
      message.success('更新任务已提交，请几分钟后查看');
      renderTable({ pageNo: 1 });
    } catch (error) {
      message.error('更新失败');
      console.log(error);
    }
  };

  // 编辑框状态下拉
  const editingStatusOptions = () => {
    const options = searchFormItems.find((item) => item.field === 'status')?.componentProps.options;
    // 不允许挂起
    return options.map((item) => {
      if (item.value === 'Analyzed(pending)' || item.value === 'Analyzed(dts)') {
        const clonedItem = Object.assign({}, item);
        clonedItem.disabled = true;
        return clonedItem;
      }
      return item;
    });
  };
</script>

<style scoped>
  .filter-container {
    margin-bottom: 24px;
  }

  /* 修改表单项样式 */
  :deep(.n-form-item) {
    display: flex;
    margin-right: 0;
    margin-bottom: 18px;
  }

  :deep(.n-form-item-label) {
    width: 90px !important;
    text-align: right;
  }

  :deep(.n-form-item-blank) {
    flex: 1;
  }

  /* 统一输入框和选择框的宽度和对齐方式 */
  :deep(.n-input),
  :deep(.n-select) {
    /* width: 300px !important; */
  }

  /* 确保输入内容左对齐 */
  :deep(.n-input__input-el),
  :deep(.n-select-option__content) {
    text-align: left !important;
  }

  /* 确保选择框的内容左对齐 */
  :deep(.n-base-selection-label) {
    text-align: left !important;
  }

  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 24px;
    padding-bottom: 8px;
  }

  .setting-icon {
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
    color: #666;
  }

  .setting-icon:hover {
    background-color: #f5f5f5;
    color: #2080f0;
    transform: rotate(30deg);
  }

  .bold-font {
    font-weight: 700;
  }

  .gradient-text {
    font-size: 14px;
    background: -webkit-linear-gradient(90deg, red 0%, green 50%, blue 100%); /* Chrome, Safari */
    background: linear-gradient(90deg, red 0%, green 50%, blue 100%); /* 标准语法 */
    -webkit-background-clip: text; /* Chrome, Safari */
    background-clip: text;
    -webkit-text-fill-color: transparent; /* Chrome, Safari */
    color: transparent;
  }

  .statistics-container {
    margin: 24px 0;
  }

  .stat-card {
    background-color: #fff;
    transition: all 0.3s;
    height: 100%;
  }

  .stat-card:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .stat-content {
    text-align: center;
  }

  .stat-label {
    color: #666;
    font-size: 14px;
    margin-bottom: 8px;
  }

  .stat-value {
    color: #2080f0;
    font-size: 24px;
    font-weight: bold;
  }

  .n-form-item {
    margin-bottom: 18px;
  }

  .n-card {
    border-radius: 8px;
  }

  .n-button {
    min-width: 80px;
  }
</style>
