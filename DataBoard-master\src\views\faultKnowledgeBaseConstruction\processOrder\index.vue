<template>
  <div>
    <n-card style="margin-bottom: 12px">
      <n-form label-placement="left" label-width="100px" label-align="left">
        <n-grid x-gap="20" :cols="4" style="margin-bottom: 12px">
          <n-gi>
            <n-form-item label="问题单号">
              <n-input v-model:value="filters.irNumber" />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="当前处理人">
              <n-select
                v-model:value="filters.currentHandlerList"
                multiple
                filterable
                placeholder="选择当前处理人"
                :options="filterUserList"
              >
              </n-select>
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="故障类型">
              <n-select
                v-model:value="filters.faultTypeList"
                :options="faultTypeList"
                multiple
                clearable
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="故障wiki状态">
              <n-select
                v-model:value="filters.wikiStatusList"
                :options="wikiStatusList"
                multiple
                clearable
              />
            </n-form-item>
          </n-gi>
        </n-grid>
        <div v-if="collapse">
          <n-grid x-gap="20" :cols="4" style="margin-bottom: 12px">
            <n-gi>
              <n-form-item label="评审结果">
                <n-select
                  v-model:value="filters.reviewResultList"
                  :options="reviewResultList"
                  multiple
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="故障状态">
                <n-select
                  v-model:value="filters.irStatusList"
                  :options="iRStatusList"
                  multiple
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="一级分类">
                <n-select
                  v-model:value="filters.primaryModuleList"
                  :options="primaryModuleList"
                  multiple
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="模块分类">
                <n-select
                  v-model:value="filters.problemCategoryList"
                  :options="problemCategoryList"
                  multiple
                  filterable
                  clearable
                />
              </n-form-item>
            </n-gi>
          </n-grid>
          <n-grid x-gap="20" :cols="4" style="margin-bottom: 12px">
            <n-gi>
              <n-form-item label="已导入知识库">
                <n-select
                  v-model:value="filters.syncedAbilityCenterList"
                  :options="syncedAbilityCenterList"
                  multiple
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="遍历人员">
                <n-select
                  v-model:value="filters.traversingResponsiblePerson"
                  :options="filterUserList"
                  clearable
                  filterable
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="输出人员">
                <n-select
                  v-model:value="filters.wikiOutputResponsiblePerson"
                  :options="filterUserList"
                  clearable
                  filterable
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="评审人员">
                <n-select
                  v-model:value="filters.reviewerResponsiblePerson"
                  :options="filterUserList"
                  clearable
                  filterable
                />
              </n-form-item>
            </n-gi>
          </n-grid>
          <n-grid x-gap="20" :cols="4" style="margin-bottom: 12px">
            <n-gi>
              <n-form-item label="导入时间">
                <n-date-picker v-model:value="filters.createTime" type="daterange" clearable />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="遍历时间">
                <n-date-picker v-model:value="filters.traversalTime" type="daterange" clearable />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="输出&修改时间" label-width="140px">
                <n-date-picker v-model:value="filters.outputTime" type="daterange" clearable />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="评审时间">
                <n-date-picker v-model:value="filters.reviewTime" type="daterange" clearable />
              </n-form-item>
            </n-gi>
          </n-grid>
          <n-space> </n-space>
        </div>
      </n-form>
      <n-space justify="center">
        <n-button secondary strong type="default" @click="handleResetFilter">重置 </n-button>
        <n-button secondary strong type="primary" @click="handleUpdateFilter">查询 </n-button>
        <n-button type="primary" icon-placement="right" @click="unfoldToggle">
          <template #icon>
            <n-icon size="14" class="unfold-icon" v-if="collapse">
              <UpOutlined />
            </n-icon>
            <n-icon size="14" class="unfold-icon" v-else>
              <DownOutlined />
            </n-icon>
          </template>
          {{ collapse ? '收起' : '展开' }}
        </n-button>
      </n-space>
    </n-card>
    <div class="layout-page-header">
      <n-space>
        <n-button secondary strong type="warning" @click="handleGetIRList()">
          <template #icon>
            <n-icon>
              <Refresh />
            </n-icon>
          </template>
          刷新
        </n-button>
        <n-button v-if="isAdmin" secondary strong type="primary" @click="showListAddModal = true"
          >导入IR
        </n-button>
        <n-button v-if="isAdmin" secondary strong type="warning" @click="handleBatchTransferUser"
          >转移处理人
        </n-button>
        <n-button v-if="isAdmin" secondary strong type="error" @click="handleBatchDelete"
          >批量删除
        </n-button>
        <n-button v-if="isAdmin" secondary strong type="warning" @click="handleGetExportIRExcel"
          >导出EXCEL
        </n-button>
      </n-space>
    </div>
    <n-data-table
      v-model:checked-row-keys="checkedRowKeys"
      :columns="columns"
      :data="tableData"
      :pagination="pagination"
      :scroll-x="2800"
      :max-height="2000"
      virtual-scroll
      remote
      style="margin-top: 20px"
      @update:filters="handleUpdateFilter"
      :on-update:checked-row-keys="onUpdateChecked"
    />
    <n-modal v-model:show="showModal">
      <n-card
        style="width: 600px"
        :title="isTransfer ? '变更处理人' : '编辑故障'"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <template #header-extra> </template>
        <n-form
          ref="formRef"
          :model="model"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="medium"
          :style="{
            maxWidth: '640px',
          }"
        >
          <n-form-item label="IR号" path="irNumber">
            <n-input v-model:value="model.irNumber" disabled />
          </n-form-item>

          <n-form-item label="标题" path="title">
            <n-input v-model:value="model.title" disabled />
          </n-form-item>
          <n-form-item label="责任人" path="currentHandler" v-if="isTransfer">
            <n-select
              v-model:value="model.currentHandler"
              :options="transferUserList"
              :disabled="model.irStatus == 4 || model.irStatus == 5"
            />
          </n-form-item>
          <n-form-item label="输出人" path="currentHandler" v-if="model.irStatus == 0">
            <n-input v-model:value="model.wikiOutputResponsiblePerson" :disabled="true" />
          </n-form-item>
          <n-form-item
            label="一级分类"
            path="primaryModule"
            v-if="model.irStatus == 0 && !isTransfer"
          >
            <n-select v-model:value="model.primaryModule" :options="primaryModuleList" />
          </n-form-item>
          <n-form-item
            label="模块分类"
            path="problemCategory"
            v-if="model.irStatus == 0 && !isTransfer"
          >
            <n-select
              v-model:value="model.problemCategory"
              :options="problemCategoryList"
              filterable
            />
          </n-form-item>
          <n-form-item label="故障分类" path="faultType" v-if="model.irStatus == 0 && !isTransfer">
            <n-select
              v-model:value="model.faultType"
              :options="faultTypeList"
              @change="handleSelectChange"
            />
          </n-form-item>

          <n-form-item
            label="Wiki地址"
            path="wikiAddress"
            :required="model.wikiStatus == 0 || model.wikiStatus == 1"
            v-if="model.irStatus == 0 || model.irStatus == 1 || model.irStatus == 3"
          >
            <n-input v-model:value="model.wikiAddress" :disabled="isTransfer" />
          </n-form-item>
          <n-form-item
            label="故障wiki状态"
            path="wikiStatus"
            v-if="(model.irStatus == 1 || model.irStatus == 3) && !isTransfer"
            required
          >
            <n-select
              @change="handleSelectChange"
              v-model:value="model.wikiStatus"
              :options="wikiStatusList"
              :disabled="isTransfer"
            />
          </n-form-item>
          <n-form-item
            label="打回原因"
            path="rejectionReason"
            v-if="
              (model.irStatus == 1 || model.irStatus == 3 || model.irStatus == 0) && !isTransfer
            "
          >
            <n-input v-model:value="model.rejectionReason" :disabled="model.irStatus == 0" />
          </n-form-item>
          <n-form-item
            label="评审结果"
            path="reviewResult"
            v-if="model.irStatus == 2 && !isTransfer"
          >
            <n-select
              @change="handleSelectChange"
              v-model:value="model.reviewResult"
              :options="reviewResultList"
              :disabled="isTransfer"
            />
          </n-form-item>
          <n-form-item label="评审建议" path="reviewSuggestions" v-if="model.irStatus == 2">
            <n-input
              v-model:value="model.reviewSuggestions"
              type="textarea"
              maxlength="100"
              :disabled="isTransfer"
              show-count
            />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" @click="handleSubmint()"> 确认 </n-button>
            <n-button secondary strong type="error" @click="showModal = false"> 取消 </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
    <n-modal v-model:show="showListAddModal">
      <n-card
        style="width: 600px"
        title="批量导入"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-button text @click="handleDownload" style="margin-bottom: 20px"
          >点击下载导入模板</n-button
        >
        <n-upload
          action="#"
          :custom-request="customRequest"
          :multiple="true"
          :default-file-list="fileList"
          accept=".xls,.xlsx,"
          :max="1"
          directory-dnd
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <ArchiveIcon />
              </n-icon>
            </div>
            <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
          </n-upload-dragger>
        </n-upload>
      </n-card>
    </n-modal>
    <n-modal v-model:show="showTransferAModal">
      <n-card
        style="width: 600px"
        title="批量转移"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form-item label="责任人" path="currentHandler" required>
          <n-select v-model:value="batchTransferUser" :options="batchTransferUserList" />
        </n-form-item>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" @click="handleSubmintBatchTransfer">
              确认
            </n-button>
            <n-button secondary strong type="error" @click="showTransferAModal = false">
              取消
            </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { Add, Refresh, Close, GridOutline } from '@vicons/ionicons5';
  import { DownOutlined, UpOutlined } from '@vicons/antd';
  import {
    NIcon,
    NButton,
    NTag,
    useDialog,
    UploadCustomRequestOptions,
    useMessage,
  } from 'naive-ui';
  import { h, ref, reactive, onBeforeMount } from 'vue';
  import {
    faultTypeList,
    iRStatusList,
    reviewResultList,
    wikiStatusList,
    irStatusMapRole,
    checkArrayValuesEquality,
    primaryModuleList,
    problemCategoryList,
    formatDateTime,
    initFilterModle,
    syncedAbilityCenterList,
    initModel,
  } from './index';
  import { useUserStore } from '@/store/modules/user';
  import { queryUserList } from '@/api/system/usermanage';
  import {
    getIRListBoard,
    updateIRBoard,
    addIRList,
    downloadTemplate,
    batchDeleteIR,
    getExportIRExcel,
    syncedAbilityCenter,
  } from '@/api/dataview/irManagement';

  const userStore = useUserStore();

  let roles = userStore.getUserInfo.roles;
  let role = '2_admin';
  const isAdmin = roles.includes('2_admin') || userStore.getUserInfo.isSuperAdmin;
  let currentHandler = userStore.getUserInfo.userName;

  const collapse = ref(false);
  const filters = ref({
    ...initFilterModle,
    currentHandlerList: JSON.parse(
      localStorage.getItem('filterUserListValue') || JSON.stringify([])
    ),
  });
  const dialog = useDialog();
  const message = useMessage();
  const formRef = ref();
  const fileList = ref([]);
  const irNumber = ref('');
  const checkedRowKeys = ref<Array<string | number>>([]);
  const transferUserList = ref([]);
  const batchTransferUserList = ref([]);
  const batchTransferUser = ref('');

  const filterUserList = ref([]);

  const showModal = ref(false);
  const showListAddModal = ref(false);
  const showTransferAModal = ref(false);
  const isAdd = ref(false);
  const isTransfer = ref(false);
  const tableData = ref([]);
  const model = ref(initModel);
  const rules = ref({
    wikiStatus: {
      required: true,
    },
    reviewResult: {
      required: true,
    },
    faultType: {
      required: true,
    },
    reviewSuggestions: {
      required: false,
    },
    problemCategory: {
      required: true,
    },
    wikiAddress: {
      required: false,
    },
    primaryModule: {
      required: true,
    },
    rejectionReason: {
      required: false,
    },
  });
  const col = [
    {
      title: '问题单号',
      key: 'irNumber',
      width: 150,
      fixed: 'left',
      resizable: true,
      render(row) {
        return h(
          'a',
          {
            href:
              row.irNumber.length == 15
                ? `https://issuereporter.developer.huawei.com/detail/${row.irNumber}/comment`
                : `https://developer.huawei.com/consumer/cn/forum/topicview?tid=${row.irNumber}`,
            target: '_blank',
            style: 'color: rgb(0, 0, 238) !important;',
          },
          row.irNumber
        );
      },
    },
    {
      title: '标题',
      key: 'title',
      width: 200,
      fixed: 'left',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '导入时间',
      key: 'createTime',
      width: 80,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
      render(row) {
        return h('div', row.createTime ? formatDateTime(row.createTime, 'yyyy-MM-dd') : '');
      },
    },
    {
      title: '上次处理时间',
      key: 'updateTime',
      width: 80,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
      render(row) {
        return h('div', row.updateTime ? formatDateTime(row.updateTime, 'yyyy-MM-dd') : '');
      },
    },
    {
      title: '遍历时间',
      key: 'traversalTime',
      width: 80,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
      render(row) {
        return h('div', row.traversalTime ? formatDateTime(row.traversalTime, 'yyyy-MM-dd') : '');
      },
    },
    {
      title: '输出时间',
      key: 'outputTime',
      width: 80,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
      render(row) {
        return h('div', row.outputTime ? formatDateTime(row.outputTime, 'yyyy-MM-dd') : '');
      },
    },
    {
      title: '评审时间',
      key: 'reviewTime',
      width: 80,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
      render(row) {
        return h('div', row.reviewTime ? formatDateTime(row.reviewTime, 'yyyy-MM-dd') : '');
      },
    },
    {
      title: '一级分类',
      key: 'primaryModule',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
    },
    {
      title: '模块分类',
      key: 'problemCategory',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
    },

    {
      title: '故障类型',
      key: 'faultType',
      width: 150,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
      render(row) {
        return h('div', faultTypeList.find((item) => item.value === row.faultType)?.label);
      },
    },
    {
      title: '故障wiki状态',
      key: 'wikiStatus',
      width: 130,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
      render(row) {
        return h('div', wikiStatusList.find((item) => item.value === row.wikiStatus)?.label);
      },
    },
    {
      title: 'wiki地址',
      key: 'wikiAddress',
      width: 100,
      ellipsis: true,
      resizable: true,
      render(row) {
        return h(
          'a',
          {
            href: row.wikiAddress,
            target: '_blank',
            style: 'color: rgb(0, 0, 238) !important;',
          },
          row.wikiAddress
        );
      },
    },
    {
      title: '评审结果',
      key: 'reviewResult',
      width: 120,
      resizable: true,
      render(row) {
        return h('div', reviewResultList.find((item) => item.value === row.reviewResult)?.label);
      },
    },
    {
      title: '评审建议',
      key: 'reviewSuggestions',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
    },

    {
      title: '故障状态',
      key: 'irStatus',
      width: 100,
      resizable: true,

      render(row) {
        return h('div', iRStatusList.find((item) => item.value === row.irStatus)?.label);
      },
    },
    {
      title: '当前处理人',
      key: 'currentHandler',
      width: 80,
      resizable: true,
    },
    {
      title: '已导入知识库',
      key: 'syncedAbilityCenter',
      width: 100,
      resizable: true,
      render(row) {
        return h('div', row.syncedAbilityCenter ? '是' : '否');
      },
    },
    {
      title: 'wikiId',
      key: 'wikiId',
      width: 100,
      resizable: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      resizable: true,
      render(row) {
        return [
          h(
            NButton,
            {
              strong: true,
              tertiary: true,
              type: 'info',
              size: 'medium',
              style: 'margin-right:10px',
              disabled:
                roles.every((item) => item !== '2_admin') && currentHandler !== row.currentHandler,
              onClick: () => {
                isTransfer.value = false;
                showModal.value = true;
                model.value = JSON.parse(JSON.stringify(row));
              },
            },
            [h('div', '编辑')]
          ),
          h(
            NButton,
            {
              strong: true,
              tertiary: true,
              type: 'error',
              size: 'medium',
              style: 'margin-right:10px',
              disabled:
                roles.every((item) => item !== '2_admin') && currentHandler !== row.currentHandler,
              onClick: async () => {
                isTransfer.value = true;
                showModal.value = true;
                model.value = JSON.parse(JSON.stringify(row));
                transferUserList.value = await handlerTransfer(row.irStatus);
              },
            },
            [h('div', '变更')]
          ),
          h(
            NButton,
            {
              strong: true,
              tertiary: true,
              type: 'warning',
              size: 'medium',
              disabled: !(
                roles.some((item) => item == '2_admin') &&
                row.irStatus == 5 &&
                !row.syncedAbilityCenter
              ),
              onClick: async () => {
                let arr = row.wikiAddress.split('/');
                let { data } = await syncedAbilityCenter({
                  irNumber: row.irNumber,
                  id: `GZ-${arr[arr.length - 1].slice(4)}`,
                  title: row.title,
                  firstClassification: row.primaryModule,
                  secondClassification: row.problemCategory,
                  wikiUrl: row.wikiAddress,
                  source: '故障知识',
                });
                if (data == 1) {
                  message.success('同步成功');
                  await handleGetIRList();
                }
              },
            },
            [h('div', '同步')]
          ),
        ];
      },
    },
  ];
  const columns = ref(col);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 50],
    itemCount: 0,
    prefix({ itemCount }) {
      return `总条数 ${itemCount}`;
    },
    onChange: (page: number) => {
      pagination.page = page;
      checkedRowKeys.value = [];
      batchTransferUser.value = '';
      handleGetIRList();
    },
    onUpdatePageSize: (pageSize: number) => {
      pagination.pageSize = pageSize;
      pagination.page = 1;
      handleGetIRList();
    },
  });

  //获取IR单列表
  const handleGetIRList = async () => {
    if (filters.value.traversalTime) {
      filters.value.traversalTime[1] += 86400000;
    }
    if (filters.value.outputTime) {
      filters.value.outputTime[1] += 86400000;
    }
    if (filters.value.reviewTime) {
      filters.value.reviewTime[1] += 86400000;
    }
    if (filters.value.createTime) {
      filters.value.createTime[1] += 86400000;
    }
    let res = await getIRListBoard({
      page: pagination.page - 1,
      pageSize: pagination.pageSize,
      ...filters.value,
    });

    if (res.status == '200') {
      message.success('获取IR单成功');
      pagination.itemCount = res.data.pageInfo.total;
      tableData.value = res.data.data.map((item, index) => {
        return { ...item, key: item.irNumber };
      });
    }
  };

  //根据单据状态获取对应角色所有人员
  const handlerTransfer = async (irStatus) => {
    let role = '';
    Object.keys(irStatusMapRole).some((key) => {
      let res = irStatusMapRole[key].some((item) => item === irStatus);

      if (res) role = key;
      return res;
    });
    isTransfer.value = true;
    let { data } = await queryUserList({
      roles: role,
      pageSize: 1000,
    });
    return data.data.map((item) => {
      return { label: item.userName, value: item.userName };
    });
  };
  //上传
  const customRequest = async ({
    file,
    data,
    headers,
    withCredentials,
    action,
    onFinish,
    onError,
    onProgress,
  }: UploadCustomRequestOptions) => {
    const formData = new FormData();
    formData.append('file', file.file as File);
    //上传接口
    try {
      let res = await addIRList(formData);
      if (res.status == '200') {
        showListAddModal.value = false;
        handleGetIRList();
        message.success('导入成功');
      } else {
        fileList.value = [];
      }
    } catch (err) {
      fileList.value = [];
      onError();
    }
  };
  //下载
  const handleDownload = async () => {
    downloadTemplate()
      .then((res) => {
        if (res) {
        } else {
          message.error('下载失败！');
        }
      })
      .catch((e) => {
        message.error(`下载失败，原因：${e?.response?.data?.error}`);
      });
  };
  //获取可筛选用户
  const handleGetFilterUserList = async () => {
    let { data } = await queryUserList({
      pageSize: 1000,
      roles: Object.keys(irStatusMapRole).join(','),
    });

    filterUserList.value = data.data.map((item) => {
      return { label: item.userName, value: item.userName };
    });
  };
  //提交处理工单
  const handleSubmint = async () => {
    // if (model.value.irNumber === 1 || model.value.irNumber === 3)
    formRef.value.validate(async (errors) => {
      if (!errors) {
        let data = {
          irNumber: model.value.irNumber,
        };
        if (isTransfer.value) {
          data.currentHandler = model.value.currentHandler;
        } else {
          if (model.value.irStatus === 0) {
            data.faultType = model.value.faultType;
            data.wikiAddress = model.value.wikiAddress;
            data.problemCategory = model.value.problemCategory;
            data.primaryModule = model.value.primaryModule;
          } else if (model.value.irStatus === 1 || model.value.irStatus === 3) {
            data.wikiAddress = model.value.wikiAddress;
            data.wikiStatus = model.value.wikiStatus;
            data.rejectionReason = model.value.rejectionReason;
          } else if (model.value.irStatus === 2) {
            data.reviewResult = model.value.reviewResult;
            data.reviewSuggestions = model.value.reviewSuggestions;
          }
        }
        try {
          let res = await updateIRBoard(data);

          if (res.status == '200') {
            message.success('提交成功');
            showModal.value = false;
            if (model.value.irStatus === 2) {
              let arr = model.value.wikiAddress.split('/');
              let { data } = await syncedAbilityCenter({
                irNumber: model.value.irNumber,
                id: `GZ-${arr[arr.length - 1].slice(4)}`,
                title: model.value.title,
                firstClassification: model.value.primaryModule,
                secondClassification: model.value.problemCategory,
                wikiUrl: model.value.wikiAddress,
                source: '故障知识',
              });
            }
          }
        } finally {
          await handleGetIRList();
        }
      }
    });
  };

  // 多选逻辑
  const onUpdateChecked = (
    keys: Array<string | number>,
    rows: object[],
    meta: { row: object | undefined; action: 'check' | 'uncheck' | 'checkAll' | 'uncheckAll' }
  ) => {
    if (!checkArrayValuesEquality(rows, 'irStatus')) {
      message.warning('请选择同一状态的单据');
    } else {
      checkedRowKeys.value = keys;
    }
  };
  // 根据单据状态查询转移人
  const handleBatchTransferUser = async () => {
    showTransferAModal.value = true;
    batchTransferUserList.value = await handlerTransfer(
      tableData.value.find((item) => item.irNumber === checkedRowKeys.value[0]).irStatus
    );
  };
  const handleSubmintBatchTransfer = async () => {
    try {
      checkedRowKeys.value.forEach(async (item) => {
        await updateIRBoard({
          irNumber: item,
          currentHandler: batchTransferUser.value,
        });
      });
      message.success('转移成功');
      showTransferAModal.value = false;
      await handleGetIRList();
      checkedRowKeys.value = [];
      batchTransferUser.value = '';
    } catch {
      message.error('转移失败');
    }
  };

  const handleSelectChange = (e) => {
    if (model.value.irStatus === 0) {
      if ([1, 3, 4].includes(e)) {
        rules.value.wikiAddress.required = true;
      } else {
        rules.value.wikiAddress.required = false;
      }
    } else if (model.value.irStatus == 2) {
      if (e == 1) {
        rules.value.reviewSuggestions.required = true;
      } else {
        rules.value.reviewSuggestions.required = false;
      }
    } else if (model.value.irStatus == 1 || model.value.irStatus == 3) {
      if (e == 3) {
        rules.value.rejectionReason.required = true;
      } else {
        rules.value.rejectionReason.required = false;
      }
    }
  };
  const handleBatchDelete = () => {
    dialog.warning({
      title: '警告',
      content: '请先确认故障单，是否删除',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          await batchDeleteIR(checkedRowKeys.value);
          message.success('删除成功');

          await handleGetIRList();
          checkedRowKeys.value = [];
        } catch {
          message.error('删除失败');
        }
      },
    });
  };
  const handleUpdateFilter = () => {
    pagination.page = 1;
    localStorage.setItem('filterUserListValue', JSON.stringify(filters.value.currentHandlerList));
    handleGetIRList();
  };
  const handleResetFilter = () => {
    filters.value = { ...initFilterModle };
    pagination.page = 1;
    handleGetIRList();
  };
  const unfoldToggle = () => {
    collapse.value = !collapse.value;
  };
  if (roles.find((role) => role == '2_admin')) {
    columns.value.unshift({
      type: 'selection',
      fixed: 'left',
    });
  }
  const handleGetExportIRExcel = async () => {
    await getExportIRExcel(filters.value);
  };
  handleGetFilterUserList();
  handleGetIRList();
</script>

<style lang="less" scoped>
  .layout-page-header {
    margin-top: 20px;
    .n-select {
      min-width: 250px;
    }
  }
</style>
