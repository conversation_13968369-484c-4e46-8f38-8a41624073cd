import type { BasicColumn } from '@/components/Table';
import service from '@/utils/axios';

export const columns: BasicColumn[] = [
  {
    title: '专项',
    key: 'date',
  },
  {
    title: '涉及应用数量',
    key: 'total',
  },
  {
    title: '舆论声量',
    key: 'init',
  },
  {
    title: '问题描述',
    key: 'analyzedDts',
  },
  {
    title: '关键进展',
    key: 'analyzedPending',
  },
  {
    title: '责任人',
    key: 'open',
  }
];

interface ItableItem {
  date: string;
  total: string;
  init: string;
  analyzedDts: string;
  analyzedPending: string;
  open: string
}
 
export const fetchTableData: ItableItem[] = [{
  date: '稳定性',
  total: '94',
  init: '1534',
  analyzedDts: '1.红果免费短剧APP在观看视频时闪退。',
  analyzedPending: '累计识别352例，已锁定计划64例，已关闭189例，锁定&解决率：71.8%',
  open: '黄幸平',
}]

export const fetchData = async () => {
  const titleMap = getTitleMap()
  const tableData: Record<string, string>[] = []
  const rawData = await service.post('/management/futOrder/overview', { afterDayCnt: '10' });
  const dateKeys = Object.keys(rawData).sort(function (a, b) {
    return new Date(a) - new Date(b);
  })
  dateKeys.forEach((dateKey) => {
    const currentRowData = {
      date: dateKey
    }
    Object.keys(rawData[dateKey]).forEach((stateKey) => {
      currentRowData[stateKey] = rawData[dateKey][stateKey]
    })
    tableData.push(currentRowData)
  })
  return tableData
};

const getTitleMap = () => {
  const map = {}
  columns.forEach(titleObj => {
    map[titleObj.key as string] = titleObj.title
  })
  return map
}