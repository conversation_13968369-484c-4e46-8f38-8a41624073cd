import { UserDto } from '@/api/system/usermanage';
import { RoleMgmtPathNameEnum } from "@/enums/RoleMgmtPathNameEnum";
import { UserRoleEnum, UserRoleCNameEnum } from "@/enums/UserRoleEnum";

export interface RoleOption {
  label: string;
  value: string;
}
export interface RoleMgmtFilterStatus {
  searchKey: string;
  searchValue: string;
  roles: string[];
  searchDepartment: string[],
  teamList: string[]
}
export interface SelectOption {
  label: string
  value: string
}
interface RoleMgmtModel {
  // 模块id
  moduleId: string;
  // 角色列表
  roles: RoleOption[];
  // 是否有多个角色
  multipleRoles: boolean;
  // 是否展示技能
  showSkills: boolean;
  // 是否展示批量导入
  batchImport: boolean;
}
const ALL_ROLE_MGMT_MODELS: Record<RoleMgmtPathNameEnum, RoleMgmtModel> = {
  [RoleMgmtPathNameEnum.ALL]: { // 所有权限，工作台-权限管理
    moduleId: '-1',
    roles: Object.keys(UserRoleEnum).map((v: string) => {
      return { label: UserRoleCNameEnum[v], value: UserRoleEnum[v] };
    }),
    multipleRoles: true,
    showSkills: false,
    batchImport: true,
  },
  [RoleMgmtPathNameEnum.ORDER_QUALITY]: {
    moduleId: '0',
    roles: [
      {
        label: UserRoleCNameEnum.ORDER_QUALITY_ADMIN,
        value: UserRoleEnum.ORDER_QUALITY_ADMIN,
      },
      {
        label: UserRoleCNameEnum.ORDER_QUALITY_SPOT_CHECK_LEADER,
        value: UserRoleEnum.ORDER_QUALITY_SPOT_CHECK_LEADER,
      },
      {
        label: UserRoleCNameEnum.ORDER_QUALITY_SPOT_CHECK_USER,
        value: UserRoleEnum.ORDER_QUALITY_SPOT_CHECK_USER,
      },
    ],
    multipleRoles: true,
    showSkills: false,
    batchImport: true,
  },
  [RoleMgmtPathNameEnum.KNOWLEDGE]: {
    moduleId: '1',
    roles: [
      {
        label: UserRoleCNameEnum.KNOWLEDGE_ADMIN,
        value: UserRoleEnum.KNOWLEDGE_ADMIN,
      },
    ],
    multipleRoles: false,
    showSkills: false,
    batchImport: true,
  },
  [RoleMgmtPathNameEnum.FAULT_KNOW]: {
    moduleId: '2',
    roles: [
      {
        label: UserRoleCNameEnum.FAULT_KNOW_ADMIN,
        value: UserRoleEnum.FAULT_KNOW_ADMIN,
      },
      {
        label: UserRoleCNameEnum.FAULT_KNOW_TRAVERSER,
        value: UserRoleEnum.FAULT_KNOW_TRAVERSER,
      },
      {
        label: UserRoleCNameEnum.FAULT_KNOW_EXPORTER,
        value: UserRoleEnum.FAULT_KNOW_EXPORTER,
      },
      {
        label: UserRoleCNameEnum.FAULT_KNOW_REVIEWER,
        value: UserRoleEnum.FAULT_KNOW_REVIEWER,
      },
    ],
    multipleRoles: true,
    showSkills: true,
    batchImport: true,
  },
  [RoleMgmtPathNameEnum.SAMPLE_CODE]: {
    moduleId: '3',
    roles: [
      {
        label: UserRoleCNameEnum.SAMPLE_CODE_ADMIN,
        value: UserRoleEnum.SAMPLE_CODE_ADMIN,
      },
      {
        label: UserRoleCNameEnum.SAMPLE_WISH_REVIEWER,
        value: UserRoleEnum.SAMPLE_WISH_REVIEWER,
      },
      {
        label: UserRoleCNameEnum.SAMPLE_CODE_REVIEWER,
        value: UserRoleEnum.SAMPLE_CODE_REVIEWER,
      },
      {
        label: UserRoleCNameEnum.SAMPLE_OUTGOING_ADMIN,
        value: UserRoleEnum.SAMPLE_OUTGOING_ADMIN,
      },
      {
        label: UserRoleCNameEnum.SAMPLE_HOSTING_ADMIN,
        value: UserRoleEnum.SAMPLE_HOSTING_ADMIN,
      },
    ],
    multipleRoles: true,
    showSkills: false,
    batchImport: false,
  },
  [RoleMgmtPathNameEnum.LISTING_PROTECTION]: {
    moduleId: '5',
    roles: [
      {
        label: UserRoleCNameEnum.LISTING_PROTECTION_ADMIN,
        value: UserRoleEnum.LISTING_PROTECTION_ADMIN,
      },
    ],
    multipleRoles: false,
    showSkills: false,
    batchImport: true,
  },
  [RoleMgmtPathNameEnum.TEST]: {
    moduleId: '6',
    roles: [
      {
        label: UserRoleCNameEnum.TEST_ADMIN,
        value: UserRoleEnum.TEST_ADMIN,
      },
      {
        label: UserRoleCNameEnum.TEST_USER,
        value: UserRoleEnum.TEST_USER,
      },
    ],
    multipleRoles: true,
    showSkills: false,
    batchImport: true,
  },
  [RoleMgmtPathNameEnum.PROTOTYPE]: {
    moduleId: '7',
    roles: [
      {
        label: UserRoleCNameEnum.PROTOTYPE_ADMIN,
        value: UserRoleEnum.PROTOTYPE_ADMIN,
      },
      {
        label: UserRoleCNameEnum.DEVICE_MANAGER,
        value: UserRoleEnum.DEVICE_MANAGER,
      },
    ],
    multipleRoles: true,
    showSkills: false,
    batchImport: true,
  },
  [RoleMgmtPathNameEnum.DEPARTMENT]: {
    moduleId: '8',
    roles: [
      {
        label: UserRoleCNameEnum.DEPARTMENT_ADMIN,
        value: UserRoleEnum.DEPARTMENT_ADMIN,
      },
      {
        label: UserRoleCNameEnum.MANAGER_USER,
        value: UserRoleEnum.MANAGER_USER,
      },
      {
        label: UserRoleCNameEnum.DTSE_LEADER,
        value: UserRoleEnum.DTSE_LEADER,
      },
    ],
    multipleRoles: true,
    showSkills: false,
    batchImport: true,
  },
  [RoleMgmtPathNameEnum.DEVELOPER_COMMUNITY]: {
    moduleId: '9',
    roles: [
      {
        label: UserRoleCNameEnum.DEVELOPER_COMMUNITY_ADMIN,
        value: UserRoleEnum.DEVELOPER_COMMUNITY_ADMIN,
      },
    ],
    multipleRoles: true,
    showSkills: false,
    batchImport: true,
  },
}

export const DEFAULT_ROLE = [
  {
    label: UserRoleCNameEnum.SUPER_ADMIN,
    value: UserRoleEnum.SUPER_ADMIN,
  },
];
export const ROLE_MGMT_SEARCH_FILTER_OPTIONS: RoleOption[] = [
  {
    value: 'userName',
    label: '姓名',
  },
  {
    value: 'account',
    label: '工号',
  },
];
export function getRoleMgmtModel(pathName: string): RoleMgmtModel {
  return ALL_ROLE_MGMT_MODELS[pathName as RoleMgmtPathNameEnum];
}
export function getDefaultFilters(roleList: RoleOption[]): RoleMgmtFilterStatus {
  const roles = roleList.map(item => item.value);
  return {
    searchKey: 'userName',
    searchValue: '',
    roles: roles.length === 1 && roles[0] === UserRoleEnum.SUPER_ADMIN ? [] : roles,
  };
}
export function getDefaultUserInfo(roleMgmtModel: RoleMgmtModel): UserDto {

  return {
    userName: '',
    account: '',
    password: '123456',
    roles: roleMgmtModel.multipleRoles ? [] : [roleMgmtModel.roles[0]?.value],
    personalSkills: '',
    leader: '',
    leaderName: '',
    residence: '',
  };
}
/**
 * 工作台里的权限管理就只显示每个领域的管理员+按角色
 */
const rolesGroup = {
  按领域: [
    'SUPER_ADMIN',
    'ORDER_QUALITY_ADMIN',
    'KNOWLEDGE_ADMIN',
    'SAMPLE_CODE_ADMIN',
    'LISTING_PROTECTION_ADMIN',
    'TEST_ADMIN',
    'PROTOTYPE_ADMIN',
    'DEPARTMENT_ADMIN',
    'ALLIANCE_COMMINITY_USER',
    'DEVELOPER_COMMUNITY_ADMIN',
  ],
  按角色: [
    'NORMAL_USER',
    'MANAGER_USER',
    'OPERATION_USER',
    'ONLY_VIEW_PUBLIC_OPINION',
    'FLY_TIGER_MEMBER',
  ],
};
export function formatRoles(roleList) {
  const rolesGroupList = [];
  Object.keys(rolesGroup).forEach((v) => {
    const tempList = [];
    rolesGroup[v].forEach((e) => {
      const tempItem = { title: v, label: UserRoleCNameEnum[e], value: UserRoleEnum[e] };
      tempList.push(tempItem);
    });
    rolesGroupList.push(tempList);
  });
  return rolesGroupList;
}

/**
 * 工号变更form
 */
export const DefaultChangeEmployeeForm = {
  oldAccount: '',
  newAccount: '',
};

/**
 * 可操作工号变更的人员
 */
export const ChangeEmployeePerson = ['w30047907'];
