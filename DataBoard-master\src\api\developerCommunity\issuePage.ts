import service from '@/utils/axios';

/**
 * 下载导入模板
 */
export const downloadTemplateService = () => {
  return service({
    url: '/community/downloadExcel',
    method: 'get',
    responseType: 'blob',
  });
};

/**
 * 导入
 */
export const importData = (data) => {
  return service({
    url: '/community/import',
    method: 'post',
    data,
  });
};

/**
 * 导出
 */
export const exportData = (data) => {
  return service({
    url: '/community/export',
    method: 'post',
    data,
    responseType: 'blob',
  });
};

/**
 * 查询
 */
export const serachList = (data) => {
  return service({
    url: '/community/queryByCondition',
    method: 'post',
    data,
  });
};

/**
 * 更新
 */
export const updateData = (data) => {
  return service({
    url: '/community/update',
    method: 'post',
    data,
  });
};

/**
 * 删除
 */
export const communityDelete = (data) => {
  return service({
    url: '/community/delete',
    method: 'post',
    data,
  });
};
