import { ewpService as service } from '@/utils/axios';

export async function mySeriousQuery(params: any): Promise<any> {
  try {
    const response = await service.post('/management/problemProcess/query', params);

    return response;
  } catch (error) {
    throw error;
  }
}

export async function mySeriousSave(params: any): Promise<any> {
  try {
    const response = await service.post('/management/problemProcess/save', params);

    return response;
  } catch (error) {
    throw error;
  }
}
