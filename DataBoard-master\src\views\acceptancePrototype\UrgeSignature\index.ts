export interface ProblemHandleFilters {
  id: number | null;
  dtsNum: string;
  appName: string;
  status: string;
  owner: string;
  guarantor: string;
  level: string;
  dtsStatus: string;
  warnTime: [number, number] | null;
  planTime: [number, number] | null;
}

export function getDefaultProblemHandleFilters(): ProblemHandleFilters {
  return {
    id: null,
    dtsNum: '',
    appName: '',
    status: '',
    owner: '',
    guarantor: '',
    level: '',
    dtsStatus: '',
    warnTime: null,
    planTime: null,
  };
}

// 物权转移 老版本
export const template_ownership_transfer = (list) => {
  const arr = [...new Set(list.map((item) => item.applicationName))];
  let subject = `开发设备使用声明-（${list[0].applicationSubject}）`;
  arr.forEach((item) => {
    subject += `【${item}】`;
  });

  let tableStr = '';

  list.forEach((item, index) => {
    tableStr += `    
    <tr>             
      <td>${index + 1}</td>
      <td>${item.productModel}</td>
      <td>${item.sn1}</td>
      <td>${item.sn2}</td>
      <td>${item.imei1}</td>
      <td>${item.imei2}</td>
      <td>${item.trackingNumber}</td>
      <td>${item.logisticsCompany}</td>
      <td>${item.recipient}</td>
      <td>${item.applicationName}</td>
      </tr>  
   `;
  });

  const str = `<!DOCTYPE html>
  <html lang="en">
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Document</title>
      <style>
          table {
              margin: 0 auto;
              border: 1px solid #000000;
              border-collapse: collapse;
          }
          thead {
            background-color: #AAE3EA;
          }
          th,
          td {
              border: 1px solid #000000;
              text-align: center;
          }
          
        .red {
          color: red;
        }
      </style>
  </head>
  
  <body>
      <div class="vditor-reset" id="vditorScrollParent">
          <p>经双方协商，现华为提供以下开发设备用于伙伴针对双方合作项目的研发及测试，请您收到开发设备后注意如下事项并回复确认。</p>
          <p>1、开发设备一经签收，则物权归属由花瓣云科技有限公司所有变更为<strong>${list[0].applicationSubject}</strong>所有。</p>
          <p>2、开发设备用于合作项目的研发测试，<strong>${list[0].applicationSubject}</strong>应遵从研发测试计划及时升级OS至目标版本，并妥善使用和保管。
          </p>
          <p>3、<strong>${list[0].applicationSubject}</strong><span class="red">应按双方签署的保密协议承担保密义务，如违反项目保密承诺，我司有权按照相关条款追究法律责任。</span></p>
          <p>4、<span
          class="red">如合作项目异常或终止，应用未最终上架，我司有权要求伙伴归还开发设备。如因伙伴管理不善导致设备丢失、设备非正常损坏等任何原因导致无法归还，我司有权要求伙伴按照以下开发设备的价值进行赔偿。</span>
          </p>
          <table style="width: 100%;">
              <thead>
                  <tr>
                      <th>序号</th>
                      <th>*产品型号</th>
                      <th>*SN1</th>
                      <th>*SN2</th>
                      <th>*IMEI1</th>
                      <th>*IMEI2</th>
                      <th>*投递单号</th>
                      <th>物流公司</th>
                      <th>*收件人信息</th>
                      <th>*应用名称</th>
                  </tr>
              </thead>
              <tbody>
                     ${tableStr}
              </tbody>
          </table>
          <p><strong class="red">说明：（回复邮件时请删除下列内容）</strong></p>
          <p>请确认后按照如下要求进行回复：</p>
          <p>1、回复内容：<strong>${list[0].applicationSubject}</strong>知悉并确认，自机器签收之时起，机器所有权归<strong>${list[0].applicationSubject}</strong>所有，在完成合作项目研发测试后无需归还。
          </p>
          <p>2、抄送要求：回复邮件中需<strong>抄送至少</strong>​<strong class="red">2位贵司参与鸿蒙项目人员</strong>​，至少1人为高层，谢谢！</p>
      </div>
  </body>
  
  </html>`;

  return { subject, content: str };
};

// 物权转移 新版本
export const template_ownership_transfer_new = (list) => {
  const arr = [...new Set(list.map((item) => item.applicationName))];
  let subject = `开发设备使用声明-（${list[0].applicationSubject}）`;
  let tableStr = '';

  list.forEach((item, index) => {
    tableStr += `    
    <tr>             
      <td>${index + 1}</td>
      <td>${item.productModel}</td>
      <td>${item.sn1}</td>
      <td>${item.sn2}</td>
      <td>${item.imei1}</td>
      <td>${item.imei2}</td>
      <td>${item.trackingNumber}</td>
      <td>${item.logisticsCompany}</td>
      <td>${item.recipient}</td>
      <td>${item.applicationName}</td>
      </tr>  
   `;
  });

  const str = `<!DOCTYPE html>
  <html lang="en">
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Document</title>
      <style>
          table {
              margin: 0 auto;
              border: 1px solid #000000;
              border-collapse: collapse;
          }
          thead {
            background-color: #AAE3EA;
          }
          th,
          td {
              border: 1px solid #000000;
              text-align: center;
          }
          
        .red {
          color: red;
        }
      </style>
  </head>
  
  <body>
      <div class="vditor-reset" id="vditorScrollParent">
          <p>经双方协商，现华为提供以下开发设备用于伙伴针对双方合作项目的研发及测试，请您收到开发设备后注意如下事项并回复确认。</p>
          <p>1、开发设备一经签收，则物权归属由花瓣云科技有限公司所有变更为<strong>${list[0].applicationSubject}</strong>所有；</p>
          <p class="red"><strong>2、禁止倒卖，禁止将设备退回给华为员工个人。</strong></p>
          <table style="width: 100%;">
              <thead>
                  <tr>
                      <th>序号</th>
                      <th>*产品型号</th>
                      <th>*SN1</th>
                      <th>*SN2</th>
                      <th>*IMEI1</th>
                      <th>*IMEI2</th>
                      <th>*投递单号</th>
                      <th>物流公司</th>
                      <th>*收件人信息</th>
                      <th>*应用名称</th>
                  </tr>
              </thead>
              <tbody>
                
                     ${tableStr}
  
              </tbody>
          </table>
          <p>请确认后按照如下要求进行回复：</p>
          <p>1、回复内容：<strong>${list[0].applicationSubject}</strong>知悉并确认，自机器签收之时起，机器所有权归<strong>${list[0].applicationSubject}</strong>所有，在完成合作项目研发测试后无需归还。</p>
          <p>2、抄送要求：回复邮件中需<strong>抄送至少2位贵司参与鸿蒙项目人员</strong>，至少1人为高层，谢谢！</p></p>
      </div>
  </body>
  
  </html>`;

  return { subject, content: str };
};

// 签收（旧）模板
export const template_DELIVERY_DEVICE = (list) => {
  const arr = [...new Set(list.map((item) => item.applicationName))];
  const subject = `伙伴开发设备签收-（${list[0].applicationSubject}）`;
  let tableStr = '';

  list.forEach((item, index) => {
    tableStr += `
    <tr>
    <td>${index + 1}</td>
    <td>${item.productModel}</td>
    <td>${item.sn1}</td>
    <td>${item.imei1}</td>
    <td>${item.imei2}</td>
    <td>${item.trackingNumber}</td>
    <td>${item.logisticsCompany}</td>
    <td>${item.recipient}</td>
    <td>${item.applicationName}</td>
    </tr>  
   `;
  });

  const str = `<!DOCTYPE html>
  <html lang="en">
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Document</title>
      <style>
          table {
              margin: 0 auto;
              border: 1px solid #000000;
              border-collapse: collapse;
          }
          thead {
            background-color: #AAE3EA;
          }
          th,
          td {
              border: 1px solid #000000;
              text-align: center;
          }
          .red {
            color: red;
          }
      </style>
  </head>
  <body>
  <div class="vditor-reset" id="vditorScrollParent" >
  <p><strong>${list[0].applicationSubject}</strong> 的伙伴好，</p>
  <p>您好，经双方协商，现华为提供以下开发设备用于伙伴针对双方合作项目的研发及测试，请您收到开发设备后回复确认签收，谢谢。</p>
  <p>开发设备清单：</p>
  <table style="width:100%;">
  <thead>
  <tr>
  <th><strong>序号</strong></th>
  <th><strong>产品型号</strong></th>
  <th><strong>SN1</strong>​<strong>号</strong></th>
  <th><strong>IMEI1</strong>​<strong>号</strong></th>
  <th><strong>IMEI2</strong>​<strong>号</strong></th>
  <th><strong>投递单号</strong></th>
  <th><strong>物流公司</strong></th>
  <th><strong>收件人信息</strong></th>
  <th><strong>应用名称</strong></th>
  </tr>
  </thead>
  <tbody>
  ${tableStr}
  </tbody>
  </table>
  <p>说明：（回复邮件时请删除下列内容）</p>
  <p>请您收到开发设备后回复“确认签收”，并抄送至少2位贵司参与鸿蒙项目人员，至少1人为高层，谢谢！</p>
  </div>
  </body>
  </html>`;

  return { subject, content: str };
};

// 签收（2B）模板
export const template_DELIVERY_DEVICE_FEEDBACK = (list) => {
  const arr = [...new Set(list.map((item) => item.applicationName))];
  let subject = `开发设备签收确认邮件-（${list[0].applicationSubject}）`;
  arr.forEach((item) => {
    subject += `（${item}）`;
  });
  let tableStr = '';

  list.forEach((item, index) => {
    tableStr += `
    <tr>
    <td>${index + 1}</td>
    <td>${item.productModel}</td>
    <td>${item.sn1}</td>
    <td>${item.imei1}</td>
    <td>${item.imei2}</td>
    <td>${item.trackingNumber}</td>
    <td>${item.logisticsCompany}</td>
    <td>${item.recipient}</td>
    <td>${item.applicationName}</td>
    <td>${item.applicationSubject}</td>
    </tr>  
   `;
  });

  const str = `<!DOCTYPE html>
  <html lang="en">
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Document</title>
      <style>
          table {
              margin: 0 auto;
              border: 1px solid #000000;
              border-collapse: collapse;
          }
          thead {
            background-color: #AAE3EA;
          }
          th,
          td {
              border: 1px solid #000000;
              text-align: center;
          }
      </style>
  </head>
  <body>
  <div class="vditor-reset" id="vditorScrollParent">
  <p>${list[0].applicationSubject}的伙伴好，</p>
  <p>经双方协商，现华为提供以下开发设备用于贵单位鸿蒙应用的测试，请您收到开发设备后​<strong>核对设备信息并回复确认</strong>​，详细信息如下</p>
  <table style="width:100%;">
  <thead>
  <tr>
  <th>序号</th>
  <th>*产品型号</th>
  <th>*SN1</th>
  <th>*IMEI1</th>
  <th>*IMEI2</th>
  <th>*投递单号</th>
  <th>物流公司</th>
  <th>*收件人信息</th>
  <th>*应用名称</th>
  <th>*单位名称</th>
  </tr>
  </thead>
  <tbody>
  ${tableStr}
  </tbody>
  </table>
  <p>测试期间，如下细则需遵守</p>
  <p>1、开发设备仅提供给贵单位进行测试，不做它用，物权归属华为所有，不得转卖。</p>
  <p>2、使用期间，请妥善使用和保管；如遇设备损坏、丢失等，请及时与华为侧接口人联系。</p>
  <p>3、测试完成后，华为将对开发设备进行回收，贵单位不能用市场上同款替代，需按签收的开发设备序列号清单进行归还。</p>
  </div>
  </body>
  </html>`;

  return { subject, content: str };
};

export const addressCName = {
  bdEmail: 'BD责任人',
  bdLeaderEmail: 'BD Leader',
  dtseEmail: 'DTSE责任人',
  dtseLeaderEmail: 'DTSE Leader',
  solutionEmail: '生态解决方案责任人',
  solutionLeaderEmail: '生态解决方案Leader',
};
