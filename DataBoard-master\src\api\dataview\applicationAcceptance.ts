import service from '@/utils/axios';

const baseUrl = '/test_application';
const testRecord = '/test_application/testing_record';
export const getApplicationList = (data) => {
  return service({
    url: `${baseUrl}/query`,
    method: 'post',
    data,
  });
};

export const addApplication = (data) => {
  return service({
    url: `${baseUrl}/add`,
    method: 'post',
    data,
  });
};
export const syncSpecial = (data) => {
  return service({
    url: `${baseUrl}/importSpecializedData`,
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};
export const addApplicationList = (data, isBasicAppData) => {
  return service({
    url: isBasicAppData ? `${baseUrl}/importBasicAppData ` : `${baseUrl}/importData`,
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};
export const updateApplication = (data) => {
  return service({
    url: `${baseUrl}/update`,
    method: 'post',
    data,
  });
};

export const exportApplication = (data) => {
  return service({
    url: `${baseUrl}/exportTestAppExcel`,
    method: 'post',
    data,
    responseType: 'blob',
  });
};
export const deleteApplication = (params) => {
  return service({
    url: `${baseUrl}/delete`,
    method: 'DELETE',
    params,
  });
};

export const addTestRes = (data) => {
  return service({
    url: `${testRecord}/add`,
    method: 'post',
    data,
  });
};
export const getTestRes = (params) => {
  return service({
    url: `${testRecord}/query`,
    method: 'get',
    params,
  });
};
export const updateTestRes = (data) => {
  return service({
    url: `${testRecord}/update`,
    method: 'post',
    data,
  });
};
export const deleteTestRes = (params) => {
  return service({
    url: `${testRecord}/delete`,
    method: 'delete',
    params,
  });
};

export const downloadTemplate = () => {
  return service({
    url: '/IRData/exportExcel',
    method: 'post',
    responseType: 'blob',
  });
};
export const importTop2k = (data) => {
  return service({
    url: `/test_application/importTop2k`,
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};
export const getDisplayedField = () => {
  return service({
    url: `/test_application/getDisplayedField`,
    method: 'get',
  });
};
export const updateDisplayedField = (data) => {
  return service({
    url: `/test_application/updateDisplayedField`,
    method: 'post',
    data,
  });
};
export const getTestingRes = (data) => {
  return service({
    url: `/test_application/testing_statistic`,
    method: 'post',
    data,
  });
};

export const getTestedRes = (params) => {
  return service({
    url: `/test_application/test_finished_statistic`,
    method: 'get',
    params,
  });
};

export const getColumns = () => {
  return service({
    url: `/test_application/column_query`,
    method: 'get',
  });
};
