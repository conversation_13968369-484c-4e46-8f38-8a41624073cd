<template>
  <div class="view-account">
    <div class="view-account-header"></div>
    <div class="view-account-container">
      <div class="view-account-top">
        <div class="view-account-top-logo"> <p style="font-size: 30px">技术支持能力中心</p> </div>
      </div>
      <div class="view-account-form">
        <n-form
          ref="formRef"
          label-placement="left"
          size="large"
          :model="formInline"
          :rules="rules"
        >
          <n-form-item label="姓名" path="userName" v-show="fasle">
            <n-input v-model:value="formInline.userName" />
          </n-form-item>
          <n-form-item path="account">
            <n-input v-model:value="formInline.account" placeholder="请输入W3账号如：s30052949">
              <template #prefix>
                <n-icon size="18" color="#808695">
                  <PersonOutline />
                </n-icon>
              </template>
            </n-input>
          </n-form-item>
          <n-form-item path="password">
            <n-input
              v-model:value="formInline.password"
              type="password"
              showPasswordOn="click"
              placeholder="请输入密码（默认密码：123456）"
            >
              <template #prefix>
                <n-icon size="18" color="#808695">
                  <LockClosedOutline />
                </n-icon>
              </template>
            </n-input>
          </n-form-item>
          <n-form-item class="default-color">
            <div class="flex justify-between">
              <div class="flex-initial">
                <n-checkbox v-model:checked="rememberMe">记住我</n-checkbox>
              </div>
              <div style="display: none" class="flex-initial order-last">
                <a href="javascript:">忘记密码</a>
              </div>
            </div>
          </n-form-item>
          <n-form-item>
            <n-button type="primary" @click="handleSubmit(1)" size="large" :loading="loading" block>
              登录
            </n-button>
          </n-form-item>
          <n-form-item style="display: none" class="default-color">
            <div class="flex view-account-other">
              <div class="flex-initial">
                <span>其它登录方式</span>
              </div>
              <div class="flex-initial mx-2">
                <a href="javascript:">
                  <n-icon size="24" color="#2d8cf0">
                    <LogoGithub />
                  </n-icon>
                </a>
              </div>
              <div class="flex-initial mx-2">
                <a href="javascript:">
                  <n-icon size="24" color="#2d8cf0">
                    <LogoFacebook />
                  </n-icon>
                </a>
              </div>
              <div class="flex-initial" style="margin-left: auto">
                <a href="javascript:">注册账号</a>
              </div>
            </div>
          </n-form-item>
        </n-form>
      </div>
      <n-modal v-model:show="showModal">
        <n-card
          :style="{ width: '600px' }"
          title="注册"
          :bordered="false"
          size="huge"
          role="dialog"
          aria-modal="true"
        >
          <n-alert type="info" style="margin-bottom: 20px"> 新注册账号密码为123456 </n-alert>
          <n-form
            :model="formInline"
            :rules="rules"
            label-placement="left"
            label-width="auto"
            require-mark-placement="right-hanging"
            size="medium"
          >
            <n-form-item label="姓名" path="userName">
              <n-input v-model:value="formInline.userName" />
            </n-form-item>
            <n-form-item label="工号" path="account">
              <n-input v-model:value="formInline.account" placeholder="请输入带姓名首字母工号" />
            </n-form-item>
          </n-form>
          <template #footer>
            <n-space>
              <n-button secondary strong type="primary" @click="handleSubmit(0)"> 确认 </n-button>
              <n-button secondary strong type="error" @click="showModal = false"> 取消 </n-button>
            </n-space>
          </template>
        </n-card>
      </n-modal>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { useUserStore } from '@/store/modules/user';
  import { useMessage } from 'naive-ui';
  import { PersonOutline, LockClosedOutline, LogoGithub, LogoFacebook } from '@vicons/ionicons5';
  import { PageEnum } from '@/enums/pageEnum';
  import { validateAccount, validateName } from "@/utils/user";
  import { LoginRsp } from "@/api/system/user";
  interface FormState {
    account: string;
    password: string;
    userName: string;
  }

  const formRef = ref();
  const message = useMessage();
  const loading = ref(false);
  const rememberMe = ref(false);
  const LOGIN_NAME = PageEnum.BASE_LOGIN_NAME;
  const REMEMBER_KEY = 'loginInfo';
  const showModal = ref(false);
  const formInline = reactive({
    account: '',
    password: '',
    isCaptcha: false,
    userName: '',
  });

  const rules = {
    account: [
      { key: 'account', required: true, message: '请输入账号', trigger: 'blur' },
      {
        key: 'account',
        validator: validateAccount,
        message: '请输入带姓首字母格式，比如张晓明的工号：z00123456，英文小写',
        trigger: ['blur', 'input'],
      },
    ],
    password: { key: 'password', required: true, message: '请输入密码', trigger: 'blur' },
    userName: [
      { key: 'userName', required: true, message: '请输入姓名', trigger: 'blur' },
      {
        key: 'userName',
        validator: validateName,
        message: '请输入真实姓名',
        trigger: ['blur', 'input'],
      },
    ],
  };

  const userStore = useUserStore();

  const router = useRouter();
  const route = useRoute();

  onMounted(() => {
    const loginInfo = localStorage.getItem(REMEMBER_KEY);
    if (loginInfo) {
      const { account, password, remember } = JSON.parse(loginInfo);
      formInline.account = account;
      formInline.password = password;
      rememberMe.value = remember;
    }
  });

  const handleSubmit = (e) => {
    formRef.value.validate(
      async (errors) => {
        if (!errors) {
          const { account, password, userName } = formInline;
          message.loading('登录中...');
          loading.value = true;

          const params: FormState = {
            account: account,
            password,
            userName,
          };
          const { status, message: msg } = await userStore.login(params)
            .catch(error => {
              return error?.response?.data as LoginRsp;
            })
            .finally(() => {
              message.destroyAll();
              loading.value = false;
            });
          if (status === '200') {
            if (rememberMe.value) {
              localStorage.setItem(
                REMEMBER_KEY,
                JSON.stringify({
                  account,
                  password,
                  remember: rememberMe.value,
                })
              );
            } else {
              localStorage.removeItem(REMEMBER_KEY);
            }
            const toPath = decodeURIComponent((route.query?.redirect || '/') as string);
            message.success('登录成功，即将进入系统');
            if (route.name === LOGIN_NAME) {
              router.replace('/');
            } else {
              router.replace(toPath);
            }
          } else if (status === '400') {
            message.info('登录失败，请检查账号密码');
          } else if (status === '410') {
            message.info('账号未注册，请联系管理员');
          } else {
            message.info(msg || '登录失败，请检查账号密码');
          }
        } else {
          message.error('请填写完整信息');
        }
      },
      (rule) => {
        let arr = e === 1 ? ['account', 'password'] : ['account', 'userName'];
        return arr.includes(rule.key);
      }
    );
  };
</script>

<style lang="less" scoped>
  .view-account {
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: auto;

    &-container {
      flex: 1;
      padding: 32px 12px;
      max-width: 384px;
      min-width: 320px;
      margin: 0 auto;
    }

    &-top {
      padding: 32px 0;
      text-align: center;

      &-desc {
        font-size: 14px;
        color: #808695;
      }
    }

    &-other {
      width: 100%;
    }

    .default-color {
      color: #515a6e;

      .ant-checkbox-wrapper {
        color: #515a6e;
      }
    }
  }

  @media (min-width: 768px) {
    .view-account {
      background-image: url('../../assets/images/login.svg');
      background-repeat: no-repeat;
      background-position: 50%;
      background-size: 100%;
    }

    .page-account-container {
      padding: 32px 0 24px 0;
    }
  }
</style>
