import { ewpService as http } from '@/utils/axios';

export function getRep(data: any) {
  return http.request({
    url: '/management/employeeRepresent/query',
    method: 'POST',
    data,
  });
}

export function addRep(data: any) {
  return http.request({
    url: '/management/employeeRepresent/save',
    method: 'POST',
    data,
  });
}

export function deleteRep(id: any) {
  return http.request({
    url: `/management/employeeRepresent/delete`,
    method: 'POST',
    data: {
      id: id,
    }
  });
}
