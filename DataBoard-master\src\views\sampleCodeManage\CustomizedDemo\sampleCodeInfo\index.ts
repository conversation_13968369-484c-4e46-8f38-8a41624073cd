import { AddDemoReq, CodeDemoDto } from "@/api/system/code";
import { Random } from "@/utils/random";
import { useUserStore } from "@/store/modules/user";

export function getAddDemoParams(params: CodeDemoDto, wishId?: number): AddDemoReq {
  const result: AddDemoReq = {
    demoId: Random.genUuid(24),
    demoName: params.demoName,
    type: params.type,
    selectValue: params.selectValue,
    apiVersion: params.apiVersion,
    demoDescription: params.demoDescription,
    demoLink: params.demoLink,
    lineNumber: params.lineNumber,
    demoSubmitter: useUserStore().getUserInfo.label,
    samples: params.samples,
    tags: params.tags,
    externalLink: params.externalLink,
  }
  wishId && (result.customizedDemoId = wishId);
  return result;
}

