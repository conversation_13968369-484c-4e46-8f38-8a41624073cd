import service from '@/utils/axios';
import { adminService as server, knowledgeService, ewpService } from '@/utils/axios';
export interface IRManageModel {
  _id: string;
  orderId: string;
  appName: string;
  ewpOwner: string;
  acceptanceOwner: string;
  irOrderStatus: string;
  irRegisTime: string;
  irOverTime: string;
}
export const getIRListBoard = (data: any) => {
  return service({
    url: '/IRData/queryIRDataByArgs',
    method: 'post',
    data: {
      pageNum: data.page || 0,
      pageSize: data.pageSize || 10,
      ...data,
    },
  });
};
export const getIRList = (params: any) => {
  return server({
    url: '/irmange',
    method: 'get',
    params: {
      pageNo: params.pageNo || 1,
      pageSize: params.pageSize || 10,
      ...params,
    },
  });
};

export const addIRList = (data) => {
  return service({
    url: '/IRData/importIRdata',
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};

export const updateIRBoard = (data) => {
  return service({
    url: '/IRData/updateIRDataByArgs',
    method: 'post',
    data,
  });
};
export const batchDeleteIR = (data) => {
  return service({
    url: '/IRData/batchDelete',
    method: 'delete',
    data,
  });
};
export const createIR = (data: Partial<IRManageModel>) => {
  return server({
    url: '/irmange/create',
    method: 'post',
    data,
  });
};

export const downloadTemplate = () => {
  return service({
    url: '/IRData/exportExcel',
    method: 'post',
    responseType: 'blob',
  });
};

export const updateIR = (id: string, data: Partial<IRManageModel>) => {
  return server({
    url: `/irmange/${id}`,
    method: 'put',
    data,
  });
};

export const deleteIR = (id: string) => {
  return server({
    url: `/irmange/${id}`,
    method: 'delete',
  });
};

export const batchCreateIR = (data: Partial<IRManageModel>[]) => {
  return server({
    url: '/irmange/batch-create',
    method: 'post',
    data,
  });
};

export const DTSTurnTo = (data: any) => {
  return ewpService({
    url: '/management/workOrder/forward',
    method: 'post',
    data,
  });
};

// 添加获取未提单统计数据的接口
export function getUnsubmittedStats(params?: { startDate?: string; endDate?: string }) {
  return server({
    url: '/irmange/acceptance-owner/stats',
    method: 'get',
    params,
  });
}

export function exportExcel() {
  return server({
    url: '/irmange/exportdata',
    method: 'post',
    headers: {
      responseEncoding: 'utf8',
    },
    responseType: 'blob',
  });
}
// 添加获取未提单统计数据的接口
export function getExportIRExcel(data) {
  return service({
    url: '/IRData/exportIRExcel',
    method: 'post',
    data,
    responseType: 'blob',
  });
}
// 添加获取未提单统计数据的接口
export function syncedAbilityCenter(data) {
  return service({
    url: '/knowledge-admin/blog/save',
    method: 'post',
    data,
  });
}

export const submitIR = (data) => {
  return ewpService({
    url: '/management/workOrder/submitIssue',
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};
