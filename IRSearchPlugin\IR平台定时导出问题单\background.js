import { SETTINGS } from "./settings.js";

chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
  if (request.action == "getCookie") {
    let listener = (details) => {
      chrome.webRequest.onCompleted.removeListener(listener);
      chrome.cookies.get(
        {
          url: "https://issuereporter.developer.huawei.com",
          name: "csrfToken",
        },
        function (cookies) {
          sendResponse({
            csrfToken: cookies.value,
            request: request,
          });
          chrome.tabs.query(
            { url: "https://issuereporter.developer.huawei.com/*" },
            (tabs) => {
              try {
                if (tabs?.[0]?.id === sender.tab.id) {
                  chrome.tabs.sendMessage(tabs[0].id, "download");
                  chrome.tabs.sendMessage(tabs[0].id, "refreshToken");
                  chrome.alarms.clear("refreshToken");
                  chrome.alarms.create("refreshToken", {
                    delayInMinutes: 50,
                  });
                }
              } catch (error) {}
            }
          );
        }
      );
    };
    chrome.webRequest.onCompleted.addListener(listener, {
      urls: [
        "https://svc-drcn.developer.huawei.com/codeserver/Common/v1/delegate",
      ],
    });
  }
  return true;
});
const getDownloadTime = () =>
  new Date(`${new Date().toDateString()}  ${SETTINGS.downloadTime}`).getTime();
let downLoadTime = getDownloadTime();
let now = Date.now();
let when =
  downLoadTime - 1000 > now ? downLoadTime : downLoadTime + 24 * 60 * 60 * 1000;
chrome.alarms.clear("irPlugTimer");
chrome.alarms.create("irPlugTimer", {
  when,
});

chrome.alarms.onAlarm.addListener((e) => {
  if (e.name === "irPlugTimer") {
    chrome.alarms.clear("irPlugTimer");
    chrome.alarms.create("irPlugTimer", {
      when: getDownloadTime() + 24 * 60 * 60 * 1000,
    });
    chrome.tabs.query(
      { url: "https://issuereporter.developer.huawei.com/*" },
      (tabs) => {
        try {
          tabs?.[0]?.id && chrome.tabs.sendMessage(tabs[0].id, "download");
        } catch (error) {}
      }
    );
  } else if (e.name === "refreshToken") {
    chrome.alarms.clear("refreshToken");
    chrome.alarms.create("refreshToken", {
      delayInMinutes: 50,
    });
    chrome.tabs.query(
      { url: "https://issuereporter.developer.huawei.com/*" },
      (tabs) => {
        try {
          tabs?.[0]?.id && chrome.tabs.sendMessage(tabs[0].id, "refreshToken");
        } catch (error) {}
      }
    );
  }
});
