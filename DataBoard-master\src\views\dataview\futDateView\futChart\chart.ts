import { ewpService as service } from '@/utils/axios';

const stateMap = {
    init: '待分析',
    analyzedDts: '待提单',
    analyzedPending: '已挂起',
    open: '问题定界',
    unlocked: '待锁定',
    tracking: '待修复',
    regression: '待回归',
    closed: '已闭环',
}

export const getData = async () => {
    return service.post('/management/futOrder/overview', { afterDayCnt: '15' });
}

/**
 * 把 rowData 整理成 echarts 需要的格式
 * @param rowData 
 */
export const getEchartOptions = (rowData) => {
    const { series, xAxisData } = getSeriesAndXAxis(rowData)
    return {
        legend: {
            selectedMode: false
        },
        yAxis: {
            type: 'value'
        },
        xAxis: {
            type: 'category',
            data: xAxisData
        },
        series
    };
}

const getSeriesAndXAxis = (rawData) => {
    const series: echarts.BarSeriesOption[] = []
    const xAxisData: string[] = []
    const dateKeys = Object.keys(rawData).sort(function(a, b) {
            return new Date(a) - new Date(b);
        })
    dateKeys.forEach(date => {
        let seriesIndex = 0
        xAxisData.push(date.slice(5))
        Object.keys(rawData[date]).forEach(state => {
            // chart 不展示总量
            if(state === 'total') {
                return;
            }
            if(series[seriesIndex]) {
                series[seriesIndex++].data!.push(rawData[date][state])
            } else {
                series[seriesIndex++] = {
                    data: [rawData[date][state]],
                    label: {
                        show: true
                    },
                    name: stateMap[state],
                    stack: 'total',
                    type: 'bar'
                }
            }
        })
    });
    return {series, xAxisData}
}
