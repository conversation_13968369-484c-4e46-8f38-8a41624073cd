import { h } from 'vue';
import {
  isOverdueList,
  ISSUE_TYPE_OPTIONS,
  moduleList,
  ProblemDto,
  sourceList,
  statusList,
} from '@/views/publicOpinionManagement/problemHandle/index';
import {DataTableBaseColumn, NButton, NTag, NTooltip} from 'naive-ui';
import {useEventBus} from "@/hooks/useEventBus";

const eventBus = useEventBus();

export const BASE_COLUMNS: DataTableBaseColumn<ProblemDto>[] = [
  {
    title: '预警时间',
    key: 'warnTime',
    width: 110,
    resizable: true,
    render(row) {
      return row.warnTime ? h('div', new Date(row.warnTime).toLocaleDateString()) : '';
    },
  },
  {
    title: 'SLA截止时间',
    key: 'slaTime',
    width: 110,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    sorter: 'default',
    render(row) {
      return row.slaTime ? h('div', new Date(row.slaTime).toLocaleDateString()) : '';
    },
  },
  {
    title: '领域',
    key: 'module',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '问题来源',
    key: 'source',
    width: 100,
    resizable: true,
    render(row) {
      return h('div', sourceList.find((item) => item.value == row.source)?.label);
    },
  },
  {
    title: '问题类型',
    key: 'issueType',
    width: 100,
    resizable: true,
    render(row) {
      const label = ISSUE_TYPE_OPTIONS.find((item) => item.value === row.issueType)?.label;
      return h('div', label ?? '');
    },
  },
  {
    title: '应用名称',
    key: 'appName',
    width: 100,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      return h(
        NTag,
        {
          size: 'medium',
        },
        [h('div', row.appName)]
      );
    },
  },
  {
    title: '问题描述',
    key: 'desc',
    width: 200,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '舆情等级',
    key: 'level',
    width: 80,
    resizable: true,
    sorter: 'default',
    render(row) {
      return !row.level
        ? ''
        : h(
            NTag,
            {
              type: row.level == 'A' || row.level == 'B' ? 'error' : 'warning',
              size: 'medium',
            },
            [h('div', row.level)]
          );
    },
  },
  {
    title: '舆情声量',
    key: 'opinionVolume',
    width: 80,
    resizable: true,
    sorter: 'default',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '问题状态',
    key: 'status',
    width: 100,
    resizable: true,
    render(row) {
      return h('div', statusList.find((item) => item.value == row.status)?.label);
    },
  },
  {
    title: () =>
      h(
        'div',
        { style: { display: 'flex', alignItems: 'center', gap: '6px', justifyContent: 'center', } },
        [
          h(
            NTooltip,
            { trigger: 'hover' },
            {
              trigger: () =>
                h('div', { style: { display: 'flex', alignItems: 'center' } }, [
                  '进展&措施',
                  h(
                    'span',
                    { style: { marginLeft: '4px', cursor: 'pointer', color: '#1890ff' } },
                    '?'
                  ),
                ]),
              default: () => '双击编辑最新进展',
            }
          ),
        ]
      ),
    key: 'progress',
    resizable: true,
    width: 220,
    ellipsis: {
      tooltip: true,
    },
    render: (row) => h(
      'div',
      {
        style: {alignItems: 'center', gap: '6px', justifyContent: 'center'}
      },
      [
        row.progress && row.progress.length>0?
          h(
            NTooltip,
            { trigger: 'hover',maxWidth:'600px' },
            {
              trigger: () =>
                h('div', {
                  style: {  alignItems: 'center',whiteSpace:'nowrap',overflow: 'hidden',textOverflow:'ellipsis',width:'inherits'},
                  onDblclick: ()=>{
                    eventBus.emit('showModel',row);
                  }
                }, [
                  row.progress
                ]),
              default: () => row.progress,
            }
          ): h('div',{style:{width:'200px',height:'50px'}, onDblclick: ()=>{
              eventBus.emit('showModel',row);
            }},()=>{})
      ]
    ),
  },
  {
    title: '知识ID',
    key: 'knowledgeId',
    width: '12%',
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      return [
        h(
          NButton,
          {
            type: 'info',
            size: 'medium',
            text: true,
            tag: 'a',
            href: row.knowledgeLink,
            target: 'blank',
          },
          [h('div', row.knowledgeId)]
        ),
      ];
    },
  },
  {
    title: '优先级',
    key: 'applicationLv',
    width: 100,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '清单来源',
    key: 'problemSource',
    width: 100,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '计划上线时间',
    key: 'planTime',
    width: 100,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      return h('div', row.planTime ? new Date(row.planTime).toLocaleDateString() : '');
    },
  },
  {
    title: '保障责任人',
    key: 'guarantor',
    width: 100,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '责任人',
    key: 'owner',
    resizable: true,
    width: 100,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '领域责任人',
    key: 'moduleOwner',
    width: 100,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: 'DTS单号',
    key: 'dtsNum',
    width: 160,
    resizable: true,
    render(row) {
      return row.dtsNum
        ? h(
            NTag,
            {
              type: 'info',
              size: 'medium',
            },
            [
              h(
                'a',
                {
                  href: `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${row.dtsNum}`,
                  target: '_blank',
                },
                row.dtsNum
              ),
            ]
          )
        : '';
    },
  },
  {
    title: 'DTS状态',
    key: 'dtsStatus',
    width: 120,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '产品',
    key: 'product',
    width: 80,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '通报人',
    key: 'informer',
    width: 80,
    ellipsis: {
      tooltip: true,
    },
    resizable: true,
  },
  {
    title: '是否超期',
    key: 'isOverdue',
    width: 80,
    ellipsis: {
      tooltip: true,
    },
    resizable: true,
    render(row) {
      return h('div', isOverdueList.find((item) => item.value == row.isOverdue)?.label);
    },
  },
  {
    title: '超期类型',
    key: 'overdueType',
    width: 80,
    ellipsis: {
      tooltip: true,
    },
    resizable: true,
  },
  {
    title: '闭环时间',
    key: 'closeTime',
    width: 110,
    ellipsis: {
      tooltip: true,
    },
    resizable: true,
    render(row) {
      return h('div', row.closeTime ? new Date(row.closeTime).toLocaleDateString() : '');
    },
  },
  {
    title: '问题锁定时间',
    key: 'planLockTime',
    width: 100,
    ellipsis: {
      tooltip: true,
    },
    resizable: true,
    render(row) {
      return h('div', row.planLockTime ? new Date(row.planLockTime).toLocaleDateString() : '');
    },
  },
  {
    title: '定界完成时间',
    key: 'locateTime',
    width: 100,
    ellipsis: {
      tooltip: true,
    },
    resizable: true,
    render(row) {
      return h('div', row.locateTime ? new Date(row.locateTime).toLocaleDateString() : '');
    },
  },
  {
    title: '是否计划锁定超期',
    key: 'isOverduePlanLock',
    width: 100,
    ellipsis: {
      tooltip: true,
    },
    resizable: true,
    render(row) {
      return h('div', isOverdueList.find((item) => item.value == row.isOverduePlanLock)?.label);
    },
  },
  {
    title: '计划锁定时间',
    key: 'planLockTime',
    width: 100,
    ellipsis: {
      tooltip: true,
    },
    resizable: true,
    render(row) {
      return h('div', row.planLockTime ? new Date(row.planLockTime).toLocaleDateString() : '');
    },
  },
  {
    title: '备注',
    key: 'textarea',
    width: 100,
    ellipsis: {
      tooltip: true,
    },
    resizable: true,
  },
];
