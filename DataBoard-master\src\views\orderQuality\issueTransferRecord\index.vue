<template>
  <div>
    <n-card class="search-container">
      <n-form
        ref="formRef"
        :model="searchModel"
        label-placement="left"
        label-width="130"
        require-mark-placement="right-hanging"
        size="medium"
      >
        <n-grid :cols="4" :x-gap="24">
          <n-form-item-gi label="IR单号" path="irNumber">
            <n-input placeholder="输入关键字查找" v-model:value="searchModel.irNumber" />
          </n-form-item-gi>
          <n-form-item-gi label="指派操作人" path="operatePerson">
            <n-input placeholder="输入关键字查找" v-model:value="searchModel.operatePerson" />
          </n-form-item-gi>
          <n-form-item-gi label="操作人运营级别" path="operateLevel">
            <n-input placeholder="输入关键字查找" v-model:value="searchModel.operateLevel" />
          </n-form-item-gi>
          <n-form-item-gi label="最高运营级别" path="outputLevel">
            <n-input placeholder="输入关键字查找" v-model:value="searchModel.outputLevel" />
          </n-form-item-gi>
        </n-grid>
        <n-grid v-if="collapse" :cols="4" :x-gap="24">
          <n-form-item-gi label="曾经处理人" path="operatePerson">
            <n-input placeholder="输入关键字查找" v-model:value="searchModel.sourcePerson" />
          </n-form-item-gi>
          <n-form-item-gi label="曾经处理人运营级别" path="operateLevel">
            <n-input placeholder="输入关键字查找" v-model:value="searchModel.sourceLevel" />
          </n-form-item-gi>
          <n-form-item-gi label="目标处理人" path="operatePerson">
            <n-input placeholder="输入关键字查找" v-model:value="searchModel.targetPerson" />
          </n-form-item-gi>
          <n-form-item-gi label="目标处理人运营级别" path="operateLevel">
            <n-input placeholder="输入关键字查找" v-model:value="searchModel.targetLevel" />
          </n-form-item-gi>
          <n-form-item-gi label="L2.5评论" path="review">
            <n-input placeholder="输入关键字查找" v-model:value="searchModel.review" />
          </n-form-item-gi>
          <n-form-item-gi label="IR单状态" path="irStatus">
            <n-input placeholder="输入关键字查找" v-model:value="searchModel.irStatus" />
          </n-form-item-gi>
          <n-form-item-gi label="IR类型" path="irType">
            <n-input placeholder="输入关键字查找" v-model:value="searchModel.irType" />
          </n-form-item-gi>
          <n-form-item-gi span="2" label="转单时间" path="updateTime">
            <n-date-picker
              v-model:value="searchModel.createTime"
              type="datetimerange"
              clearable
              :style="{ width: '160%' }"
              format="yyyy/MM/dd HH:mm:ss"
              start-placeholder="选择范围查找"
              end-placeholder="选择范围查找"
            />
          </n-form-item-gi>
          <n-form-item-gi span="2" label="关单时间" path="closeTime">
            <n-date-picker
              v-model:value="searchModel.closeTime"
              type="datetimerange"
              clearable
              :style="{ width: '160%' }"
              format="yyyy/MM/dd HH:mm:ss"
              start-placeholder="选择范围查找"
              end-placeholder="选择范围查找"
            />
          </n-form-item-gi>
        </n-grid>
      </n-form>
      <n-space justify="center">
        <n-button secondary strong type="primary" @click="searchData()"> 查询 </n-button>
        <n-button secondary strong type="default" @click="refreshSearch()"> 重置 </n-button>
        <n-button type="primary" icon-placement="right" @click="collapse = !collapse">
          <template #icon>
            <n-icon size="14" class="unfold-icon" v-if="collapse">
              <UpOutlined />
            </n-icon>
            <n-icon size="14" class="unfold-icon" v-else>
              <DownOutlined />
            </n-icon>
          </template>
          {{ collapse ? '收起' : '展开' }}
        </n-button>
      </n-space>
    </n-card>
    <n-card>
      <n-space>
        <n-button secondary strong type="warning" @click="refreshData()">
          <template #icon>
            <n-icon>
              <Refresh />
            </n-icon>
          </template>
          刷新
        </n-button>
        <n-button v-if="isAdmin" secondary strong type="primary" @click="showListAddModal = true">
          批量导入
        </n-button>
      </n-space>
      <n-data-table
        :columns="columnsAll"
        :data="data"
        :row-key="getRowKeys"
        :pagination="pagination"
        :loading="loading"
        scroll-x
        remote
        style="margin-top:20px"
      />
    </n-card>
    <n-modal v-model:show="showListAddModal">
      <n-card
        style="width: 600px"
        title="批量导入"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-button text @click="handleDownload" style="margin-bottom: 20px"
          >点击下载导入模板</n-button
        >
        <n-upload
          action="#"
          :custom-request="customRequest"
          :multiple="true"
          :default-file-list="fileList"
          accept=".xls,.xlsx,"
          :max="1"
          directory-dnd
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <Add />
              </n-icon>
            </div>
            <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
          </n-upload-dragger>
        </n-upload>
      </n-card>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { computed } from 'vue';
  import { Add, Refresh, Close, GridOutline } from '@vicons/ionicons5';
  import { DownOutlined, UpOutlined } from '@vicons/antd';
  import  basicTable from './basicTable.vue';
  import { NIcon, NButton, NFlex, useMessage ,NTooltip} from 'naive-ui';
  import { ref, reactive, onMounted, h } from 'vue';
  import { cloneDeep, debounce } from 'lodash-es';
  import {
    IRTransferRecordQuery,
    issuesImport,
    issuesDownloadTemplate,
  } from '@/api/dataview/issueTransferRecord';
  import { defaultSearchModel, formatTime ,outColumns} from './index.ts';
  import { useUserStore } from '@/store/modules/user';
  const loading = ref(true);
  const isAdmin = ref(false);
  let searchModelReactive = reactive(cloneDeep(defaultSearchModel));
  const searchModel = ref(searchModelReactive);
  const isSearch = ref(false);
  const collapse = ref(true);
  const searchParam = ref({});
  const showListAddModal = ref(false);
  const fileList = ref([]);
  const userStore = useUserStore();
  const roles = userStore.getUserInfo.roles;
  isAdmin.value = roles.includes('0_admin') || userStore.getUserInfo.isSuperAdmin;
  const message = useMessage();
  const getRowKeys = (row) => {
    return row.irNumber;
  };
  const data =ref([])
  const expand = ref([
    {
      title: '',
      key: '',
      width: 40,
      fixed: 'left',
      type: 'expand',
      renderExpand: (row, expand) => {
        return h(basicTable,{
          'data':row.listChild
        })
      },
    }
  ])
  const columns = ref(outColumns)
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 50],
    itemCount: 0,
    prefix({ itemCount }) {
      return `总条数 ${itemCount}`;
    },
    onChange: (page: number) => {
      pagination.page = page;
      search();
    },
    onUpdatePageSize: (pageSize: number) => {
      pagination.pageSize = pageSize;
      pagination.page = 1;
      search();
    },
  });
  const searchData = () => {
    searchParam.value = searchModel.value;
    isSearch.value = true;
    search();
  };

  const refreshSearch = () => {
    searchModelReactive = reactive(cloneDeep(defaultSearchModel));
    searchModel.value = searchModelReactive;
    isSearch.value = false;
    search();
  };

  const refreshData = () => {
    search();
  };

  const search = debounce(async () => {
    loading.value = true;
    let res;
    try {
      let param = {};
      if (isSearch.value) {
        param = cloneDeep(searchParam.value);
        param.closeTimeBegin = param.closeTime?.[0] ? formatTime(param.closeTime?.[0]) : null;
        param.closeTimeEnd = param.closeTime?.[1] ? formatTime(param.closeTime?.[1]) : null;
        param.createTimeBegin = param.createTime?.[0] ? formatTime(param.createTime?.[0]) : null;
        param.createTimeEnd = param.createTime?.[1] ? formatTime(param.createTime?.[1]) : null;
        delete param.closeTime;
        delete param.createTime;
      }
      param.pageNumber = String(pagination.page-1);
      param.pageSize = String(pagination.pageSize);
      let res = await IRTransferRecordQuery(param);
      if (res.status === '200') {
        data.value = []
        pagination.itemCount = res?.data?.count || 0;
        Object.keys(res?.data?.map).forEach(key => {
        data.value.push({
            ...res.data.map[key][0],
            listChild:res.data.map[key]
          })
      });
      
      }
    } catch (e) {}
    loading.value = false;
  }, 300);

  onMounted(() => {
    search();
  });
  //上传
  const customRequest = async ({ file, onError }: UploadCustomRequestOptions) => {
    const formData = new FormData();
    formData.append('file', file.file as File);
    //上传接口
    try {
      let res = await issuesImport(formData);
      if (res.status == '200') {
        showListAddModal.value = false;
        search();
        message.success('导入成功');
      } else {
        fileList.value = [];
      }
    } catch (err) {
      fileList.value = [];
      onError();
    }
  };
  //下载
  const handleDownload = async () => {
    issuesDownloadTemplate()
      .then((res) => {
        if (!res) {
          message.error('下载失败！');
        }
      })
      .catch((e) => {
        message.error(`下载失败，原因：${e?.response?.data?.error}`);
      });
  };
  const columnsAll = computed(() => expand.value.concat(columns.value));
</script>

<style lang="less" scoped>
  .search-container {
    margin-bottom: 10px;
  }
</style>
