import { ewpService as service } from '@/utils/axios';

export async function allIssueType(): Promise<any> {
  try {
    const response = await service.get('/management/agFeedback/allIssueType');
    return response;
  } catch (error) {
    throw error;
  }
}

export async function allUserType(): Promise<any> {
  try {
    const response = await service.get('/management/nssFeedback/allUserType');
    return response;
  } catch (error) {
    throw error;
  }
}
