import service from '@/utils/axios';

export const getRatioStatistic = (data) => {
  return service({
    url: `/IRTransport/getRatioStatistic`,
    method: 'post',
    data,
  });
};
export const  getNumberStatistic = (data) => {
  return service({
    url: `/IRTransport/getNumberStatistic`,
    method: 'post',
    data,
  });
};
export const exportNumberStatistic = (data) => {
  return service({
    url: `/IRTransport/exportNumberStatistic`,
    method: 'post',
    responseType: 'blob',
    data
  });
};
export const exportRatioStatistic = (data) => {
  return service({
    url: `/IRTransport/exportRatioStatistic`,
    method: 'post',
    responseType: 'blob',
    data
  });
};
