<template>
  <div class="basic-table">
    <h2 style="margin-left:24px;font-weight: bold">Ir单转单历史记录</h2>
   <n-data-table  :columns="sampleDevidesUsageRegistrationColumn" :data="data" scroll-x style="margin-top:12px;margin-left:24px;" />
  </div>
</template>

<script  lang="ts" setup>
const props = defineProps<{data: data}>();
import { ref } from 'vue';
import { sampleDevidesUsageRegistrationColumn} from './index.ts';
const columns = ref(sampleDevidesUsageRegistrationColumn);
</script>
<style lang="less" scoped>
.basic-table{
  max-width: 1500px;
}
:deep(table thead tr th) {
    background-color: #bdd7ee !important;
    font-size: 14px;
    font-weight: bold !important;
}

</style>