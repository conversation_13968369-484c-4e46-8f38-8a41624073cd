<template>
  <div>
    <n-card class="search-card">
      <n-form
        ref="searchFormRef"
        :model="searchForm"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        @submit.prevent="handleSearch"
      >
        <n-grid :cols="24" :x-gap="24">
          <n-grid-item v-for="item in visibleSearchFormItems" :key="item.field" :span="6">
            <n-form-item :label="item.label" :path="item.field">
              <n-input
                v-if="item.component === 'Input'"
                v-model:value="searchForm[item.field]"
                clearable
                @keyup.enter="handleSearch"
              />
              <n-select
                v-else-if="item.component === 'Select'"
                v-model:value="searchForm[item.field]"
                :options="item.options"
                clearable
              />
              <n-date-picker
                v-else-if="item.component === 'DateRangePicker'"
                v-model:value="searchForm[item.field]"
                type="daterange"
                clearable
                @update:value="handleDateRangeUpdate(item.field)"
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        <div class="form-actions">
          <n-space>
            <n-button @click="resetSearch">重置</n-button>
            <n-button type="primary" attr-type="submit" @click="handleSearch">查询</n-button>
            <n-button @click="toggleExpandForm">
              {{ isExpanded ? '收起' : '展开' }}
              <template #icon>
                <n-icon>
                  <chevron-down v-if="!isExpanded" />
                  <chevron-up v-else />
                </n-icon>
              </template>
            </n-button>
          </n-space>
        </div>
      </n-form>
    </n-card>

    <n-card>
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
        "
      >
        <n-space align="center">
          <n-button type="error" secondary :loading="exportLoading" @click="handleExport">
            导出Excel
          </n-button>
        </n-space>
      </div>

      <n-data-table
        remote
        :columns="tableColumns"
        @update:filters="handleFiltersChange"
        :data="tableData"
        :pagination="pagination"
        :loading="loading"
        :bordered="false"
        :single-line="false"
        striped
        :scroll-x="4000"
        :row-key="(row) => row.id"
        :max-height="435"
      />
    </n-card>

    <!-- 新增/编辑抽检工单对话框 -->
    <n-modal
      v-model:show="showModal"
      preset="card"
      title="抽检工单"
      style="width: 700px"
      :mask-closable="false"
    >
      <n-form
        ref="formRef"
        :model="formData"
        label-placement="left"
        :label-width="120"
        size="medium"
      >
        <n-grid :cols="24" :x-gap="24">
          <n-grid-item :span="12">
            <n-form-item label="抽检日期" path="checkDate">
              <n-date-picker
                disabled
                v-model:value="formData.checkDate"
                type="date"
                clearable
                style="width: 100%"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item :span="12">
            <n-form-item label="定界日期" path="delimitationDate">
              <n-date-picker
                disabled
                v-model:value="formData.delimitationDate"
                type="date"
                clearable
                style="width: 100%"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item :span="12">
            <n-form-item label="应用名称" path="appName">
              <n-input disabled v-model:value="formData.appName" placeholder="请输入应用名称" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item :span="12">
            <n-form-item label="问题单号" path="orderId">
              <n-input disabled v-model:value="formData.orderId" placeholder="请输入问题单号" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item :span="12">
            <n-form-item label="知识ID" path="knowledgeId">
              <n-input disabled v-model:value="formData.knowledgeId" placeholder="请输入知识ID" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item :span="12">
            <n-form-item label="模块" path="module">
              <n-input disabled v-model:value="formData.module" placeholder="请输入模块" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item :span="12">
            <n-form-item label="抽检人" path="checker">
              <n-input disabled v-model:value="formData.checker" placeholder="请输入抽检人" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item :span="12">
            <n-form-item label="EWP应用责任人" path="ewpOwner">
              <n-input disabled v-model:value="formData.ewpOwner" placeholder="请输入责任人" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item :span="12">
            <n-form-item label="小组" path="ewpGroup">
              <n-input disabled v-model:value="formData.ewpGroup" placeholder="请输入小组" />
            </n-form-item>
          </n-grid-item>
          <n-grid-item :span="12">
            <n-form-item label="是否完成整改" path="amendmentDone">
              <n-select
                v-model:value="formData.amendmentDone"
                :options="rectifiedOptions"
                placeholder="请选择整改状态"
                clearable
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item :span="24">
            <n-form-item label="定界规范" path="delimitationSpecifications">
              <n-select
                v-model:value="formData.delimitationSpecifications"
                :options="
                  boundarySpecOptions.map((item) => ({
                    label: `${item.code} ${item.text}`,
                    value: item.code,
                  }))
                "
                multiple
                placeholder="请选择定界规范"
                clearable
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item :span="24">
            <n-form-item label="分析结论规范" path="analysisSpecifications">
              <n-select
                v-model:value="formData.analysisSpecifications"
                :options="
                  analysisSpecOptions.map((item) => ({
                    label: `${item.code} ${item.text}`,
                    value: item.code,
                  }))
                "
                multiple
                placeholder="请选择分析结论规范"
                clearable
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item :span="24">
            <n-form-item label="修改建议规范" path="amendmentSpecifications">
              <n-select
                v-model:value="formData.amendmentSpecifications"
                :options="
                  suggestionSpecOptions.map((item) => ({
                    label: `${item.code} ${item.text}`,
                    value: item.code,
                  }))
                "
                multiple
                placeholder="请选择修改建议规范"
                clearable
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item :span="24">
            <n-form-item label="流程规范" path="procedureSpecifications">
              <n-select
                v-model:value="formData.procedureSpecifications"
                :options="
                  processSpecOptions.map((item) => ({
                    label: `${item.code} ${item.text}`,
                    value: item.code,
                  }))
                "
                multiple
                placeholder="请选择流程规范"
                clearable
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item :span="24">
            <n-form-item label="备注" path="checkComment">
              <n-input
                v-model:value="formData.checkComment"
                type="textarea"
                placeholder="请输入备注"
                :autosize="{ minRows: 3, maxRows: 5 }"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item :span="24">
            <n-form-item label="澄清说明" path="clarification">
              <n-input
                v-model:value="formData.clarification"
                type="textarea"
                placeholder="请输入澄清说明"
                :autosize="{ minRows: 3, maxRows: 5 }"
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item :span="12">
            <n-form-item label="复审结果" path="reviewResult">
              <n-select
                v-model:value="formData.reviewResult"
                :options="reviewResultOptions"
                placeholder="请选择复审结果"
                clearable
              />
            </n-form-item>
          </n-grid-item>
          <n-grid-item :span="12">
            <n-form-item label="抽检结果" path="checkResult">
              <n-select
                v-model:value="formData.checkResult"
                :options="checkResultOptions"
                placeholder="请选择抽检结果"
                clearable
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showModal = false">取消</n-button>
          <n-button type="primary" @click="handleSubmit" :loading="submitting">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, h, reactive, onMounted } from 'vue';
  import {
    NButton,
    NCard,
    NSpace,
    NDataTable,
    NModal,
    NForm,
    NFormItem,
    NInput,
    NSelect,
    NDatePicker,
    useMessage,
    NGrid,
    NGridItem,
    FormInst,
    FormRules,
    NDivider,
    NIcon,
  } from 'naive-ui';
  import { ChevronUp, ChevronDown } from '@vicons/carbon';
  import {
    columns,
    boundarySpecOptions,
    analysisSpecOptions,
    suggestionSpecOptions,
    processSpecOptions,
    checkResultOptions,
    rectifiedOptions,
    reviewResultOptions,
  } from './columns';
  import {
    getSpotCheckOrders,
    saveSpotCheckOrder,
    deleteSpotCheckOrder,
  } from '../../../../api/dataview/myOrder/spotCheck';
  import { getModules } from '@/api/dataview/myOrder';
  import { useUserStore } from '@/store/modules/user';

  const message = useMessage();
  const formRef = ref<FormInst | null>(null);
  const loading = ref(false);
  const submitting = ref(false);
  const isExpanded = ref(false);

  // 表格数据
  const tableData = ref<any[]>([]);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 50],
    itemCount: 0,
    prefix({ itemCount }) {
      return `Total：${itemCount}`;
    },
    onChange: (page: number) => {
      pagination.page = page;
      fetchTableData();
    },
    onUpdatePageSize: (pageSize: number) => {
      pagination.pageSize = pageSize;
      pagination.page = 1;
      fetchTableData();
    },
  });
  const searchFormRef = ref(null);
  const userStore = useUserStore();
  const userInfo: any = userStore.getUserInfo || {};
  // 搜索表单
  const searchForm = reactive({
    appName: '',
    orderId: '',
    knowledgeId: '',
    module: '',
    checker: userInfo.userName,
    checkDate: null as any,
    startCheckDate: '',
    endCheckDate: '',
    delimitationDate: null as any,
    startDelimitationDate: '',
    endDelimitationDate: '',
    checkResult: null,
    ewpOwner: '',
    ewpGroup: '',
    amendmentDone: null,
    reviewResult: null,
    reviewOverdue: null,
    amendmentOverdue: null,
    spotcheckResult: null,
  });

  // 搜索表单项定义
  const searchFormItems = [
    {
      field: 'appName',
      label: '应用名称',
      component: 'Input',
    },
    {
      field: 'orderId',
      label: '问题单号',
      component: 'Input',
    },
    {
      field: 'knowledgeId',
      label: '知识ID',
      component: 'Input',
    },
    {
      field: 'module',
      label: '模块',
      component: 'Select',
      options: ref([]),
    },
    {
      field: 'checker',
      label: '抽检人',
      component: 'Input',
    },
    {
      field: 'checkDate',
      label: '抽检日期',
      component: 'DateRangePicker',
    },
    {
      field: 'delimitationDate',
      label: '定界时间',
      component: 'DateRangePicker',
    },
    {
      field: 'checkResult',
      label: '抽检结果',
      component: 'Select',
      options: checkResultOptions,
    },
    {
      field: 'ewpOwner',
      label: 'EWP责任人',
      component: 'Input',
    },
    {
      field: 'ewpGroup',
      label: '小组',
      component: 'Input',
    },
    {
      field: 'amendmentDone',
      label: '整改状态',
      component: 'Select',
      options: rectifiedOptions,
    },
    {
      field: 'reviewResult',
      label: '复审结果',
      component: 'Select',
      options: reviewResultOptions,
    },
    {
      field: 'reviewOverdue',
      label: '复审超期',
      component: 'Select',
      options: rectifiedOptions,
    },
    {
      field: 'amendmentOverdue',
      label: '整改超期',
      component: 'Select',
      options: rectifiedOptions,
    },
    {
      field: 'spotcheckResult',
      label: '抽检超期',
      component: 'Select',
      options: rectifiedOptions,
    },
  ];

  // 可见的搜索表单项
  const visibleSearchFormItems = computed(() => {
    return isExpanded.value ? searchFormItems : searchFormItems.slice(0, 4);
  });

  // 切换展开/收起
  const toggleExpandForm = () => {
    isExpanded.value = !isExpanded.value;
  };

  // 新增/编辑表单
  const showModal = ref(false);
  const formData = reactive({
    id: null as number | null,
    checkDate: null as number | null,
    delimitationDate: null as number | null,
    appName: '',
    orderId: '',
    knowledgeId: '',
    module: '',
    checker: '',
    delimitationSpecifications: [],
    analysisSpecifications: [],
    amendmentSpecifications: [],
    procedureSpecifications: [],
    checkComment: '',
    ewpOwner: '',
    ewpGroup: '',
    amendmentDone: '',
    reviewResult: '',
    clarification: '',
    reviewOverdue: '',
    amendmentOverdue: '',
    checkResult: '',
  });

  // 处理日期范围更新
  const handleDateRangeUpdate = (field: string) => {
    if (searchForm[field]) {
      if (field === 'checkDate') {
        searchForm.startCheckDate = searchForm.checkDate[0]
          ? formatDate(searchForm.checkDate[0])
          : '';
        searchForm.endCheckDate = searchForm.checkDate[1]
          ? formatDate(searchForm.checkDate[1])
          : '';
      } else if (field === 'delimitationDate') {
        searchForm.startDelimitationDate = searchForm.delimitationDate[0]
          ? formatDate(searchForm.delimitationDate[0])
          : '';
        searchForm.endDelimitationDate = searchForm.delimitationDate[1]
          ? formatDate(searchForm.delimitationDate[1])
          : '';
      }
    } else {
      if (field === 'checkDate') {
        searchForm.startCheckDate = '';
        searchForm.endCheckDate = '';
      } else if (field === 'delimitationDate') {
        searchForm.startDelimitationDate = '';
        searchForm.endDelimitationDate = '';
      }
    }
  };

  // 格式化日期
  const formatDate = (date: Date | number) => {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 获取表格数据
  const fetchTableData = async () => {
    loading.value = true;
    try {
      const params = {
        pageNo: pagination.page,
        pageSize: pagination.pageSize,
        ...searchForm,
      };
      const { total, records } = await getSpotCheckOrders(params);
      tableData.value = records;
      pagination.itemCount = total;
    } catch (error) {
      message.error('获取数据失败');
    } finally {
      loading.value = false;
    }
  };

  // 搜索方法
  const handleSearch = () => {
    pagination.page = 1;
    fetchTableData();
  };
  const moduleOptions = ref<{ label: string; value: string }[]>([]);
  const moduleOptionsValues = ref<string[]>([]);
  const fecthModules = async () => {
    const res = await getModules();
    moduleOptions.value = res.map((item: string) => {
      moduleOptionsValues.value.push(item);
      return {
        label: item,
        value: item,
      };
    });
    moduleOptions.value.push({
      label: '空',
      value: '-',
    });

    // 更新搜索表单项中的模块选项
    const moduleItem = searchFormItems.find((item) => item.field === 'module');
    if (moduleItem) {
      moduleItem.options = moduleOptions.value;
    }
  };
  // 重置搜索
  const resetSearch = () => {
    // 重置表单
    Object.keys(searchForm).forEach((key) => {
      searchForm[key] = [
        'startCheckDate',
        'endCheckDate',
        'startDelimitationDate',
        'endDelimitationDate',
      ].includes(key)
        ? ''
        : key.includes('Date') || key.includes('Time')
        ? null
        : '';
    });
    // 重新获取数据
    pagination.page = 1;
    fetchTableData();
    message.info('已重置搜索条件');
  };

  // 打开编辑模态框
  const openEditModal = (row: any) => {
    // const userInfo = userStore.getUserInfo || {};
    // // 检查当前用户是否为抽检人或管理员
    // const hasPermission = userInfo.userName === row.checker || userInfo.userName === row.ewpOwner;
    // if (!hasPermission) {
    //   message.error('您没有权限编辑此记录');
    //   return;
    // }

    resetFormData();

    // 处理字段名称不一致的问题
    const fieldMappings = {
      delimitationSpecifications: row.delimitationSpecifications || row.delimitationSpecification,
      analysisSpecifications: row.analysisSpecifications || row.analysisSpecification,
      amendmentSpecifications: row.amendmentSpecifications || row.amendmentSpecification,
      procedureSpecifications: row.procedureSpecifications || row.procedureSpecification,
    };

    Object.keys(formData).forEach((key) => {
      if (key in fieldMappings) {
        formData[key] = fieldMappings[key];
      } else if (key in row) {
        formData[key] = row[key];
      }
    });

    showModal.value = true;
  };

  // 重置表单数据
  const resetFormData = () => {
    Object.keys(formData).forEach((key) => {
      formData[key] =
        key.includes('Date') || key.includes('Time')
          ? null
          : Array.isArray(formData[key])
          ? []
          : typeof formData[key] === 'number'
          ? null
          : '';
    });
  };

  // 提交表单
  const handleSubmit = () => {
    // const userInfo = userStore.getUserInfo || {};
    // // 检查当前用户是否为抽检人或管理员
    // const hasPermission = userInfo.userName === formData.checker || userInfo.userName === 'admin';

    // if (!hasPermission) {
    //   message.error('您没有权限修改此记录，只有抽检人本人或管理员可以修改');
    //   return;
    // }

    formRef.value?.validate(async (errors) => {
      if (!errors) {
        submitting.value = true;
        try {
          // 格式化日期并处理规范字段
          const data = {
            ...formData,
            checkDate: formData.checkDate ? formatDate(formData.checkDate) : '',
            delimitationDate: formData.delimitationDate
              ? formatDate(formData.delimitationDate)
              : '',
            // 确保前后端字段名称和数组格式一致
            delimitationSpecifications: Array.isArray(formData.delimitationSpecifications)
              ? formData.delimitationSpecifications
              : [],
            analysisSpecifications: Array.isArray(formData.analysisSpecifications)
              ? formData.analysisSpecifications
              : [],
            amendmentSpecifications: Array.isArray(formData.amendmentSpecifications)
              ? formData.amendmentSpecifications
              : [],
            procedureSpecifications: Array.isArray(formData.procedureSpecifications)
              ? formData.procedureSpecifications
              : [],
          };
          const res = await saveSpotCheckOrder(data);
          message.success('更新成功');
          showModal.value = false;
          fetchTableData();
        } catch (error) {
          message.error('保存失败');
        } finally {
          submitting.value = false;
        }
      } else {
        message.error('请填写必填项');
      }
    });
  };

  const handleFiltersChange = (filters: any) => {
    // 合并筛选条件到搜索表单
    Object.assign(searchForm, filters);
    // 重置到第一页
    pagination.page = 1;
    // 重新获取数据
    fetchTableData();
  };
  // 表格列配置
  const tableColumns = computed(() => {
    const cols = [...columns];

    // 添加操作列
    cols.push({
      title: '操作',
      key: 'actions',
      fixed: 'right' as const,
      resizable: true,
      filterMultiple: false,
      filterOptions: [],
      filter: () => true,
      render: (row) => {
        return h(
          NSpace,
          { align: 'center' },
          {
            default: () => [
              h(
                NButton,
                {
                  size: 'small',
                  type: 'primary',
                  onClick: () => openEditModal(row),
                },
                { default: () => '编辑' }
              ),
            ],
          }
        );
      },
    });

    return cols;
  });
  const exportLoading = ref(false);
  const handleExport = () => {
    return new Promise((resolve, reject) => {
      const req = new XMLHttpRequest();
      exportLoading.value = true;
      const queryParams: any = {
        pageNo: pagination.page,
        pageSize: pagination.pageSize,
        ...searchForm,
      };

      req.open(
        'POST',
        `http://${window.location.host}/ewp/management/spot-check/orders/sheet`,
        true
      );
      req.responseType = 'blob';
      req.setRequestHeader('Content-Type', 'application/json');
      req.onload = function () {
        const data = req.response;
        if (data.size === 0) {
          exportLoading.value = false;
          reject(new Error('No data to export'));
          return;
        }
        const blob = new Blob([data]);
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = '抽检工单导出.xlsx';
        a.href = blobUrl;
        a.click();
        exportLoading.value = false;
        resolve(true);
      };
      req.send(JSON.stringify(queryParams));
    });
  };
  // 组件挂载时获取数据
  onMounted(() => {
    fetchTableData();
    fecthModules();
  });
</script>

<style scoped>
  .search-card {
    margin-bottom: 24px;
  }

  .flex {
    display: flex;
  }

  .justify-end {
    justify-content: flex-end;
  }
  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 0px;
  }
</style>
