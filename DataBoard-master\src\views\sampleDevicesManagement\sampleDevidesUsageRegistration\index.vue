<template>
  <div>
    <n-card class="search-container">
      <n-form
        ref="formRef"
        :model="searchModel"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        size="medium"
      >
        <n-grid :cols="24" :x-gap="24">
          <n-form-item-gi :span="8" label="地域" path="region">
            <n-select v-model:value="searchModel.region" :options="regionList" clearable />
          </n-form-item-gi>
          <n-form-item-gi :span="8" label="状态" path="status">
            <n-select v-model:value="searchModel.status" :options="statusList" clearable />
          </n-form-item-gi>
          <n-form-item-gi :span="8" label="用途" path="purpose">
            <n-select v-model:value="searchModel.purpose" :options="purposeList" clearable />
          </n-form-item-gi>
          <n-form-item-gi :span="8" label="渠道" path="purpose">
            <n-select v-model:value="searchModel.channel" :options="channelList" clearable />
          </n-form-item-gi>
          <n-form-item-gi :span="8" label="应用名称" path="purpose">
            <n-input v-model:value="searchModel.applicationName" />
          </n-form-item-gi>
        </n-grid>
      </n-form>
      <n-space justify="center">
        <n-button secondary strong type="primary" @click="searchData()"> 查询 </n-button>
        <n-button secondary strong type="default" @click="refreshSearch()"> 重置 </n-button>
      </n-space>
    </n-card>
    <n-card>
      <n-space>
        <n-button secondary strong type="warning" @click="refreshData()">
          <template #icon>
            <n-icon>
              <Refresh />
            </n-icon>
          </template>
          刷新
        </n-button>
        <n-button
          :disabled="!hasPermission"
          secondary
          strong
          type="primary"
          @click="showListAddModal = true"
        >
          导入
        </n-button>
        <n-button
          :disabled="!hasPermission"
          secondary
          strong
          type="primary"
          @click="
            showModal = true;
            isAdd = true;
          "
        >
          <template #icon>
            <n-icon>
              <Add />
            </n-icon>
          </template>
          添加
        </n-button>
      </n-space>
      <n-data-table
        :columns="columns"
        :data="data"
        :pagination="pagination"
        :loading="loading"
        style="margin-top: 20px"
      />
    </n-card>
    <n-modal v-model:show="showListAddModal">
      <n-card
        style="width: 600px"
        title="批量导入"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-button text @click="handleDownload" style="margin-bottom: 20px"
          >点击下载导入模板</n-button
        >
        <n-upload
          action="#"
          :custom-request="customRequest"
          :multiple="true"
          :default-file-list="fileList"
          accept=".xls,.xlsx,"
          :max="1"
          directory-dnd
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <Add />
              </n-icon>
            </div>
            <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
          </n-upload-dragger>
        </n-upload>
      </n-card>
    </n-modal>

    <n-modal v-model:show="showModal" :on-after-leave="handleAfterLeave">
      <n-card
        :style="{ width: '1200px' }"
        :title="isAdd ? '新增' : '编辑'"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <template #header-extra> </template>
        <n-form
          ref="formRef"
          :model="model"
          :rules="rules"
          label-placement="left"
          label-width="120"
          require-mark-placement="right-hanging"
          size="medium"
        >
          <n-grid :cols="24" :x-gap="24">
            <n-form-item-gi :span="12" label="设备渠道" path="channel">
              <n-select v-model:value="model.channel" :options="channelList" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="地域" path="region">
              <n-select v-model:value="model.region" :options="regionList" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="应用名称" path="applicationName">
              <n-input v-model:value="model.applicationName" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="设备品类" path="category">
              <n-input v-model:value="model.category" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="设备型号" path="model">
              <n-input v-model:value="model.model" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="设备编号" path="deviceNumber">
              <n-input v-model:value="model.deviceNumber" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="借用人" path="borrower">
              <n-input v-model:value="model.borrower" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="借用时间" path="borrowTime">
              <n-date-picker
                v-model:value="model.borrowTime"
                type="date"
                :style="{ width: '100%' }"
              />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="预期归还时间" path="expectedReturnTime">
              <n-date-picker
                v-model:value="model.expectedReturnTime"
                type="date"
                :style="{ width: '100%' }"
              />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="状态" path="status">
              <n-select v-model:value="model.status" :options="statusList" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="用途" path="purpose">
              <n-select v-model:value="model.purpose" :options="purposeList" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="归还人" path="returner">
              <n-input v-model:value="model.returner" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="归还时间" path="returnTime">
              <n-date-picker
                v-model:value="model.returnTime"
                type="date"
                :style="{ width: '100%' }"
              />
            </n-form-item-gi>
          </n-grid>
        </n-form>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" @click="handleSubmint()"> 确认 </n-button>
            <n-button secondary strong type="error" @click="showModal = false"> 取消 </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { Add, Refresh, Close, GridOutline } from '@vicons/ionicons5';
  import { NIcon, NButton, NFlex, useMessage } from 'naive-ui';
  import { ref, reactive, onMounted, h } from 'vue';
  import { cloneDeep, debounce } from 'lodash-es';
  import {
    defaultModel,
    defaultSearchModel,
    paginationReactive,
    statusList,
    purposeList,
  } from './index.ts';
  import { format, channelList, regionList } from '../sampleDevicesRecord/index.ts';
  import {
    deviceBRInsert,
    deviceBRSelect,
    deviceBRSelectBy,
    deviceBRUpdate,
    BRUpload,
    downloadBRTemplate,
  } from '@/api/sampleDevices/index';
  import { useUserStore } from '@/store/modules/user';
  import { UserRoleEnum } from "@/enums/UserRoleEnum";
  const userInfo = useUserStore().getUserInfo;
  const hasPermission = ref((userInfo.roles || []).includes(UserRoleEnum.DEVICE_MANAGER));
  const showModal = ref(false);
  const isAdd = ref(false);
  const loading = ref(true);
  const formRef = ref();
  let modelReactive = reactive(cloneDeep(defaultModel));
  const model = ref(modelReactive);
  let searchModelReactive = reactive(cloneDeep(defaultSearchModel));
  const searchModel = ref(searchModelReactive);
  const isSearch = ref(false);
  const searchParam = ref({});
  const showListAddModal = ref(false);
  const fileList = ref([]);
  const rules = ref({
    channel: {
      required: true,
      message: '请选择渠道',
      trigger: 'blur',
    },
    region: {
      required: true,
      message: '请选择地域',
      trigger: 'blur',
    },
    applicationName: {
      required: true,
      message: '请输入应用名称',
      trigger: 'blur',
    },
    deviceCategory: {
      required: true,
      message: '请输入设备品类',
      trigger: 'blur',
    },
    category: {
      required: true,
      message: '请输入品类',
      trigger: 'blur',
    },
    borrower: {
      required: true,
      message: '请输入借用人',
      trigger: 'blur',
    },
    borrowTime: {
      required: true,
      message: '请选择借用时间',
      trigger: 'blur',
      validator(rule: FormItemRule, value: string) {
        if (!value) {
          return false;
        }
        return true;
      },
    },
    status: {
      required: true,
      message: '请选择状态',
      trigger: 'blur',
    },
    purpose: {
      required: true,
      message: '请选择用途',
      trigger: 'blur',
    },
  });
  const data = ref();
  const message = useMessage();
  const sampleDevidesUsageRegistrationColumn = [
    {
      title: '序号',
      key: 'serialNumber',
      minWidth: 60,
      resizable: true,
    },
    {
      title: '设备',
      key: '设备',
      align: 'center',
      children: [
        {
          title: '渠道',
          key: 'channel',
          minWidth: 100,
          resizable: true,
        },
        {
          title: '地域',
          key: 'region',
          minWidth: 100,
          resizable: true,
        },
        {
          title: '应用名称',
          key: 'applicationName',
          minWidth: 100,
          resizable: true,
        },
        {
          title: '品类',
          key: 'category',
          minWidth: 100,
          resizable: true,
        },
        {
          title: '型号',
          key: 'model',
          minWidth: 100,
          resizable: true,
        },
        {
          title: '设备编号',
          key: 'deviceNumber',
          minWidth: 100,
          resizable: true,
        },
      ],
    },
    {
      title: '借用',
      key: '借用',
      align: 'center',
      children: [
        {
          title: '借用人',
          key: 'borrower',
          minWidth: 100,
          resizable: true,
        },
        {
          title: '借用时间',
          key: 'borrowTime',
          minWidth: 150,
          render: (row) => {
            return format(row.borrowTime);
          },
          resizable: true,
        },
        {
          title: '预期归还时间',
          key: 'expectedReturnTime',
          minWidth: 150,
          render: (row) => {
            return format(row.expectedReturnTime);
          },
          resizable: true,
        },
        {
          title: '状态',
          key: 'status',
          minWidth: 100,
          resizable: true,
        },
        {
          title: '用途',
          key: 'purpose',
          minWidth: 100,
          resizable: true,
        },
      ],
    },
    {
      title: '归还',
      key: 'return',
      align: 'center',
      children: [
        {
          title: '归还人',
          key: 'returner',
          minWidth: 100,
          resizable: true,
        },
        {
          title: '归还时间',
          key: 'returnTime',
          minWidth: 100,
          render: (row) => {
            return format(row.expectedReturnTime);
          },
          resizable: true,
        },
      ],
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      minWidth: 100,
      render: (row) => {
        return [
          h(
            NButton,
            {
              strong: true,
              tertiary: true,
              type: 'info',
              size: 'medium',
              disabled: !hasPermission.value,
              onClick: () => {
                model.value = cloneDeep(row);
                isAdd.value = false;
                showModal.value = true;
              },
            },
            () => '编辑'
          ),
        ];
      },
    },
  ];
  const columns = ref(sampleDevidesUsageRegistrationColumn);

  const handleAfterLeave = () => {
    modelReactive = reactive(cloneDeep(defaultModel));
    model.value = modelReactive;
  };

  const pagination = ref(paginationReactive);

  const searchData = () => {
    searchParam.value = searchModel.value;
    isSearch.value = true;
    search();
  };

  const refreshSearch = () => {
    searchModelReactive = reactive(cloneDeep(defaultSearchModel));
    searchModel.value = searchModelReactive;
    isSearch.value = false;
    search();
  };

  const refreshData = () => {
    search();
  };

  const search = debounce(async () => {
    loading.value = true;
    let res;
    if (isSearch.value) {
      let param = cloneDeep(searchParam.value);
      param.applicationName = param.applicationName?.trim();
      res = await deviceBRSelectBy(param);
    } else {
      res = await deviceBRSelect();
    }
    if (res.status === '200') {
      paginationReactive.itemCount = res?.data?.count || 0;
      data.value = res?.data?.list || [];
    }
    loading.value = false;
  }, 300);

  const handleSubmint = debounce(async () => {
    formRef.value.validate(async (errors) => {
      if (!errors) {
        let data = JSON.parse(JSON.stringify(model.value));
        try {
          let res = isAdd.value ? await deviceBRInsert(data) : await deviceBRUpdate(data);
          if (res.status == '200') {
            message.success('提交成功');
            showModal.value = false;
            search();
          }
        } catch (err) {
          message.error(err.message);
        }
      }
    });
  }, 300);

  onMounted(() => {
    search();
  });

  //上传
  const customRequest = async ({ file, onError }: UploadCustomRequestOptions) => {
    const formData = new FormData();
    formData.append('file', file.file as File);
    //上传接口
    try {
      let res = await BRUpload(formData);
      if (res.status == '200') {
        showListAddModal.value = false;
        search();
        message.success('导入成功');
      } else {
        fileList.value = [];
      }
    } catch (err) {
      fileList.value = [];
      onError();
    }
  };
  //下载
  const handleDownload = async () => {
    downloadBRTemplate()
      .then((res) => {
        if (!res) {
          message.error('下载失败！');
        }
      })
      .catch((e) => {
        message.error(`下载失败，原因：${e?.response?.data?.error}`);
      });
  };
</script>

<style lang="less" scoped>
  .search-container {
    margin-bottom: 10px;
  }
</style>
