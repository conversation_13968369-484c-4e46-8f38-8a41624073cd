<template>
  <div class="app-container">
    <div class="filter-container">
      <n-card>
        <n-form
          :model="searchForm"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          @submit.prevent="handleSearch"
        >
          <n-grid :cols="24" :x-gap="24">
            <n-grid-item v-for="item in visibleSearchFormItems" :key="item.field" :span="6">
              <n-form-item :label="item.label" :path="item.field">
                <template v-if="item.field === 'deviceList'">
                  <n-select
                    v-model:value="searchForm['deviceList']"
                    filterable
                    placeholder="搜索产品"
                    :options="deviceOptions"
                    :loading="deviceLoading"
                    clearable
                    multiple
                    remote
                    @search="handleSearchDevice"
                    @focus="handleSearchDevice('')"
                  />
                </template>
                <n-input
                  v-else-if="item.component === 'Input'"
                  v-model:value="searchForm[item.field]"
                  clearable
                  @keyup.enter="handleSearch"
                />
                <n-select
                  v-else-if="item.component === 'Select' && item.componentProps?.options"
                  v-model:value="searchForm[item.field]"
                  :options="item.componentProps.options"
                  clearable
                  :filterable="item.componentProps.filterable"
                  :multiple="
                    item.field === 'orderStatus' ||
                    item.field === 'issueTypeList' ||
                    item.field === 'deviceList'
                  "
                />
                <n-date-picker
                  v-else-if="item.component === 'DateRangePicker'"
                  v-model:value="searchForm[item.field]"
                  type="daterange"
                  clearable
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>
          <div class="form-actions">
            <n-space>
              <n-button @click="resetForm" type="default">重置</n-button>
              <n-button type="primary" attr-type="submit">查询</n-button>
              <n-button @click="toggleExpandForm" type="default">
                {{ isExpanded ? '收起' : '展开' }}
                <template #icon>
                  <n-icon>
                    <chevron-down v-if="!isExpanded" />
                    <chevron-up v-else />
                  </n-icon>
                </template>
              </n-button>
            </n-space>
          </div>
        </n-form>
      </n-card>
    </div>

    <n-card style="margin-top: 12px">
      <n-space align="center" style="margin-bottom: 10px; margin-top: -9px">
        <n-space>
          <n-button @click="confirmAddQuestion"> 新增</n-button>
        </n-space>
        <n-upload accept=".xlsx, .xls" :custom-request="handleFileUpload" :show-file-list="false">
          <n-button type="info" secondary :loading="importing">
            {{ importing ? '正在导入...' : '导入应用' }}
          </n-button>
        </n-upload>
        <n-button type="info" @click="handleExport" :loading="exportLoading"> 导出 </n-button>
      </n-space>
      <n-data-table
        remote
        :bordered="false"
        :single-line="false"
        striped
        @update:filters="handleFiltersChange"
        :columns="columns"
        :data="tableData"
        :pagination="paginationReactive"
        :loading="loading"
        :scroll-x="3000"
        :row-key="(row) => row.id"
        :checked-row-keys="checkedRowKeys"
        @update:checked-row-keys="handleCheckedRowKeysChange"
        @update:sorter="handleSorterChange"
        :max-height="477"
      />
    </n-card>

    <n-drawer v-model:show="dialogVisible" :width="520">
      <n-drawer-content :title="isAdd ? '新增' : '编辑'" closable>
        <n-form
          :model="actionForm"
          :rules="dynamicRules"
          :rule-props="{ immediate: true }"
          label-width="auto"
          label-placement="left"
          ref="formRef"
        >
          <n-form-item label="DTS单号" path="dtsOrderId">
            <n-input
              v-model:value.trim="actionForm.dtsOrderId"
              required
              placeholder="请输入DTS单号"
            />
          </n-form-item>
          <n-form-item label="用户提单时间" path="userSubmissionTime">
            <n-date-picker
              v-model:value="actionForm.userSubmissionTime"
              value-format="yyyy/MM/dd"
              type="date"
              clearable
            />
          </n-form-item>
          <n-form-item label="应用名称" path="appName">
            <n-input
              v-model:value.trim="actionForm.appName"
              required
              placeholder="请输入应用名称"
              @change="selectUsername"
            />
          </n-form-item>
          <n-form-item label="是否top2000">
            <n-select
              v-model:value="actionForm.top"
              placeholder="请选择"
              :options="trueFalseOption"
              clearable
            />
          </n-form-item>
          <!--          <n-form-item label="场景名称" path="sceneName">-->
          <!--            <n-input-->
          <!--              v-model:value.trim="actionForm.sceneName"-->
          <!--              required-->
          <!--              placeholder="请输入场景名称"-->
          <!--            />-->
          <!--          </n-form-item>-->
          <n-form-item label="问题描述" path="orderDescription">
            <n-input
              v-model:value.trim="actionForm.orderDescription"
              required
              placeholder="请输入问题描述"
              type="textarea"
              clearable
            />
          </n-form-item>
          <n-form-item label="问题类型">
            <n-select
              v-model:value="actionForm.issueTypeList"
              placeholder="请选择类型"
              :options="typeOptions"
              clearable
              multiple
            />
          </n-form-item>
          <n-form-item label="状态" path="orderStatus">
            <n-select
              v-model:value="actionForm.orderStatus"
              placeholder="请选择状态"
              :options="dtsStatusOptions"
              clearable
            />
          </n-form-item>
          <n-form-item label="闭环时间" path="solutionPlan">
            <n-date-picker
              v-model:value="actionForm.solutionPlan"
              value-format="yyyy/MM/dd"
              type="date"
              clearable
            />
          </n-form-item>
          <!--          <n-form-item label="问题级别" path="appType">-->
          <!--            <n-select-->
          <!--              v-model:value="actionForm.severity"-->
          <!--              placeholder="请选择问题级别"-->
          <!--              :options="questionLevelOptions"-->
          <!--              clearable-->
          <!--            />-->
          <!--          </n-form-item>-->
          <!--          <n-form-item label="舆情声量" path="opinionVolume">-->
          <!--            <n-input-->
          <!--              v-model:value.trim="actionForm.opinionVolume"-->
          <!--              required-->
          <!--              placeholder="请输入舆情声量"-->
          <!--            />-->
          <!--          </n-form-item>-->
          <n-form-item label="问题进展" @dblclick="formProgressClick">
            <n-input placeholder="请在表格内双击问题进展填写" type="textarea" disabled clearable />
          </n-form-item>
          <n-form-item label="产品（可搜索）">
            <n-select
              v-model:value="actionForm.deviceList"
              filterable
              placeholder="搜索产品"
              :options="deviceOptions"
              :loading="deviceLoading"
              clearable
              multiple
              remote
              @search="handleSearchDevice"
              @focus="handleSearchDevice('')"
            />
          </n-form-item>
          <n-form-item label="EWP责任人">
            <n-select
              v-model:value="actionForm.ewpOwner"
              placeholder="请选择应用责任人"
              :options="employeesNameOptions"
              clearable
              filterable
            />
          </n-form-item>
          <n-form-item label="应用PM">
            <n-input v-model:value.trim="actionForm.appPM" placeholder="请输入应用PM" />
          </n-form-item>
          <n-form-item label="反馈人/响应群名">
            <n-input v-model:value.trim="actionForm.feedbackSource" placeholder="反馈人/响应群名" />
          </n-form-item>
          <n-form-item label="来源是否闭环">
            <n-select
              v-model:value="actionForm.sourceClosed"
              :options="sourceClosedOption"
              clearable
              placeholder="请选择"
            />
          </n-form-item>
          <n-form-item label="来源" path="source">
            <n-select
              v-model:value="actionForm.source"
              :options="sourceOptions"
              clearable
              placeholder="请选择来源"
            />
          </n-form-item>
          <n-form-item label="来源登记人">
            <n-input v-model:value="actionForm.sourceOwner" placeholder="请输入来源登记人" />
          </n-form-item>
          <n-form-item label="服务单号">
            <n-input v-model:value.trim="actionForm.orderId" placeholder="请输入服务单号" />
          </n-form-item>
          <n-form-item label="应用版本">
            <n-input v-model:value.trim="actionForm.appVersion" placeholder="请输入应用版本" />
          </n-form-item>
          <n-form-item label="系统版本">
            <n-input v-model:value.trim="actionForm.osVersion" placeholder="请输入系统版本" />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space>
            <n-button @click="cancel">取消</n-button>
            <n-button type="primary" @click="sureAdd">确认</n-button>
          </n-space>
        </template>
      </n-drawer-content>
    </n-drawer>

    <ProblemProgressModal
      v-model:show="showProblemModal"
      :order-id="currentProblem?.orderId || ''"
      :current-problem="currentProblem"
      :after-submit="afterSubmit"
    />
  </div>
</template>

<script lang="ts">
  export default {
    name: 'Dtsstate',
  };
</script>

<script lang="ts" setup>
  import { ref, reactive, onMounted, computed, h } from 'vue';
  import { FormRules, PaginationProps, FormInst, NTag } from 'naive-ui';
  import {
    NCard,
    NForm,
    NFormItem,
    NInput,
    NSelect,
    NButton,
    NSpace,
    NGrid,
    NGridItem,
    NUpload,
    useMessage,
    NDataTable,
    NDatePicker,
    NTooltip,
  } from 'naive-ui';
  import {
    dtsStatusOptions,
    employeesNameOptions,
    getSearchFormItems,
    sourceClosedOption,
    sourceOptions,
    trueFalseOption,
    typeOptions,
  } from './columns';
  import { filterObjectValues, formatDateTime } from '@/utils';
  import {
    addQuestion,
    deleteQuestion,
    getInfoFromOrderId,
    getQuestions,
    syncQuestion,
  } from '@/api/dataview/serviceAndOtherQuestions';
  import { ChevronDown, ChevronUp } from '@vicons/ionicons5';
  import { BasicColumn } from '@/components/Table';
  import { queryByNameOrType } from '@/api/dataview/phoneConfig';
  import { ewpService as service } from '@/utils/axios';
  import ProblemProgressModal from '@/views/dataview/myOrder/components/ProblemProgressModal.vue';
  import dayjs from 'dayjs';

  interface SearchFormType {
    registerDate: [Date, Date] | null;
    complaintId: string;
    issueTypeList: string[];
    complaintContent: string;
    occurrenceTime: [Date, Date] | null;
    productName: string;
    productVersion: string;
    relatedApp: string;
    appOwner: string;
    isTop1000: boolean | null;
    isNewIssue: boolean | null;
    dtsUrl: string;
    isRegisteredInternal: boolean | null;
    remarks: string;
    top: string;
    sourceOwner: string;
    orderStatus: string[];
    [key: string]: any;
  }

  interface TableRowType {
    id: string | number;

    [key: string]: any;
  }

  const importing = ref(false);
  const isImporting = ref(false);
  const exportLoading = ref(false);
  const isExporting = ref(false);

  //设备类型搜索多选
  const deviceOptions = ref([]);
  const deviceLoading = ref(false);
  const handleSearchDevice = async (query: string) => {
    const params = {
      pageNo: 1,
      pageSize: 20,
      nameOrType: query,
    };
    deviceLoading.value = true;
    const res = await queryByNameOrType(params);
    deviceOptions.value = res.map((item) => {
      return {
        label: item,
        value: item,
      };
    });
    deviceLoading.value = false;
  };

  const searchForm = reactive<SearchFormType>({
    registerDate: null,
    complaintId: '',
    issueTypeList: ['风险问题'],
    complaintContent: '',
    occurrenceTime: null,
    productName: '',
    productVersion: '',
    relatedApp: '',
    appOwner: '',
    isTop1000: null,
    isNewIssue: null,
    dtsUrl: '',
    isRegisteredInternal: null,
    remarks: '',
    top: '',
    sourceOwner: '',
    orderStatus: [],
    userSubmissionTime: null,
  });

  //新增模块
  const dialogVisible = ref(false);
  const isAdd = ref(true);
  const formRef = ref<FormInst | null>(null);
  const actionForm = ref({
    id: null,
    orderId: '',
    source: '',
    // userSubmissionDate: '',
    userSubmissionTime: '',
    appName: '',
    appVersion: '',
    // sceneName: '',
    orderDescription: '',
    dtsOrderId: '',
    orderStatus: '',
    solutionPlan: '',
    // solutionPlanDate: null,
    // severity: '',
    // opinionVolume: '',
    progress: '',
    device: '',
    deviceList: [],
    osVersion: '',
    ewpOwner: '',
    feedbackSource: '',
    sourceClosed: '否',
    appPM: '',
    sourceOwner: '',
    top: '',
    issueType: '',
    issueTypeList: [],
  });
  const dynamicRules = reactive<FormRules>({
    appName: [{ required: true, message: '请输入应用名称' }],
    orderDescription: [{ required: true, message: '请输入问题描述' }],
    source: [{ required: true, message: '请选择来源' }],
    userSubmissionTime: [{ required: true, message: '请输入提单时间' }],
  });

  const tableData = ref<TableRowType[]>([]);
  const loading = ref(false);
  const message = useMessage();
  const searchFormItems = computed(() => getSearchFormItems());
  const paginationReactive = reactive<PaginationProps>({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    prefix({ itemCount }) {
      return `Total：${itemCount}`;
    },
    onChange: (page: number) => {
      paginationReactive.page = page;
      fetchData();
    },
    onUpdatePageSize: (pageSize: number) => {
      paginationReactive.pageSize = pageSize;
      paginationReactive.page = 1;
      fetchData();
    },
  });

  const sortState = ref({
    sortField: 'userSubmissionTime',
    sortOrder: 'desc',
  });

  const isExpanded = ref(false);
  const visibleSearchFormItems = computed(() => {
    return isExpanded.value ? searchFormItems.value : searchFormItems.value.slice(0, 4);
  });

  // 表格选择框
  const selectedRows = ref<TableRowType[]>([]);
  const checkedRowKeys = ref<(string | number)[]>([]);
  const handleCheckedRowKeysChange = (keys: (string | number)[]) => {
    const newSelectedRows = keys
      .map((key) => {
        const existingRow = selectedRows.value.find((row) => row.id === key);
        if (existingRow) {
          return existingRow;
        }
        return tableData.value.find((row) => row.id === key);
      })
      .filter(Boolean) as TableRowType[];

    selectedRows.value = newSelectedRows;
    checkedRowKeys.value = keys;
  };

  const handleSorterChange = (sorter) => {
    if (sorter) {
      const { columnKey, order } = sorter;
      sortState.value.sortField = columnKey;
      sortState.value.sortOrder = order === 'ascend' ? 'asc' : 'desc';
    } else {
      sortState.value.sortField = 'userSubmissionTime';
      sortState.value.sortOrder = 'desc';
    }
    paginationReactive.page = 1; // 重置到第一页
    fetchData();
  };

  // 关联DTS单模态框
  const showRelateModal = ref(false);

  // 关联DTS单
  const relateDTS = ref('');

  const handleSearch = () => {
    paginationReactive.page = 1;
    fetchData();
  };

  const resetForm = () => {
    Object.keys(searchForm).forEach((key) => {
      searchForm[key as keyof SearchFormType] = key.includes('Date') ? null : '';
    });
    searchForm.orderStatus = [];
    searchForm.userSubmissionTime = null;
    searchForm.issueTypeList = ['风险问题'];
    sortState.value.sortField = 'userSubmissionTime';
    sortState.value.sortOrder = 'desc';
    fetchData();
  };

  const toggleExpandForm = () => {
    isExpanded.value = !isExpanded.value;
  };

  // 修改 formatDate 函数
  const formatDate = (date: number | Date | null): string => {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const fetchData = async () => {
    loading.value = true;

    try {
      const queryParams: Record<string, any> = {
        ...filterObjectValues(searchForm),
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
        sortField: sortState.value.sortField,
        sortOrder: sortState.value.sortOrder,
        // orderStatus: searchForm.orderStatus.length === 0 ? ['待锁定'] : searchForm.orderStatus,
        // issueType: searchForm.issueType.length === 0 ? ['风险问题'] : searchForm.issueType,
      };

      if (searchForm.registerDate) {
        const [start, end] = searchForm.registerDate;
        queryParams.startTime = formatDate(start);
        queryParams.endTime = formatDate(end);
      }
      if (searchForm.userSubmissionTime) {
        const [start, end] = searchForm.userSubmissionTime;
        queryParams.startUserSubmissionTime = formatDate(start);
        queryParams.endUserSubmissionTime = formatDate(end);
      }
      queryParams.channel = '服务';
      const { total, records } = await getQuestions(queryParams);
      tableData.value = records;
      console.log(tableData.value, 'table');
      paginationReactive.itemCount = total;
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loading.value = false;
    }
  };

  const handleFiltersChange = (filters: any) => {
    // 合并筛选条件到搜索表单
    Object.assign(searchForm, filters);
    // 重置到第一页
    paginationReactive.page = 1;
    // 重新获取数据
    fetchData();
  };

  function openIMChatClick(expId) {
    let expIds = expId.split(',');
    expIds.forEach((id) => {
      eSpaceCtrl.showImDialog(id, function (err) {});
    });
  }

  const columns = computed(() => createColumns());

  type ExtendedBasicColumn = BasicColumn & { toWelink?: boolean };

  const createColumns = () => {
    let basicColumns: ExtendedBasicColumn[] = [
      {
        title: '来源是否闭环',
        key: 'sourceClosed',
        resizable: true,
        width: 110,
        align: 'center',
      },
      {
        title: '问题类型',
        key: 'issueTypeList',
        ellipsis: {
          tooltip: true,
        },
        show: true,
        width: 180,
        filterOptions: typeOptions,
        resizable: true,
        render(row) {
          return h(NSelect, {
            value: row.issueTypeList?.length === 0 || !row.issueTypeList ? null : row.issueTypeList,
            options: typeOptions,
            size: 'small',
            // clearable: true,
            multiple: true,
            style: { width: '150px' },
            async onUpdateValue(value) {
              if (!value) value = '';
              row.issueTypeList = value;
              await addQuestion({
                ...row,
                issueTypeList: value,
              });
              message.success('更新成功');
              fetchData();
            },
          });
        },
      },
      {
        title: '用户提单时间',
        key: 'userSubmissionTime',
        resizable: true,
        sorter: true,
        width: 130,
        render(row) {
          return formatDateTime(row.userSubmissionTime);
        },
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '应用名称',
        key: 'appName',
        resizable: true,
        width: 120,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '问题描述',
        key: 'orderDescription',
        resizable: true,
        width: 200,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: () =>
          h(
            'div',
            {
              style: {
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                justifyContent: 'center',
              },
            },
            [
              h(
                NTooltip,
                { trigger: 'hover' },
                {
                  trigger: () =>
                    h('div', { style: { display: 'flex', alignItems: 'center' } }, [
                      '问题最新进展',
                      h(
                        'span',
                        { style: { marginLeft: '4px', cursor: 'pointer', color: '#1890ff' } },
                        '?'
                      ),
                    ]),
                  default: () => '双击编辑最新进展',
                }
              ),
            ]
          ),
        key: 'progress',
        resizable: true,
        width: 200,
        ellipsis: {
          tooltip: true,
        },
        render: (row) =>
          h(
            'div',
            {
              style: { alignItems: 'center', gap: '6px', justifyContent: 'center' },
            },
            [
              row.progress && row.progress.length > 0
                ? h(
                    NTooltip,
                    { trigger: 'hover', maxWidth: '600px' },
                    {
                      trigger: () =>
                        h(
                          'div',
                          {
                            style: {
                              alignItems: 'center',
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              width: 'inherits',
                              height: 'inherits',
                            },
                            onDblclick: () => {
                              showModel(row);
                            },
                          },
                          [row.progress]
                        ),
                      default: () => row.progress,
                    }
                  )
                : h(
                    'div',
                    {
                      style: { width: '200px', height: '50px' },
                      onDblclick: () => {
                        showModel(row);
                      },
                    },
                    () => {}
                  ),
            ]
          ),
      },
      {
        title: 'EWP责任人',
        key: 'ewpOwner',
        resizable: true,
        width: 120,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '产品',
        key: 'deviceList',
        resizable: true,
        width: 120,
        render: (row) => {
          if (row.deviceList?.length === 0 || !row.deviceList) return '—';
          return h(
            NSpace,
            { wrap: true },
            {
              default: () =>
                row.deviceList.map((product) => {
                  const isAll = product === 'all';
                  return h(
                    NTag,
                    {
                      type: isAll ? 'default' : 'success',
                      round: true,
                      size: 'small',
                    },
                    { default: () => product }
                  );
                }),
            }
          );
        },
      },
      {
        title: '反馈人/响应群名',
        key: 'feedbackSource',
        resizable: true,
        width: 140,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '是否top2000',
        key: 'top',
        resizable: true,
        width: 120,
        align: 'center',
      },
      {
        title: '应用PM',
        key: 'appPM',
        resizable: true,
        width: 120,
        render: (row) => {
          return h(
            'a',
            {
              href: '#',
              onClick: (e) => {
                e.preventDefault();
                openIMChatClick(row.appPM);
              },
              style: {
                color: '#1288ff',
                textDecoration: 'none',
                cursor: 'pointer',
              },
            },
            row.appPM
          );
        },
      },
      {
        title: 'DTS单号',
        key: 'dtsOrderId',
        resizable: true,
        width: 200,
        ellipsis: {
          tooltip: true,
        },
        render: (row) => {
          return h(
            'a',
            {
              href: '#',
              onClick: (e) => {
                window.open(
                  `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${row.dtsOrderId}`,
                  '_blank'
                );
              },
              style: {
                color: '#1288ff',
                textDecoration: 'none',
                cursor: 'pointer',
              },
            },
            row.dtsOrderId
          );
        },
      },
      {
        title: '来源登记人',
        key: 'sourceOwner',
        resizable: true,
        width: 120,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '来源',
        key: 'source',
        resizable: true,
        width: 120,
        ellipsis: {
          tooltip: true,
        },
      },
      // {
      //   title: '场景名称',
      //   key: 'sceneName',
      //   resizable: true,
      //   width: 120,
      //   ellipsis: {
      //     tooltip: true,
      //   },
      // },
      {
        title: '状态',
        key: 'orderStatus',
        resizable: true,
        width: 130,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '闭环时间',
        key: 'solutionPlan',
        resizable: true,
        width: 150,
        ellipsis: {
          tooltip: true,
        },
        render(row) {
          return formatDateTime(row.solutionPlan);
        },
      },
      {
        title: '服务单号',
        key: 'orderId',
        resizable: true,
        width: 240,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '系统版本',
        key: 'osVersion',
        resizable: true,
        width: 120,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '应用版本',
        key: 'appVersion',
        resizable: true,
        width: 120,
        ellipsis: {
          tooltip: true,
        },
      },
      // {
      //   title: '问题级别',
      //   key: 'severity',
      //   resizable: true,
      //   width: 120,
      //   ellipsis: {
      //     tooltip: true,
      //   },
      // },
      // {
      //   title: '舆情声量',
      //   key: 'opinionVolume',
      //   resizable: true,
      //   width: 200,
      //   sorter: true,
      //   ellipsis: {
      //     tooltip: true,
      //   },
      // },
      {
        title: '操作',
        key: 'actions',
        width: 280,
        fixed: 'right',
        render(row) {
          return h(
            'div',
            {
              style: {
                display: 'flex',
                gap: '8px',
              },
            },
            [
              h(
                NButton,
                {
                  strong: true,
                  tertiary: true,
                  size: 'small',
                  onClick: () => handleEdit(row),
                },
                '编辑'
              ),
              /*  h(
                NButton,
                {
                  size: 'small',
                  type: 'error',
                  strong: true,
                  onClick: () => handleDelete(row),
                },
                '删除'
              ),*/
              h(
                NButton,
                {
                  strong: true,
                  tertiary: true,
                  size: 'small',
                  type: 'info',
                  disabled: row.synchronizedFlag === '是',
                  onClick: () => handleSyncData(row),
                },
                '同步生态舆情'
              ),
            ]
          );
        },
      },
    ];
    basicColumns.map((item) => {
      if (item.toWelink === true) {
        item.render = (row) => {
          return h(
            'a',
            {
              href: '#',
              onClick: (e) => {
                e.preventDefault();
                openIMChatClick(row[item.key]);
              },
              style: {
                color: '#1288ff',
                textDecoration: 'none',
                cursor: 'pointer',
              },
            },
            row[item.key]
          );
        };
      }
    });
    return basicColumns;
  };

  const cancel = () => (dialogVisible.value = false);

  const timestampToDate = (timestamp: number): string => {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const sureAdd = async () => {
    formRef.value?.validate(async (errors) => {
      try {
        if (!errors) {
          // if (actionForm.value.userSubmissionTime) {
          //   actionForm.value.userSubmissionDate = formatDate(actionForm.value.userSubmissionTime);
          // } else {
          //   actionForm.value.userSubmissionTime = '';
          // }
          // if (actionForm.value.solutionPlanDate) {
          //   actionForm.value.solutionPlan = formatDate(actionForm.value.solutionPlanDate);
          // } else {
          //   actionForm.value.solutionPlan = '';
          // }
          console.log(actionForm.value.solutionPlan, 'solution');
          await addQuestion({
            ...actionForm.value,
            userSubmissionTime: timestampToDate(actionForm.value.userSubmissionTime),
            // sourceClosed: actionForm.value.sourceClosed,
            // solutionPlan: timestampToDate(actionForm.value.solutionPlan),
            channel: '服务',
          });
          message.success(isAdd.value ? '新增成功' : '修改成功');
          dialogVisible.value = false;
          fetchData();
        }
      } catch (error) {
        message.error(isAdd.value ? '新增成功' : '修改成功');
      }
    });
  };

  const selectUsername = async () => {
    const data = await getInfoFromOrderId(actionForm.value.appName);
    console.log(data, 'data');
    if (data.ewpOwner) {
      actionForm.value.ewpOwner = data.ewpOwner;
    }
    if (data.appPM) {
      actionForm.value.appPM = data.appPM;
    }
  };

  // const selectInfo = async () => {
  //   const info = await getInfoFromOrderId(actionForm.value.orderId);
  //   if (info.appVersion) {
  //     actionForm.value.appVersion = info.appVersion;
  //   }
  //   if (info.osVersion) {
  //     actionForm.value.osVersion = info.osVersion;
  //   }
  // };

  const confirmAddQuestion = () => {
    dialogVisible.value = true;
    isAdd.value = true;
    actionForm.value = {
      id: null,
      orderId: '',
      source: '',
      userSubmissionTime: null,
      // userSubmissionDate: null,
      appName: '',
      appVersion: '',
      // sceneName: '',
      orderDescription: '',
      dtsOrderId: '',
      orderStatus: '',
      solutionPlan: null,
      // solutionPlanDate: null,
      // severity: '',
      // opinionVolume: '',
      progress: '',
      deviceList: [],
      device: '',
      osVersion: '',
      ewpOwner: '',
      appPM: '',
      feedbackSource: '',
      sourceClosed: '否',
      sourceOwner: '',
      top: '',
      issueTypeList: [],
      issueType: '',
    };
  };

  const handleEdit = (row) => {
    actionForm.value = { ...row };
    actionForm.value.userSubmissionTime = actionForm.value.userSubmissionTime || null;
    actionForm.value.solutionPlan = actionForm.value.solutionPlan || null;
    // actionForm.value.device = actionForm.value.deviceList;
    actionForm.value.issueTypeList = actionForm.value.issueTypeList || [];
    actionForm.value.sourceClosed = actionForm.value.sourceClosed || '否';
    dialogVisible.value = true;
    isAdd.value = false;
  };

  const handleDelete = async (row) => {
    try {
      await deleteQuestion(row.id);
      message.success('删除成功');
      await fetchData();
    } catch (error) {
      message.success('删除失败');
    }
  };

  const handleSyncData = async (row) => {
    try {
      await syncQuestion(row.id);
      message.success('同步成功');
      await fetchData();
    } catch (error) {
      message.error(`${error.message}`);
    }
  };

  // 导入
  const handleFileUpload = async ({ file }) => {
    importing.value = true;
    try {
      const formData = new FormData();
      formData.append('file', file.file);
      formData.append('channel', '服务');

      const response = await service.post('/management/service/orders/sheet/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      message.success('应用数据导入成功');
      fetchData(); // Refresh the table data
    } catch (error) {
      console.error('Failed to import application data:', error);
      message.error('应用数据导入失败');
    } finally {
      importing.value = false;
    }
  };

  // 导出
  const handleExport = () => {
    return new Promise((resolve, reject) => {
      const req = new XMLHttpRequest();
      exportLoading.value = true;

      const queryParams: Record<string, any> = {
        ...filterObjectValues(searchForm),
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
        sortField: sortState.value.sortField,
        sortOrder: sortState.value.sortOrder,
      };
      if (searchForm.registerDate) {
        const [start, end] = searchForm.registerDate;
        queryParams.startTime = formatDate(start);
        queryParams.endTime = formatDate(end);
      }
      queryParams.channel = '服务';
      req.open('POST', `http://${window.location.host}/ewp/management/service/orders/sheet`, true);
      req.responseType = 'blob';
      req.setRequestHeader('Content-Type', 'application/json');
      req.onload = function () {
        const data = req.response;
        const blob = new Blob([data]);
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = '服务渠道监控' + timestampToDate(Date.now()) + '.xls';
        a.href = blobUrl;
        a.click();
        exportLoading.value = false;
        resolve(true);
      };
      req.send(JSON.stringify(queryParams));
    });
  };

  const formProgressClick = () => {
    if (isAdd.value) return;
    showModel(actionForm.value);
  };

  const progressModel = () => {
    const showProblemModal = ref(false);
    const currentProblem = ref({
      orderId: null,
      appName: '',
      description: '',
    });
    const selectData = ref({});
    const afterSubmit = async (data) => {
      //更新最新进展
      selectData.value.progress =
        dayjs(data.createTime).format('MM/DD') + '：' + data.problemProcess;
      await addQuestion(selectData.value);
    };
    const showModel = (row) => {
      selectData.value = row;
      currentProblem.value.orderId = 'service' + row.id;
      currentProblem.value.appName = row.appName;
      currentProblem.value.description = row.orderDescription;
      showProblemModal.value = true;
    };
    return {
      showProblemModal,
      currentProblem,
      selectData,
      afterSubmit,
      showModel,
    };
  };

  const { showProblemModal, currentProblem, selectData, afterSubmit, showModel } = progressModel();

  onMounted(() => {
    fetchData();
    handleSearchDevice('');
  });
</script>

<style scoped>
  /* 修改表单项样式 */
  :deep(.n-form-item) {
    display: flex;
    margin-right: 0;
    margin-bottom: 18px;
  }

  :deep(.n-form-item-label) {
    width: 90px !important;
    text-align: right;
  }

  :deep(.n-form-item-blank) {
    flex: 1;
  }

  /* 统一输入框和选择框的宽度和对齐方式 */
  :deep(.n-input),
  :deep(.n-select) {
    /* width: 300px !important; */
  }

  /* 确保输入内容左对齐 */
  :deep(.n-input__input-el),
  :deep(.n-select-option__content) {
    text-align: left !important;
  }

  /* 确保选择框的内容左对齐 */
  :deep(.n-base-selection-label) {
    text-align: left !important;
  }

  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 6px;
    padding-bottom: 3px;
  }

  .setting-icon {
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
    color: #666;
  }

  .setting-icon:hover {
    background-color: #f5f5f5;
    color: #2080f0;
    transform: rotate(30deg);
  }

  .bold-font {
    font-weight: 700;
  }

  .gradient-text {
    font-size: 14px;
    background: -webkit-linear-gradient(90deg, red 0%, green 50%, blue 100%); /* Chrome, Safari */
    background: linear-gradient(90deg, red 0%, green 50%, blue 100%); /* 标准语法 */
    -webkit-background-clip: text; /* Chrome, Safari */
    background-clip: text;
    -webkit-text-fill-color: transparent; /* Chrome, Safari */
    color: transparent;
  }

  .statistics-container {
    margin: 24px 0;
  }

  .stat-card {
    background-color: #fff;
    transition: all 0.3s;
    height: 100%;
  }

  .stat-card:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .stat-content {
    text-align: center;
  }

  .stat-label {
    color: #666;
    font-size: 14px;
    margin-bottom: 8px;
  }

  .stat-value {
    color: #2080f0;
    font-size: 24px;
    font-weight: bold;
  }

  .n-form-item {
    margin-bottom: 18px;
  }

  .n-card {
    border-radius: 8px;
  }

  .n-button {
    min-width: 80px;
  }
</style>
