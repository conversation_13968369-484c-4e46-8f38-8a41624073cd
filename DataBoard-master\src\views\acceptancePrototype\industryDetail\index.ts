import service from '@/utils/axios';

// 查询
export const getIndustryList = (data, applicationName) => {
  const baseUrl = applicationName ? '/functionList' : '/industry_base';
  return service({
    url: `${baseUrl}/query`,
    method: 'post',
    data,
  });
};

// 导出
export const exportData = (params) => {
  return service({
    url: `/industry_base/export`,
    method: 'get',
    params,
    responseType: 'blob',
  });
};

// 更新
export const updateIndustryList = (data) => {
  return service({
    url: '/industry_base/multiUpdate',
    method: 'post',
    data,
  });
};
