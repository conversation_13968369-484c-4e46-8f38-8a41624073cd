<template>
  <div>
    <n-space vertical>
      <n-card>
        <n-spin :show="progressLoading">
          <n-form
            ref="formRef"
            :model="progressModel"
            label-placement="top"
            label-width="80"
            require-mark-placement="right-hanging"
            size="medium"
          >
            <n-grid :cols="24" :x-gap="24">
              <n-form-item-gi :span="24" label="关键进展：" path="storageTime">
                <n-input
                  :disabled="!isEditProgress"
                  type="textarea"
                  v-model:value="progressModel.progress"
                  placeholder="点击编辑可更新关键进展"
                />
              </n-form-item-gi>
            </n-grid>
          </n-form>
        </n-spin>
        <n-space>
          <n-button v-if="isEditProgress" secondary strong type="default" @click="cancelEdit()">
            取消
          </n-button>
          <n-button secondary strong type="primary" @click="editProgress()">
            {{ isEditProgress ? '保存' : '编辑' }}
          </n-button>
        </n-space>
      </n-card>
      <n-card>
        <n-space>
          <n-button secondary strong type="warning" @click="refreshData()">
            <template #icon>
              <n-icon>
                <Refresh />
              </n-icon>
            </template>
            刷新
          </n-button>
          <n-button secondary strong type="primary" @click="showListAddModal = true">
            导入
          </n-button>
          <n-button
            secondary
            strong
            type="primary"
            @click="
              showModal = true;
              isAdd = true;
            "
          >
            <template #icon>
              <n-icon>
                <Add />
              </n-icon>
            </template>
            添加
          </n-button>
          <n-button secondary strong type="error" @click="remove()"> 一键清空表格数据 </n-button>
          <n-button
            secondary
            strong
            type="error"
            :disabled="!selectRows.length"
            @click="remove(null, true)"
          >
            批量删除
          </n-button>
        </n-space>
        <n-data-table
          remote
          :columns="columns"
          :data="data"
          :pagination="pagination"
          :loading="loading"
          style="margin-top: 20px"
          scroll-x
          :row-key="(row: RowData) => row.orderId"
          @update:checked-row-keys="handleCheck"
        />
      </n-card>
    </n-space>

    <n-modal v-model:show="showListAddModal">
      <n-card
        style="width: 600px"
        title="批量导入"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-button text @click="handleDownload" style="margin-bottom: 20px"
          >点击下载导入模板</n-button
        >
        <n-upload
          action="#"
          :custom-request="customRequest"
          :multiple="true"
          :default-file-list="fileList"
          accept=".xls,.xlsx,"
          :max="1"
          directory-dnd
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <Add />
              </n-icon>
            </div>
            <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
          </n-upload-dragger>
        </n-upload>
      </n-card>
    </n-modal>

    <n-modal v-model:show="showModal" :on-after-leave="handleAfterLeave">
      <n-card
        :style="{ width: '1200px' }"
        :title="isAdd ? '新增' : '编辑'"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <template #header-extra> </template>
        <n-form
          ref="formRef"
          :model="model"
          :rules="rules"
          label-placement="left"
          label-width="100"
          require-mark-placement="right-hanging"
          size="medium"
        >
          <n-grid :cols="24" :x-gap="24">
            <n-form-item-gi :span="12" label="工单ID" path="orderId">
              <n-input :disabled="!isAdd" v-model:value="model.orderId" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="责任人" path="owner">
              <n-input v-model:value="model.owner" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="IR单问题类型" path="problemCategory ">
              <n-input v-model:value="model.problemCategory" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="超期时长" path="overdueDuration">
              <n-input v-model:value="model.overdueDuration" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="标题" path="title">
              <n-input v-model:value="model.title" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="问题分类" path="irType">
              <n-input v-model:value="model.irType" />
            </n-form-item-gi>
            <n-form-item-gi :span="24" label="不符合规范" path="desc">
              <n-input type="textarea" v-model:value="model.desc" />
            </n-form-item-gi>
          </n-grid>
        </n-form>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" @click="handleSubmint()"> 确认 </n-button>
            <n-button secondary strong type="error" @click="showModal = false"> 取消 </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>
<script lang="ts" setup>
  import { ref, reactive, h, onMounted } from 'vue';
  import { Add, Refresh, Close, GridOutline } from '@vicons/ionicons5';
  import { NIcon, useMessage, useDialog, NButton, NFlex } from 'naive-ui';
  import { paginationReactive, defaultModel } from './index';
  import {
    orderAdd,
    orderDelete,
    orderUpdate,
    orderQuery,
    orderImport,
    orderDownloadTemplate,
    orderBatchDelete,
  } from '@/api/dataview/orderQualityManager';
  import { cloneDeep, debounce } from 'lodash-es';

  const formRef = ref();
  const defaultProgress = ref('');
  const progressModelReactive = reactive({ progress: defaultProgress.value });
  const progressModel = ref(progressModelReactive);
  const isEditProgress = ref(false);
  const isAdd = ref(true);
  const showModal = ref(false);
  const showListAddModal = ref(false);
  const fileList = ref([]);
  const loading = ref(false);
  const progressLoading = ref(false);
  const message = useMessage();
  const dialog = useDialog();
  const selectRows = ref([]);
  const sampleDevidesUsageRegistrationColumn = [
    {
      type: 'selection',
      fixed: 'left',
    },
    {
      title: '工单ID',
      key: 'orderId',
      width: 160,
      fixed: 'left',
    },
    {
      title: '标题',
      key: 'title',
      width: 200,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '责任人',
      key: 'owner',
      resizable: true,
      width: 200,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '不符合规范',
      key: 'desc',
      width: 200,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '问题分类',
      key: 'problemCategory',
      width: 200,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: 'IR单问题类型',
      key: 'irType',
      width: 200,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '超期时长',
      key: 'overdueDuration',
      width: 200,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 140,
      fixed: 'right',
      render: (row) => {
        return [
          h(NFlex, { wrap: false }, () => [
            h(
              NButton,
              {
                strong: true,
                tertiary: true,
                type: 'info',
                size: 'medium',
                onClick: () => {
                  model.value = cloneDeep(row);
                  isAdd.value = false;
                  showModal.value = true;
                },
              },
              {
                default: () => '编辑',
              }
            ),
            h(
              NButton,
              {
                strong: true,
                tertiary: true,
                type: 'error',
                size: 'medium',
                onClick: () => {
                  remove(row.orderId);
                },
              },
              {
                default: () => '删除',
              }
            ),
          ]),
        ];
      },
    },
  ];
  const columns = ref(sampleDevidesUsageRegistrationColumn);
  paginationReactive.onChange = (page: number) => {
    paginationReactive.page = page;
    search();
  };
  paginationReactive.onUpdatePageSize = (pageSize: number) => {
    paginationReactive.pageSize = pageSize;
    paginationReactive.page = 1;
    search();
  };
  const pagination = ref(paginationReactive);
  const rules = ref({
    orderId: {
      required: true,
      message: '请输入工单ID',
      trigger: 'blur',
    },
  });
  const data = ref([]);
  let modelReactive = reactive(cloneDeep(defaultModel));
  const model = ref(modelReactive);
  const cancelEdit = () => {
    progressModel.value.progress = defaultProgress.value;
    isEditProgress.value = false;
  };

  const handleCheck = (rowKeys) => {
    selectRows.value = rowKeys;
  };

  const editProgress = async () => {
    if (isEditProgress.value) {
      progressLoading.value = true;
      try {
        let res = await orderUpdate({ summary: progressModel.value.progress });
        if (res.status == '200') {
          message.success('提交成功');
          getProgress();
        }
      } catch (e) {}
      progressLoading.value = false;
    }
    isEditProgress.value = !isEditProgress.value;
  };

  const handleAfterLeave = () => {
    modelReactive = reactive(cloneDeep(defaultModel));
    model.value = modelReactive;
  };

  const remove = (orderId?, isBatchDelete?) => {
    dialog.error({
      title: '提示',
      content: '数据删除后不可恢复，确认执行操作？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          let res = orderId
            ? await orderDelete({ orderId: orderId })
            : await orderBatchDelete({
                orderIds: isBatchDelete ? selectRows.value?.join(',') : '',
              });
          if (res?.status) {
            message.success('删除成功');
          }
          if (isBatchDelete) {
            selectRows.value = [];
          }
        } catch (err) {}
        search();
      },
      onNegativeClick: () => {},
    });
  };
  // 获取当强进展内容
  const getProgress = async () => {
    progressLoading.value = true;
    try {
      let res = await orderQuery({
        havSummary: true,
      });
      defaultProgress.value = res?.data?.data?.[0]?.summary || '';
      progressModel.value.progress = defaultProgress.value;
    } catch (e) {}
    progressLoading.value = false;
  };

  // 查询表格数据
  const search = debounce(async () => {
    loading.value = true;
    try {
      let res = await orderQuery({
        pageNum: pagination.value.page,
        pageSize: pagination.value.pageSize,
        havSummary: false,
      });
      if (res.status === '200') {
        data.value = res?.data?.data || [];
        paginationReactive.itemCount = res?.data?.pageInfo?.total || 0;
        paginationReactive.pageCount =
          Math.ceil(res?.data?.pageInfo?.total / pagination.value.pageSize) || 0;
      }
    } catch (e) {}
    loading.value = false;
  }, 300);

  const refreshData = () => {
    getProgress();
    search();
  };

  const handleSubmint = debounce(async () => {
    formRef.value.validate(async (errors) => {
      if (!errors) {
        let data = JSON.parse(JSON.stringify(model.value));
        try {
          var res = isAdd.value ? await orderAdd(data) : await orderUpdate(data);
          if (res.status == '200') {
            message.success('提交成功');
            showModal.value = false;
            search();
          }
        } catch (err) {
          message.error(err.message);
        }
      }
    });
  }, 300);
  //上传
  const customRequest = async ({ file, onError }: UploadCustomRequestOptions) => {
    const formData = new FormData();
    formData.append('file', file.file as File);
    //上传接口
    try {
      let res = await orderImport(formData);
      if (res.status == '200') {
        showListAddModal.value = false;
        search();
        message.success('导入成功');
      } else {
        fileList.value = [];
      }
    } catch (err) {
      fileList.value = [];
      onError();
    }
  };
  //下载
  const handleDownload = async () => {
    orderDownloadTemplate()
      .then((res) => {
        if (!res) {
          message.error('下载失败！');
        }
      })
      .catch((e) => {
        message.error(`下载失败，原因：${e?.response?.data?.error}`);
      });
  };
  onMounted(() => {
    refreshData();
  });
</script>
