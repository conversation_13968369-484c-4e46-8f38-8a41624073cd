import service from '@/utils/axios';

export const IRTransferRecordQuery = (data) => {
  return service({
    url: `/IRTransport/query`,
    method: 'post',
    data,
  });
};

export const issuesImport = (data) => {
  return service({
    url: `/IRTransport/importExcel`,
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};
export const issuesDownloadTemplate = () => {
  return service({
    url: `/IRTransport/exportExcel`,
    method: 'post',
    responseType: 'blob',
  });
};