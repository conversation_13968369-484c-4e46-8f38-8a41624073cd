import { ewpService as service } from '@/utils/axios';

export const getFUTAPPList = async (params: any): Promise<any> => {
  try {
    const response = await service.post('/management/futAppList/query', params);
    return response;
  } catch (error) {
    console.error('Error fetching fut app list:', error);
    throw error;
  }
};

export const addFUTAPP = async (params: any): Promise<any> => {
  try {
    const response = await service.post('/management/futAppList/save', params);
    return response;
  } catch (error) {
    console.error('Error add fut app:', error);
    throw error;
  }
};

export const updateFUTAPP = async (params: any): Promise<any> => {
  try {
    const response = await service.put('/management/futAppList//update', params);
    return response;
  } catch (error) {
    console.error('Error update fut app:', error);
    throw error;
  }
};

export const deleteFUTAPP = async (params: any): Promise<any> => {
  try {
    const response = await service.delete(`/management/futAppList/delete/${params}`, );
    return response;
  } catch (error) {
    console.error('Error update fut app:', error);
    throw error;
  }
};

