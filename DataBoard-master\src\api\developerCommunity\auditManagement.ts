import service from '@/utils/axios';

/**
 * 下载导入模板
 */
export const downloadTemplateService = () => {
  return service({
    url: '/reviewManager/downloadExcel',
    method: 'get',
    responseType: 'blob',
  });
};

/**
 * 导入
 */
export const importData = (data) => {
  return service({
    url: '/reviewManager/import',
    method: 'post',
    data,
  });
};

/**
 * 导出
 */
export const exportData = (data) => {
  return service({
    url: '/reviewManager/export',
    method: 'post',
    data,
    responseType: 'blob',
  });
};

/**
 * 查询
 */
export const serachList = (data) => {
  return service({
    url: '/reviewManager/queryByCondition',
    method: 'post',
    data,
  });
};

/**
 * 编辑
 */
export const updateData = (data) => {
  return service({
    url: '/reviewManager/edit',
    method: 'post',
    data,
  });
};
