<template>
  <div class="app-detail">
    <n-card class="header-card">
      <div class="header-content">
        <n-button @click="goBack" class="back-button">
          <template #icon>
            <n-icon><arrow-back /></n-icon>
          </template>
          返回
        </n-button>
        <h1 class="app-title">{{ appName }} 详情</h1>
      </div>
    </n-card>
    <n-card class="table-card top-card">
      <n-space vertical>
        <n-data-table
          :columns="topColumns"
          :data="topTableData"
          :bordered="false"
          size="small"
          :pagination="false"
          :scroll-x="1000"
          :single-line="false"
          striped
        />
        <n-data-table
          :columns="twoColumns"
          :data="twoTableData"
          :bordered="false"
          size="small"
          :pagination="false"
          :scroll-x="1000"
          :single-line="false"
          striped
        />
      </n-space>
    </n-card>
    <FunctionList  ref="funRef"/>
    <n-card class="table-card">
      <template #header>
        <div class="chart-title">体验分析报告</div>
      </template>
      <n-space vertical>
        <n-card :bordered="false" size="small">
          <n-tabs type="line" animated class="custom-tabs" @update:value="tabChange">
            <template #prefix>
              <div style="width: 12px"></div>
            </template>
            <template #suffix>
              <n-button
                secondary
                type="primary"
                size="small"
                class="export-button"
                @click="handleExport"
                :loading="exportLoading"
              >
                <template #icon>
                  <n-icon>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M13 10h5l-6-6-6 6h5v8h2v-8M4 19v-7h2v7h12v-7h2v7H4Z"
                      />
                    </svg>
                  </n-icon>
                </template>
                导出
              </n-button>
            </template>
            <n-tab-pane v-for="tab in tabs" :key="tab.key" :name="tab.key" :tab="tab.title">
              <n-data-table
                remote
                :columns="tab.tabColumns || tabColumns"
                :data="tabTableData"
                :bordered="false"
                size="small"
                :pagination="paginationMissing"
                :single-line="false"
                striped
                min-height="400px"
                max-height="400px"
              />
            </n-tab-pane>
          </n-tabs>
        </n-card>
      </n-space>
    </n-card>
    <div class="charts" style="margin-top: 10px">
      <div class="chart">
        <n-spin :show="loadingTrend">
          <n-card class="chart-card">
            <template #header>
              <div class="chart-title">应用问题声量趋势</div>
            </template>
            <div ref="trendChartRef" class="chart-container"></div>
          </n-card>
        </n-spin>
      </div>
      <div class="chart">
        <n-spin :show="loadingDistribution">
          <n-card class="chart-card">
            <template #header>
              <div class="chart-title">应用问题级别分布</div>
            </template>
            <div ref="distributionChartRef" class="chart-container"></div>
          </n-card>
        </n-spin>
      </div>
    </div>
    <n-card class="table-card">
      <template #header>
        <div class="chart-title">工单状态</div>
      </template>
      <n-data-table
        remote
        :columns="columns"
        :data="tableData"
        :loading="loadingTable"
        :pagination="paginationReactive"
        @update:sorter="handleSorterChange"
        @update:filters="handleFiltersChange"
        :scroll-x="3500"
      />
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, nextTick, h } from 'vue';
import FunctionList from '../../comp/functionList/functionList.vue';
import { useRoute, useRouter } from 'vue-router';
import {
  NSpin,
  NCard,
  NButton,
  NTooltip,
  NIcon,
  NDataTable,
  useMessage,
  useDialog,
  NTag,
  NSelect,
  NDatePicker,
} from 'naive-ui';
import { updateOwnerFunction } from '@/api/dataview/appAnalysis';
import { ArrowBack, Add, Refresh } from '@vicons/ionicons5';
import * as echarts from 'echarts';
import { ewpService as service } from '@/utils/axios';
import { createColumns } from '../myOrder/columns';
import {
  query,
  add,
  update,
  deleteRow,
  importExcel,
  downloadTemplate,
  checkNew,
} from '@/api/dataview/featuresList';
import { cloneDeep } from 'lodash-es';
import {
  queryCustomerIssue,
  queryWorkOrder,
  updateQualityInfo,
  workOrderScene,
} from '@/api/dataview/appAnalysis';
import { w } from '@faker-js/faker/dist/airline-BnpeTvY9';
function toPercent(point) {
  var str = Number(point * 100).toFixed(2);
  return str;
}
const loading = ref(true);
const featureData = ref([]);
const featuresOptions = ref([]);
const funRef = ref();
const fileList = ref([]);
const message = useMessage();
const dialog = useDialog();

const route = useRoute();
const router = useRouter();
const appName = ref(route.query.appName as string);
const appType = ref(route.query.appType as string);
const company = ref(route.query.company as string);

const trendChartRef = ref<HTMLElement | null>(null);
const distributionChartRef = ref<HTMLElement | null>(null);

const loadingTrend = ref(true);
const loadingDistribution = ref(true);

const tableData = ref([]);
const loadingTable = ref(true);
const paginationReactive = reactive({
  page: 1,
  pageSize: 8,
  showSizePicker: true,
  pageSizes: [8, 20, 50],
  prefix({ itemCount }) {
    return `Total：${itemCount}`;
  },
  onChange: (page: number) => {
    paginationReactive.page = page;
    fetchTicketData();
  },
  onUpdatePageSize: (pageSize: number) => {
    paginationReactive.pageSize = pageSize;
    paginationReactive.page = 1;
    fetchTicketData();
  },
});
const sortState = ref({
  sortField: 'riskScore',
  sortOrder: 'desc',
});
// 如果不需要显示操作列，传入 false
const columns = createColumns(false);

const developmentStatusOptions = [
  { label: '开发中', value: '开发中' },
  { label: '暂停开发', value: '暂停开发' },
  { label: '已完成', value: '已完成' },
];

// 添加版本计划选项
const versionPlanOptions = [
  { label: '已完成', value: '已完成' },
  { label: '已规划(320)', value: '已规划(320)' },
  { label: '未规划(320)', value: '未规划(320)' },
];
const getfeatureData = async () => {
  try {
    let res = await query({
      pageNum: 1,
      pageSize: 1000,
      appName: appName.value,
      appType: appType.value,
      company: company.value,
    });
    if (res.status === '200') {
      featuresOptions.value =
        res?.data?.data.map((item) => {
          let list = '';
          if (item.secondLevel) {
            list = item.thirdLevel
              ? `${item.firstLevel}-${item.secondLevel}-${item.thirdLevel}`
              : `${item.firstLevel}-${item.secondLevel}`;
          } else {
            list = item.firstLevel;
          }

          return {
            label: list,
            value: list,
          };
        }) || [];
    }
  } catch (e) {}
  loading.value = false;
};
onMounted(async () => {
  await Promise.all([
    fetchTrendData(),
    fetchDistributionData(),
    fetchTicketData(),
    getfeatureData(),
  ]);
});

async function fetchTrendData() {
  try {
    const response = await service.get(
      `/management/appOverView/volumeTrend?appName=${appName.value}`
    );
    if (response) {
      renderTrendChart(response);
    }
  } catch (error) {
    console.error('Failed to fetch trend data:', error);
  } finally {
    loadingTrend.value = false;
  }
}

async function fetchDistributionData() {
  try {
    const response = await service.get(
      `/management/appOverView/levelDistribution?appName=${appName.value}`
    );

    if (response) {
      renderDistributionChart(response);
    }
  } catch (error) {
    console.error('Failed to fetch distribution data:', error);
  } finally {
    loadingDistribution.value = false;
  }
}

async function fetchTicketData() {
  try {
    loadingTable.value = true;
    const { total, records } = await service.post(`/management/workOrder/query`, {
      appName: appName.value,
      pageNo: paginationReactive.page,
      pageSize: paginationReactive.pageSize,
      sortField: sortState.value.sortField,
      sortOrder: sortState.value.sortOrder,
      ...columnFilters,
    });
    console.log('total:', total);

    tableData.value = records;
    paginationReactive.itemCount = total;
  } catch (error) {
    console.error('Failed to fetch ticket data:', error);
  } finally {
    loadingTable.value = false;
  }
}

function renderTrendChart(data) {
  if (trendChartRef.value) {
    const chart = echarts.init(trendChartRef.value);
    chart.setOption({
      tooltip: { trigger: 'item' },
      xAxis: { type: 'category', data: data.map((item) => item.date) },
      yAxis: { type: 'value' },
      series: [
        {
          data: data.map((item) => item.volume),
          type: 'line',
        },
      ],
    });
  }
}
const handleSorterChange = (sorter) => {
  if (sorter) {
    const { columnKey, order } = sorter;
    sortState.value.sortField = columnKey;
    sortState.value.sortOrder = order === 'ascend' ? 'asc' : 'desc';
  } else {
    sortState.value.sortField = 'riskScore';
    sortState.value.sortOrder = 'desc';
  }
  paginationReactive.page = 1; // 重置到第一页
  fetchTicketData();
};
/**
 * 列筛选  目前只加模块筛选
 */
const columnFilters = {
  module: null,
};
const handleFiltersChange = (filters: any) => {
  columnFilters.module = filters.module || [];
  fetchTicketData();
};
function renderDistributionChart(data) {
  if (distributionChartRef.value) {
    const chart = echarts.init(distributionChartRef.value);
    chart.setOption({
      tooltip: { trigger: 'item' },
      series: [
        {
          type: 'pie',
          radius: '50%',
          data: data.map((item) => ({ value: item.count, name: item.level })),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    });
  }
}

function goBack() {
  router.push({ name: 'Appstate' });
}

function timestampToYYYYMMDD(timestamp) {
  const date = new Date(timestamp); // 将时间戳转换为 Date 对象
  const year = date.getFullYear(); // 获取年份
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 获取月份，加 1 并补零
  const day = String(date.getDate()).padStart(2, '0'); // 获取日期并补零

  return `${year}-${month}-${day}`; // 拼接为 yyyy-mm-dd 格式
}
const topTableData = ref();
const twoTableData = ref();
// 上方表格列定义
const topColumns = [
  {
    title: '功能完备度',
    key: 'qualityAttributes',
    align: 'center',
    children: [
      {
        title: '开发状态',
        key: 'developmentStatus',
        align: 'center',
        width: 100,
        render(row) {
          return h(NSelect, {
            value: row.developmentStatus,
            options: developmentStatusOptions,
            size: 'small',
            style: { width: '100px' },
            async onUpdateValue(value) {
              console.log('value:', value);
              row.developmentStatus = value;
              // 这里可以添加保存逻辑
              let res = await updateQualityInfo({
                appName: appName.value,
                developmentStatus: value,
              });
              message.success('保存成功');
            },
          });
        },
      },
      {
        title: '完成度',
        key: 'functionEnhancementPercentage',
        align: 'center',
      },
      {
        title: '版本状态',
        key: 'plan',
        align: 'center',
        width: 100,
        render(row) {
          return h(NSelect, {
            value: row.plan,
            options: versionPlanOptions,
            size: 'small',
            style: { width: '120px' },
            async onUpdateValue(value) {
              row.plan = value;
              // 调用保存接口
              let res = await updateQualityInfo({
                appName: appName.value,
                plan: value,
              });
              message.success('保存成功');
            },
          });
        },
      },
    ],
  },
  {
    title: '体验完备度',
    key: 'qualityAttributes',
    align: 'center',
    children: [
      {
        title: '功能',
        key: 'function',
        align: 'center',
        children: [
          {
            title: '声量',
            key: 'criticalNum',
            align: 'center',
          },
          {
            title: '占比',
            key: 'criticalPercent',
            align: 'center',
            render: (row) => `${row.criticalPercent}%`,
          },
        ],
      },
      {
        title: '性能功耗',
        key: 'performance',
        align: 'center',
        children: [
          {
            title: '声量',
            key: 'performanceNum',
            align: 'center',
          },
          {
            title: '占比',
            key: 'performancePercent',
            align: 'center',
            render: (row) => `${row.performancePercent}%`,
          },
        ],
      },
      {
        title: '稳定性',
        key: 'stability',
        align: 'center',
        children: [
          {
            title: '声量',
            key: 'crashNum',
            align: 'center',
          },
          {
            title: '占比',
            key: 'crashPercent',
            align: 'center',
            render: (row) => `${row.crashPercent}%`,
          },
        ],
      },
      {
        title: 'UX',
        key: 'ux',
        align: 'center',
        children: [
          {
            title: '声量',
            key: 'uxNum',
            align: 'center',
          },
          {
            title: '占比',
            key: 'uxPercent',
            align: 'center',
            render: (row) => `${row.uxPercent}%`,
          },
        ],
      },
      {
        title: '其他',
        key: 'other',
        align: 'center',
        children: [
          {
            title: '声量',
            key: 'otherNum',
            align: 'center',
          },
          {
            title: '占比',
            key: 'otherPercent',
            align: 'center',
            render: (row) => `${row.otherPercent}%`,
          },
        ],
      },
      {
        title: '总计',
        key: 'total',
        align: 'center',
        children: [
          {
            title: '声量',
            key: 'opinionNum',
            align: 'center',
          },
          {
            title: '占比',
            key: 'allPercent',
            align: 'center',
            render: (row) => `${row.allPercent}%`,
          },
        ],
      },
    ],
  },

  // {
  //   title: '操作',
  //   key: 'operation',
  //   width: 100,
  //   fixed: 'right',
  //   render(row, index) {
  //     return h(
  //       NButton,
  //       {
  //         text: true,
  //         strong: true,
  //         type: 'info',
  //         size: 'tiny',
  //         onClick: () => {},
  //       },
  //       {
  //         default: () => '编辑',
  //       }
  //     );
  //   },
  // },
];
const twoColumns = [
  {
    title: '消费者问题状态',
    key: '',
    align: 'center',
    children: [
      {
        title: '重大问题',
        key: '',
        align: 'center',
        children: [
          {
            title: 'A类',
            key: 'aNum',
            align: 'center',
          },
          {
            title: 'B类',
            key: 'bNum',
            align: 'center',
          },
          {
            title: 'C类',
            key: 'cNum',
            align: 'center',
          },
          {
            title: 'D类',
            key: 'dNum',
            align: 'center',
          },
        ],
      },
      {
        title: '全量问题',
        key: '',
        align: 'center',
        children: [
          {
            title: '严重问题',
            key: '',
            align: 'center',
            children: [
              {
                title: '待定界',
                key: 'severeWaitDealNum',
                align: 'center',
              },
              {
                title: '待锁定',
                key: 'severeWaitLockNum',
                align: 'center',
              },
              {
                title: '待修复',
                key: 'severeWaitRepairNum',
                align: 'center',
              },
              {
                title: '待回归',
                key: 'severeWaitRecheckNum',
                align: 'center',
              },
              {
                title: '已闭环',
                key: 'severeCloseNum',
                align: 'center',
              },
            ],
          },
          {
            title: '一般问题',
            key: '',
            align: 'center',
            children: [
              {
                title: '待定界',
                key: 'waitDealNum',
                align: 'center',
              },
              {
                title: '待锁定',
                key: 'waitLockNum',
                align: 'center',
              },
              {
                title: '待修复',
                key: 'waitRepairNum',
                align: 'center',
              },
              {
                title: '待回归',
                key: 'waitRecheckNum',
                align: 'center',
              },
              {
                title: '已闭环',
                key: 'closeNum',
                align: 'center',
              },
            ],
          },
        ],
      },
    ],
  },
];
// 加载工单数据
const loadWorkOrderData = async (appName: string) => {
  try {
    loading.value = true;
    const data = await queryWorkOrder({ appName });

    // 更新上方表格数据
    topTableData.value = [
      {
        appName: data.appName,
        developmentStatus: data.developmentStatus,
        plan: data.plan, // 这里的值应该是 '已完成'/'已规划(320)'/'未规划(320)' 中的一个
        functionEnhancementPercentage: data.functionEnhancementPercentage,
        crashNum: data.crashNum,
        crashPercent: toPercent(data.crashPercent),
        uxNum: data.uxNum,
        uxPercent: toPercent(data.uxPercent),
        criticalNum: data.criticalNum,
        criticalPercent: toPercent(data.criticalPercent),
        performanceNum: data.performanceNum,
        performancePercent: toPercent(data.performancePercent),
        otherNum: data.otherNum,
        otherPercent: toPercent(data.otherPercent),
        opinionNum: data.opinionNum,
        allPercent: toPercent(data.allPercent),
      },
    ];
  } catch (error) {
    console.error('Failed to load work order data:', error);
  } finally {
    loading.value = false;
  }
};
const loadWorkOrderStatus = async (appName: string) => {
  try {
    loading.value = true;
    const data = await queryCustomerIssue({ appName });

    // 更新上方表格数据
    twoTableData.value = [
      {
        aNum: data.anum,
        bNum: data.bnum,
        cNum: data.cnum,
        dNum: data.dnum,
        severeWaitDealNum: data.severeWaitDealNum,
        severeWaitLockNum: data.severeWaitLockNum,
        severeWaitRepairNum: data.severeWaitRepairNum,
        severeWaitRecheckNum: data.severeWaitRecheckNum,
        severeCloseNum: data.severeCloseNum,
        waitDealNum: data.waitDealNum,
        waitLockNum: data.waitLockNum,
        waitRepairNum: data.waitRepairNum,
        waitRecheckNum: data.waitRecheckNum,
        closeNum: data.closeNum,
      },
    ];
  } catch (error) {
    console.error('Failed to load work order data:', error);
  } finally {
    loading.value = false;
  }
};
// Tab定义
const tabs = [
  {
    key: 'functionMiss',
    title: '功能缺失',
    tabColumns: [
      {
        title: '场景名称',
        key: 'sceneName',
        width: 200,
        align: 'center',
        resizable: true,
        ellipsis: {
          tooltip: true,
        },
        render: (row) => {
          const description = row.sceneName.split('】').pop();
          return h('span', {}, description);
        },
      },
      {
        title: '归属功能',
        key: 'ownerFunction',
        align: 'center',
        resizable: true,
        ellipsis: {
          tooltip: true,
        },
        render(row) {
          return h(NSelect, {
            value: row.ownerFunction,
            clearable: true,
            filterable: true,
            options: featuresOptions.value,
            size: 'small',
            title: row.ownerFunction,
            renderLabel:renderOptionWithTooltip,
            style: { width: '100%' },
            placeholder:'请输入选择归属功能',
            async onUpdateValue(v) {
              row.ownerFunction = v || undefined;
              try {
                let res = await updateOwnerFunction({
                  dtsOrder:
                    row.workOrderSceneDetails
                      ?.filter((item) => item?.dtsNo)
                      ?.map((item) => {
                        return item.dtsNo;
                      }) || [],
                  ownerFunction: v || '',
                });
                funRef.value.search()
              } catch (e) {}
            },
          });
        },
      },
      {
        title: '提及声量',
        key: 'opinionNum',
        align: 'center',
        resizable: true,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '提及占比',
        key: 'percent',
        render: (row) => `${toPercent(row.percent)}%`,
        align: 'center',
        resizable: true,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '状态跟踪',
        key: 'workOrderSceneDetails',
        align: 'center',
        resizable: true,
        ellipsis: {
          tooltip: true,
        },
        render: (row) => {
          if (!row.workOrderSceneDetails?.length) return '';

          return h(
            'div',
            { class: 'table-cell-list' },
            row.workOrderSceneDetails.map((detail: any) => {
              const statusMap = {
                pending: { text: '待处理', type: 'warning' },
                processing: { text: '处理中', type: 'info' },
                completed: { text: '已完成', type: 'success' },
              };
              return h(
                'div',
                {
                  class: row.workOrderSceneDetails.length > 1 ? 'cell-item' : '',
                },
                h(
                  NTag,
                  {
                    type: statusMap[detail.state]?.type || 'default',
                    size: 'small',
                  },
                  { default: () => statusMap[detail.state]?.text || detail.state }
                )
              );
            })
          );
        },
      },
      {
        title: '解决计划',
        key: 'workOrderSceneDetails',
        align: 'center',
        resizable: true,
        ellipsis: {
          tooltip: true,
        },
        render: (row) => {
          if (!row.workOrderSceneDetails?.length) return '';

          return h(
            'div',
            { class: 'table-cell-list' },
            row.workOrderSceneDetails.map((detail: any) =>
              h(
                'div',
                {
                  class: `solution-item ${row.workOrderSceneDetails.length > 1 ? 'cell-item' : ''}`,
                },
                detail.solutionPlan
              )
            )
          );
        },
      },
      {
        title: '跟踪单号',
        key: 'workOrderSceneDetails',
        align: 'center',
        resizable: true,
        ellipsis: {
          tooltip: true,
        },
        render: (row) => {
          if (!row.workOrderSceneDetails?.length) return '';

          return h(
            'div',
            { class: 'table-cell-list' },
            row.workOrderSceneDetails.map((detail: any) =>
              h(
                'div',
                {
                  class: row.workOrderSceneDetails.length > 1 ? 'cell-item' : '',
                },
                h(
                  NTag,
                  {
                    type: 'info',
                    size: 'small',
                    onClick: async () => {
                      const { dtsNo } = detail;
                      window.open(
                        `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${dtsNo}`,
                        '_blank'
                      );
                    },
                  },

                  { default: () => detail.dtsNo }
                )
              )
            )
          );
        },
      },
    ],
  },
  { key: 'functionFalut', title: '功能故障' },
  { key: 'performance', title: '性能功耗' },
  { key: 'stability', title: '稳定性' },
  { key: 'ux', title: 'UX体验' },
  // { key: 'security', title: '安全' },
  // { key: 'compatibility', title: '兼容性' },
];
const curTab = ref(tabs[0].title);
const exportLoading = ref(false);
const tabChange = (value: any) => {
  curTab.value = tabs.find((item) => item.key === value)!.title;
  loadWorkOrderScene(appName.value, curTab.value);
};
const renderOptionWithTooltip = ( option ) => {
  return h(
    NTooltip,
    {
      trigger: 'hover',
      placement: 'right',
      delay: 300,
      style: { maxWidth: '300px' }
    },
    {
      trigger: () => h('span', { class: 'option-content' }, option.label),
      default: () => option.label
    }
  )
}
const handleExport = () => {
  return new Promise((resolve, reject) => {
    const req = new XMLHttpRequest();
    exportLoading.value = true;
    const queryParams = {
      appName: appName.value,
    };
    req.open(
      'POST',
      `http://${window.location.host}/ewp/management/workOrder/exportAppReport`,
      true
    );
    req.responseType = 'blob';
    req.setRequestHeader('Content-Type', 'application/json');
    req.onload = function () {
      const data = req.response;
      const blob = new Blob([data]);
      const blobUrl = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.download = '导出.xlsx';
      a.href = blobUrl;
      a.click();
      exportLoading.value = false;
      resolve(true);
    };
    req.send(JSON.stringify(queryParams));
  });
};
const paginationMissing = reactive({
  page: 1,
  pageSize: 9,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [9, 20, 50, 100],
  pageSlot: 5,
  prefix({ itemCount }) {
    return `Total：${itemCount}`;
  },
  onChange: (page: number) => {
    paginationMissing.page = page;
    loadWorkOrderScene(appName.value, curTab.value);
  },
  onUpdatePageSize: (pageSize: number) => {
    paginationMissing.pageSize = pageSize;
    paginationMissing.page = 1;
    loadWorkOrderScene(appName.value, curTab.value);
  },
});
// Tab表格列定义
const tabColumns = [
  {
    title: '场景名称',
    key: 'sceneName',
    width: 200,
    align: 'center',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    render: (row) => {
      const description = row.sceneName.split('】').pop();
      return h('span', {}, description);
    },
  },
  {
    title: '归属功能',
    key: 'ownerFunction',
    align: 'center',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '故障分类',
    key: 'problemKind',
    align: 'center',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '提及声量',
    key: 'opinionNum',
    align: 'center',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '提及占比',
    key: 'percent',
    render: (row) => `${toPercent(row.percent)}%`,
    align: 'center',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '状态跟踪',
    key: 'workOrderSceneDetails',
    align: 'center',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    render: (row) => {
      if (!row.workOrderSceneDetails?.length) return '';

      return h(
        'div',
        { class: 'table-cell-list' },
        row.workOrderSceneDetails.map((detail: any) => {
          const statusMap = {
            pending: { text: '待处理', type: 'warning' },
            processing: { text: '处理中', type: 'info' },
            completed: { text: '已完成', type: 'success' },
          };
          return h(
            'div',
            {
              class: row.workOrderSceneDetails.length > 1 ? 'cell-item' : '',
            },
            h(
              NTag,
              {
                type: statusMap[detail.state]?.type || 'default',
                size: 'small',
              },
              { default: () => statusMap[detail.state]?.text || detail.state }
            )
          );
        })
      );
    },
  },
  {
    title: '解决计划',
    key: 'workOrderSceneDetails',
    align: 'center',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    render: (row) => {
      if (!row.workOrderSceneDetails?.length) return '';

      return h(
        'div',
        { class: 'table-cell-list' },
        row.workOrderSceneDetails.map((detail: any) =>
          h(
            'div',
            {
              class: `solution-item ${row.workOrderSceneDetails.length > 1 ? 'cell-item' : ''}`,
            },
            detail.solutionPlan
          )
        )
      );
    },
  },
  {
    title: '跟踪单号',
    key: 'workOrderSceneDetails',
    align: 'center',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    render: (row) => {
      if (!row.workOrderSceneDetails?.length) return '';

      return h(
        'div',
        { class: 'table-cell-list' },
        row.workOrderSceneDetails.map((detail: any) =>
          h(
            'div',
            {
              class: row.workOrderSceneDetails.length > 1 ? 'cell-item' : '',
            },
            h(
              NTag,
              {
                type: 'info',
                size: 'small',
                onClick: async () => {
                  const { dtsNo } = detail;
                  window.open(
                    `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${dtsNo}`,
                    '_blank'
                  );
                },
              },

              { default: () => detail.dtsNo }
            )
          )
        )
      );
    },
  },
];
const tabTableData = ref();
async function loadWorkOrderScene(appName: string, type: string) {
  try {
    const { total, records } = await workOrderScene({
      appName,
      type,
      pageNo: paginationMissing.page,
      pageSize: paginationMissing.pageSize,
    });
    tabTableData.value = records;
    paginationMissing.itemCount = total;
  } catch (error) {
    console.error('Failed to load work order scene data:', error);
  }
}
// 监听窗口大小变化
onMounted(() => {
  loadWorkOrderData(appName.value);
  loadWorkOrderStatus(appName.value);
  loadWorkOrderScene(appName.value, curTab.value);
});
</script>

<style scoped>
.app-detail {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.header-card {
  margin-bottom: 10px;
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-button {
  flex-shrink: 0;
}

.app-title {
  font-size: 24px;
  color: #303133;
  margin: 0;
  padding: 10px 0;
  flex-grow: 1;
}

.charts {
  display: flex;
  justify-content: space-between;
  gap: 10px;
}

.chart {
  flex: 1;
}

.chart-card {
  height: 100%;
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.chart-title {
  font-size: 18px;
  color: #303133;
  font-weight: bold;
}

.chart-container {
  width: 100%;
  height: 300px;
}

@media (max-width: 768px) {
  .charts {
    flex-direction: column;
  }
}
.top-card {
  margin-bottom: 10px;
}

.table-card {
  margin-top: 10px;
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.custom-table td) {
  padding-top: 2px !important;
  padding-bottom: 2px !important;
}
:deep(.add-new-line) {
  display: none !important;
  cursor: pointer;
}
:deep(tr:hover .add-new-line) {
  display: inline-block !important;
}
:deep(tr:hover .add-new-line.disabled) {
  cursor: not-allowed;
}
</style>
