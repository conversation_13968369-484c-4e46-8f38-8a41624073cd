<template>
  <div class="fut-questions-container">
    <questions-table :source="props.source"></questions-table>
  </div>
</template>

<script lang="ts">
export default {
  name: 'FutQuestions'
}
</script>

<script lang="ts" setup>
import questionsTable from './questionsTable/questionsTable.vue'

// 父组件参数
const props = defineProps<{
  source: string;
}>();
</script>

<style scoped>
.fut-questions-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
  width: 100%;
}
</style>
