<template>
  <div>
    <template v-for="(action, index) in getActions" :key="index" :render-icon="action.icon">
      <n-button v-bind="action" style="margin-right: 10px" text >

        {{ action.label }}
      </n-button>
    </template>
  </div>
</template>

<script lang="ts">
  import { defineComponent, PropType, computed, toRaw, onMounted } from 'vue';
  export default defineComponent({
    props: {
      actions: {
        type: Object,
        default: null,
        required: true,
      },
    },
    setup(props) {
      onMounted(() => {
        // console.log(props);
      });
      const getActions = computed(() => {
        return props.actions;
      });
      return {
        getActions,
      };
    },
  });
</script>
