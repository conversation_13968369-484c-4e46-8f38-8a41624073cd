import {
  CodeDemoDto,
  DeleteDemoReq,
} from "@/api/system/code";
import { storage } from "@/utils/Storage";
import { CURRENT_USER } from "@/store/mutation-types";
import { UserDto } from "@/api/system/usermanage";
import { useUserStore } from "@/store/modules/user";
import { FilterOption } from "naive-ui/es/data-table/src/interface";
import {
  ALL_REVIEW_STATUS,
  CODE_REVIEW_STATUS_MAP,
  INDUSTRY_OPTIONS,
  SEND_TYPE_OPTIONS,
} from "./consts";
import { FilterStatus } from "./types";

// 获取全部状态数据
export function getAllStatusOptions(): FilterOption[] {
  return ALL_REVIEW_STATUS.map(item => ({
    value: item,
    label: CODE_REVIEW_STATUS_MAP[item]
  }))
}
export function getDefaultFilterStatus(demoId: string): FilterStatus {
  return {
    searchKey: demoId ? 'demoId' : 'demoName',
    searchValue: demoId ? demoId : '',
    apiVersion: [],
    reviewStatus: [],
    selectValue: [],
    onlyMine: false,
  }
}
export function getDefaultDeleteInfo(): DeleteDemoReq {
  return {
    deletePerson: useUserStore().getUserInfo.label,
    deleteReason: '',
    deleteDemoIdList: [],
  }
}
export function isUserAdmin(): boolean {
  return !!storage.get<UserDto>(CURRENT_USER)?.roles?.includes('3_admin');
}

export function getIndustryLabel(value: string): string {
  return INDUSTRY_OPTIONS.find(item => item.value === value)?.label ?? '';
}
export function getSendTypeLabel(value: string): string {
  return SEND_TYPE_OPTIONS.find(item => item.value === value)?.label ?? '';
}
