export enum RoleMgmtPathNameEnum {
  ALL = 'roleManagement',
  KNOWLEDGE = 'knowledgeRoleManagement',
  FAULT_KNOW = 'faultKnowRoleManagement',
  SAMPLE_CODE = 'codeRoleManagement',
  LISTING_PROTECTION = 'listingProtectionRoleManagement',
  TEST = 'testRoleManagement',
  PROTOTYPE = 'prototypeRoleManagement',
  DEPARTMENT = 'departmentRoleManagement',
  ORDER_QUALITY = 'orderQualityRoleManagement',
  DEVELOPER_COMMUNITY = 'developerCommunityRoleManagement',
}
