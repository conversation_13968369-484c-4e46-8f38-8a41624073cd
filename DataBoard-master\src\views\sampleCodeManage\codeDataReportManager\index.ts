import { reactive } from 'vue';

export const defaultModel = {
  appName: null,
  scene: null,
  upscale: null,
  rating: null,
};

export const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  pageCount: 10,
  itemCount: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  prefix({ itemCount }) {
    return `总计：${itemCount}`;
  },
  onChange: (page: number) => {
    paginationReactive.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    paginationReactive.pageSize = pageSize;
    paginationReactive.page = 1;
  },
});
