<template>
  <div>
    <n-card>
      <div>
        <div class="layout-page-header">
          <!-- eslint-disable-next-line vue/html-self-closing -->
          <n-page-header title="批量生成邮件小工具"></n-page-header>
          <n-space style="margin-top: 15px">
            <n-button secondary strong type="error" @click="showModal(OWNERSHIP_TRANSFER)">
              物权转移(旧)
            </n-button>
            <n-button secondary strong type="info" @click="showModal(OWNERSHIP_TRANSFER_NEW)">
              物权转移(2B)
            </n-button>
            <n-button secondary strong type="warning" @click="showModal(DELIVERY_DEVICE)">
              设备签收(旧)
            </n-button>
            <n-button secondary strong type="error" @click="showModal(DELIVERY_DEVICE_NEW)">
              设备签收(2B)
            </n-button>
          </n-space>
        </div>
        <n-modal v-model:show="showListAddModal">
          <n-card
            style="width: 600px"
            title="批量导入"
            :bordered="false"
            size="huge"
            role="dialog"
            aria-modal="true"
          >
            <n-button text @click="handleDownload" style="margin-bottom: 20px"
              >点击下载导入模板</n-button
            >
            <n-upload
              action="#"
              :custom-request="customRequest"
              :multiple="true"
              :default-file-list="fileList"
              accept=".xls,.xlsx,"
              :max="1"
              directory-dnd
            >
              <n-upload-dragger>
                <div style="margin-bottom: 12px">
                  <n-icon size="48" :depth="3">
                    <ArchiveIcon />
                  </n-icon>
                </div>
                <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
              </n-upload-dragger>
            </n-upload>
          </n-card>
        </n-modal>
      </div>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
  import { Add, Refresh, Close, GridOutline } from '@vicons/ionicons5';
  import {
    NIcon,
    NButton,
    NTag,
    useDialog,
    UploadCustomRequestOptions,
    useMessage,
  } from 'naive-ui';
  import { h, ref, reactive, onBeforeMount } from 'vue';
  import { useRouter } from 'vue-router';
  import {
    template_ownership_transfer,
    template_ownership_transfer_new,
    template_ownership_transfer_INHOUSE,
    template_DELIVERY_DEVICE,
    template_DELIVERY_DEVICE_FEEDBACK,
    addressCName,
  } from './index';
  import { useUserStore } from '@/store/modules/user';
  import { getEmployeeList, queryUserList } from '@/api/system/usermanage';
  import {
    getProblemList,
    addProblem,
    updateProblem,
    downloadTemplate,
    uploadExcel,
    createOutlookFile,
    downloadExcel,
    getAppInfo,
    getAddress,
  } from '@/api/dataview/urgeSignature';

  const OWNERSHIP_TRANSFER = '0'; // 物权转移模板（PAD设备转移还需要使用老模板）
  const OWNERSHIP_TRANSFER_NEW = '1'; // 物权转移 新模板
  const DELIVERY_DEVICE = '2'; // 设备寄送模板
  const DELIVERY_DEVICE_NEW = '3'; // 设备催签模板

  const userStore = useUserStore();
  const filterUserListValue = ref(
    JSON.parse(localStorage.getItem('filterUserListValue') || JSON.stringify([]))
  );

  const router = useRouter();
  const currentUser = userStore.getUserInfo;

  const collapse = ref(false);
  const showListAddModal = ref(false);
  const message = useMessage();
  const fileList = ref([]);
  const currentMailType = ref('0');

  const handleDownload = async () => {
    downloadTemplate()
      .then((res) => {
        if (res) {
        } else {
          message.error('下载失败！');
        }
      })
      .catch((e) => {
        message.error(`下载失败，原因：${e?.response?.data?.error}`);
      });
  };
  const customRequest = async ({ file, onError }) => {
    const formData = new FormData();
    formData.append('file', file.file as File);
    //上传接口
    formData.forEach((item) => {
      console.log(item);
    });
    try {
      let { data } = await uploadExcel(formData);
      let res = data.data;
      const errMsg = {};
      const keys = Object.keys(res);
      for (let i = 0; i < keys.length; i++) {
        if (!keys[i]) {
          continue;
        }
        let data = res[keys[i]];
        let mailInfo = { subject: '', content: '' };
        const otherCcToList: string[] = [];
        const itemData = data[0] || {};
        switch (currentMailType.value) {
          case OWNERSHIP_TRANSFER:
            mailInfo = template_ownership_transfer(data);
            break;
          case OWNERSHIP_TRANSFER_NEW:
            mailInfo = template_ownership_transfer_new(data);
            otherCcToList.push(
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>'
            );
            break;
          case DELIVERY_DEVICE:
            mailInfo = template_DELIVERY_DEVICE(data);
            otherCcToList.push(
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>',
              '<EMAIL>'
            );
            break;
          case DELIVERY_DEVICE_NEW:
            mailInfo = template_DELIVERY_DEVICE_FEEDBACK(data);
            break;
        }
        errMsg[mailInfo.subject] = [];
        const appInfo = await getAppInfo({
          applicationName: itemData.applicationName || '',
          applicationSubject: itemData.applicationSubject || '',
        });
        // 查询到四组一队邮箱
        if (appInfo.appName) {
          const { data: addressData } = await getAddress({
            BD: appInfo.bdOwner || '',
            BDLeader: appInfo.bdLeader || '',
            DTSE: appInfo.dtseOwner || '',
            DTSELeader: appInfo.dtseLeader || '',
            solution: appInfo.solutionOwner || '',
            solutionLeader: appInfo.solutionLeader || '',
          });
          Object.keys(addressData).forEach((v) => {
            if (addressData[v]) {
              otherCcToList.push(addressData[v]);
            } else {
              errMsg[mailInfo.subject].push(`${addressCName[v]}`);
            }
          });
        } else {
          errMsg[mailInfo.subject].push('四组一队');
        }
        // 去重
        const ccToList = [...new Set(data[0].emailCcAddress.split(';').concat(otherCcToList))];
        let { subject, content } = mailInfo;
        let pushData = {
          recipientList: data[0].emailAddress.split(';'),
          ccToList: ccToList.filter((v) => v),
          subject,
          content,
        };
        let fileRes = await createOutlookFile(pushData);

        downloadExcel(fileRes.data);
      }
      showListAddModal.value = false;
      let errorMsgStr = '';
      Object.keys(errMsg).forEach((v) => {
        if (errMsg[v].length) {
          errorMsgStr += `${v}邮件中，未查询到${errMsg[v].join(
            '、'
          )}邮箱信息，如需添加，请手动添加！`;
        }
      });
      if (errorMsgStr) {
        message.info(errorMsgStr, { duration: 8000, keepAliveOnHover: true, closable: true });
      }
    } catch (error) {
      onError();
    }
  };

  const showModal = (mailType: string) => {
    showListAddModal.value = true;
    currentMailType.value = mailType;
  };
</script>

<style lang="less" scoped>
  .layout-page-header {
    margin-top: 20px;
    .n-select {
      min-width: 250px;
    }
  }
  .unfold-icon {
    display: flex;
    align-items: center;
    height: 100%;
    margin-left: -3px;
  }
</style>
