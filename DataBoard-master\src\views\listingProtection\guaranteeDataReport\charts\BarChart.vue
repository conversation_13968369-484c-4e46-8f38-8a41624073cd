<template>
  <div ref="barChartDom" id="barChartDom" class="chart-container"></div>
</template>

<script lang="ts" setup>
import { onMounted } from "vue"
import * as echarts from "echarts"

let chart

let { title, yData, xData } = defineProps(['title', 'xData', 'yData'])

onMounted(() => {
  initChart(title, xData, yData)
  resizeChart()
})

const resizeChart = () => {
  window.addEventListener('resize', () => {
    if (chart) {
      chart.resize()
    }

  })
}

// 基础配置一下Echarts
function initChart(title: string, xData: string[], yData: number[]) {
  chart = echarts.init(document.getElementById("barChartDom"));
  // 把配置和数据放这里
  chart.setOption({
    title: {
      text: title,
      x: 'center',
      y: 10
    },

    xAxis: {
      type: 'category',
      data: xData
    },
    tooltip: {
      trigger: "axis"
    },
    yAxis: {
      type: "value"
    },
    series: [
      {
        data: yData,
        type: 'bar',
        barWidth: '50%',
      }
    ]
  });
  window.onresize = function () {
    //自适应大小
    chart.resize();
  };
}


</script>

<style scoped>
.fut-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
  width: 100%;
  justify-content: space-between;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
