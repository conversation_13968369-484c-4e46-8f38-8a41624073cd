import { reactive } from 'vue';

export const defaultModel = {
  qualificationRate: null,
  currentPeriodDefectiveOrders: null,
  date: null,
};

export const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  prefix({ itemCount }) {
    return `总计：${itemCount}`;
  },
  onChange: (page: number) => {
    paginationReactive.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    paginationReactive.pageSize = pageSize;
    paginationReactive.page = 1;
  },
});
