{
  "compilerOptions": {
    "noUnusedLocals": false, //不检查未使用的变量
    "noUnusedParameters": false, //不检查未使用的参数
    "target": "esnext",
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "allowSyntheticDefaultImports": true,
    "strictFunctionTypes": false,
    "jsx": "preserve",
    "baseUrl": ".",
    "allowJs": true,
    "sourceMap": true,
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "experimentalDecorators": true,
    "lib": ["dom", "esnext"],
    "typeRoots": ["./node_modules/@types/",
      "./types"
    ],
    "noImplicitAny": false,
    "skipLibCheck": true,
    "paths": {
      "@/*": ["src/*"],
      "/#/*": ["types/*"]
    }
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "types/**/*.d.ts",
    "types/**/*.ts",
    "build/**/*.ts",
    "build/**/*.d.ts",
    "mock/**/*.ts",
    "components.d.ts",
    "vite.config.ts",
  ],
  "exclude": [
    "../node_modules", "dist", "**/*.js"]
}
