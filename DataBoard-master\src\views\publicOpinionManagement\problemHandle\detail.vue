<template>
  <div>
    <n-card>
      <div class="header">
        <n-button @click="back" icon-placement="left" secondary strong>
          <template #icon>
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 512 512">
              <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="48"
                d="M244 400L100 256l144-144"></path>
              <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="48"
                d="M120 256h292"></path>
            </svg>
          </template>
          返回
        </n-button>
        <h1 class="app-title">{{ desc }}</h1>
      </div>
    </n-card>
    <div class="charts">
      <div class="chart">
        <n-spin :show="loadingTrend">
          <n-card class="chart-card">
            <template #header>
              <div class="chart-title">舆情问题声量趋势</div>
            </template>
            <div ref="trendChartRef" class="chart-container"></div>
          </n-card>
        </n-spin>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter, useRoute } from 'vue-router';
import { ref, onMounted } from 'vue'
import { NCard, NSpin } from "naive-ui";
import * as echarts from "echarts";
import service from "@/utils/axios";
import { formatDateTime } from '.';

const router = useRouter()
const route = useRoute()
const desc = ref(route.query.desc);
const dtsNum = ref(route.query.dtsNum);
const loadingTrend = ref(true);
const trendChartRef = ref<HTMLElement | null>(null);

const back = () => {
  router.go(-1)
}

async function fetchTrendData() {
  try {
    const response = await service.get(
      `/dts/voiceTrend?dtsOrder=${dtsNum.value}`
    );
    if (response?.data) {
      renderTrendChart(response.data);
    }
  } catch (error) {
    console.error('Failed to fetch trend data:', error);
  } finally {
    loadingTrend.value = false;
  }
}
function renderTrendChart(data) {
  if (trendChartRef.value) {
    Array.isArray(data) && data.sort((a, b) => {
      return a.date > b.date ? 1 : -1
    })
    const chart = echarts.init(trendChartRef.value);
    chart.setOption({
      tooltip: { trigger: 'item' },
      xAxis: { type: 'category', data: data.map((item) => item.date) },
      yAxis: { type: 'value' },
      series: [
        {
          data: data.map((item) => item.sumVoice),
          type: 'line',
          smooth: true,
        },
      ],
    });
  }
}

onMounted(async () => {
  await fetchTrendData();
})

</script>

<style lang="less" scoped>
.header {
  display: flex;
  align-items: center;
  gap: 20px;
}

.app-title {
  font-size: 24px;
  color: #303133;
  margin: 0;
  padding: 10px 0;
  flex-grow: 1;
}

.charts {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  margin-top: 12px;
}

.chart {
  flex: 1;
}

.chart-card {
  height: 100%;
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.chart-title {
  font-size: 18px;
  color: #303133;
  font-weight: bold;
}

.chart-container {
  width: 100%;
  height: 300px;
}

@media (max-width: 768px) {
  .charts {
    flex-direction: column;
  }
}
</style>
