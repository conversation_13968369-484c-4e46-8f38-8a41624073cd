<template>
  <div class="process-page">
    <!-- 步骤指示器 -->
    <n-card class="steps-card">
      <n-steps :current="currentStep" :status="stepStatus" class="steps-wrapper">
        <n-step
          v-for="(step, index) in steps"
          :key="index"
          :title="step.title"
          :description="step.description"
          :status="getStepStatus(index)"
        >
          <template #icon>
            <n-icon size="24" :color="getStepIconColor(index)">
              <component :is="step.icon" />
            </n-icon>
          </template>
          <template #extra>
            <div class="step-extra-info">
              <n-tag
                v-if="step.handler"
                :type="getStepTagType(index)"
                size="small"
                round
                class="handler-tag"
              >
                处理人: {{ step.handler }}
              </n-tag>
              <n-text :depth="3" class="time-info">
                {{ step.time || '待处理' }}
              </n-text>
            </div>
          </template>
        </n-step>
      </n-steps>

      <div class="progress-timeline hidden">
        <div class="timeline-item" v-for="(step, index) in steps" :key="index">
          <n-text :class="['time-label', { active: index <= currentStep }]">
            {{ step.expectedTime || '预计 2h' }}
          </n-text>
        </div>
      </div>
    </n-card>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <n-card class="content-wrapper" :title="currentStepInfo.title">
        <template #header-extra>
          <n-tag :type="getStatusType" size="medium">
            {{ currentStepInfo.status }}
          </n-tag>
        </template>

        <!-- 定位定界流程 -->
        <n-form
          v-if="currentStep === 0"
          ref="locateFormRef"
          :model="locateForm"
          :rules="locateRules"
          label-placement="left"
          label-width="120"
          require-mark-placement="right-hanging"
        >
          <n-grid :cols="2" :x-gap="24">
            <n-grid-item>
              <n-form-item label="问题单号" path="orderId">
                <n-input-group>
                  <n-input v-model:value="locateForm.orderId" readonly style="width: 70%" />
                  <n-button type="primary" ghost style="width: 30%" @click="openDtsLink">
                    查看DTS
                  </n-button>
                </n-input-group>
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="应用名称" path="appName">
                <n-input v-model:value="locateForm.appName" readonly />
              </n-form-item>
            </n-grid-item>
          </n-grid>

          <n-form-item label="问题描述" path="description">
            <n-input
              v-model:value="locateForm.description"
              type="textarea"
              :autosize="{ minRows: 3 }"
              readonly
            />
          </n-form-item>

          <n-divider title-placement="left">定位分析</n-divider>

          <n-grid :cols="2" :x-gap="24">
            <n-grid-item>
              <n-form-item label="模块" path="module" required>
                <n-select
                  v-model:value="locateForm.module"
                  :options="MODULE_OPTIONS"
                  @update:value="handleModuleChange"
                />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="是否重复问题" path="isRepeat">
                <n-radio-group v-model:value="locateForm.isRepeat">
                  <n-space>
                    <n-radio :value="false">否</n-radio>
                    <n-radio :value="true">是</n-radio>
                  </n-space>
                </n-radio-group>
              </n-form-item>
            </n-grid-item>
          </n-grid>

          <n-form-item v-if="locateForm.isRepeat" label="重复问题单号" path="repeatDtsId">
            <n-input v-model:value="locateForm.repeatDtsId" placeholder="请输入重复的DTS单号" />
          </n-form-item>

          <n-form-item label="原因分析" path="analysis" required>
            <n-input
              v-model:value="locateForm.analysis"
              type="textarea"
              :autosize="{ minRows: 6 }"
              :default-value="DEFAULT_ANALYSIS_TEMPLATE"
            />
          </n-form-item>

          <n-divider title-placement="left">处理流转</n-divider>

          <n-grid :cols="2" :x-gap="24">
            <n-grid-item>
              <n-form-item label="下一步处理方式" path="nextStep" required>
                <n-select
                  v-model:value="locateForm.nextStep"
                  :options="nextStepOptions"
                  @update:value="handleNextStepChange"
                />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="下一步处理人" path="nextHandler" required>
                <n-input v-model:value="locateForm.nextHandler" />
              </n-form-item>
            </n-grid-item>
          </n-grid>
        </n-form>

        <!-- 锁定计划流程 -->
        <n-form
          v-if="currentStep === 1"
          ref="planFormRef"
          :model="planForm"
          :rules="planRules"
          label-placement="left"
          label-width="120"
          require-mark-placement="right-hanging"
        >
          <n-grid :cols="2" :x-gap="24">
            <n-grid-item>
              <n-form-item label="下一步处理方式" path="nextStep">
                <n-select v-model:value="planForm.nextStep" :options="nextStepOptions" />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="下一步处理人" path="nextHandler">
                <n-input v-model:value="planForm.nextHandler" />
              </n-form-item>
            </n-grid-item>
          </n-grid>
          <n-form-item label="当前处理人" path="currentHandler">
            <n-input v-model:value="planForm.currentHandler" disabled />
          </n-form-item>
          <n-form-item label="计划锁定时间" path="planTime">
            <n-date-picker v-model:value="planForm.planTime" type="datetime" />
          </n-form-item>
          <n-form-item label="问题归属" path="attribution">
            <n-select v-model:value="planForm.attribution" :options="PROBLEM_ATTRIBUTION_OPTIONS" />
          </n-form-item>
          <n-form-item label="解决方案" path="solution">
            <n-input v-model:value="planForm.solution" type="textarea" :rows="6" />
          </n-form-item>
        </n-form>

        <!-- 问题闭环流程 -->
        <n-form
          v-if="currentStep === 2"
          ref="closeFormRef"
          :model="closeForm"
          :rules="closeRules"
          label-placement="left"
          label-width="120"
          require-mark-placement="right-hanging"
        >
          <n-grid :cols="2" :x-gap="24">
            <n-grid-item>
              <n-form-item label="当前处理人" path="currentHandler">
                <n-input v-model:value="closeForm.currentHandler" disabled />
              </n-form-item>
            </n-grid-item>
            <n-grid-item>
              <n-form-item label="下一步处理人" path="nextHandler">
                <n-input v-model:value="closeForm.nextHandler" />
              </n-form-item>
            </n-grid-item>
          </n-grid>
          <n-form-item label="处理方式" path="handleMethod">
            <n-input v-model:value="closeForm.handleMethod" />
          </n-form-item>
          <n-form-item label="关闭类型" path="closeType">
            <n-select v-model:value="closeForm.closeType" :options="CLOSE_TYPE_OPTIONS" />
          </n-form-item>
          <n-form-item label="测试报告/处理建议" path="report">
            <n-input v-model:value="closeForm.report" type="textarea" :rows="6" />
          </n-form-item>
        </n-form>
      </n-card>

      <!-- 右侧辅助分析区域 -->
      <n-card v-if="currentStep === 0" class="analysis-sidebar">
        <n-tabs type="line" animated>
          <!-- 相似问题分析 -->
          <n-tab-pane name="similar" tab="相似问题分析">
            <n-space vertical>
              <n-text>问题相似度分析结果:</n-text>
              <n-table :bordered="false" :single-line="false">
                <thead>
                  <tr>
                    <th>DTS单号</th>
                    <th>相似度</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="item in similarIssues" :key="item.id">
                    <td>{{ item.dtsId }}</td>
                    <td>
                      <n-progress
                        type="line"
                        :percentage="item.similarity"
                        :indicator-placement="'inside'"
                        processing
                      />
                    </td>
                    <td>
                      <n-button text type="primary" @click="viewSimilarIssue(item)">
                        查看
                      </n-button>
                    </td>
                  </tr>
                </tbody>
              </n-table>
            </n-space>
          </n-tab-pane>

          <!-- 自动定位分析 -->
          <n-tab-pane name="locate" tab="自动定位分析">
            <n-space vertical>
              <n-descriptions :column="1" bordered>
                <n-descriptions-item label="问题类型"> 功能异常 </n-descriptions-item>
                <n-descriptions-item label="影响范围"> 中等 </n-descriptions-item>
                <n-descriptions-item label="建议优先级"> P2 </n-descriptions-item>
              </n-descriptions>
              <n-collapse>
                <n-collapse-item title="日志分析" name="logs">
                  <n-text type="error">发现异常日志 3 条</n-text>
                  <n-code :code="sampleErrorLog" language="bash" />
                </n-collapse-item>
              </n-collapse>
            </n-space>
          </n-tab-pane>

          <!-- 知识推荐 -->
          <n-tab-pane name="knowledge" tab="知识推荐">
            <n-space vertical>
              <n-card
                v-for="item in knowledgeList"
                :key="item.id"
                :title="item.title"
                size="small"
                class="knowledge-card"
                @click="applyKnowledge(item)"
              >
                <n-text depth="3">{{ item.summary }}</n-text>
                <template #footer>
                  <n-space justify="end">
                    <n-tag :bordered="false" type="info"> 应用次数: {{ item.usageCount }} </n-tag>
                  </n-space>
                </template>
              </n-card>
            </n-space>
          </n-tab-pane>
        </n-tabs>
      </n-card>
    </div>

    <!-- 底部操作按钮 -->
    <div class="footer-actions">
      <n-space justify="center" :size="24">
        <n-button v-if="currentStep > 0" @click="handlePrevStep" :loading="loading" size="large">
          上一步
        </n-button>
        <n-button type="primary" @click="handleNextStep" :loading="loading" size="large">
          {{ currentStep === 2 ? '提交' : '下一步' }}
        </n-button>
      </n-space>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted } from 'vue';
  import {
    NCard,
    NSteps,
    NStep,
    NForm,
    NFormItem,
    NInput,
    NSelect,
    NRadioGroup,
    NRadio,
    NDatePicker,
    NButton,
    NSpace,
    NGrid,
    NGridItem,
    useMessage,
    NTag,
    NDivider,
    NInputGroup,
    NTabs,
    NTabPane,
    NProgress,
    NDescriptions,
    NDescriptionsItem,
    NCollapse,
    NCollapseItem,
    NCode,
    NText,
  } from 'naive-ui';
  import {
    MODULE_OPTIONS,
    PROBLEM_ATTRIBUTION_OPTIONS,
    CLOSE_TYPE_OPTIONS,
    DEFAULT_ANALYSIS_TEMPLATE,
  } from './columns';
  import { useRoute, useRouter } from 'vue-router';
  import { Search, LocationOutline, LockClosed, CheckmarkCircle } from '@vicons/ionicons5';

  const route = useRoute();
  const router = useRouter();
  const message = useMessage();

  // 步骤控制
  const currentStep = ref(0);
  const loading = ref(false);
  const stepStatus = ref<'process' | 'error' | 'finish'>('process');

  const stepTitles = ['定位定界', '锁定计划', '问题闭环'];

  // 表单引用
  const locateFormRef = ref(null);
  const planFormRef = ref(null);
  const closeFormRef = ref(null);

  // 表单数据
  const locateForm = ref({
    nextStep: null,
    nextHandler: '',
    currentHandler: '',
    module: null,
    isRepeat: false,
    repeatDtsId: '',
    analysis: DEFAULT_ANALYSIS_TEMPLATE,
    orderId: '',
    appName: '',
    description: '',
  });

  const planForm = ref({
    nextStep: null,
    nextHandler: '',
    currentHandler: '',
    planTime: null,
    attribution: null,
    solution: '',
  });

  const closeForm = ref({
    currentHandler: '',
    nextHandler: '',
    handleMethod: '',
    closeType: null,
    report: '',
  });

  // 下一步处理方式选项
  const nextStepOptions = [
    { label: '流程1', value: 'flow1' },
    { label: '流程2', value: 'flow2' },
    { label: '流程3', value: 'flow3' },
  ];

  // 表单验证规则
  const locateRules = {
    nextStep: { required: true, message: '请选择下一步处理方式' },
    nextHandler: { required: true, message: '请输入下一步处理人' },
    module: { required: true, message: '请选择模块' },
  };

  const planRules = {
    nextStep: { required: true, message: '请选择下一步处理方式' },
    nextHandler: { required: true, message: '请输入下一步处理人' },
    planTime: { required: true, message: '请选择计划锁定时间' },
    attribution: { required: true, message: '请选择问题归属' },
  };

  const closeRules = {
    nextHandler: { required: true, message: '请输入下一步处理人' },
    handleMethod: { required: true, message: '请输入处理方式' },
    closeType: { required: true, message: '请选择关闭类型' },
  };

  // 获取当前表单引用
  const getCurrentFormRef = () => {
    const formRefs = {
      0: locateFormRef,
      1: planFormRef,
      2: closeFormRef,
    };
    return formRefs[currentStep.value];
  };

  // 处理上一步
  const handlePrevStep = () => {
    if (currentStep.value > 0) {
      currentStep.value--;
    }
  };

  // 处理下一步
  const handleNextStep = async () => {
    try {
      loading.value = true;
      const formRef = getCurrentFormRef();
      await formRef.value?.validate();

      if (currentStep.value === 2) {
        // 最后一步，提交所有数据
        await handleSubmitAll();
        message.success('提交成功');
        router.push('/myTodo');
      } else {
        // 进入下一步
        currentStep.value++;
      }
    } catch (error) {
      console.error('Validation failed:', error);
      message.error('请填写完整信息');
    } finally {
      loading.value = false;
    }
  };

  // 提交所有数据
  const handleSubmitAll = async () => {
    const allData = {
      locate: locateForm.value,
      plan: planForm.value,
      close: closeForm.value,
    };

    // TODO: 调用API提交数据
    console.log('Submit all data:', allData);
  };

  // 初始化数据
  const initData = () => {
    const id = route.query.id;
    if (id) {
      // TODO: 根据ID获取数据
      console.log('Init data with id:', id);
    }
  };

  // 页面加载时初始化数据
  initData();

  // Mock数据
  const mockDtsData = {
    orderId: 'DTS2024032500123',
    appName: '示例应用',
    description: '【功能异常】应用在启动时偶发性崩溃，影响用户正常使用',
    currentHandler: '张三',
    status: '开发人员实施修改',
  };

  // 步骤配置
  const steps = [
    {
      title: '定位定界',
      description: '问题定位和定界分析',
      icon: LocationOutline,
      handler: '张三',
      time: '2024-03-25 10:30',
      expectedTime: '预计 2h',
    },
    {
      title: '锁定计划',
      description: '确定解决计划',
      icon: LockClosed,
      handler: '李四',
      time: '',
      expectedTime: '预计 4h',
    },
    {
      title: '问题闭环',
      description: '问题解决与闭环',
      icon: CheckmarkCircle,
      handler: '',
      time: '',
      expectedTime: '预计 2h',
    },
  ];

  // 当前步骤信息
  const currentStepInfo = computed(() => {
    const statusMap = {
      0: { title: '问题定位', status: '定位中' },
      1: { title: '解决计划', status: '待锁定' },
      2: { title: '问题闭环', status: '待闭环' },
    };
    return statusMap[currentStep.value];
  });

  // 获取步骤图标颜色
  const getStepIconColor = (index: number) => {
    if (index < currentStep.value) return '#18a058';
    if (index === currentStep.value) return '#2080f0';
    return '#d9d9d9';
  };

  // 获取步骤状态
  const getStepStatus = (index: number) => {
    if (index < currentStep.value) return 'finish';
    if (index === currentStep.value) return stepStatus.value;
    return 'wait';
  };

  // 获取步骤标签类型
  const getStepTagType = (index: number) => {
    if (index < currentStep.value) return 'success';
    if (index === currentStep.value) return 'warning';
    return 'default';
  };

  // 获取状态标签类型
  const getStatusType = computed(() => {
    const typeMap = {
      定位中: 'warning',
      待锁定: 'info',
      待闭环: 'success',
    };
    return typeMap[currentStepInfo.value.status];
  });

  // 处理模块变化
  const handleModuleChange = (value: string) => {
    // 根据选择的模块自动填充部分分析内容
    const moduleAnalysisMap = {
      '4796-UX体验': '涉及用户体验问题，主要表现在...',
      '4796-功能故障': '功能异常，具体表现为...',
      '4796-稳定性': '稳定性问题，复现场景为...',
      '4796-性能功耗': '性能问题，主要影响...',
    };

    if (moduleAnalysisMap[value]) {
      locateForm.value.analysis = moduleAnalysisMap[value] + '\n\n' + DEFAULT_ANALYSIS_TEMPLATE;
    }
  };

  // 处理下一步变化
  const handleNextStepChange = (value: string) => {
    // 根据选择的下一步自动推荐处理人
    const recommendHandlers = {
      flow1: ['张三', '李四'],
      flow2: ['王五', '赵六'],
      flow3: ['孙七', '周八'],
    };

    if (recommendHandlers[value]) {
      employeesOptions.value = recommendHandlers[value].map((name) => ({
        label: name,
        value: name,
      }));
    }
  };

  // 打开DTS链接
  const openDtsLink = () => {
    const url = `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${locateForm.value.orderId}`;
    window.open(url, '_blank');
  };

  // 初始化表单数据
  onMounted(() => {
    // 模拟加载DTS数据
    Object.assign(locateForm.value, mockDtsData);
  });

  // 模拟数据
  const similarIssues = ref([
    { id: 1, dtsId: 'DTS20240324001', similarity: 85 },
    { id: 2, dtsId: 'DTS20240323002', similarity: 72 },
    { id: 3, dtsId: 'DTS20240322003', similarity: 65 },
  ]);

  const sampleErrorLog = ref(`
  [ERROR] 2024-03-25 10:15:23.456 
  Exception in thread "main" java.lang.NullPointerException
      at com.example.Main.process(Main.java:123)
  `);

  const knowledgeList = ref([
    {
      id: 1,
      title: '常见启动崩溃分析方案',
      summary: '针对应用启动崩溃的常见原因分析和解决方案...',
      usageCount: 156,
      content: '详细的分析内容...',
    },
    {
      id: 2,
      title: '性能优化最佳实践',
      summary: '应用性能优化的关键点和实施建议...',
      usageCount: 89,
      content: '详细的优化方案...',
    },
  ]);

  // 查看相似问题
  const viewSimilarIssue = (item) => {
    window.open(`https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${item.dtsId}`, '_blank');
  };

  // 应用知识库内容
  const applyKnowledge = (item) => {
    locateForm.value.analysis = item.content + '\n' + locateForm.value.analysis;
    message.success('已应用知识库内容');
  };
</script>

<style scoped lang="less">
  .process-page {
    padding: 24px;
    background-color: #f5f7fa;
    min-height: 100vh;
  }

  .steps-card {
    margin-bottom: 24px;
    background: linear-gradient(135deg, #ffffff 0%, #f5f7fa 100%);
    border: none;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;

    :deep(.n-card__content) {
      padding: 40px 32px 24px;
    }
  }

  .steps-wrapper {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;

    :deep(.n-step) {
      &::before {
        background-color: #e5e7eb !important;
        height: 2px;
        top: 22px;
      }

      .n-step-header {
        padding-right: 0;

        &__title {
          font-size: 16px;
          font-weight: 600;
          color: #1f2937;
          margin-bottom: 4px;
          transition: all 0.3s ease;
        }

        &__description {
          font-size: 13px;
          color: #6b7280;
          max-width: 140px;
          line-height: 1.5;
        }
      }

      .n-step-indicator {
        width: 44px;
        height: 44px;
        background: #fff;
        border: 2px solid #e5e7eb;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        z-index: 1;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        &__icon {
          font-size: 22px;
        }

        &--process {
          background: #ecf5ff;
          border-color: #409eff;
          transform: scale(1.1);
          box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.1);
        }

        &--finish {
          background: #f0f9eb;
          border-color: #67c23a;
          box-shadow: 0 0 0 4px rgba(103, 194, 58, 0.1);
        }

        &--error {
          background: #fff0f0;
          border-color: #f56c6c;
          box-shadow: 0 0 0 4px rgba(245, 108, 108, 0.1);
        }
      }

      &--process {
        .n-step-header__title {
          color: #409eff;
        }
      }

      &--finish {
        &::before {
          background-color: #67c23a !important;
        }
        .n-step-header__title {
          color: #67c23a;
        }
      }
    }
  }

  .step-extra-info {
    margin-top: 12px;
    display: flex;
    align-items: center;
    gap: 12px;

    .handler-tag {
      font-size: 12px;
      padding: 2px 10px;
      border-radius: 12px;
      font-weight: 500;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    }

    .time-info {
      font-size: 12px;
      color: #666;
      background: #f5f7fa;
      padding: 2px 8px;
      border-radius: 4px;
    }
  }

  .progress-timeline {
    margin-top: 32px;
    padding: 0 80px;
    display: flex;
    justify-content: space-between;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 60px;
      right: 60px;
      height: 3px;
      background: #e5e7eb;
      transform: translateY(-50%);
    }

    .timeline-item {
      position: relative;
      flex: 1;
      text-align: center;
      z-index: 1;

      .time-label {
        background: #fff;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 13px;
        color: #666;
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
        display: inline-block;

        &.active {
          background: #ecf5ff;
          color: #409eff;
          border-color: #409eff;
          font-weight: 500;
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
        }
      }
    }
  }

  .main-content {
    display: flex;
    gap: 24px;
    max-width: 1400px;
    margin: 0 auto;
  }

  .content-wrapper {
    flex: 1;
  }

  .analysis-sidebar {
    width: 360px;
    height: fit-content;

    .knowledge-card {
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }
  }

  :deep(.n-progress) {
    width: 100px;
  }

  :deep(.n-code) {
    margin-top: 8px;
  }

  .footer-actions {
    max-width: 1000px;
    margin: 0 auto;
    padding: 24px 0;

    .n-button {
      min-width: 120px;
      height: 40px;
      font-size: 15px;
    }
  }

  :deep(.n-form-item) {
    .n-form-item-label {
      font-weight: 500;
    }
  }

  :deep(.n-input) {
    .n-input__input-el {
      font-size: 14px;
    }
  }

  :deep(.n-select) {
    .n-base-selection {
      font-size: 14px;
    }
  }
</style>
