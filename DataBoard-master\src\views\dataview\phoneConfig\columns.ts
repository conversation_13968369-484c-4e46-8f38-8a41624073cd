import { h } from 'vue';
import { NTag } from 'naive-ui';
import { ComponentType } from '@/components/Form/src/types';
import { FormSchema } from '@/components/Form/src/types/form';

interface ComponentStyle {
  width: string;
  textAlign: string;
}

interface ComponentProps {
  style: ComponentStyle;
  clearable?: boolean;
  placeholder?: string;
  options?: Array<{ label: string; value: any }>;
  filterable?: boolean;
}

interface SearchFormItem {
  field: string;
  label: string;
  component: 'Input' | 'Select' | 'DateRangePicker';
  componentProps?: ComponentProps;
}

// 创建表格列定义
export const createColumns = (): any => {
  const baseColumns = [
    {
      title: '传播名',
      key: 'name',
      resizable: true,
      width: 280,
      align: 'center',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: 'BETA代号',
      key: 'internalType',
      resizable: true,
      width: 200,
      align: 'center',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '产品型号',
      key: 'commercialType',
      resizable: true,
      width: 200,
      align: 'center',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '机型类别',
      key: 'deviceShape',
      resizable: true,
      width: 200,
      align: 'center',
      ellipsis: {
        tooltip: true,
      },
    },
  ];

  return baseColumns;
};

// 创建一个通用的样式对象
const commonStyle: ComponentProps = {
  style: {
    width: '300px',
    textAlign: 'left',
  },
  clearable: true,
  placeholder: '请输入',
};

// 为 Select 组件创建特定样式
const selectStyle: ComponentProps = {
  ...commonStyle,
  style: {
    ...commonStyle.style,
  },
  filterable: true,
  placeholder: '请选择',
};

// 搜索表单定义
export const getSearchFormItems = (): FormSchema[] => [
  {
    field: 'name',
    component: 'NInput',
    label: '传播名',
    componentProps: {
      placeholder: '请输入传播名',
    },
  },
  {
    field: 'internalType',
    component: 'NInput',
    label: 'BETA代号',
    componentProps: {
      placeholder: '请输入BETA代号',
    },
  },
  {
    field: 'commercialType',
    component: 'NInput',
    label: '产品型号',
    componentProps: {
      placeholder: '请输入产品型号',
    },
  },
  {
    field: 'deviceShape',
    component: 'NInput',
    label: '机型类别',
    componentProps: {
      placeholder: '请输入机型类别',
    },
  },
];
