(function (global) {
  let ws = null;
  let url = "ws://localhost";
  let ports = [27197, 27198, 27199];
  let index = 0;
  let connected;
  let timeout = 30000;

  let cid = 0;
  let callbacks = {};
  let pollingTimer;
  let userStatus = {};
  let heartbeatInterval = 20 * 1000;
  let heartbeatTimer = null;
  let connectTimer = null;

  function newGuid() {
    let s4 = function () {
      return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    };
    return (
      s4() +
      s4() +
      "-" +
      s4() +
      "-" +
      s4() +
      "-" +
      s4() +
      "-" +
      s4() +
      s4() +
      s4()
    );
  }

  let sendHeartbeat = function () {
    heartbeatTimer = setTimeout(function () {
      if (connected) {
        send(
          {
            type: "heartbeat",
          },
          function () {
            sendHeartbeat();
          }
        );
      } else {
        clearTimeout(heartbeatTimer);
      }
    }, heartbeatInterval);
  };

  let connect = function (interval) {
    pollingTimer = setTimeout(function () {
      if (index >= ports.length) {
        index = 0;
      }
      ws = new WebSocket(url + ":" + ports[index++]);
      bindEvents();
    }, interval || 0);
  };

  let onopen = function (evt) {
    ws.send("0" + newGuid());
    connectTimer = setTimeout(function () {
      ws.close();
    }, 5000);
  };

  let onclose = function (evt) {
    if (connected || typeof connected === "undefined") {
      connected = false;
      error();
    }
    connect();
  };

  let connectionSucceeded = function (data) {
    connected = true;
    sendHeartbeat();
    clearTimeout(pollingTimer);
    clearTimeout(connectTimer);
    ready(data);
  };

  let onmessage = function (evt) {
    let data = evt.data;
    if (typeof data !== "string") return;

    data = data.replace(/^\d+/, "");
    if (!data) return;

    try {
      data = JSON.parse(data);
    } catch (e) {
      window.console && console.error(e);
      return;
    }

    if (connected) {
      let event = events[data.type];
      if (event) {
        return event(data.data);
      }
      let cid = data.cid;
      let cb = callbacks[cid];
      if (cb) {
        if (data.ok) {
          cb(null, data.data);
        } else {
          cb({ ok: data.ok });
        }
        clearCallback(cid);
      }
    } else {
      if (data.type === "eSpace-ctrl-connection-success") {
        connectionSucceeded(data.data);
      } else {
        ws.close();
      }
    }
  };

  let bindEvents = function () {
    ws.onopen = onopen;
    ws.onclose = onclose;
    ws.onmessage = onmessage;
  };

  let clearCallback = function (cid) {
    clearTimeout(apiTimers[cid]);
    delete callbacks[cid];
    delete apiTimers[cid];
  };

  let apiTimers = {};
  let send = function (argv, cb) {
    let id = cid++;
    id = id + "";
    argv.cid = id;
    if (!connected) {
      cb &&
        setTimeout(cb, 0, { ok: false, message: "eSpace is not logged in." });
      return;
    }
    if (typeof cb === "function") {
      callbacks[id] = cb;
      apiTimers[id] = setTimeout(function () {
        cb({ ok: false, message: "time out" });
        clearCallback(id);
      }, timeout);
    }
    ws.send("1" + JSON.stringify(argv));
  };

  let ready = function () {};

  let out = {};

  out.init = function (conf) {
    if (conf) {
      timeout = conf.timeout || 30000;
    }
    connect();
  };

  out.ready = function (cb) {
    ready = cb;
  };

  let error = function () {};
  out.error = function (cb) {
    error = cb;
  };

  let attrToArr = function (name, total, object) {
    let result = [];
    for (let i = 0; i < total; i++) {
      let attrName = name;
      if (i) {
        attrName += i;
      }
      let attrVal = object[attrName];
      if (attrVal) {
        result.push(attrVal);
      }
    }
    return result;
  };

  let events = {};
  /**
   * 事件绑定
   * @param {String} event 事件名
   * @param {Function} hander 事件处理函数
   *
   * example:
   * out.on('user-status-change', function(data){
   *   console.log(data)
   * })
   */
  out.on = function (event, hander) {
    events[event] = hander;
  };

  /**
   * 获取用户信息
   * @param {String|Array} account 单个帐号或者帐号数组
   * @param {Function} cb 回调函数
   */
  out.getUserInfo = function (account, cb) {
    let fn = function (err, data) {
      if (err) {
        return cb(err);
      }
      let formatInfo = function (user) {
        return {
          account: user.account,
          name: user.name,
          mobile: attrToArr("mobile", 6, user),
          office_phone: attrToArr("office_phone", 6, user),
          home_phone: user.home_phone,
          ip_phone: user.ip_phone,
          other_phone: user.other_phone,
        };
      };
      if (data.account) {
        cb(null, formatInfo(data));
      } else {
        let result = {};
        for (let p in data) {
          let user = data[p];
          result[p] = user ? formatInfo(user) : user;
        }
        cb(null, result);
      }
    };
    send(
      {
        type: "get-user-info",
        param: account,
      },
      fn
    );
  };

  /**
   * 订阅用户状态
   * @param {String|Array} accounts 单个帐号或者帐号数组
   * @param {Function} cb 回调函数
   */
  out.subscribeUserStatus = function (accounts, cb) {
    if (Array.isArray(accounts)) {
      accounts.forEach(function (account) {
        userStatus[account] = true;
      });
    }
    send(
      {
        type: "subscribe-user-status",
        param: accounts,
      },
      cb
    );
  };

  /**
   * 拉起单人语音
   * @param {String} account 帐号
   * @param {String} num 可选，电话号码或voip
   * @param {Function} cb 回调函数
   */
  out.eSpaceCall = function (account, num, cb) {
    send(
      {
        type: "espace-call",
        param: {
          account: account,
          number: num,
        },
      },
      cb
    );
  };

  /**
   * 拉起单人语音
   * @param {String} account 帐号
   * @param {Function} cb 回调函数
   */
  out.eSpaceCallByAccount = function (account, cb) {
    send(
      {
        type: "espace-call",
        param: {
          account: account,
        },
      },
      cb
    );
  };

  /**
   * 拉起单人语音
   * @param {String} number 电话号码
   * @param {Function} cb 回调函数
   */
  out.eSpaceCallByNumber = function (number, cb) {
    send(
      {
        type: "espace-call",
        param: {
          number: number,
        },
      },
      cb
    );
  };

  /**
   * 拉起单聊IM窗口
   * @param {String} account 帐号
   * @param {Function} cb 回调函数
   */
  out.showImDialog = function (account, cb) {
    send(
      {
        type: "show-espace-im-dialog",
        param: account,
      },
      cb
    );
  };

  /**
   * 拉起群组IM窗口
   * @param {String} gid 群组id
   * @param {Function} cb 回调函数
   */
  out.showGroupDialog = function (gid, cb) {
    send(
      {
        type: "show-espace-im-group-dialog",
        param: gid,
      },
      cb
    );
  };

  /**
   * 添加到联系人列表
   * @param {String} account 帐号
   * @param {Function} cb 回调函数
   */
  out.addContactList = function (account, cb) {
    send(
      {
        type: "add-contact-list",
        param: account,
      },
      cb
    );
  };

  if (!window.WebSocket) {
    let notFn = function () {
      return window.console && console.error("The browser is not supported");
    };
    for (let api in out) {
      let fn = out[api];
      if (typeof fn === "function") {
        out[api] = notFn;
      }
    }
  }

  if (typeof module !== "undefined" && typeof exports === "object") {
    window.eSpaceCtrl = out;
    module.exports = out;
  } else if (typeof define === "function" && (define.amd || define.cmd)) {
    define("eSpaceCtrl", function () {
      return out;
    });
  } else {
    window.eSpaceCtrl = out;
  }
})(window);
