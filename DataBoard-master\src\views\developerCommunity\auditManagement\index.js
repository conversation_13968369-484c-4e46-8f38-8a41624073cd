export const defaultSearchForm = {
  problemId: '',
  processorName: '',
  reviewer: '',
  reviewStatus: '',
  reviewIsRectify: '',
  reviewStatusAgain: '',
};

/**
 * 筛选表单Item
 */
export const searchFormItems = [
  {
    field: 'problemId',
    label: '问答ID',
    component: 'Input',
  },
  {
    field: 'processorName',
    label: '处理人姓名',
    component: 'Input',
  },
  {
    field: 'reviewer',
    label: '交叉审核人',
    component: 'Input',
  },
  {
    field: 'reviewStatus',
    label: '审核状态',
    component: 'Input',
  },
  {
    field: 'reviewIsRectify',
    label: '是否整改',
    component: 'Input',
  },
  {
    field: 'reviewStatusAgain',
    label: '复审状态',
    component: 'Input',
  },
];

/**
 * 编辑表单Item
 */
export const editFormItems = [
  {
    field: 'problemId',
    label: '问答ID',
    component: 'Input',
    disabled: true,
  },
  {
    field: 'reviewer',
    label: '交叉审核人',
    component: 'Input',
  },
  {
    field: 'reviewStatus',
    label: '审核状态',
    component: 'Input',
  },
  {
    field: 'reviewResultCategory',
    label: '审核问题分类',
    component: 'Input',
  },
  {
    field: 'reviewComments',
    label: '审核意见',
    component: 'Input',
  },
  {
    field: 'reviewIsRectify',
    label: '是否整改',
    component: 'Input',
  },
  {
    field: 'reviewStatusAgain',
    label: '复审状态',
    component: 'Input',
  },
  {
    field: 'reviewCommentsAgain',
    label: '复审意见',
    component: 'Input',
  },
  {
    field: 'reviewTime',
    label: '审核时间',
    component: 'DatePicker',
  },
  {
    field: 'rectifyDeadline',
    label: '整改截止日期',
    component: 'DatePicker',
  },
];
