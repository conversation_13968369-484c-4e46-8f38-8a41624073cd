<template>
  <div class="tracking-container">
    <div class="tracking-header">
      <div class="header-left">
        <h2>{{ title }}专项跟踪</h2>
        <span class="subtitle">{{ subtitle }}</span>
      </div>
      <div class="header-right">
        <n-statistic label="总问题数" :value="totalIssues">
          <template #suffix>
            <span class="text-sm text-gray-500">个</span>
          </template>
        </n-statistic>
        <n-statistic :label="'去' + title" @click="goto" style="cursor: pointer">
          <template #suffix>
            <span class="text-sm text-gray-500">Go</span>
          </template>
        </n-statistic>
      </div>
    </div>

    <!-- 添加图表容器 -->
    <div class="charts-container">
      <n-card class="chart-card">
        <div ref="trendChart" class="chart"></div>
      </n-card>
      <n-card class="chart-card" v-if="keyDtsList.length > 0">
        <h2 style="font-size: 16px; font-weight: 800; text-align: center; margin-bottom: 10px"
          >{{ title }}Top问题</h2
        >
        <!-- <div ref="distributionChart" class="chart"></div> -->
        <n-data-table
          remote
          :columns="topColumns"
          :data="keyDtsList"
          :pagination="false"
          :bordered="false"
          :max-height="400"
          size="small"
          :scroll-x="1000"
        />
      </n-card>
    </div>

    <n-card class="mt-4">
      <template #header>
        <div class="table-header">
          <div class="header-title">详细数据</div>
          <div class="header-extra">
            <n-space>
              <n-select
                v-model:value="timeRange"
                :options="timeOptions"
                size="small"
                style="width: 80px"
                placeholder="请选择时间范围"
                @update:value="getData"
              />
              <n-button-group>
                <n-button size="small" type="primary" @click="getData">刷新</n-button>
              </n-button-group>
            </n-space>
          </div>
        </div>
      </template>
      <n-data-table
        :columns="columns"
        :data="dataSource"
        :bordered="false"
        :single-line="false"
        size="small"
        :row-class-name="rowClassName"
      />
    </n-card>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, computed, onMounted, h } from 'vue';
  import {
    NDataTable,
    NCard,
    NStatistic,
    NButton,
    NButtonGroup,
    NSpace,
    NSelect,
    NTag,
  } from 'naive-ui';
  import { trackingColumns } from '../data/tableColumns';
  import type { TrackingItem } from '../types/tracking';
  import { getStaticCommonProblem } from '@/api/tracking/index';
  import * as echarts from 'echarts';
  import { useRouter } from 'vue-router';
  import { getWorkOrderList } from '@/api/dataview/appState';
  export default defineComponent({
    name: 'TrackingBase',
    components: {
      NDataTable,
      NCard,
      NStatistic,
      NButton,
      NButtonGroup,
      NSpace,
      NSelect,
    },
    props: {
      title: {
        type: String,
        required: true,
      },
      subtitle: {
        type: String,
        default: 'Issue Tracking',
      },
      problemMark: {
        type: String,
        required: true,
      },
    },
    setup(props) {
      const router = useRouter();
      const topColumns = [
        {
          title: '问题单号',
          key: 'orderId',
          width: 200,
          render: (row) => {
            return h(
              NTag,
              {
                type: 'info',
                bordered: false,
                onClick: async () => {
                  const { orderId } = row;
                  // 根据orderId跳转到对应的详情页面
                  window.open(
                    `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${orderId}`,
                    '_blank'
                  );
                },
              },
              {
                default: () => row.orderId,
              }
            );
          },
        },
        {
          title: '应用名称',
          key: 'appName',
          width: 120,
          render: (row) => {
            return h(
            NTag,
            {
            bordered: false,
            style: {
              cursor: 'pointer',
            },
          },
          [
            h(
              NTag,
              {
                bordered: false,
                onClick: () => handleAppNameClick(row.appName),
              },
              {
                default: () => row.appName,
              }
            ),
            // TOP标记
            row.top
              ? h(
                  NTag,
                  {
                    type: 'warning',
                    size: 'tiny',
                    bordered: false,
                    style: {
                      position: 'absolute',
                      right: '-8px',
                      top: '-8px',
                      padding: '0 4px',
                      transform: 'scale(0.8)',
                      fontSize: '12px',
                      lineHeight: '16px',
                      minWidth: '16px',
                      textAlign: 'center',
                      zIndex: 1,
                      color: 'red',
                    },
                  },
                  { default: () => row.top.toUpperCase() }
                )
              : null,
          ]);
          },
        },
        {
          title: '问题描述',
          key: 'description',
          ellipsis: {
            tooltip: true,
          },
          show: true,
          render: (row) => {
            const description = true
              ? row.description.split('】').pop() || row.description
              : row.description;
            return h('span', {}, description);
          },
        },
        {
          title: '应用责任人',
          key: 'ewpOwner',
          show: true,
          width: 100,
        },
        {
          title: 'DTS单状态',
          key: 'status',
          width: 120,
          render(row) {
            const statusMap = {
              CCB方案审核: { text: '处理中', type: 'warning' },
              开发人员实施修改: { text: '处理中', type: 'info' },
              已解决: { text: '已解决', type: 'success' },
            };
            const status = statusMap[row.status] || { text: '待处理', type: 'error' };
            return h(
              NTag,
              {
                type: status.type,
                size: 'small',
              },
              { default: () => row.status }
            );
          },
        },
        {
          title: '问题进展',
          key: 'progress',
          // fixed: 'right',
          ellipsis: {
            tooltip: true,
          },
          show: true,
        },
        // {
        //   title: '操作',
        //   key: 'actions',
        //   fixed: 'right',
        //   render: (row) => {
        //     return h('div', { style: { display: 'flex', gap: '8px' } }, [
        //       h(
        //         NButton,
        //         {
        //           size: 'small',
        //           // onClick: () => handleRemark?.(row),
        //         },
        //         { default: () => '问题进展' }
        //       ),
        //     ]);
        //   },
        // },
      ];
      const timeOptions = [
        { label: '日', value: 3 },
        { label: '周', value: 2 },
        { label: '月', value: 1 },
        { label: '年', value: 0 },
      ];
      const columns = ref(trackingColumns);
      const timeRange = ref(1);
      const dataSource = ref<TrackingItem[]>([]);
      const totalIssues = ref(0);
      const trendChart = ref(null);
      const keyDtsList = ref([]);
      const distributionChart = ref(null);

      const goto = () => {
        router.push({ name: 'MyTodo', query: { fromPage: props.title } });
      };
      const getTotal = async () => {
        let data = await getWorkOrderList({
          severity: [],
          status: [],
          commonProblemMark: props.title,
          pageNo: 1,
          pageSize: 999,
          sortField: 'riskScore',
          sortOrder: 'desc',
        });
        totalIssues.value = data.total;
        keyDtsList.value = data.records
          .filter((item) => ['开发人员实施修改', 'CCB方案审核'].includes(item.status))
          .slice(0, 8);
      };
      const rowClassName = (row: TrackingItem) => {
        const closeRate = parseFloat(row.closedNum);
        if (closeRate === 100) return 'high-performance-row';
        if (closeRate >= 80) return 'good-performance-row';
        if (closeRate < 60) return 'low-performance-row';
        return '';
      };

      const initTrendChart = () => {
        if (!trendChart.value) return;
        const chart = echarts.init(trendChart.value);
        const option = {
          title: {
            text: `${props.title}问题趋势`,
            left: 'center',
          },
          tooltip: {
            trigger: 'axis',
          },
          xAxis: {
            type: 'category',
            data: dataSource.value.map((item) => item.time),
          },
          yAxis: {
            type: 'value',
            name: '问题数量',
          },
          series: [
            {
              name: '问题数量',
              type: 'line',
              smooth: true,
              data: dataSource.value.map((item) => item.totalNum),
              itemStyle: {
                color: '#409EFF',
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(64,158,255,0.3)' },
                  { offset: 1, color: 'rgba(64,158,255,0.1)' },
                ]),
              },
            },
          ],
        };

        chart.setOption(option);
        window.addEventListener('resize', () => chart.resize());
      };

      const initDistributionChart = () => {
        if (!distributionChart.value) return;
        const chart = echarts.init(distributionChart.value);

        const option = {
          title: {
            text: `${props.title}问题类型分布`,
            left: 'center',
          },
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)',
          },
          legend: {
            orient: 'vertical',
            left: 'left',
          },
          series: [
            {
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2,
              },
              label: {
                show: false,
                position: 'center',
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '20',
                  fontWeight: 'bold',
                },
              },
              labelLine: {
                show: false,
              },
              data: dataSource.value.map((item) => ({
                name: item.type,
                value: item.total,
              })),
            },
          ],
        };

        chart.setOption(option);
        window.addEventListener('resize', () => chart.resize());
      };

      const getData = async () => {
        dataSource.value = await getStaticCommonProblem({
          commonProblemMark: props.problemMark,
          timeGranularity: timeRange.value,
        });
        initTrendChart();
        initDistributionChart();
      };

      onMounted(() => {
        getData();
        getTotal();
      });

      return {
        columns,
        getData,
        keyDtsList,
        topColumns,
        dataSource,
        totalIssues,
        rowClassName,
        timeRange,
        timeOptions,
        goto,
        trendChart,
        distributionChart,
      };
    },
  });
</script>

<style scoped>
  @import '../styles/common.css';

  .charts-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin: 10px 0;
  }

  .chart-card {
    padding: 10px;
    overflow-x: hidden;
  }

  .chart {
    height: 400px;
    width: 100%;
  }
</style>
