import { reactive } from 'vue';
import service from '@/utils/axios';

export const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  pageCount: 10,
  itemCount: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  prefix({ itemCount }) {
    return `总计：${itemCount}`;
  },
  onChange: (page: number) => {
    paginationReactive.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    paginationReactive.pageSize = pageSize;
    paginationReactive.page = 1;
  },
});

/**
 * 查询
 */
export const orderQuery = (data) => {
  return service({
    url: '/criticalTest/queryAll',
    method: 'post',
    data,
  });
};
/**
 * 导入
 */
export const orderImport = (data) => {
  return service({
    url: `/criticalTest/import`,
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};
