<script setup lang="ts">
import { CodeSampleDrawerMode, TAG_OPTIONS } from "@/views/sampleCodeManage/CustomizedDemo/consts";
import { CodeWishReviewStatusEnum } from "@/enums/CodeWishReviewStatusEnum";
import { CodeWishFormModel } from "@/views/sampleCodeManage/CustomizedDemo/index";
import { computed, ref } from "vue";
import { addDemo, CodeDemoDto, queryDemoList, updateDemo } from "@/api/system/code";
import { FormRules, UploadFileInfo, useDialog, useMessage } from "naive-ui";
import { updateCustomizedDemo } from "@/api/system/customizedDemo";
import { getAddDemoParams } from "./index";
import { useUserStore } from "@/store/modules/user";
import {
  ALL_API_VERSION,
  ALL_DEMO_TYPES,
  getDefaultDemoInfo,
  getFilterOptions, SAMPLE_CODE_WEBSITE_URL,
} from "@/views/sampleCodeManage/consts";
import { CODE_SAMPLE_FORM_RULES } from './consts'

const props = defineProps<{
  wishInfo: CodeWishFormModel,
  isAdmin: boolean,
}>();
const emit = defineEmits<{
  (e: 'close', needRefresh: boolean): void,
}>();
const isShow = defineModel<boolean>('show', { required: true });
const mode = defineModel<CodeSampleDrawerMode>('mode', { required: true });
const codeInfo = ref<CodeDemoDto>(getDefaultDemoInfo());
const fileList = ref<UploadFileInfo[]>([]);
const codeFormRules: FormRules = CODE_SAMPLE_FORM_RULES;
const codeFormRef = ref();
const onlyNumber = (val: string) => /^\d*$/.test(val);
const dialog = useDialog();
const message = useMessage();
const typeOptions = getFilterOptions(ALL_DEMO_TYPES);
const previewImageUrlRef = ref('');
const showPreviewModal = ref(false);
const isLoading = ref(false);
const userInfo = useUserStore().getUserInfo;
// demo基础信息是否仅展示
const isCodeInfoOnlyShow = computed(() => {
  if (mode.value === CodeSampleDrawerMode.SHOW_ONLY) {
    return true;
  } else if (mode.value === CodeSampleDrawerMode.ADD) {
    return false;
  } else {
    return userInfo.label !== props.wishInfo.demoAuthor;
  }
});
// demo信息弹窗展示时的回调，请求管理demo信息
function handleCodeInfoShow() {
  if (props.wishInfo.linkDemoId) {
    isLoading.value = true;
    queryDemoList({
      pageNum: 1,
      pageSize: 1,
      demo: {
        demoId: props.wishInfo.linkDemoId,
      },
    }).then(({data}) => {
      if (!data.data.length) {
        return;
      }
      codeInfo.value = data.data[0];
      fileList.value = [];
      if (codeInfo.value.samples) {
        codeInfo.value.samples.forEach((url, index) => {
          if (!url) return;
          fileList.value.push({
            url: url,
            name: url.includes("7.185.27.69:9090") ? url + ".png" : url,
            id: String(index),
            status: "finished",
          });
        });
      }
    })
      .finally(() => {
        isLoading.value = false;
      })
  }
}
function initModel() {
  codeInfo.value = getDefaultDemoInfo();
  fileList.value = [];
}

// 解除关联
const handleDisassociate = () => {
  dialog.warning({
    title: '提示',
    content: `确定解除关联吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      disAssociatingSampleCode();
      emit('close', true);
    },
    onNegativeClick: () => {
    },
  });
}
// demo关联心愿单
function associatingSampleCode(linkDemoId: string) {
  if (!props.wishInfo.id) {
    message.error('心愿单状态异常！');
    return;
  }
  updateCustomizedDemo({
    id: props.wishInfo.id,
    linkDemoId,
  }).then((res) => {
    if (res) {
      message.success('心愿单关联样例代码成功！');
      emit('close', true);
    } else {
      message.error('心愿单关联样例代码失败！');
    }
  }).catch((e) => {
    message.error(`心愿单关联样例代码失败，原因：${e?.response?.data?.error}`);
  })
}
// demo解除关联心愿单
function disAssociatingSampleCode() {
  if (!props.wishInfo.id) {
    message.error('心愿单状态异常！');
    return;
  }
  updateCustomizedDemo({
    id: props.wishInfo.id,
    linkDemoId: 'null',
  }).then((res) => {
    if (res) {
      message.success('解除关联成功！');
      emit('close', true);
    } else {
      message.error('解除关联失败！');
    }
  }).catch((e) => {
    message.error(`解除关联失败，原因：${e?.response?.data?.error}`);
  })
}
function uploadFinish(options: {
  file: UploadFileInfo,
  event: ProgressEvent,
}) {
  if (options.file.status === "finished") {
    const response = (options.event?.target as XMLHttpRequest).response;
    let url = JSON.parse(response)?.url;
    codeInfo.value.samples.push(url);
    return options.file;
  }
  return undefined;
}
function handleRemove(options: {
  file: UploadFileInfo;
  fileList: UploadFileInfo[];
  index: number;
}) {
  fileList.value = options.fileList.filter(
    (item) => options.file.id !== item.id
  );
  codeInfo.value.samples = options.fileList
    .filter(item => item.url && options.file.id !== item.id)
    .map(item => item.url!);
}
function handlePreview(file: UploadFileInfo) {
  const { url } = file;
  previewImageUrlRef.value = url!;
  showPreviewModal.value = true;
}
// 添加demo
function handleAddCodeSubmit() {
  codeFormRef.value.validate(async (errors) => {
    if (!errors) {
      addDemo(getAddDemoParams(codeInfo.value, props.wishInfo.id)).then((res) => {
        if (res?.data) {
          message.success('添加样例代码成功！');
          if (props.wishInfo.id) {
            associatingSampleCode(res.data);
          }
          emit('close', true);
        } else {
          message.error('添加样例代码失败！');
        }
      }).catch((e) => {
        message.error(`添加样例代码失败，原因：${e?.response?.data?.error}`);
      })
    } else {
      console.log(errors)
    }
  });
}
// 编辑demo
function handleEditCodeSubmit() {
  codeFormRef.value.validate(async (errors) => {
    if (!errors) {
      // todo: 新增samples
      updateDemo(codeInfo.value).then((res) => {
        if (res) {
          message.success('修改样例代码信息成功！');
          emit('close', true);
        } else {
          message.error('修改样例代码信息失败！');
        }
      }).catch((e) => {
        message.error(`修改样例代码信息失败，原因：${e?.response?.data?.error}`);
      })
    } else {
      console.log(errors)
    }
  });
}
</script>

<template>
  <n-drawer
    v-model:show="isShow"
    :width="600"
    :on-after-enter="handleCodeInfoShow"
    :on-after-leave="initModel"
  >
    <n-drawer-content closable>
      <template #header>
        <n-space v-if="mode === CodeSampleDrawerMode.ADD">新增样例代码</n-space>
        <n-space v-else-if="mode === CodeSampleDrawerMode.EDIT">修改样例代码信息</n-space>
        <n-space v-else align="center">样例代码信息</n-space>
      </template>
      <template #default>
        <n-spin size="large" :show="isLoading">
          <n-alert
            v-if="mode !== CodeSampleDrawerMode.SHOW_ONLY"
            title="信息安全合规提示！"
            type="warning"
            :bordered="false"
            style="margin-bottom: 15px"
          >
            <n-ellipsis expand-trigger="click" line-clamp="2" :tooltip="false">
              原则上示例代码需100%外溢供外部开发者参考使用，因此所有入仓demo均需要仔细检查信息安全。<br/>
              涉及外部：<br/>
              1. 非原创demo严禁入库，引入外部开发者demo涉嫌版权信息安全。<br/>
              2.
              使用外部图片，需检查是否涉版权风险，优先使用内部无风险资源图片（图片中不含内部信息）。<br/>
              3. 严禁含有伙伴资源，常见场景：<br/>
              ·
              应用名或项目路径含有伙伴信息（禁止使用伙伴应用名拼音缩写等，应改成功能名称或行业通用名称）；<br/>
              · 引用伙伴文件或图片等资源（ux、图片资源）；<br/>
              ·
              应用内文字提示等含伙伴信息（xxxx公司隐私协议、各种文字ui或提示含伙伴主体名称）。<br/>
              涉及内部：<br/>
              1. 代码中严禁含有签名、copyright、Lisence。<br/>
              2.
              代码中严禁含有内网地址、内部文件、内部涉密图片、图片水印含工号、邮箱、密码等内部信息。<br/>
              3. 项目文件中需移除自动生成的文件和目录，如：<br/>
              · .hvigor<br/>
              · .idea<br/>
              · .VSCodeCounter<br/>
              · build<br/>
              · oh_modules<br/>
              · local.properties<br/>
              · package-lock.json<br/>
              最后提醒：伙伴资产不允许进公司网络和代码仓，信息安全和商业合规要注意。<br/>
            </n-ellipsis>
          </n-alert>
          <n-form
            ref="codeFormRef"
            :model="codeInfo"
            :rules="codeFormRules"
            label-placement="left"
            label-width="auto"
            require-mark-placement="right-hanging"
            size="medium"
            :style="{
              maxWidth: '640px',
              marginBottom: '20px'
            }"
          >
            <n-form-item label="心愿单">
              <div v-if="props.wishInfo.id">{{ props.wishInfo.demoName }}/{{ props.wishInfo.id }}</div>
              <div v-else>新增后自动生成心愿单并关联</div>
            </n-form-item>
            <n-form-item label="名称" path="demoName">
              <div v-if="isCodeInfoOnlyShow">
                {{ codeInfo.demoName }}
              </div>
              <n-input
                clearable
                v-else
                placeholder="请输入Demo名称"
                v-model:value="codeInfo.demoName"
              />
            </n-form-item>
            <n-form-item label="描述" path="demoDescription">
              <div v-if="isCodeInfoOnlyShow">
                {{ codeInfo.demoDescription }}
              </div>
              <n-input
                v-else
                clearable
                type="textarea"
                v-model:value="codeInfo.demoDescription"
                :autosize="{
                  minRows: 3,
                  maxRows: 5,
                }"
              />
            </n-form-item>
            <n-form-item label="分类" path="selectValue">
              <div v-if="isCodeInfoOnlyShow">
                {{ codeInfo.selectValue }}
              </div>
              <n-select
                v-else
                v-model:value="codeInfo.selectValue"
                placeholder="请选择分类"
                :options="typeOptions"
              />
            </n-form-item>
            <n-form-item label="标签" path="tags">
              <div v-if="isCodeInfoOnlyShow">
                {{ codeInfo.tags.join('/') || '--' }}
              </div>
              <n-select
                v-else
                clearable
                multiple
                tag
                filterable
                v-model:value="codeInfo.tags"
                :max-tag-count="3"
                placeholder="请选择标签或输入自定义标签"
                :options="TAG_OPTIONS"
              />
            </n-form-item>
            <n-form-item label="API版本" path="apiVersion">
              <div v-if="isCodeInfoOnlyShow">
                {{ codeInfo.apiVersion }}
              </div>
              <n-select
                v-else
                filterable
                v-model:value="codeInfo.apiVersion"
                :options="ALL_API_VERSION.map((v)=> ({ label: v, value: v }) )"
              />
            </n-form-item>
            <n-form-item label="开发语言" path="type">
              <div v-if="isCodeInfoOnlyShow">
                {{ codeInfo.type }}
              </div>
              <n-radio-group
                v-else
                v-model:value="codeInfo.type"
                name="language"
              >
                <n-space>
                  <n-radio value="ArkTS"> ArkTS </n-radio>
                  <n-radio value="C++"> C++ </n-radio>
                </n-space>
              </n-radio-group>
            </n-form-item>
            <n-form-item label="内部链接" path="demoLink">
              <div v-if="isCodeInfoOnlyShow">
                <a :href="codeInfo.demoLink" target="_blank">{{ codeInfo.demoLink }}</a>
              </div>
              <n-input
                v-else
                clearable
                v-model:value="codeInfo.demoLink"
                placeholder="请输入样例代码链接"
              />
            </n-form-item>
            <n-form-item label="外溢链接" path="externalLink">
              <div v-if="isCodeInfoOnlyShow">
                <a
                  v-if="codeInfo.externalLink"
                  :href="codeInfo.externalLink"
                  target="_blank"
                >
                  {{ codeInfo.externalLink }}
                </a>
                <span v-else>--</span>
              </div>
              <n-input
                v-else
                clearable
                placeholder="请输入外溢链接"
                v-model:value="codeInfo.externalLink"
              />
            </n-form-item>
            <n-form-item label="行数" path="lineNumber">
              <div v-if="isCodeInfoOnlyShow">
                {{ codeInfo.lineNumber }}
              </div>
              <n-input v-else :allow-input="onlyNumber" v-model:value="codeInfo.lineNumber"
                       placeholder="请输入样例代码行数"/>
            </n-form-item>
<!--            <n-form-item label="示例链接" path="samples">-->
<!--              <div v-if="isCodeInfoOnlyShow && codeInfo.samples?.length">-->
<!--                <div v-for="sample in codeInfo.samples">{{ sample }}</div>-->
<!--              </div>-->
<!--              <div v-else-if="isCodeInfoOnlyShow && !codeInfo.samples?.length">-->
<!--                <div>&#45;&#45;</div>-->
<!--              </div>-->
<!--              <n-dynamic-input-->
<!--                v-else-->
<!--                show-sort-button-->
<!--                v-model:value="codeInfo.samples"-->
<!--                placeholder="请输入示例链接"-->
<!--                :min="1"-->
<!--                :max="6"-->
<!--              />-->
<!--            </n-form-item>-->
            <n-form-item label="示例">
              <n-upload
                action="https://dtse.cbg.huawei.com:8777/upload"
                list-type="image-card"
                @finish="uploadFinish"
                :default-file-list="fileList"
                @preview="handlePreview"
                @remove="handleRemove"
                :disabled="isCodeInfoOnlyShow"
              >
              </n-upload>
              <n-modal
                v-model:show="showPreviewModal"
                preset="card"
                style="width: 400px"
                title="示例"
              >
                <img :src="previewImageUrlRef" style="width: 100%" />
              </n-modal>
            </n-form-item>
            <n-form-item v-if="isCodeInfoOnlyShow" label="提交人" path="demoSubmitter">
              <div>{{ codeInfo.demoSubmitter || '--' }}</div>
            </n-form-item>
          </n-form>
        </n-spin>
      </template>
      <template #footer>
        <n-space v-if="mode === CodeSampleDrawerMode.ADD" justify="end">
          <n-button secondary strong type="default" @click="isShow = false">
            取消
          </n-button>
          <n-button secondary strong type="success" @click="handleAddCodeSubmit">
            确认
          </n-button>
        </n-space>
        <n-space v-if="mode === CodeSampleDrawerMode.EDIT" justify="end">
          <n-button secondary strong type="default" @click="mode = CodeSampleDrawerMode.SHOW_ONLY">
            取消
          </n-button>
          <n-button secondary strong type="success" @click="handleEditCodeSubmit">
            确认
          </n-button>
        </n-space>
        <n-space
          v-if="mode === CodeSampleDrawerMode.SHOW_ONLY"
          justify="end"
        >
          <n-button
            v-if="wishInfo.reviewStatus === CodeWishReviewStatusEnum.PASSED"
            secondary
            strong
            tag="a"
            target="_blank"
            :href="SAMPLE_CODE_WEBSITE_URL"
            type="success"
          >
            跳转示例代码网站查看
          </n-button>
          <n-button secondary strong type="default" @click="isShow = false">
            返回
          </n-button>
          <n-button
            secondary
            strong
            type="error"
            @click="mode = CodeSampleDrawerMode.EDIT"
            :disabled="userInfo.label !== wishInfo.demoAuthor"
          >
            编辑
          </n-button>
          <n-button
            v-if="wishInfo.reviewStatus === CodeWishReviewStatusEnum.UNDER_DEVELOPMENT"
            secondary
            strong
            type="warning"
            @click="handleDisassociate"
          >
            解除关联
          </n-button>
        </n-space>
      </template>
    </n-drawer-content>
  </n-drawer>
</template>

<style scoped lang="less">

</style>
