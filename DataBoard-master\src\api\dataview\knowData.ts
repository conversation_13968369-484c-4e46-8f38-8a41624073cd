import { ewpService as service } from '@/utils/axios';

export async function getLatestIndicator(): Promise<any> {
  try {
    const response = await service.get('/management/faultTree/latestIndicator');

    return response;
  } catch (error) {
    throw error;
  }
}

export async function getUsageRateDetail(): Promise<any> {
  try {
    const response = await service.get('/management/faultTree/usageRateDetail');

    return response;
  } catch (error) {
    throw error;
  }
}
export async function getIndicatorTrend(): Promise<any> {
  try {
    const response = await service.get('/management/faultTree/indicatorTrend');

    return response;
  } catch (error) {
    throw error;
  }
}
