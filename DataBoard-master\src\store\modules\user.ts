import { defineStore } from 'pinia';
import { store } from '@/store';
import { CURRENT_USER, IS_SCREENLOCKED, REDIRECT_PATH } from '@/store/mutation-types';
import { TABS_ROUTES } from '@/store/mutation-types';
import { storage } from '@/utils/Storage';
import { getW3EmployeeInfo, login, LoginReq } from '@/api/system/user';
import { queryUser, UserDto } from '@/api/system/usermanage';
import { ROLE_LEVELS, UserRoleEnum } from "@/enums/UserRoleEnum";
import { isDevMode } from "@/utils/env";
import router from "@/router";
import { PageEnum } from "@/enums/pageEnum";
import { ResultEnum } from "@/enums/httpEnum";

const DEFAULT_USER_INFO: UserVo = {
  userName: '',
  account: '',
  label: '',
  roles: [],
  isSuperAdmin: false,
  modules: [],
  personalSkills: '',
  password: '',
  residence: '',
  leader: '',
  team: '',
};
export interface IUserState {
  account: string;
  permissions: any[];
  info: UserVo;
  roles: string[]; // 新增 roles
}
export interface UserVo {
  userName: string;
  account: string;
  label: string;
  roles: string[];
  isSuperAdmin: boolean;
  modules: string[];
  personalSkills: string;
  password: string;
  residence: string;
  leader: string;
  email: string;
  team: string;
}
function getUserVo(info: UserDto): UserVo {
  return {
    email: info.email || '',
    userName: info.userName,
    account: info.account,
    roles: info.roles,
    password: info.password,
    personalSkills: info.personalSkills,
    modules: info.modules ?? [],
    label: getUserLabel(info),
    isSuperAdmin: info.roles.includes(UserRoleEnum.SUPER_ADMIN),
    leader: info.leader,
    residence: info.residence,
    team: info.team || '',
  };
}
export function getUserLabel(info: UserDto): string {
  if (info?.userName && info?.account) {
    return `${info.userName}\/${info.account}`;
  }
  return '';
}
export const useUserStore = defineStore({
  id: 'app-user',
  state: (): IUserState => ({
    permissions: [],
    info: storage.get(CURRENT_USER) ?? DEFAULT_USER_INFO,
    roles: [], // 初始化 roles
    account: '',
  }),
  getters: {
    getPermissions(): [any][] {
      return this.permissions;
    },
    getRoles(): string[] {
      return this.roles;
    },
    getUserInfo(): UserVo {
      return this.info;
    },
    getAccount(): string {
      return this.account;
    },
  },
  actions: {
    setPermissions(permissions) {
      this.permissions = permissions;
    },
    setUserInfo(info: UserVo) {
      this.info = info;
    },
    setRoles(roles) {
      this.roles = roles;
    },
    setAccount(account: string) {
      this.account = account;
    },
    async login(params: LoginReq): Promise<{
      status: string;
      message: string;
      data: UserDto;
    }> {
      const response = await login(params);

      if (Number(response?.status) === ResultEnum.SUCCESS && response?.data) {
        const ex = 7 * 24 * 60 * 60;
        const userVo = getUserVo(response.data);
        storage.set(CURRENT_USER, userVo, ex);
        storage.set(IS_SCREENLOCKED, false);

        this.setUserInfo(userVo);
        this.setAccount(userVo.account);
      }
      return response;
    },

    w3Login(): void {
      location.href = `https://dtse.cbg.huawei.com/board/accessCode?redirect=${window.location.href}`;
    },

    // 获取用户信息
    async getInfo(): Promise<UserVo | null> {
      let currentUser = storage.get(CURRENT_USER);
      if (!currentUser) {
        const rsp = await queryUser({
          pageNum: 1,
          pageSize: 1,
          user: {
            account: this.getAccount,
          }
        }).catch(() => null)
        if (rsp?.data?.data?.length) {
          currentUser = getUserVo(rsp.data.data[0]);
          storage.set(CURRENT_USER, currentUser);
          storage.set(IS_SCREENLOCKED, false);
          this.setPermissions(currentUser.roles);
          this.setUserInfo(currentUser);
          return currentUser;
        } else {
          return null;
        }
      }
      return currentUser;
    },
    // 检查是否已登录
    async checkIsLoggedIn(): Promise<boolean> {
      // 开发环境仅判断本地是否有用户信息
      if (isDevMode()) {
        return !!storage.get(CURRENT_USER);
      }
      const res = await getW3EmployeeInfo().catch(e => {
        return null;
      });
      if (!res?.data?.uid) {
        return false;
      }
      this.setAccount(res.data.uid);
      return true;
    },

    // 登出
    logout(): void {
      this.setPermissions([]);
      this.setUserInfo(DEFAULT_USER_INFO);
      this.setAccount('');
      storage.remove(CURRENT_USER);
      // 移除标签页
      storage.remove(TABS_ROUTES);
      // redirectPath 存缓存
      // let startURL = 'https://dtse.cbg.huawei.com';
      // if (isDevMode()) {
      //   startURL = 'http://localhost:40000'; // 本地测试
      // }
      // const redirectPath = window.location.href.replace(startURL, '');
      const redirectPath = window.location.href;
      storage.setCookie(REDIRECT_PATH, encodeURIComponent(redirectPath));
      if (isDevMode()) {
        router.push(PageEnum.BASE_LOGIN);
      } else {
        location.href = 'https://dtse.cbg.huawei.com/board/logout';
      }
    },
  },
});

// Need to be used outside the setup
export function useUser() {
  return useUserStore(store);
}
