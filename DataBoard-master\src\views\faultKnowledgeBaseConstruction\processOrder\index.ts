export const faultTypeList = [
  {
    label: '非故障',
    value: 0,
  },
  {
    label: '已匹配',
    value: 1,
  },
  {
    label: '故障类型不存在',
    value: 2,
  },
  {
    label: '缺少故障定界步骤',
    value: 3,
  },
  {
    label: '解决方案不适用或缺少解决方案',
    value: 4,
  },
  {
    label: '重复单或工单无解决方案关单',
    value: 5,
  },
  {
    label: '升级版本解决',
    value: 6,
  },
];

export const reviewResultList = [
  {
    label: '通过',
    value: 0,
  },
  {
    label: '不通过',
    value: 1,
  },
];
export const wikiStatusList = [
  {
    label: '已新增',
    value: 0,
  },
  {
    label: '已修改',
    value: 1,
  },
  {
    label: '无法输出为Wiki',
    value: 2,
  },
  {
    label: '问题意见分类错误',
    value: 3,
  },
];
export const iRStatusList = [
  {
    label: '待遍历',
    value: 0,
  },
  {
    label: '待输出',
    value: 1,
  },
  {
    label: '待评审',
    value: 2,
  },
  {
    label: '待修改',
    value: 3,
  },
  {
    label: '已中止',
    value: 4,
  },
  {
    label: '已完成',
    value: 5,
  },
];
export const irStatusMapRole = {
  '2_divide_user': [0],
  '2_output_user': [1, 3],
  '2_review_user': [2],
  '2_admin': [],
};

export const primaryModuleList = [
  { label: '应用框架', value: '应用框架' },
  { label: '系统', value: '系统' },
  { label: '应用服务', value: '应用服务' },
  { label: '媒体', value: '媒体' },
  { label: '图形', value: '图形' },
  { label: 'AI', value: 'AI' },
  { label: '三方框架', value: '三方框架' },
  { label: '工具', value: '工具' },
  { label: '测试', value: '测试' },
  { label: 'DFX', value: 'DFX' },
  { label: 'NDK开发', value: 'NDK开发' },
  { label: '其他', value: '其他' },
  { label: 'ArkUI', value: 'ArkUI' },
];
export const problemCategoryList = [
  { label: '方舟编程语言', value: '方舟编程语言' },
  { label: '方舟Web', value: '方舟Web' },
  { label: '卡片开发服务', value: '卡片开发服务' },
  { label: 'UI设计套件', value: 'UI设计套件' },
  { label: '程序框架', value: '程序框架' },
  { label: '数据管理', value: '数据管理' },
  { label: '后台任务', value: '后台任务' },
  { label: '文件', value: '文件' },
  { label: '输入法', value: '输入法' },
  { label: '进程间通信', value: '进程间通信' },
  { label: '无障碍', value: '无障碍' },
  { label: '本地化', value: '本地化' },
  { label: '自由流转', value: '自由流转' },
  { label: '一次开发，多端部署', value: '一次开发，多端部署' },
  { label: '安全', value: '安全' },
  { label: '网络', value: '网络' },
  { label: '基础功能', value: '基础功能' },
  { label: '硬件', value: '硬件' },
  { label: '调测调优', value: '调测调优' },
  { label: '华为账号', value: '华为账号' },
  { label: '应用市场', value: '应用市场' },
  { label: '位置/地图', value: '位置/地图' },
  { label: '钱包/支付', value: '钱包/支付' },
  { label: '分享/推送/通知', value: '分享/推送/通知' },
  { label: '联系人/通话', value: '联系人/通话' },
  { label: '日历/天气', value: '日历/天气' },
  { label: '广告', value: '广告' },
  { label: '游戏', value: '游戏' },
  { label: '运动健康', value: '运动健康' },
  { label: '实况窗', value: '实况窗' },
  { label: '融合场景', value: '融合场景' },
  { label: '云开发', value: '云开发' },
  { label: '音视频', value: '音视频' },
  { label: '图片', value: '图片' },
  { label: '相机', value: '相机' },
  { label: '铃声', value: '铃声' },
  { label: '扫码', value: '扫码' },
  { label: '2D/3D', value: '2D/3D' },
  { label: '图形加速', value: '图形加速' },
  { label: 'AR', value: 'AR' },
  { label: '语音', value: '语音' },
  { label: '视觉', value: '视觉' },
  { label: '模型推理', value: '模型推理' },
  { label: '意图框架', value: '意图框架' },
  { label: 'RN框架', value: 'RN框架' },
  { label: 'Flutter框架', value: 'Flutter框架' },
  { label: 'UniApp框架', value: 'UniApp框架' },
  { label: '其他框架', value: '其他框架' },
  { label: 'DevEco Studio', value: 'DevEco Studio' },
  { label: 'Command Line Tools', value: 'Command Line Tools' },
  { label: 'DevEco Service', value: 'DevEco Service' },
  { label: '单元测试和UI测试', value: '单元测试和UI测试' },
  { label: '流程规范', value: '流程规范' },
  { label: '专项测试', value: '专项测试' },
  { label: '体验测试', value: '体验测试' },
  { label: '测试工具', value: '测试工具' },
  { label: '自动化', value: '自动化' },
  { label: '行业积累', value: '行业积累' },
  { label: '性能', value: '性能' },
  { label: '功耗', value: '功耗' },
  { label: '稳定性', value: '稳定性' },
  { label: 'CAPI', value: 'CAPI' },
  { label: 'NDK基础', value: 'NDK基础' },
  { label: '上架分发', value: '上架分发' },
  { label: '元服务', value: '元服务' },
  { label: '三方库', value: '三方库' },
  { label: '其他', value: '其他' },
  { label: '方舟UI框架', value: '方舟UI框架' },
];
export const initModel = {
  irNumber: 'IR号',
  title: '标题',
  problemCategory: 0,
  currentHandler: '标题',
  faultType: 0,
  wikiAddress: '标题',
  reviewSuggestions: '标题',
  wikiStatus: 0,
  reviewResult: 0,
  irStatus: 2,
  primaryModule: '',
  rejectionReason: '',
  wikiOutputResponsiblePerson: '',
};
export const initFilterModle = {
  irNumber: '',
  currentHandlerList: [],
  faultTypeList: [],
  wikiStatusList: [],
  reviewResultList: [],
  irStatusList: [],
  primaryModuleList: [],
  problemCategoryList: [],
  traversalTime: null,
  outputTime: null,
  reviewTime: null,
  createTime: null,
  syncedAbilityCenterList: null,
  wikiOutputResponsiblePerson: '',
  traversingResponsiblePerson: '',
  reviewerResponsiblePerson: '',
};
export const syncedAbilityCenterList = [
  {
    label: '是',
    value: true,
  },
  {
    label: '否',
    value: false,
  },
];

export function checkArrayValuesEquality(arr, key) {
  if (arr.length === 0) {
    return true; // 如果数组为空，则返回true
  }

  const firstValue = arr[0][key]; // 获取第一个对象的对应值

  for (let i = 1; i < arr.length; i++) {
    if (arr[i][key] !== firstValue) {
      return false; // 如果有任何一个对象的对应值与第一个对象的对应值不相等，则返回false
    }
  }

  return true; // 如果所有对象的对应值都相等，则返回true
}
export function formatDateTime(date, format) {
  date = new Date(date);
  const o = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12, // 小时
    'H+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
    a: date.getHours() < 12 ? '上午' : '下午', // 上午/下午
    A: date.getHours() < 12 ? 'AM' : 'PM', // AM/PM
  };
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      );
    }
  }
  return format;
}
// export function is
