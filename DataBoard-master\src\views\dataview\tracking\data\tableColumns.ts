import { BasicColumn } from '@/components/Table';

export const trackingColumns: BasicColumn[] = [
  {
    title: '日期',
    key: 'time',
    width: 100,
    align: 'center',
    fixed: 'left',
  },
  {
    title: '问题提交人填写',
    key: 'newTicketNum',
    width: 130,
    align: 'center',
  },
  {
    title: '测试（项目）经理审核',
    key: 'ticketReviewNum',
    width: 160,
    align: 'center',
  },
  {
    title: '开发人员实施修改',
    key: 'solutionImplementationNum',
    width: 140,
    align: 'center',
  },
  {
    title: 'CCB方案审核',
    key: 'solutionReviewNum',
    width: 130,
    align: 'center',
  },
  {
    title: '审核人员审核修改',
    key: 'implementationReviewNum',
    width: 140,
    align: 'center',
  },
  {
    title: 'CMO归档',
    key: 'archiveByDeveloperNum',
    width: 110,
    align: 'center',
  },
  {
    title: '测试经理组织测试',
    key: 'regressionTestAssignmentNum',
    width: 130,
    align: 'center',
  },
  {
    title: '测试人员回归测试',
    key: 'regressionTestNum',
    width: 130,
    align: 'center',
  },
  {
    title: '关闭',
    key: 'closedNum',
    width: 80,
    align: 'center',
  },
  {
    title: '挂起',
    key: 'suspendNum',
    width: 80,
    align: 'center',
  },
  {
    title: '撤销',
    key: 'cancelNum',
    width: 80,
    align: 'center',
  },

  {
    title: '待定界',
    key: 'waitDealNum',
    width: 80,
    align: 'center',
  },
  {
    title: '待锁定',
    key: 'waitLockNum',
    width: 80,
    align: 'center',
  },
  {
    title: '待回归',
    key: 'waitRecheckNum',
    width: 80,
    align: 'center',
  },
  {
    title: '待修复',
    key: 'waitRepairNum',
    width: 80,
    align: 'center',
  },
  {
    title: '合计',
    key: 'totalNum',
    width: 80,
    align: 'center',
    fixed: 'right',
  },
];
