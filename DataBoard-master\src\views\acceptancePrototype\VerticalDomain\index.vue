<template>
  <div>
    <!-- 搜索区域 -->
    <n-card style="margin-bottom: 12px">
      <n-form label-placement="left" label-width="100px" label-align="left">
        <n-grid v-if="collapse" x-gap="20" :cols="4" style="margin-bottom: 12px">
          <n-gi>
            <n-form-item label="底表名称">
              <n-input v-model:value="filters.industryName" />
            </n-form-item>
          </n-gi>
        </n-grid>
      </n-form>
      <n-space justify="center">
        <n-button secondary strong type="default" @click="handleResetFilter">重置 </n-button>
        <n-button
          secondary
          strong
          type="primary"
          @click="
            message.loading('获取应用列表中', {
              duration: 200000,
            });
            fetchData();
          "
          >查询
        </n-button>
        <n-button type="primary" icon-placement="right" @click="unfoldToggle">
          <template #icon>
            <n-icon size="14" class="unfold-icon" v-if="collapse">
              <UpOutlined />
            </n-icon>
            <n-icon size="14" class="unfold-icon" v-else>
              <DownOutlined />
            </n-icon>
          </template>
          {{ collapse ? '收起' : '展开' }}
        </n-button>
      </n-space>
    </n-card>
    <!-- 按钮区域 -->
    <div class="layout-page-header">
      <n-button
        secondary
        strong
        type="primary"
        :disabled="!isTestAdmin"
        @click="showListAddModal = true"
      >
        新增
      </n-button>
      <n-button
        type="warning"
        style="margin-left: 12px"
        secondary
        strong
        :disabled="!selectRows.length"
        @click="onExportClick"
        :loading="exportLoading"
      >
        批量导出
      </n-button>
      <n-button
        style="margin-left: 12px"
        :disabled="!isTestAdmin || !selectRows.length"
        secondary
        strong
        type="error"
        @click="remove()"
        >批量删除
      </n-button>
    </div>
    <n-card class="table-card" style="margin-top: 20px">
      <n-data-table
        :columns="columns"
        :row-key="(row: RowData) => row.industryName"
        @update:checked-row-keys="handleCheck"
        :data="tableData"
        :pagination="paginationReactive"
        max-height="700px"
        virtual-scroll
        row-class-name="custom-table"
        remote
      />
    </n-card>
    <n-modal v-model:show="showListAddModal" :on-after-leave="modelClose">
      <n-card
        style="width: 600px"
        title="新增"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form
          ref="formAddFile"
          :model="addFileModel"
          :rules="addFileRules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="medium"
          label-align="left"
        >
          <n-form-item label="底表名称" path="industryName">
            <n-input v-model:value="addFileModel.industryName" />
          </n-form-item>
          <n-form-item label="选择文件" path="file">
            <n-upload
              action="#"
              :multiple="true"
              :custom-request="addFileCustomRequest"
              accept=".xls,.xlsx,"
              :max="1"
              directory-dnd
              :on-remove="fileRemove"
            >
              <n-upload-dragger>
                <div>
                  <n-icon size="48" :depth="3">
                    <Add />
                  </n-icon>
                </div>
                <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
              </n-upload-dragger>
            </n-upload>
          </n-form-item> 
        </n-form>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" @click="submitFile()">确认</n-button>
            <n-button secondary strong type="error" @click="modelClose()">取消</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
    <n-modal v-model:show="showListEditModal">
      <n-card
        style="width: 600px"
        title="更新"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <div style="margin-bottom: 20px">底表名称：{{ editIndustryName }}</div>
        <n-upload
          action="#"
          :multiple="true"
          :custom-request="customRequest"
          accept=".xls,.xlsx,"
          :max="1"
          directory-dnd
        >
          <n-upload-dragger>
            <div>
              <n-icon size="48" :depth="3">
                <Add />
              </n-icon>
            </div>
            <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
          </n-upload-dragger>
        </n-upload>
      </n-card>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, h, reactive, onMounted } from 'vue';
import { NButton, useDialog, useMessage, UploadCustomRequestOptions } from 'naive-ui';
import { queryImported, importIndustry, delIndustryList, exportData } from './index';
import { useUserStore } from '@/store/modules/user';
import { UserRoleEnum } from '@/enums/UserRoleEnum';
import { useRouter } from 'vue-router';
import { log } from 'console';
  import { DownOutlined, UpOutlined } from '@vicons/antd';
const tableData = ref([]);
const dialog = useDialog();
const userStore = useUserStore();
const roles = userStore.getUserInfo.roles;
const router = useRouter();
const loadingRef = ref(true);
const isTestAdmin = ref(false);
isTestAdmin.value = roles.includes(UserRoleEnum.TEST_ADMIN);
// isTestAdmin.value = true;

const exportLoading = ref(false);
const message = useMessage();
const selectRows = ref([]);
  const addFileRules = ref({
    industryName: {
      required: true,
      validator: (rule, value) => {
        if (!value) {
          return new Error('请输入底表名称');
        }
        return true;
      },
    },
    file: {
      required: true,
      validator: (rule, value) => {
        if (!addFileModel.value.file) {
          return new Error('请上传文件');
        }
        return true;
      },
    },
  });
  const formAddFile = ref();
  let addFileModel = ref({
    industryName: '',
    file: null,
  });
const columns = [
  {
    type: 'selection',
    fixed: 'left',
  },
  {
    title: '垂域共性功能底表',
    key: 'industryName',
  },
  {
    title: '操作',
    key: 'action',
    width: 250,
    fixed: 'right',
    render(row) {
      return [
        h(
          NButton,
          {
            size: 'small',
            style: {
              marginRight: '6px',
            },
            secondary: true,
            type: 'warning', // 添加主要颜色样式
            onClick: () => {
              router.push({
                name: 'IndustryDetail',
                query: {
                  industryName: row.industryName,
                },
              });
            },
          },
          { default: () => '查看详情' }
        ),
        h(
          NButton,
          {
            strong: true,
            tertiary: true,
            secondary: true,
            style: 'margin-right:12px',
            type: 'error',
            size: 'small',
            disabled: !isTestAdmin.value,
            onClick: () => {
              showListEditModal.value = true;
              editIndustryName.value = row.industryName;
            },
          },
          [h('div', '更新')]
        ),
        h(
          NButton,
          {
            strong: true,
            tertiary: true,
            secondary: true,
            style: 'margin-right:12px',
            type: 'error',
            size: 'small',
            disabled: !isTestAdmin.value,
            onClick: () => {
              dialog.warning({
                title: '警告',
                content: '是否删除该数据',
                positiveText: '确定',
                negativeText: '取消',
                onPositiveClick: async () => {
                  try {
                    await delIndustryList([row.industryName]);
                    let index = selectRows.value.indexOf(row.industryName);
                    if (index > -1) {
                      selectRows.value.splice(index, 1);
                    }
                    message.success('删除成功');
                    await searchData();
                  } catch {
                    message.error('删除失败');
                  }
                },
              });
            },
          },
          [h('div', '删除')]
        ),
      ];
    },
  },
];
const onExportClick = async () => {
  exportLoading.value = true;
  for (let i = 0, length = selectRows.value.length; i < length; i++) {
    let industryName = selectRows.value[i];
    try {
      await exportData({ industryName });
    } catch (e) {
      console.log(e);
      message.warning('导出失败！');
    } finally {
      exportLoading.value = false;
    }
  }
};
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [20, 50, 100],
  prefix({ itemCount }) {
    return `Total：${itemCount}`;
  },
  onChange: (page: number) => {
    paginationReactive.page = page;
    fetchData();
  },
  onUpdatePageSize: (pageSize: number) => {
    paginationReactive.pageSize = pageSize;
    paginationReactive.page = 1;
    fetchData();
  },
});
const searchData = () => {
  selectRows.value = [];
  paginationReactive.page = 1;
  fetchData();
};
const fetchData = async () => {
  loadingRef.value = true;
  let params = {
    pageNum: paginationReactive.page,
    pageSize: paginationReactive.pageSize,
    industryName: filters.value.industryName
  };
  let res = await queryImported(params);
  if (res.status == '200') {
    // message.destroyAll();
    paginationReactive.itemCount = res.data.pageInfo.total;
    // checkedRowKeys.value = [];
    tableData.value = (res.data?.data || []).map((item) => {
      return {
        industryName: item,
      };
    });
  }
  message.destroyAll();
};
// 在组件挂载时获取数据
onMounted(() => {
  searchData();
});
  const filters = ref({ industryName: '' });
  const collapse = ref(true);
const showListAddModal = ref(false);
  const showListEditModal = ref(false);
  const editIndustryName = ref('');
  //更新
  const customRequest = async ({ file, onError }: UploadCustomRequestOptions) => {
    const formData = new FormData();
    formData.append('file', file.file as File);
    formData.append('industryName', editIndustryName.value);
    try {
      let res = await importIndustry(formData);
      if (res.status == '200') {
        showListEditModal.value = false;
        searchData();
        message.success('更新成功！');
      } else {
        message.error('更新失败！');
      }
    } catch (err) {
      onError();
    }
  };
  const unfoldToggle = () => {
    collapse.value = !collapse.value;
  };
  const handleResetFilter = () => {
    filters.value = { industryName: '' };
    fetchData();
  };
const handleCheck = (rowKeys) => {
  selectRows.value = rowKeys;
};
const remove = () => {
  dialog.warning({
    title: '提示',
    content: '数据删除后不可恢复，确认执行操作？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        let res = await delIndustryList(selectRows.value);
        if (res?.status) {
          message.success('删除成功');
        }
        selectRows.value = [];
      } catch (err) {}
      searchData();
    },
    onNegativeClick: () => {},
  });
};
  const modelClose = () => {
    showListAddModal.value = false;
    addFileModel = ref({});
  };
  const submitFile = () => {
    formAddFile.value.validate(async (errors) => {
      if (!errors) {
        const formData = new FormData();
        formData.append('file', addFileModel.value.file);
        formData.append('industryName', addFileModel.value.industryName);
        //上传接口
        try {
          let res = await importIndustry(formData);
          if (res.status == '200') {
            showListAddModal.value = false;
            searchData();
            addFileModel = ref({});
            message.success('新增成功');
          } else {
            message.error('新增失败！');
          }
        } catch (err) {
          addFileModel = ref({});
        }
      }
    });
  };
  const addFileCustomRequest = ({ file, onError }: UploadCustomRequestOptions) => {
    if (file) {
      addFileModel.value.file = file.file as File;
    } else {
      addFileModel.value.file = null;
    }
  };
  const fileRemove = () => {
    addFileModel.value.file = null;
  };
</script>

<style lang="less" scoped>
.layout-page-header {
  margin-top: 20px;
}
</style>