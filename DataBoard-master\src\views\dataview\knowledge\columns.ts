import { h, ref } from 'vue';
import { NButton, NTag, NSelect, NTooltip } from 'naive-ui';
import { BasicColumn } from '@/components/Table';
import {
  DTS_HANDLE_TAG,
  FUT_HANDLE_TAG,
  SERVICE_KNOW_TAG
} from "@/views/dataview/personManage/tagContant";
import {
  getPersonOptionsOnlyName
} from "@/views/dataview/personManage/staffCommonUtils";
import {getStaffList} from "@/views/dataview/personManage/staff";
import {getMaxIntNumber} from "@/utils/commonUtils";
interface SelectionColumn {
  type: 'selection';
  disabled?: (row: any) => boolean;
  multiple?: boolean;
}

// 修改 allColumnList 的定义，添加一个映射对象来关联标题和key
export const columnKeyMap = {
  问题单号: 'orderId',
  应用名称: 'appName',
  问题单描述: 'description',
  应用责任人: 'ewpOwner',
  风险指数: 'riskScore',
  声量值: 'opinionNum',
  FUT声量: 'opinionFut',
  BetaClub声量: 'opinionBeta',
  NSS声量: 'opinionNss',
  严重程度: 'severity',
  DTS单状态: 'status',
  当前处理人: 'currentHandler',
  关联IR单号: 'irOrderId',
  IR单状态: 'irOrderStatus',
  IR单解决状态: 'irStatus',
  模块: 'module',
  问题归属: 'dtsAttribution',
  计划解决日期: 'solvePlanDate',
  问题进展: 'progress',
  操作: 'actions',
};

export const allColumnList = Object.keys(columnKeyMap);

// 在文件顶部添加
interface ExtendedColumn extends BasicColumn<any> {
  show?: boolean;
  defaultShow?: boolean;
}

interface ExtendedSelectionColumn extends SelectionColumn {
  show?: boolean;
  defaultShow?: boolean;
}

export const dtsHandleEmployee = await getPersonOptionsOnlyName(DTS_HANDLE_TAG);

export const serviceKnowEmployee = await getPersonOptionsOnlyName(SERVICE_KNOW_TAG);

export const statusList = [
  {
    label: '定界中',
    value: '0',
  },
  {
    label: '待锁定',
    value: '1',
  },
  {
    label: '已锁定',
    value: '2',
  },
  {
    label: '已闭环',
    value: '3',
  },
  {
    label: '观察声量',
    value: '4',
  },
];
// 修改 createColumns 的返回类型
export const createColumns = (
  handleEdit?: (row: any) => void,
  handleAppNameClick?: (name: string) => void,
  handleAuthor?: (row: any) => void,
  handleGeneratingknowledge?: (row: any) => void
): (ExtendedColumn | ExtendedSelectionColumn)[] => {
  const baseColumns: (ExtendedColumn | ExtendedSelectionColumn)[] = [
    {
      title: '应用名称',
      key: 'appName',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render: (row) => {
        return h('div', { style: { position: 'relative' } }, [
          h(
            NTag,
            {
              bordered: false,
              style: {
                cursor: 'pointer',
              },
              onClick: () => handleAppNameClick?.(row.appName),
            },
            {
              default: () => row.appName,
            }
          ),
        ]);
      },
    },
    {
      title: 'DTS单号',
      key: 'dtsId',
      width: 170,
      show: true,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return row.dtsId
          ? h(
              NTag,
              {
                type: 'info',
                size: 'medium',
              },
              [
                h(
                  'a',
                  {
                    href: `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${row.dtsId}`,
                    target: '_blank',
                  },
                  row.dtsId
                ),
              ]
            )
          : '';
      },
    },
    {
      title: '问题描述',
      key: 'description',
      resizable: true,
      width: 400,
      ellipsis: {
        tooltip: true,
      },
      show: true,
      render: (row) => {
        const description = true
          ? row.description.split('】').pop() || row.description
          : row.description;
        return h('span', {}, description);
      },
    },
    {
      title: '应用责任人',
      key: 'ewpOwner',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '舆情等级',
      key: 'opinionLevel',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      sorter: 'default',
      filterOptions: [
        { label: 'A', value: 'A' },
        { label: 'B', value: 'B' },
        { label: 'C', value: 'C' },
        { label: 'D', value: 'D' },
      ],
      filter: true,
      render(row) {
        return !row.opinionLevel
          ? ''
          : h(
              NTag,
              {
                type: row.opinionLevel == 'A' || row.opinionLevel == 'B' ? 'error' : 'warning',
                size: 'medium',
              },
              [h('div', row.opinionLevel)]
            );
      },
    },
    {
      title: '舆情状态',
      key: 'opinionStatus',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      filterOptions: [
        { label: '定界中', value: '定界中' },
        { label: '待锁定', value: '待锁定' },
        { label: '已锁定', value: '已锁定' },
        { label: '已闭环', value: '已闭环' },
        { label: '观察声量', value: '观察声量' },
      ],
      filter: true,
    },
    {
      title: '知识类型',
      key: 'type',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '规避方案',
      key: 'tempResolveScheme',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '知识解决计划',
      key: 'weknowSolvePlanTime',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '反馈口径',
      key: 'feedbackWay',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: 'DTS单级别',
      key: 'dtsLevel',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: 'DTS单状态',
      key: 'dtsStatus',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '风险指数',
      key: 'riskScore',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '计划解决时间',
      key: 'solvePlanTime',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '知识状态',
      key: 'status',
      filterOptions: [
        { label: '待分配', value: '待分配' },
        { label: '待输出', value: '待输出' },
        { label: '待修改', value: '待修改' },
        { label: '已上线', value: '已上线' },
        { label: '待下线', value: '待下线' },
        { label: '已下线', value: '已下线' },
      ],
      filter: true,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '知识作者',
      key: 'author',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '知识ID',
      key: 'weknowId',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return [
          h(
            NButton,
            {
              type: 'info',
              size: 'medium',
              text: true,
              tag: 'a',
              href: `https://consumer-tkb.huawei.com/weknow/index.html#!a/detail.html?contextNo=${row.weknowId}`,
              target: 'blank',
            },
            [h('div', row.weknowId)]
          ),
        ];
      },
    },
    {
      title: '操作',
      key: 'actions',
      fixed: 'right',
      width: 260,
      render: (row) => {
        return h('div', { style: { display: 'flex', gap: '8px' } }, [
          h(
            NButton,
            {
              strong: true,
              tertiary: true,
              size: 'small',
              onClick: () => handleEdit?.(row),
            },
            { default: () => '编辑' }
          ),
          h(
            NButton,
            {
              strong: true,
              tertiary: true,
              size: 'small',
              onClick: () => handleAuthor?.(row),
            },
            { default: () => '分配作者' }
          ),
          h(
            NButton,
            {
              strong: true,
              tertiary: true,
              size: 'small',
              onClick: () => handleGeneratingknowledge?.(row),
            },
            { default: () => '一键生成知识' }
          ),
        ]);
      },
    },
  ];
  // 从localStorage获取表格设置
  const columnsSetting = JSON.parse(
    window.localStorage.getItem('__4796_admin_dts_columns_setting') || '{}'
  );

  // 应用表格设置
  baseColumns.forEach((col) => {
    if ('key' in col) {
      // 如果有默认不显示的设置，且localStorage中没有对应的设置，则使用默认设置
      if (col.defaultShow === false && !columnsSetting[col.key]) {
        col.show = false;
      } else if (columnsSetting[col.key]) {
        // 否则使用localStorage中的设置
        col.show = columnsSetting[col.key].show !== 'false';
      }
    }
  });

  // 过滤掉 show 为 false 的列
  const filteredColumns = baseColumns.filter((col) => {
    if ('type' in col && col.type === 'selection') return true;
    return col.show !== false;
  });

  return filteredColumns;
};

// 创建一个通用的样式对象
const commonStyle = {
  style: {
    width: '300px',
    textAlign: 'left',
  },
  clearable: true,
  placeholder: '请输入',
};

// 为 Select 组件创建特定样式
const selectStyle = {
  ...commonStyle,
  style: {
    ...commonStyle.style,
  },
  filterable: true,
  placeholder: '请选择',
};

export const getSearchFormItems = () => [
  {
    field: 'dtsId',
    label: '问题单号',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'appName',
    label: '应用名称',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'description',
    label: '问题单描述',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'dtsStatus',
    label: 'DTS单状态',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: [
        { label: '问题提交人填写', value: '问题提交人填写' },
        { label: '测试（项目）经理审核', value: '测试（项目）经理审核' },
        { label: '开发人员实施修改', value: '开发人员实施修改' },
        { label: 'CCB方案审核', value: 'CCB方案审核' },
        { label: '审核人员审核修改', value: '审核人员审核修改' },
        { label: 'CMO归档', value: 'CMO归档' },
        { label: '测试经理组织测试', value: '测试经理组织测试' },
        { label: '测试人员回归测试', value: '测试人员回归测试' },
        { label: '关闭', value: '关闭' },
        { label: '挂起', value: '挂起' },
      ],
    },
  },
  {
    field: 'ewpOwner',
    label: '应用责任人',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: dtsHandleEmployee,
    },
  },
  {
    field: 'opinionStatus',
    label: '舆情状态',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: [
        {
          label: '定界中',
          value: '定界中',
        },
        {
          label: '待锁定',
          value: '待锁定',
        },
        {
          label: '已锁定',
          value: '已锁定',
        },
        {
          label: '已闭环',
          value: '已闭环',
        },
        {
          label: '观察声量',
          value: '观察声量',
        },
      ],
    },
  },
  {
    field: 'opinionLevel',
    label: '舆情等级',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: [
        {
          label: 'A',
          value: 'A',
        },
        {
          label: 'B',
          value: 'B',
        },
        {
          label: 'C',
          value: 'C',
        },
        {
          label: 'D',
          value: 'D',
        },
        {
          label: '—',
          value: '',
        },
      ],
    },
  },
  {
    field: 'type',
    label: '知识类型',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: [
        {
          label: '应用缺失',
          value: '应用缺失',
        },
        {
          label: '功能缺失',
          value: '功能缺失',
        },
        {
          label: '功能故障',
          value: '功能故障',
        },
        {
          label: '特殊场景',
          value: '特殊场景',
        },
        {
          label: '应用全场景',
          value: '应用全场景',
        },
      ],
    },
  },
  {
    field: 'status',
    label: '知识状态',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: [
        {
          label: '待分配',
          value: '待分配',
        },
        {
          label: '待输出',
          value: '待输出',
        },
        {
          label: '待修改',
          value: '待修改',
        },
        {
          label: '已上线',
          value: '已上线',
        },
        {
          label: '待下线',
          value: '待下线',
        },
        {
          label: '已下线',
          value: '已下线',
        },
      ],
    },
  },
  {
    field: 'author',
    label: '知识作者',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: serviceKnowEmployee,
    },
  },
  {
    field: 'weKnowSolvePlanTime',
    label: '计划解决时间',
    component: 'Input',
    componentProps: commonStyle,
  },
];
