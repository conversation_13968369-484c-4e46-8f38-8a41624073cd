<template>
  <div>
    <n-grid x-gap="20" :cols="1">
      <n-gi>
        <n-spin :show="contactLoading">
          <n-card style="margin-bottom: 12px" title="紧急联系人维护">
            <n-form
              ref="contactFormRef"
              label-placement="left"
              label-width="100"
              label-align="right"
              :model="contactModel"
              :rules="rules"
            >
              <n-grid x-gap="20" :cols="4" style="margin-bottom: 12px">
                <n-gi>
                  <n-form-item label="联系人姓名" path="emergencyContact">
                    <n-input
                      :disabled="!isEditContact && hasContact"
                      v-model:value="contactModel.emergencyContact"
                      clearable
                    />
                  </n-form-item>
                </n-gi>
                <n-gi>
                  <n-form-item label="关系" path="relation">
                    <n-select
                      :disabled="!isEditContact && hasContact"
                      v-model:value="contactModel.relation"
                      :options="relationList"
                      filterable
                      clearable
                    />
                  </n-form-item>
                </n-gi>
                <n-gi>
                  <n-form-item label="手机号" path="phone">
                    <n-input
                      :disabled="!isEditContact && hasContact"
                      v-model:value="contactModel.phone"
                      clearable
                    />
                  </n-form-item>
                </n-gi>
                <n-gi>
                  <n-form-item style="padding-left: 45px">
                    <n-button
                      v-if="!hasContact"
                      secondary
                      strong
                      type="primary"
                      @click="handleSubmintContact"
                      >提交</n-button
                    >
                    <n-space>
                      <n-button
                        v-if="hasContact"
                        secondary
                        strong
                        type="primary"
                        @click="handleEditConfirm"
                        >{{ isEditContact ? '保存' : '编辑' }}</n-button
                      >
                      <n-button
                        v-if="hasContact && isEditContact"
                        secondary
                        strong
                        type="primary"
                        @click="handleEditCancel"
                        >取消</n-button
                      >
                    </n-space>
                  </n-form-item>
                </n-gi>
              </n-grid>
            </n-form>
          </n-card>
        </n-spin>
      </n-gi>
    </n-grid>

    <n-card style="margin-bottom: 12px">
      <n-form label-placement="left" label-width="100" label-align="right">
        <n-grid x-gap="20" :cols="4" style="margin-bottom: 12px">
          <n-gi>
            <n-form-item label="主管" path="leaderList">
              <n-select
                v-model:value="filters.leaderList"
                :options="leaderList"
                filterable
                multiple
                clearable
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="组别" path="teamList">
              <n-select
                v-model:value="filters.teamList"
                :options="teamList"
                filterable
                multiple
                clearable
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="出差省份">
              <n-select
                v-model:value="filters.travelProvinceList"
                :options="travelProvinceList"
                filterable
                clearable
                multiple
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="出差城市">
              <n-select
                v-model:value="filters.travelCityList"
                :options="travelCityList"
                filterable
                clearable
                multiple
              />
            </n-form-item>
          </n-gi>
        </n-grid>
        <div v-if="collapse">
          <n-grid x-gap="20" :cols="4">
            <n-gi>
              <n-form-item label="应用名称">
                <n-select
                  v-model:value="filters.supportAppList"
                  :options="supportAppList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="姓名">
                <n-select
                  v-model:value="filters.userNoList"
                  :options="userNoList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="出差开始时间">
                <n-date-picker
                  v-model:value="filters.travelStartDateList"
                  type="daterange"
                  :style="{ width: '100%' }"
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="出差结束时间">
                <n-date-picker
                  v-model:value="filters.travelEndDateList"
                  type="daterange"
                  :style="{ width: '100%' }"
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="月份" path="month">
                <n-date-picker
                  v-model:value="filters.month"
                  type="month"
                  clearable
                  :style="{ width: '100%' }"
                />
              </n-form-item>
            </n-gi>
          </n-grid>
        </div>
      </n-form>
      <n-space justify="center">
        <n-button secondary strong type="default" @click="handleResetFilter">重置 </n-button>
        <n-button secondary strong type="primary" @click="handleUpdateFilter()">查询 </n-button>
        <n-button type="primary" icon-placement="right" @click="unfoldToggle">
          <template #icon>
            <n-icon size="14" class="unfold-icon" v-if="collapse">
              <UpOutlined />
            </n-icon>
            <n-icon size="14" class="unfold-icon" v-else>
              <DownOutlined />
            </n-icon>
          </template>
          {{ collapse ? '收起' : '展开' }}
        </n-button>
      </n-space>
    </n-card>
    <!-- 添加 -->
    <n-modal v-model:show="showModal" :on-after-leave="initModel">
      <div>
        <n-card
          :style="{ width: '600px' }"
          :title="isAdd ? '新增出差记录' : '编辑出差记录'"
          :bordered="false"
          size="huge"
          role="dialog"
          aria-modal="true"
        >
          <template #header-extra> </template>
          <n-form
            ref="formRef"
            :model="model"
            :rules="rules"
            label-placement="left"
            label-width="auto"
            require-mark-placement="right-hanging"
            size="medium"
          >
            <n-form-item label="应用名" path="supportApp">
              <n-select
                v-model:value="model.supportApp"
                :options="applicationNameList"
                filterable
                tag
                clearable
                multiple
              />
            </n-form-item>
            <n-form-item label="城市" path="travelCity">
              <n-cascader
                v-model:value="model.travelCity"
                check-strategy="child"
                placeholder="请选择城市"
                expand-trigger="hover"
                :options="districts"
                :show-path="true"
                children-field="districts"
                value-field="name"
                label-field="name"
                filterable
                @update:value="handleUpdateValue"
              />
            </n-form-item>
            <n-form-item label="出差开始时间" path="travelStartDate">
              <n-date-picker
                :style="{ width: '100%' }"
                v-model:value="model.travelStartDate"
                :is-date-disabled="disableTravelStartDate"
                clearable
              />
            </n-form-item>
            <n-form-item label="出差结束时间" path="travelEndDate">
              <n-date-picker
                :style="{ width: '100%' }"
                v-model:value="model.travelEndDate"
                :is-date-disabled="disableTravelEndDate"
                clearable
              />
            </n-form-item>
            <n-form-item label="工作日记" path="workDiary">
              <n-input v-model:value="model.workDiary" type="textarea" />
            </n-form-item>
          </n-form>

          <template #footer>
            <n-space>
              <n-button secondary strong type="primary" @click="handleSubmint()"> 确认 </n-button>
              <n-button secondary strong type="error" @click="showModal = false"> 取消 </n-button>
            </n-space>
          </template>
        </n-card>
      </div>
    </n-modal>
    <n-card title="出差信息维护">
      <div class="layout-page-header">
        <n-space>
          <n-button secondary strong type="warning" @click="handleGetTravelInformation()">
            <template #icon>
              <n-icon>
                <Refresh />
              </n-icon>
            </template>
            刷新
          </n-button>

          <n-button
            secondary
            strong
            type="primary"
            @click="
              isAdd = true;
              showModal = true;
            "
            >新增
          </n-button>
        </n-space>
      </div>
      <n-data-table
        :pagination="pagination"
        :columns="columns"
        :data="tableData"
        style="margin-top: 20px"
        :single-line="false"
        max-height="700px"
        scroll-x
        remote
        @update:sorter="handleSort"
      />
    </n-card>
  </div>
</template>

<script lang="ts" setup>
  import { Add, Refresh, Close, GridOutline } from '@vicons/ionicons5';
  import {
    NFlex,
    NIcon,
    NButton,
    NTag,
    useDialog,
    UploadCustomRequestOptions,
    useMessage,
  } from 'naive-ui';
  import { h, ref, reactive, onBeforeMount, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import {
    formatDateTime,
    getDefaultContactFilters,
    districts,
    emergencyContactModelTemplate,
    relationList,
    travelInformationModelTemplate,
    teamOptions,
  } from '../index';
  import {
    getContactList,
    addContact,
    deleteContact,
    updateContact,
    getTravelInformation,
    updateTravelInformation,
    deleteTravelInformation,
    addTravelInformation,
    getTravelInformationCol,
  } from '@/api/dataview/businessTravelersManage';
  import { DownOutlined, UpOutlined } from '@vicons/antd';
  import { useUserStore } from '@/store/modules/user';
  import { getEmployeeList, editUser } from '@/api/system/usermanage';
  import { getColumns } from '@/api/dataview/applicationAcceptance';
  import { UserRoleEnum } from '@/enums/UserRoleEnum';
  const contactModel = ref({ ...emergencyContactModelTemplate });
  import { debounce, cloneDeep } from 'lodash-es';
  const props = defineProps({
    leaderList: {
      default: () => [],
      required: true,
    },
  });
  watch(
    () => props.leaderList,
    () => {
      leaderList.value = props.leaderList;
    }
  );
  const userStore = useUserStore();

  const roles = userStore.getUserInfo.roles;
  const userNo = userStore.getUserInfo.account;
  const userInfo = ref({ ...userStore.getUserInfo });
  const contactLoading = ref(true);
  const isEditContact = ref(false);
  const hasContact = ref(false);
  const filters = ref(getDefaultContactFilters());
  const dialog = useDialog();
  const message = useMessage();
  const tableData = ref([]);
  const teamList = ref(teamOptions);
  const leaderList = ref(reactive(props.leaderList));
  const showModal = ref(false);
  const sortCol = ref(null);
  const model = ref({ ...travelInformationModelTemplate });
  const rules = ref({
    emergencyContact: {
      required: true,
      message: '请输入联系人姓名',
    },
    relation: {
      required: true,
      message: '请选择关系',
    },
    phone: {
      required: true,
      message: '请输入手机号',
    },
    supportApp: {
      required: true,
      message: '请选择应用名称',
    },
    travelProvince: {
      required: true,
    },
    travelCity: {
      required: true,
      message: '请选择城市',
    },
    travelStartDate: {
      required: true,
      validator: (rule, value) => {
        if (!value) {
          return new Error('请选择出差开始时间');
        } else if (model.value.travelEndDate && value > model.value.travelEndDate) {
          return new Error('时间范围不正确');
        }
        return true;
      },
    },
    travelEndDate: {
      required: true,
      validator: (rule, value) => {
        if (!value) {
          return new Error('请选择出差结束时间');
        } else if (model.value.travelStartDate && value < model.value.travelStartDate) {
          return new Error('时间范围不正确');
        }
        return true;
      },
    },
    userName: {
      required: true,
    },
    residence: {
      required: true,
    },
    leader: {
      required: true,
    },
  });
  const isAdd = ref(false);
  const contactFormRef = ref();
  const formRef = ref();
  const supportAppList = ref([]);
  const travelCityList = ref([]);
  const travelProvinceList = ref([]);
  const applicationNameList = ref();
  const userNoList = ref([]);
  const userMap = new Map();
  const userList = ref([]);

  const col = [
    {
      title: '姓名',
      key: 'userName',
      width: 80,
    },
    {
      title: '工号',
      key: 'userNo',
      width: 110,
    },
    {
      title: '常驻地',
      key: 'residence',
      width: 80,
    },
    {
      title: '组别',
      key: 'team',
      width: 80,
    },
    {
      title: '主管',
      key: 'leader',
      width: 150,
    },

    {
      title: '支撑应用',
      key: 'supportApp',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '省份',
      key: 'travelProvince',
      width: 70,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '城市',
      key: 'travelCity',
      width: 70,
    },
    {
      title: '天数',
      key: 'travelDays',
      width: 100,
      sorter: true,
    },
    {
      title: '出差开始时间',
      key: 'travelStartDate',
      width: 120,
      render(row) {
        return row?.travelStartDate ? new Date(row.travelStartDate).toLocaleDateString() : '';
      },
    },
    {
      title: '出差结束时间',
      key: 'travelEndDate',
      width: 120,
      render(row) {
        return row?.travelEndDate ? new Date(row.travelEndDate).toLocaleDateString() : '';
      },
    },
    {
      title: '工作日记',
      key: 'workDiary',
      width: 200,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 600px;overflow-y: auto',
        },
      },
    },

    {
      title: '操作',
      key: 'action',
      width: 160,
      fixed: 'right',
      render(row) {
        return [
          h(NFlex, { wrap: false, justify: 'start' }, () => [
            h(
              NButton,
              {
                strong: true,
                tertiary: true,
                type: 'info',
                size: 'medium',
                onClick: async () => {
                  isAdd.value = false;
                  showModal.value = true;
                  let rowData = cloneDeep(row);
                  try {
                    rowData.supportApp = rowData.supportApp.split('，');
                    rowData.travelEndDate = new Date(row.travelEndDate).getTime();
                    rowData.travelStartDate = new Date(row.travelStartDate).getTime();
                  } catch (err) {}
                  model.value = rowData;
                },
              },
              { default: () => [h('div', '编辑')] }
            ),
            h(
              NButton,
              {
                strong: true,
                tertiary: true,
                type: 'error',
                size: 'medium',
                onClick: () => {
                  dialog.error({
                    title: '警告',
                    content: '是否删除出差信息',
                    positiveText: '确定',
                    negativeText: '取消',
                    onPositiveClick: async () => {
                      try {
                        await deleteTravelInformation({
                          id: row.id,
                        });
                        message.success('删除成功');
                        await handleGetTravelInformation();
                        handleGetTravelInformationCol();
                      } catch {
                        message.error('删除失败');
                      }
                    },
                  });
                },
              },
              { default: () => [h('div', '删除')] }
            ),
          ]),
        ];
      },
    },
  ];
  const columns = ref(col);
  const collapse = ref(false);
  const unfoldToggle = () => {
    collapse.value = !collapse.value;
  };
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 50],
    itemCount: 0,
    prefix({ itemCount }) {
      return `总条数 ${itemCount}`;
    },
    onChange: (page: number) => {
      pagination.page = page;
      handleGetTravelInformation();
    },
    onUpdatePageSize: (pageSize: number) => {
      pagination.pageSize = pageSize;
      pagination.page = 1;
      handleGetTravelInformation();
    },
  });
  const disableTravelStartDate = (ts: number) => {
    return model.value.travelEndDate ? ts > model.value.travelEndDate : null;
  };
  const disableTravelEndDate = (ts: number) => {
    return model.value.travelStartDate ? ts < model.value.travelStartDate : null;
  };
  const handleUpdateValue = (
    value: string | number | Array<string | number> | null,
    option,
    pathValues
  ) => {
    model.value.travelProvince = pathValues[0]['name'];
  };
  const handleUpdateFilter = () => {
    pagination.page = 1;
    handleGetTravelInformation();
  };
  const initModel = () => {
    model.value = { ...travelInformationModelTemplate };
  };
  //获取出差信息
  const handleGetTravelInformation = async () => {
    let params = {
      pageNum: pagination.page,
      pageSize: pagination.pageSize,
    };
    if (sortCol.value?.columnKey === 'travelDays' && sortCol.value?.order) {
      params.travelDaysOrder = sortCol.value.order === 'descend' ? 'desc' : 'asc';
    }
    for (let key in filters.value) {
      if (filters.value[key]) {
        params[key] = filters.value[key];
      }
    }
    if (params?.month) {
      params.year = new Date(params.month).getFullYear();
      params.month = new Date(params.month).getMonth() + 1;
    }
    try {
      let res = await getTravelInformation(params);
      if (res.status == '200') {
        tableData.value = res.data.travelList;
        pagination.itemCount = res.data.pageInfo.total;
      }
    } catch (e) {}
  };
  const handleCurrentContact = async () => {
    contactLoading.value = true;
    try {
      let { data } = await getContactList({
        userList: [userNo],
        pageNum: 1,
        pageSize: 10,
      });
      if (data?.list?.length > 0) {
        hasContact.value = true;
        contactModel.value = data.list[0];
      }
    } catch (e) {}
    contactLoading.value = false;
  };
  const handleEditConfirm = async () => {
    if (isEditContact.value) {
      await handleSubmintContact();
    } else {
      isEditContact.value = !isEditContact.value;
    }
  };
  const handleEditCancel = () => {
    isEditContact.value = false;
    handleCurrentContact();
  };
  const handleSubmintContact = async () => {
    contactFormRef.value.validate(async (errors) => {
      if (!errors) {
        if (hasContact.value) {
          var res = await updateContact({
            ...contactModel.value,
            account: userNo,
          });
        } else {
          var res = await addContact({
            ...contactModel.value,
            account: userNo,
          });
          hasContact.value = true;
        }
        if (res.status == '200') {
          message.success('提交成功');
        }
        handleCurrentContact();
        isEditContact.value = false;
      }
    });
  };

  const handleResetFilter = () => {
    pagination.page = 1;
    filters.value = getDefaultContactFilters();
    handleGetTravelInformation();
  };
  const handleGetTravelInformationCol = async () => {
    let { data } = await getTravelInformationCol();
    let list = [];
    data?.['support_app']?.forEach((item) => list.push(...(item?.split('，') || [])));

    supportAppList.value = Array.from(new Set(list)).map((item) => ({
      label: item,
      value: item,
    }));
    travelCityList.value = data['travel_city'].map((item) => ({
      label: item,
      value: item,
    }));
    travelProvinceList.value = data['travel_province'].map((item) => ({
      label: item,
      value: item,
    }));
    userNoList.value = data['user_no'].map((item) => ({
      label: userMap.get(item) ? userMap.get(item) : item,
      value: item,
    }));
    {
      let { data } = await getColumns();
      applicationNameList.value = data['application_name'].map((item) => {
        return {
          label: item,
          value: item,
        };
      });
      applicationNameList.value.unshift({
        label: '其他',
        value: '其他',
      });
    }
  };
  //提交处理应用
  const handleSubmint = debounce(async () => {
    formRef.value.validate(async (errors) => {
      if (!errors) {
        let params = cloneDeep(model.value);
        params.supportApp = params.supportApp?.join('，') || '';
        if (isAdd.value) {
          params.userNo = userNo;
          var res = await addTravelInformation(params);
          handleGetTravelInformationCol();
        } else {
          var res = await updateTravelInformation(params);
        }
        if (res.status == '200') {
          message.success('提交成功');
          await handleGetTravelInformation();
          handleGetTravelInformationCol;
          showModal.value = false;
        }
      }
    });
  }, 300);
  const handlequeryUserList = async () => {
    let { data } = await getEmployeeList();
    userList.value = data.map((item) => {
      userMap.set(item.account, item.userName + '/' + item.account);
      return {
        label: item.userName + '/' + item.account,
        value: item.account,
      };
    });
    handleGetTravelInformationCol();
    handleGetTravelInformation();
  };
  const handleUpdateUser = async () => {
    var res = await editUser(userInfo.value);
    if (res.status == '200') {
      message.success('提交成功');
      await handleGetTravelInformation();
      localStorage.setItem(
        'CURRENT-USER',
        JSON.stringify({
          ...JSON.parse(localStorage.getItem('CURRENT-USER')),
          value: userInfo.value,
        })
      );
    }
  };

  const handleSort = (col) => {
    sortCol.value = col;
    handleGetTravelInformation();
  };

  handlequeryUserList();
  handleCurrentContact();
</script>

<style lang="less" scoped>
  :deep(.maxHeight)::-webkit-scrollbar {
    // display: none;
  }
  .layout-page-header {
    .n-select {
      min-width: 250px;
    }
  }
  .unfold-icon {
    display: flex;
    align-items: center;
    height: 100%;
    margin-left: -3px;
  }
</style>
