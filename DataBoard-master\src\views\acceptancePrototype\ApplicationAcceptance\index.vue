<template>
  <div>
    <n-card style="margin-bottom: 12px">
      <n-form label-placement="left" label-width="100px" label-align="left">
        <n-grid x-gap="20" :cols="4" style="margin-bottom: 12px">
          <n-gi v-if="keyList.includes('applicationName')">
            <n-form-item label="应用名称">
              <n-select
                v-model:value="filters.applicationName"
                :options="applicationNameList"
                filterable
                clearable
              />
            </n-form-item>
          </n-gi>
          <n-gi v-if="keyList.includes('company')">
            <n-form-item label="应用归属公司">
              <n-select
                v-model:value="filters.company"
                :options="companyList"
                filterable
                clearable
              />
            </n-form-item>
          </n-gi>
          <n-gi v-if="keyList.includes('applicationName')">
            <n-form-item label="批量应用名称">
              <n-input v-model:value="filters.applicationNameList" clearable />
            </n-form-item>
          </n-gi>

          <n-gi v-if="keyList.includes('applicationType')">
            <n-form-item label="应用形态">
              <n-select
                v-model:value="filters.applicationType"
                :options="applicationTypeList"
                filterable
                clearable
              />
            </n-form-item>
          </n-gi>
        </n-grid>
        <div v-if="collapse">
          <n-grid x-gap="20" :cols="4" style="margin-bottom: 12px">
            <n-gi v-if="keyList.includes('kcpStage')" >
              <n-form-item label="已完成KCP阶段">
                <n-select
                  v-model:value="filters.kcpStageList"
                  :options="kcpStageList"
                  multiple
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('functionEnhancementPriority')">
              <n-form-item label="优先级">
                <n-select
                  v-model:value="filters.functionEnhancementPriorityList"
                  :options="functionEnhancementPriorityList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('applicationPriority')">
              <n-form-item label="应用层级">
                <n-select
                  v-model:value="filters.label"
                  :options="labelList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('applyVerticalDomain')">
              <n-form-item label="应用垂域">
                <n-select
                  v-model:value="filters.applyVerticalDomainList"
                  :options="applyVerticalDomainList"
                  filterable
                  clearable
                  multiple                 
                  :max-tag-count="2"
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('province')">
              <n-form-item label="所属代表处">
                <n-select
                  v-model:value="filters.provinceList"
                  :options="provinceList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('isServiceCompanyUndertakes')">
              <n-form-item label="服务公司承接">
                <n-select
                  v-model:value="filters.isServiceCompanyUndertakesList"
                  :options="isServiceList"
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('destList')">
              <n-form-item label="DTSE责任人">
                <n-select
                  v-model:value="filters.destList"
                  :options="destList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('acceptances')">
              <n-form-item label="体验测试责任人">
                <n-select
                  v-model:value="filters.acceptances"
                  :options="acceptancesList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>

            <n-gi v-if="keyList.includes('firstSla')">
              <n-form-item label="首轮SLA">
                <n-select
                  v-model:value="filters.firstSlaList"
                  :options="firstSLAList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('releaseConclusion')">
              <n-form-item label="上架结论">
                <n-select
                  v-model:value="filters.releaseConclusionList"
                  :options="releaseConclusionList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
             <n-gi v-if="keyList.includes('firstLevelAcceptance')">
              <n-form-item label="一级验收方式">
                <n-select
                  v-model:value="filters.firstLevelAcceptanceList"
                  :options="firstLevelAcceptanceList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
             <n-gi v-if="keyList.includes('secondLevelAcceptance')">
              <n-form-item label="二级验收方式">
                <n-select
                  v-model:value="filters.secondLevelAcceptanceList"
                  :options="secondLevelAcceptanceList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('latestTestConclusion')">
              <n-form-item label="最新验收结论">
                <n-select
                  v-model:value="filters.latestTestConclusionList"
                  :options="testResList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('rounds')">
              <n-form-item label="轮次">
                <n-select
                  v-model:value="filters.roundsList"
                  :options="roundsList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('isFunctionEnhancements')">
              <n-form-item label="功能增强">
                <n-select
                  v-model:value="filters.isFunctionEnhancementsList"
                  :options="checkList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>

            <n-gi v-if="keyList.includes('isDataInheritance')">
              <n-form-item label="数据继承">
                <n-select
                  v-model:value="filters.isDataInheritanceList"
                  :options="checkList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('involvedFoldingScreen')">
              <n-form-item label="折叠屏">
                <n-select
                  v-model:value="filters.involvedFoldingScreenList"
                  :options="checkList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('puraXExternalScreen')">
              <n-form-item label="Pura X外屏">
                <n-select
                  v-model:value="filters.puraXExternalScreenList"
                  :options="checkList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('pad')">
              <n-form-item label="PAD">
                <n-select
                  v-model:value="filters.padList"
                  :options="checkList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('pc')">
              <n-form-item label="PC">
                <n-select
                  v-model:value="filters.pcList"
                  :options="checkList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('innovativeSpecial')">
              <n-form-item label="创新特性">
                <n-select
                  v-model:value="filters.innovativeSpecialList"
                  :options="checkList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('standardApp')">
              <n-form-item label="A标应用">
                <n-select
                  v-model:value="filters.standardAppList"
                  :options="checkList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('hwAccountLogin')">
              <n-form-item label="华为账号登录">
                <n-select
                  v-model:value="filters.hwAccountLoginList"
                  :options="checkList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('hwAccountOneKeyLogin')">
              <n-form-item label="华为账号一键登录">
                <n-select
                  v-model:value="filters.hwAccountOneKeyLoginList"
                  :options="checkList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('permissionSecurityPicker')">
              <n-form-item label="权限安全picker">
                <n-select
                  v-model:value="filters.permissionSecurityPickerList"
                  :options="checkList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('digitalCashier')">
              <n-form-item label="数字收银台">
                <n-select
                  v-model:value="filters.digitalCashierList"
                  :options="checkList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi v-if="keyList.includes('physicalCashier')">
              <n-form-item label="实物收银台">
                <n-select
                  v-model:value="filters.physicalCashierList"
                  :options="checkList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
          </n-grid>
        </div>
      </n-form>
      <n-space justify="center">
        <n-button secondary strong type="default" @click="handleResetFilter">重置 </n-button>
        <n-button
          secondary
          strong
          type="primary"
          @click="
            message.loading('获取应用列表中', {
              duration: 200000,
            });
            handleUpdateFilter();
          "
          >查询
        </n-button>
        <n-button type="primary" icon-placement="right" @click="unfoldToggle">
          <template #icon>
            <n-icon size="14" class="unfold-icon" v-if="collapse">
              <UpOutlined />
            </n-icon>
            <n-icon size="14" class="unfold-icon" v-else>
              <DownOutlined />
            </n-icon>
          </template>
          {{ collapse ? '收起' : '展开' }}
        </n-button>
      </n-space>
    </n-card>
    <n-card>
      <div>
        <div class="layout-page-header">
          <n-space style="align-items: center">
            <n-button
              secondary
              strong
              type="warning"
              @click="
                message.loading('获取应用列表中', {
                  duration: 200000,
                });
                handleGetAPPList();
              "
            >
              <template #icon>
                <n-icon>
                  <Refresh />
                </n-icon>
              </template>
              刷新
            </n-button>
            <n-button
              type="error"
              secondary
              strong      
              :disabled="!isTestAdmin"
              @click="
                showAddModal = true;
                isAdd = true;
              "
            >
              添加
            </n-button>
            <!-- <n-button
              secondary
              type="default"
              strong
              @click="
                showListAddModal = true;
                isBasicAppData = false;
              "
              >导入应用测试信息
            </n-button> -->
            <n-button
              secondary
              strong
              type="primary"
              :disabled="!isTestAdmin"
              @click="
                showListAddModal = true;
                isBasicAppData = true;
              "
              >导入应用基本信息
            </n-button>
            <n-button
              secondary
              strong
              type="success"
              :disabled="!(isTestAdmin || isTestUser)"
              @click="handleEditList"
              >批量编辑
            </n-button>
            <n-button
              secondary
              strong
              type="error"
              :disabled="!isTestAdmin"
              @click="showSpecialModal = true"
              >同步专项信息
            </n-button>
            <n-button
              v-if="/^[a-zA-Z](300|00)/.test(currentUser.account)"
              type="error"
              :disabled="!isTestAdmin"
              @click="handleBatchDelete()"
              >批量删除
            </n-button>
            <n-button secondary strong type="warning" @click="handleExportProblem()"
              >导出
            </n-button>
<n-button secondary strong type="primary" @click="showImportModal = true"
              >同步Top2k应用信息
            </n-button>
            <n-checkbox
              id="onlyTop2K"
              v-model:checked="onlyTop2k"
              @update:checked="handleGetAPPList"
            >
              只看top2k应用
            </n-checkbox>
          </n-space>
          <div v-if="isTestAdmin">
            <n-button text @click="showEditCols = true">
              <template #icon>
                <n-icon>
                  <Settings></Settings>
                </n-icon>
              </template>
            </n-button>
          </div>
        </div>
        <n-data-table
          v-model:checked-row-keys="checkedRowKeys"
          :columns="columns"
          :data="tableData"
          :pagination="pagination"
          :scroll-x="scrollX"
          max-height="700px"
          virtual-scroll
          :on-update:checked-row-keys="onUpdateChecked"
          remote
          style="margin-top: 20px"
        />
        <!-- 添加 -->
        <n-modal v-model:show="showAddModal" :on-after-leave="initModel">
          <div>
            <n-card
              :style="{ width: '600px' }"
              title="新增应用"
              :bordered="false"
              size="huge"
              role="dialog"
              aria-modal="true"
            >
              <template #header-extra> </template>
              <n-form
                ref="formAddRef"
                :model="model"
                :rules="rules"
                label-placement="left"
                label-width="auto"
                require-mark-placement="right-hanging"
                size="medium"
                label-align="left"
              >
                <n-form-item label="应用名称" path="applicationName">
                  <n-input v-model:value="model.applicationName" />
                </n-form-item>
                <n-form-item label="应用归属公司" path="company">
                  <n-input v-model:value="model.company" />
                </n-form-item>
                <n-form-item label="应用形态" path="applicationType">
                  <n-select v-model:value="model.applicationType" :options="applicationTypeList" />
                </n-form-item>
              </n-form>

              <template #footer>
                <n-space>
                  <n-button secondary strong type="primary" @click="handleSubmint()">
                    确认
                  </n-button>
                  <n-button secondary strong type="error" @click="showAddModal = false">
                    取消
                  </n-button>
                </n-space>
              </template>
            </n-card>
          </div>
        </n-modal>
        <!-- 批量添加 -->
        <n-modal v-model:show="showListAddModal">
          <n-card
            style="width: 600px"
            title="批量导入"
            :bordered="false"
            size="huge"
            role="dialog"
            aria-modal="true"
          >
            <!-- <n-button text @click="handleDownload" style="margin-bottom: 20px"
              >点击下载导入模板</n-button
            > -->
            <n-upload
              action="#"
              :custom-request="customRequest"
              :multiple="true"
              :default-file-list="fileList"
              accept=".xls,.xlsx,"
              :max="1"
              directory-dnd
            >
              <n-upload-dragger>
                <div style="margin-bottom: 12px">
                  <n-icon size="48" :depth="3">
                    <ArchiveIcon />
                  </n-icon>
                </div>
                <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
              </n-upload-dragger>
            </n-upload>
          </n-card>
        </n-modal>
         <!-- 同步Top2k应用信息 -->
        <n-modal v-model:show="showImportModal">
          <n-card
            style="width: 600px"
            title="同步Top2k应用信息"
            :bordered="false"
            size="huge"
            role="dialog"
            aria-modal="true"
          >
            <n-upload
              action="#"
              :custom-request="importTop"
              :multiple="true"
              :default-file-list="fileList"
              accept=".xls,.xlsx,"
              :max="1"
              directory-dnd
            >
              <n-upload-dragger>
                <div style="margin-bottom: 12px">
                  <n-icon size="48" :depth="3">
                    <ArchiveIcon />
                  </n-icon>
                </div>
                <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
              </n-upload-dragger>
            </n-upload>
          </n-card>
        </n-modal>

        <!-- 编辑表格列 -->
        <n-modal v-model:show="showEditCols">
          <n-card
            style="width: 700px"
            title="编辑表格列"
            :bordered="false"
            size="huge"
            role="dialog"
            aria-modal="true"
          >
            <n-form
              ref="formRef"
              label-placement="left"
              label-width="auto"
              require-mark-placement="right-hanging"
              size="medium"
            >
              <n-checkbox-group v-model:value="columnList">
                <n-space
                  item-style="display: flex"
                  style="display: grid; grid-template-columns: 1fr 1fr 1fr"
                >
                  <n-checkbox
                    v-for="(item, index) in allColumns"
                    :key="index"
                    :value="item.title"
                    :label="item.title"
                  />
                </n-space>
              </n-checkbox-group>
            </n-form>
            <template #footer>
              <n-space>
                <n-button secondary strong type="primary" @click="handleUpdateDisplayedField">
                  确认
                </n-button>
                <n-button secondary strong type="error" @click="showEditCols = false">
                  取消
                </n-button>
              </n-space>
            </template>
          </n-card>
        </n-modal>

        <!-- 同步信息专项 -->
        <n-modal v-model:show="showSpecialModal">
          <n-card
            style="width: 600px"
            title="同步专项信息"
            :bordered="false"
            size="huge"
            role="dialog"
            aria-modal="true"
          >
            <n-upload
              action="#"
              :custom-request="customSpecialRequest"
              :multiple="true"
              :default-file-list="fileSpecialList"
              accept=".xls,.xlsx,"
              :max="1"
              directory-dnd
            >
              <n-upload-dragger>
                <div style="margin-bottom: 12px">
                  <n-icon size="48" :depth="3">
                    <ArchiveIcon />
                  </n-icon>
                </div>
                <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
              </n-upload-dragger>
            </n-upload>
          </n-card>
        </n-modal>
        <!-- 编辑 -->
        <n-modal v-model:show="showEditModal" :on-after-leave="initModel">
          <div>
            <n-card
              :style="{ width: '1200px' }"
              title="编辑应用"
              :bordered="false"
              size="huge"
              role="dialog"
              aria-modal="true"
            >
              <template #header-extra> </template>
              <n-space style="margin-bottom: 20px">
                <n-button
                  secondary
                  strong
                  type="primary"
                  @click="
                    showTestModal = true;
                    isTestAdd = true;
                    isListEdit = false;
                    testModel.userNo = model.acceptances[0];
                  "
                >
                  新增记录
                </n-button>
              </n-space>
              <n-data-table
                :columns="testColumns"
                :data="tableTestData"
                :scroll-x="1000"
                :max-height="300"
                virtual-scroll
                remote
                style="margin-bottom: 20px"
              />
              <n-form
                ref="formRef"
                :model="model"
                :rules="rules"
                label-placement="left"
                label-width="auto"
                require-mark-placement="right-hanging"
                size="medium"
                label-align="left"
              >
                <n-grid :cols="24" :x-gap="24">
                  <n-form-item-gi  v-if="keyList.includes('applicationName')" :span="12" label="应用名称" path="applicationName">
                    <n-input v-model:value="model.applicationName" :disabled="!isTestAdmin" />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('company')"  :span="12" label="应用归属公司" path="company">
                    <n-input v-model:value="model.company" :disabled="!isTestAdmin" />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('applicationType')" :span="12" label="应用形态" path="applicationType">
                    <n-select
                      v-model:value="model.applicationType"
                      :options="applicationTypeList"
                      :disabled="!isTestAdmin"
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('kcpStage')" :span="12" label="已完成KCP阶段" path="kcpStage">
                    <n-select
                      v-model:value="model.kcpStage"
                      :options="kcpStageList"
                      :disabled="!isTestAdmin"
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('acceptances')" :span="12" label="体验测试责任人" path="acceptances">
                    <n-select
                      v-model:value="model.acceptances"
                      :options="userList"
                      filterable
                      clearable
                      multiple
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('releaseConclusion')" :span="12" label="上架结论" path="releaseConclusion">
                    <n-select
                      v-model:value="model.releaseConclusion"
                      :options="releaseConclusionList"
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('firstLevelAcceptance')" :span="12" label="一级验收方式 " path="firstLevelAcceptance">
                    <n-select
                      v-model:value="model.firstLevelAcceptance"
                      :options="firstLevelAcceptanceList"
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('secondLevelAcceptance')" :span="12" label="二级验收方式" path="secondLevelAcceptance">
                    <n-select
                      v-model:value="model.secondLevelAcceptance"
                      :options="secondLevelAcceptanceList"
                      clearable
                      multiple
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('isServiceCompanyUndertakes')" :span="12" label="服务公司承接" path="isServiceCompanyUndertakes">
                    <n-select
                      v-model:value="model.isServiceCompanyUndertakes"
                      :options="isServiceList"
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('isFunctionEnhancements')" :span="12" label="功能增强" path="isFunctionEnhancements">
                    <n-select
                      v-model:value="model.isFunctionEnhancements"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('functionEnhancementPercentage')"
                    :span="12"
                    label="功能完备度"
                    path="functionEnhancementPercentage"
                  >
                    <n-input v-model:value="model.functionEnhancementPercentage">
                      <template #suffix> % </template>
                    </n-input>
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('isDataInheritance')" :span="12" label="数据继承" path="isDataInheritance">
                    <n-select
                      v-model:value="model.isDataInheritance"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('involvedFoldingScreen')" :span="12" label="折叠屏" path="involvedFoldingScreen">
                    <n-select
                      v-model:value="model.involvedFoldingScreen"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                   <n-form-item-gi  v-if="keyList.includes('puraXExternalScreen')" :span="12" label="Pura X外屏" path="puraXExternalScreen">
                    <n-select
                      v-model:value="model.puraXExternalScreen"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('pad')" :span="12" label="PAD" path="pad">
                    <n-select v-model:value="model.pad" :options="checkList" filterable clearable />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('pc')" :span="12" label="PC" path="pc">
                    <n-select v-model:value="model.pc" :options="checkList" filterable clearable />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('innovativeSpecial')" :span="12" label="创新特性" path="innovativeSpecial">
                    <n-select
                      v-model:value="model.innovativeSpecial"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('standardApp')" :span="12" label="A标应用" path="standardApp">
                    <n-select
                      v-model:value="model.standardApp"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('hwAccountLogin')" :span="12" label="华为账号登录" path="hwAccountLogin">
                    <n-select
                      v-model:value="model.hwAccountLogin"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('hwAccountOneKeyLogin')" :span="12" label="华为账号一键登录" path="hwAccountOneKeyLogin">
                    <n-select
                      v-model:value="model.hwAccountOneKeyLogin"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('permissionSecurityPicker')" :span="12" label="权限安全picker" path="permissionSecurityPicker">
                    <n-select
                      v-model:value="model.permissionSecurityPicker"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('digitalCashier')" :span="12" label="数字收银台" path="digitalCashier">
                    <n-select
                      v-model:value="model.digitalCashier"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('physicalCashier')" :span="12" label="实物收银台" path="physicalCashier">
                    <n-select
                      v-model:value="model.physicalCashier"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>

                  <n-form-item-gi  v-if="keyList.includes('remark')" :span="12" label="备注" path="remark">
                    <n-input v-model:value="model.remark" type="textarea" clearable />
                  </n-form-item-gi>
                </n-grid>
              </n-form>
              <template #footer>
                <n-space>
                  <n-button secondary strong type="primary" @click="handleSubmint()">
                    确认
                  </n-button>
                  <n-button secondary strong type="error" @click="showEditModal = false">
                    取消
                  </n-button>
                </n-space>
              </template>
            </n-card>
          </div>
        </n-modal>
        <!-- 批量编辑 -->
        <n-modal v-model:show="showListEditModal" :on-after-leave="initModel">
          <div>
            <n-card
              :style="{ width: '1200px' }"
              title="批量编辑应用"
              :bordered="false"
              size="huge"
              role="dialog"
              aria-modal="true"
            >
              <template #header-extra> </template>
              <n-space style="margin-bottom: 20px">
                <n-button
                  secondary
                  strong
                  type="primary"
                  @click="
                    showTestModal = true;
                    isTestAdd = true;
                    isListEdit = true;
                  "
                >
                  新增记录
                </n-button>
              </n-space>
              <n-data-table
                :columns="testColumns"
                :data="tableTestData"
                :scroll-x="1000"
                :max-height="300"
                virtual-scroll
                remote
                style="margin-bottom: 20px"
              />
              <n-form
                ref="formRef"
                :model="model"
                :rules="rules"
                label-placement="left"
                label-width="auto"
                require-mark-placement="right-hanging"
                size="medium"
                label-align="left"
              >
                <n-grid :cols="24" :x-gap="24">
                  <n-form-item-gi  v-if="keyList.includes('kcpStage')" :span="12" label="已完成KCP阶段" path="kcpStage">
                    <n-select
                      v-model:value="model.kcpStage"
                      :options="kcpStageList"
                      :disabled="!isTestAdmin"
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('releaseConclusion')" :span="12" label="上架结论" path="releaseConclusion">
                    <n-select
                      v-model:value="model.releaseConclusion"
                      :options="releaseConclusionList"
                      clearable
                    />
                  </n-form-item-gi>
                    <n-form-item-gi  v-if="keyList.includes('firstLevelAcceptance')" :span="12" label="一级验收方式 " path="firstLevelAcceptance">
                    <n-select
                      v-model:value="model.firstLevelAcceptance"
                      :options="firstLevelAcceptanceList"
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('secondLevelAcceptance')" :span="12" label="二级验收方式" path="secondLevelAcceptance">
                    <n-select
                      v-model:value="model.secondLevelAcceptance"
                      :options="secondLevelAcceptanceList"
                      @change="secondLevelAcceptanceaChange"
                      clearable
                      multiple
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('isFunctionEnhancements')" :span="12" label="功能增强" path="isFunctionEnhancements">
                    <n-select
                      v-model:value="model.isFunctionEnhancements"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('functionEnhancementPercentage')"
                    :span="12"
                    label="功能完备度"
                    path="functionEnhancementPercentage"
                  >
                    <n-input v-model:value="model.functionEnhancementPercentage">
                      <template #suffix> % </template>
                    </n-input>
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('isDataInheritance')" :span="12" label="数据继承" path="isDataInheritance">
                    <n-select
                      v-model:value="model.isDataInheritance"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('involvedFoldingScreen')" :span="12" label="折叠屏" path="involvedFoldingScreen">
                    <n-select
                      v-model:value="model.involvedFoldingScreen"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('pad')" :span="12" label="PAD" path="pad">
                    <n-select v-model:value="model.pad" :options="checkList" filterable clearable />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('pc')" :span="12" label="PC" path="pc">
                    <n-select v-model:value="model.pc" :options="checkList" filterable clearable />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('puraXExternalScreen')" :span="12" label="Pura X外屏" path="puraXExternalScreen">
                    <n-select
                      v-model:value="model.puraXExternalScreen"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('innovativeSpecial')" :span="12" label="创新特性" path="innovativeSpecial">
                    <n-select
                      v-model:value="model.innovativeSpecial"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('standardApp')" :span="12" label="A标应用" path="standardApp">
                    <n-select
                      v-model:value="model.standardApp"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('hwAccountLogin')" :span="12" label="华为账号登录" path="hwAccountLogin">
                    <n-select
                      v-model:value="model.hwAccountLogin"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('hwAccountOneKeyLogin')" :span="12" label="华为账号一键登录" path="hwAccountOneKeyLogin">
                    <n-select
                      v-model:value="model.hwAccountOneKeyLogin"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('permissionSecurityPicker')" :span="12" label="权限安全picker" path="permissionSecurityPicker">
                    <n-select
                      v-model:value="model.permissionSecurityPicker"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('digitalCashier')" :span="12" label="数字收银台" path="digitalCashier">
                    <n-select
                      v-model:value="model.digitalCashier"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>
                  <n-form-item-gi  v-if="keyList.includes('physicalCashier')" :span="12" label="实物收银台" path="physicalCashier">
                    <n-select
                      v-model:value="model.physicalCashier"
                      :options="checkList"
                      filterable
                      clearable
                    />
                  </n-form-item-gi>

                  <n-form-item-gi  v-if="keyList.includes('remark')" :span="12" label="备注" path="remark">
                    <n-input v-model:value="model.remark" type="textarea" clearable />
                  </n-form-item-gi>
                </n-grid>
              </n-form>
              <template #footer>
                <n-space>
                  <n-button secondary strong type="primary" @click="handleSubmint()">
                    确认
                  </n-button>
                  <n-button secondary strong type="error" @click="showListEditModal = false">
                    取消
                  </n-button>
                </n-space>
              </template>
            </n-card>
          </div>
        </n-modal>
        <!-- 添加测试记录 -->
        <n-drawer
          v-model:show="showTestModal"
          :on-after-leave="initTestModel"
          :width="502"
          placement="right"
        >
          <n-drawer-content :title="isTestAdd ? '新增测试记录' : '编辑测试记录'">
            <template #header-extra> </template>
            <n-form
              ref="formTestRef"
              :model="testModel"
              :rules="testResRules"
              label-placement="left"
              label-width="auto"
              require-mark-placement="right-hanging"
              size="medium"
              label-align="left"
            >
              <n-form-item label="转测时间" path="testStartDate">
                <n-date-picker v-model:value="testModel.testStartDate" clearable />
              </n-form-item>
              <n-form-item label="完成时间" path="testEndDate">
                <n-date-picker v-model:value="testModel.testEndDate" clearable />
              </n-form-item>
              <n-form-item label="验收人" path="userNo">
                <n-select
                  v-model:value="testModel.userNo"
                  :options="userList"
                  filterable
                  clearable
                />
              </n-form-item>
              <n-form-item label="验收结论" path="testConclusion">
                <n-select
                  v-model:value="testModel.testConclusion"
                  :options="testResList"
                  filterable
                  clearable
                />
              </n-form-item>
            </n-form>
            <template #footer>
              <n-space>
                <n-button secondary strong type="primary" @click="handleTestSubmint()">
                  确认
                </n-button>
                <n-button secondary strong type="error" @click="showTestModal = false">
                  取消
                </n-button>
              </n-space>
            </template>
          </n-drawer-content>
        </n-drawer>
      </div>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
  import { Add, Refresh, Close, GridOutline, Settings } from '@vicons/ionicons5';
  import {
    NIcon,
    NButton,
    NTag,
    useDialog,
    UploadCustomRequestOptions,
    useMessage,
  } from 'naive-ui';
  import { h, ref, reactive, onBeforeMount, onMounted,nextTick} from 'vue';
  import { useRouter } from 'vue-router';
  import {
    formatDateTime,
    getDefaultProblemHandleFilters,
    modelTemplate,
    applicationTypeList,
    isServiceList,
    checkList,
    testResModelTemplate,
    testResList,
    releaseConclusionList,
    provinceList,
    applyVerticalDomainList,
    labelList,
    functionEnhancementPriorityList,
    kcpStageList,
    specialInformationKeys,
    firstLevelAcceptanceList,
    secondLevelAcceptanceList
  } from './index';
  import { useUserStore } from '@/store/modules/user';
  import { getEmployeeList } from '@/api/system/usermanage';
  import {
    addTestRes,
    getTestRes,
    updateTestRes,
    deleteTestRes,
    getApplicationList,
    addApplication,
    updateApplication,
    exportApplication,
    deleteApplication,
    addApplicationList,
    downloadTemplate,
    getColumns,
    syncSpecial,
    importTop2k,
    getDisplayedField,
    updateDisplayedField,
  } from '@/api/dataview/applicationAcceptance';
  import { DownOutlined, UpOutlined } from '@vicons/antd';
  import { Json } from '@vicons/carbon';
  import { UserRoleEnum } from '@/enums/UserRoleEnum';
  let currentAppId = '';
  const userStore = useUserStore();
  const keyList = ref([]);
  import dayjs from 'dayjs';
  const roles = userStore.getUserInfo.roles;
  const isTestAdmin = roles.includes(UserRoleEnum.TEST_ADMIN);
  const columnList = ref([]);
  const isTestUser = roles.includes(UserRoleEnum.TEST_USER);
  const filterUserListValue = ref(
    JSON.parse(localStorage.getItem('filterUserListValue') || JSON.stringify([]))
  );

  const router = useRouter();
  const currentUser = userStore.getUserInfo;
  const isBasicAppData = ref(false);

  const filters = ref(getDefaultProblemHandleFilters());
  const collapse = ref(false);
  const dialog = useDialog();
  const message = useMessage();
  const formRef = ref();
  const formAddRef = ref();
  const formTestRef = ref();
  const fileList = ref([]);
  const fileSpecialList = ref([]);
  const userList = ref([]);
  const userMap = new Map();
  const applicationNameList = ref([]);
  const companyList = ref([]);
  const applicationPackageNameList = ref([]);
  const roundsList = ref([]);
  const firstSLAList = ref([]);
  const destList = ref([]);
  const acceptancesList = ref([]);

  const showEditModal = ref(false);
  const showAddModal = ref(false);
  const showTestModal = ref(false);
  const showListAddModal = ref(false);
  const showImportModal = ref(false);
  const showEditCols = ref(false);
  const scrollX = ref(0);
  const showListEditModal = ref(false);
  const onlyTop2k = ref(true);
  const showSpecialModal = ref(false);
  const isAdd = ref(false);
  const isTestAdd = ref(false);
  const isTransfer = ref(false);
  const isListEdit = ref(false);
  const tableData = ref([]);
  const tableTestData = ref([]);
  const model = ref({ ...modelTemplate });
  const testModel = ref({ ...testResModelTemplate });
  const rules = ref({
    applicationName: {
      required: true,
    },
    company: {
      required: true,
    },
    applicationType: {
      required: true,
    },
    provinceList: {
      required: true,
    },
  });
  /**
   * 1、验收结论不能为空
   * 2、如果验收结论是测试中，转测时间不能为空
   * 3、如果验收结论非测试中，转测时间和完成时间都不能为空
   * 4、如果验收结论非测试中，完成时间不能早于转测时间
   */
  const testResRules = ref({
    testStartDate: {
      required: true,
      validator: (rule, value) => {
        if (!value) {
          return new Error('转测时间不能为空');
        }
        return true;
      },
    },
    testEndDate: {
      validator: (rule, value) => {
        const testConclusion = testModel.value.testConclusion;
        if (testConclusion && !testConclusion.endsWith('测试中') && !value) {
          return new Error('完成时间不能为空');
        }
        const testStartDate = testModel.value.testStartDate;
        const start = typeof testStartDate === 'string' ? `${testStartDate} 00:00:00` : testStartDate;
        const end = typeof value === 'string' ? `${value} 00:00:00` : value;
        if (testStartDate && value && new Date(start).getTime() > new Date(end).getTime()) {
          return new Error('完成时间不能早于转测时间');
        }
        return true;
      },
    },
    userNo: {
      required: true,
      validator: (rule, value) => {
        if (!value) {
          return new Error('验收人不能为空');
        }
        return true;
      },
    },
    testConclusion: {
      required: true,
      validator: (rule, value) => {
        if (!value) {
          return new Error('验收结论不能为空');
        }
        return true;
      },
    },
  });
  const allColumns = [
    {
      title: '应用名称',
      key: 'applicationName',
      width: 100,
      fixed: 'left',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '应用归属公司',
      key: 'company',
      width: 100,
      resizable: true,
      fixed: 'left',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '应用形态',
      key: 'applicationType',
      width: 80,
      resizable: true,
      fixed: 'left',
    },
    {
      title: '已完成KCP阶段',
      key: 'kcpStage',
      width: 120,
      resizable: true,
    },
    {
      title: '应用层级',
      key: 'applicationPriority',
      width: 100,
      resizable: true,
    },
    {
      title: '应用垂域',
      key: 'applyVerticalDomain',
      width: 100,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '所属代表处',
      key: 'province',
      width:100,
      resizable: true,
    },
    {
      title: '功能增强优先级',
      key: 'functionEnhancementPriority',
      width: 120,
      resizable: true,
    },
    {
      title: 'DTSE责任人',
      key: 'destList',
      width: 180,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return h(
          'div',
          row.destList.map((item) => (userMap.get(item) ? userMap.get(item) : item)).join(',')
        );
      },
    },
    {
      title: '体验测试责任人',
      key: 'acceptances',
      width:140,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return h(
          'div',
          row.acceptances.map((item) => (userMap.get(item) ? userMap.get(item) : item)).join(',')
        );
      },
    },
    {
      title: '上架结论',
      key: 'releaseConclusion',
      width: 100,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '一级验收方式',
      key: 'firstLevelAcceptance',
      width: 120,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
        {
      title: '二级验收方式',
      key: 'secondLevelAcceptance',
      width: 120,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '轮次',
      key: 'rounds',
      width: 50,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return row.rounds ? row.rounds : '';
      },
    },
    {
      title: '最新验收结论',
      key: 'latestTestConclusion',
      width: 100,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '最新转测时间',
      key: 'lastTestTime',
      width: 100,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return row.lastTestTime
          ? dayjs(row.lastTestTime).format('YYYY-MM-DD')
          : '';
      },
    },
    {
      title: '最新完成时间',
      key: 'lastCompletedTime',
      width: 120,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return row.lastCompletedTime
          ? dayjs(row.lastCompletedTime).format('YYYY-MM-DD')
          : '';
      },
    },
    {
      title: '首轮转测时间',
      key: 'firstTestTime',
      width: 100,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return row.firstTestTime
          ? dayjs(row.firstTestTime).format('YYYY-MM-DD')
          : '';
      },
    },
    {
      title: '首轮完成时间',
      key: 'firstCompletedTime',
      width: 100,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return row.firstCompletedTime
          ? dayjs(row.firstCompletedTime).format('YYYY-MM-DD')
          : '';
      },
    },
    {
      title: '首轮SLA',
      key: 'firstSla',
      width: 100,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return row.firstSla ? row.firstSla : '';
      },
    },
    {
      title: '功能增强',
      key: 'isFunctionEnhancements',
      width: 100,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '功能完备度',
      key: 'functionEnhancementPercentage',
      width: 110,
      resizable: true,
      render(row) {
        return row.functionEnhancementPercentage ? row.functionEnhancementPercentage + '%' : '';
      },
    },
    {
      title: '数据继承',
      key: 'isDataInheritance',
      resizable: true,
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        const data = row.isDataInheritance || row.dataInheritanceDefault || '';
        return setSpecialInformationTH(data);
      },
    },
    {
      title: '折叠屏',
      key: 'involvedFoldingScreen',
      width: 100,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        const data = row.involvedFoldingScreen || row.foldingScreenDefault || '';
        return setSpecialInformationTH(data);
      },
    },
    {
      title: 'PAD',
      key: 'pad',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
      render(row) {
        const data = row.pad || row.padDefault || '';
        return setSpecialInformationTH(data);
      },
    },
    {
      title: 'PC',
      key: 'pc',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
      render(row) {
        const data = row.pc || row.pcDefault || '';
        return setSpecialInformationTH(data);
      },
    },
    {
      title: '创新特性',
      key: 'innovativeSpecial',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
      render(row) {
        const data = row.innovativeSpecial || row.innovativeSpecialDefault || '';
        return setSpecialInformationTH(data);
      },
    },
    {
      title: 'A标应用',
      key: 'standardApp',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
      render(row) {
        const data = row.standardApp || row.astandardAppDefault || '';
        return setSpecialInformationTH(data);
      },
    },
    {
      title: '华为账号登录',
      key: 'hwAccountLogin',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
      render(row) {
        const data = row.hwAccountLogin || row.hwAccountLoginDefault || '';
        return setSpecialInformationTH(data);
      },
    },
    {
      title: '华为账号一键登录',
      key: 'hwAccountOneKeyLogin',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
      render(row) {
        const data = row.hwAccountOneKeyLogin || row.hwAccountOneKeyLoginDefault || '';
        return setSpecialInformationTH(data);
      },
    },
    {
      title: '权限安全picker',
      key: 'permissionSecurityPicker',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
      render(row) {
        const data = row.permissionSecurityPicker || row.permissionSecurityPickerDefault || '';
        return setSpecialInformationTH(data);
      },
    },
    {
      title: '数字收银台',
      key: 'digitalCashier',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
      render(row) {
        const data = row.digitalCashier || row.digitalCashierDefault || '';
        return setSpecialInformationTH(data);
      },
    },
    {
      title: '实物收银台',
      key: 'physicalCashier',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
      render(row) {
        const data = row.physicalCashier || row.physicalCashierDefault || '';
        return setSpecialInformationTH(data);
      },
    },
    {
      title: 'Pura X外屏',
      key: 'puraXExternalScreen',
      width: 100,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '服务公司承接',
      key: 'isServiceCompanyUndertakes',
      width: 100,
    },
    {
      title: '备注',
      key: 'remark',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      resizable: true,
    },
    {
      title: '最近更新人',
      key: 'updateUserNo',
      width: 120,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return userMap.get(row.updateUserNo) ? userMap.get(row.updateUserNo) : row.updateUserNo;
      },
    },
    {
      title: '更新时间',
      key: 'updateTime',
      width: 100,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return row.updateTime
          ? dayjs(row.updateTime).format('YYYY-MM-DD')
          : '';
      },
    },
  ];
  const columns = ref([]);
  const testColumns = ref([
    {
      type: 'selection',
    },
    {
      title: '转测时间',
      key: 'testStartDate',
      width: 100,
      resizable: true,
    },
    {
      title: '完成时间',
      key: 'testEndDate',
      width: 100,
      resizable: true,
    },
    {
      title: '验收人',
      key: 'userNo',
      width: 200,
      resizable: true,
      render(row) {
        return h(
          'div',
          row.userNo
            .split(',')
            .map((item) => (userMap.get(item) ? userMap.get(item) : item))
            .join(',')
        );
      },
    },

    {
      title: '验收结论',
      key: 'testConclusion',
      width: 100,
      resizable: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 160,
      fixed: 'right',
      render(row) {
        return [
          h(
            NButton,
            {
              strong: true,
              tertiary: true,
              type: 'info',
              size: 'medium',
              style: 'margin-right:20px',
              onClick: () => {
                isTestAdd.value = false;
                showTestModal.value = true;
                testModel.value = JSON.parse(JSON.stringify(row));
              },
            },
            [h('div', '编辑')]
          ),
          h(
            NButton,
            {
              strong: true,
              tertiary: true,
              style: 'margin-right:20px',
              type: 'error',
              size: 'medium',
              onClick: () => {
                dialog.warning({
                  title: '警告',
                  content: '是否删除测试记录',
                  positiveText: '确定',
                  negativeText: '取消',

                  onPositiveClick: async () => {
                    try {
                      await deleteTestRes({
                        id: row.id,
                        updateUserNo: currentUser.account,
                      });
                      message.success('删除成功');
                      await handleGetTestRes(currentAppId);
                      await handleGetAPPList();
                    } catch {
                      message.error('删除失败');
                    }
                  },
                });
              },
            },
            [h('div', '删除')]
          ),
        ];
      },
    },
  ]);
  const checkedRowKeys = ref<Array<string | number>>([]);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 50],
    itemCount: 0,
    prefix({ itemCount }) {
      return `总条数 ${itemCount}`;
    },
    onChange: (page: number) => {
      message.loading('获取应用列表中', {
        duration: 200000,
      });
      pagination.page = page;
      handleGetAPPList();
    },
    onUpdatePageSize: (pageSize: number) => {
      message.loading('获取应用列表中', {
        duration: 200000,
      });
      pagination.pageSize = pageSize;
      pagination.page = 1;
      handleGetAPPList();
    },
  });

  const initModel = () => {
    model.value = { ...modelTemplate };
  };
  const initTestModel = () => {
    testModel.value = { ...testResModelTemplate };
  };
  //获取应用列表
  const handleGetAPPList = async () => {
    let params = {
      pageNum: pagination.page,
      pageSize: pagination.pageSize,
      app: {
        onlyTop2k: onlyTop2k.value,
        },
    };
    for (let key in filters.value) {
      if (filters.value[key]) {
        params['app'][key] = filters.value[key];
        if (key.endsWith('Time')) {
          params['app'][key][1] += 86400000;
        }
        if (key == 'applicationNameList') {
          params['app'][key] = params['app'][key].split(' ');
        }
        if (key === 'label') {
          params['app'][key] = params['app'][key].join(',');
        }
      }
    }

    let res = await getApplicationList(params);
    if (res.status == '200') {
      message.destroyAll();
      pagination.itemCount = res.data.pageInfo.total;
      checkedRowKeys.value = [];
      tableData.value = res.data.appList.map((item, index) => {
        return { ...item, key: item.id };
      });
    }
  };
  onMounted(() => {
  getDisplayedCols();
});
const getDisplayedCols = async () => {
  keyList.value=[];
  let res = await getDisplayedField();
  if (res.status == '200') {
    columnList.value = res.data;
    const comCol = [];
    const selCol = [{ type: 'selection', width: 60, fixed: 'left' }];
    const configCol = [
      {
        title: '操作',
        key: 'action',
        width: 210,
        fixed: 'right',
        render(row) {
          return [
            h(
              NButton,
              {
                strong: true,
                tertiary: true,
                type: 'info',
                size: 'small',
                style: 'margin-right:12px',
                disabled: !(isTestAdmin || isTestUser),
                onClick: async () => {
                  isAdd.value = false;
                  isListEdit.value = false;
                  showEditModal.value = true;
                  model.value = JSON.parse(JSON.stringify(row));
                  // 默认值回填
                   model.value.secondLevelAcceptance =
                    row.secondLevelAcceptance?row.secondLevelAcceptance.split(','):'';
                  model.value.isDataInheritance =
                    row.isDataInheritance || row.dataInheritanceDefault || '';
                  model.value.involvedFoldingScreen =
                    row.involvedFoldingScreen || row.foldingScreenDefault || '';
                  model.value.pad = row.pad || row.padDefault || '';
                  model.value.pc = row.pc || row.pcDefault || '';
                  model.value.innovativeSpecial =
                    row.innovativeSpecial || row.innovativeSpecialDefault || '';
                  model.value.standardApp = row.standardApp || row.astandardAppDefault || '';
                  model.value.hwAccountLogin =
                    row.hwAccountLogin || row.hwAccountLoginDefault || '';
                  model.value.hwAccountOneKeyLogin =
                    row.hwAccountOneKeyLogin || row.hwAccountOneKeyLoginDefault || '';
                  model.value.permissionSecurityPicker =
                    row.permissionSecurityPicker || row.permissionSecurityPickerDefault || '';
                  model.value.digitalCashier =
                    row.digitalCashier || row.digitalCashierDefault || '';
                  model.value.physicalCashier =
                    row.physicalCashier || row.physicalCashierDefault || '';
                  currentAppId = row.id;
                  handleGetTestRes(row.id);
                },
              },
              [h('div', '编辑')]
            ),
            h(
              NButton,
              {
                strong: true,
                tertiary: true,
                style: 'margin-right:12px',
                type: 'error',
                size: 'small',
                disabled: !isTestAdmin,
                onClick: () => {
                  dialog.warning({
                    title: '警告',
                    content: '是否删除应用',
                    positiveText: '确定',
                    negativeText: '取消',

                    onPositiveClick: async () => {
                      try {
                        await deleteApplication({
                          id: row.id,
                        });
                        message.success('删除成功');
                        let index = checkedRowKeys.value.indexOf(row.id);
                        if (index > -1) {
                          checkedRowKeys.value.splice(index, 1);
                        }
                        await handleGetAPPList();
                      } catch {
                        message.error('删除失败');
                      }
                    },
                  });
                },
              },
              [h('div', '删除')]
            ),
            h(
              NButton,
              {
                size: 'small',
                secondary: true,
                type: 'warning', // 添加主要颜色样式
                onClick: () => {
                  router.push({
                    name: 'TestAppdetail',
                    query: {
                      appName: row.applicationName,
                      appType: row.applicationType,
                      company: row.company,
                    },
                  });
                },
              },
              { default: () => '查看详情' }
            ),
          ];
        },
      },
    ];   
    allColumns.forEach((item) => {
      if (res.data.includes(item.title)) {
        comCol.push(item);
        keyList.value.push(item.key)
      }
    });
    columns.value = selCol.concat(comCol).concat(configCol);

    scrollX.value = columns.value.length * 120;
  }
};
// 编辑表格列
const handleUpdateDisplayedField = async () => {
  let cols = [];
  allColumns.forEach((item) => {
    if (columnList.value.includes(item.title)) {
      cols.push(item.title);
    }
  });
  let res = await updateDisplayedField(cols);
  if (res.status == '200') {
    message.success('更新成功！');
    nextTick(() => {
      getDisplayedCols();
    });
    showEditCols.value = false;
  } else {
    message.error('更新失败！');
  }
};
const handleUpdateFilter = () => {
    pagination.page = 1;
    handleGetAPPList();
  };
  // 多选逻辑
  const onUpdateChecked = (
    keys: Array<string | number>,
    rows: object[],
    meta: { row: object | undefined; action: 'check' | 'uncheck' | 'checkAll' | 'uncheckAll' }
  ) => {
    checkedRowKeys.value = keys;
  };
  const handleBatchDelete = () => {
    dialog.warning({
      title: '警告',
      content: '请先确认应用是否删除',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          message.loading('删除中');
          for (let index = 0; index < checkedRowKeys.value.length; index++) {
            const id = checkedRowKeys.value[index];
            await deleteApplication({
              id,
            });
          }
          message.destroyAll();
          message.success('删除成功');
          await handleGetAPPList();
          checkedRowKeys.value = [];
        } catch {
          message.error('删除失败');
        }
      },
    });
  };
  //提交处理应用
  const handleSubmint = async () => {
    if (!isListEdit.value) {
      if (isAdd.value) {
        formAddRef.value.validate(async (errors) => {
          if (!errors) {
            let data = {
              ...JSON.parse(JSON.stringify(model.value)),
              updateUserNo: currentUser.account,
            };
            delete data.label;
            delete data.applyVerticalDomainList;
            var res = await addApplication(data);
            if (res.status == '200') {
              message.success('提交成功');
              await handleGetAPPList();
              await handleGetColumns();
              showAddModal.value = false;
            }
          }
        });
      } else {
        formRef.value.validate(async (errors) => {
          if (!errors) {
            let data = {
              ...JSON.parse(JSON.stringify(model.value)),
              updateUserNo: currentUser.account,
              secondLevelAcceptance: model.value.secondLevelAcceptance?.length ? model.value.secondLevelAcceptance.join(',') : null,
              functionEnhancementPercentage: model.value.functionEnhancementPercentage
                ? model.value.functionEnhancementPercentage
                : 0,
            };
            // 处理专项信息值
            data = specialInformationValue({ ...data });
            var res = await updateApplication(data);
            if (res.status == '200') {
              message.success('修改成功');
              showEditModal.value = false;
              await handleGetAPPList();
              await handleGetColumns();
            }
          }
        });
      }
    } else {
      let arr = Object.keys(model.value);
      let data = {};
      arr.forEach((key) => {
        if (
          typeof model.value[key] == 'object' &&
          model.value[key] != null &&
          model.value[key].length == 0
        ) {
        } else {
          if (model.value[key]) {
            data[key] = model.value[key];
          }
        }
      });
      message.loading('修改中');
       // 默认的筛选去掉
      delete data.label;
      delete data.applyVerticalDomainList;
      for (let index = 0; index < checkedRowKeys.value.length; index++) {
        const id = checkedRowKeys.value[index];
        await updateApplication({ ...data, id, updateUserNo: currentUser.account });
      }
      await handleGetAPPList();
      await handleGetColumns();
      showListEditModal.value = false;
      message.destroyAll();
    }
  };
  const handleExportProblem = () => {
    let params = {onlyTop2k: onlyTop2k.value,};
    for (let key in filters.value) {
      if (filters.value[key]) {
        params[key] = filters.value[key];
        if (key == 'applicationNameList') {
          params[key] = params[key].split(' ');
        }
        if (key === 'label') {
          params[key] = params[key].join(',');
        }
      }
    }
    exportApplication(params);
  };
  const handlequeryUserList = async () => {
    let { data } = await getEmployeeList();
    userList.value = data.map((item) => {
      userMap.set(item.account, item.userName + '/' + item.account);
      return {
        label: item.userName + '/' + item.account,
        value: item.account,
      };
    });
    handleGetAPPList();
  };

  const handleUpdateFiltetableTestDatar = () => {
    pagination.page = 1;
    handleGetAPPList();
  };
  const handleResetFilter = () => {
    filters.value = getDefaultProblemHandleFilters();
    handleGetAPPList();
  };
  const unfoldToggle = () => {
    collapse.value = !collapse.value;
  };
  const handleGetTestRes = async (id) => {
    let res = await getTestRes({
      appId: id,
      appIdList: '',
    });
    if (res.status == '200') {
      tableTestData.value = res.data;
    }
  };
  //提交测试结论
  const handleTestSubmint = async () => {
    formTestRef.value.validate(async (errors) => {
      if (!errors) {
        if (!isListEdit.value) {
          let data = { ...testModel.value, appId: currentAppId, updateUserNo: currentUser.account };
          if (!isTestAdd.value) {
            var res = await updateTestRes(data);
          } else {
            var res = await addTestRes(data);
          }
          if (res.status == '200') {
            message.success('提交成功');
            showTestModal.value = false;
            await handleGetTestRes(currentAppId);
            await handleGetAPPList();
            await handleGetColumns();
          }
        } else {
          if (isTestAdd.value) {
            let data = { ...testModel.value, updateUserNo: currentUser.account };
            message.loading('添加中');
            for (let index = 0; index < checkedRowKeys.value.length; index++) {
              const id = checkedRowKeys.value[index];
              await addTestRes({ ...data, appId: id });
            }
            message.destroyAll();
            message.success('添加成功');
          } else {
            let { data } = await getTestRes({
              appIdList: checkedRowKeys.value.join(','),
              appId: 0,
            });
            message.loading('编辑中');
            for (let index = 0; index < data.length; index++) {
              const item = data[index];
              await updateTestRes({
                ...testModel.value,
                id: item.id,
                updateUserNo: currentUser.account,
              });
            }
            message.destroyAll();
            message.success('编辑成功');
          }
          showTestModal.value = false;
          await handleGetAPPList();
          await handleGetTestRes(checkedRowKeys.value[0]);
          await handleGetColumns();
        }
      }
    });
  };
  //下载
  const handleDownload = async () => {
    downloadTemplate()
      .then((res) => {
        if (res) {
        } else {
          message.error('下载失败！');
        }
      })
      .catch((e) => {
        message.error(`下载失败，原因：${e?.response?.data?.error}`);
      });
  };
  //上传
  const customRequest = async ({
    file,
    data,
    headers,
    withCredentials,
    action,
    onFinish,
    onError,
    onProgress,
  }: UploadCustomRequestOptions) => {
    const formData = new FormData();
    formData.append('file', file.file as File);
    //上传接口
    try {
      let res = await addApplicationList(formData, isBasicAppData.value);
      if (res.status == '200') {
        showListAddModal.value = false;
        handleGetAPPList();
        handleGetColumns();
        message.success('导入成功');
      } else {
        fileList.value = [];
      }
    } catch (err) {
      fileList.value = [];
      onError();
    }
  };
  const importTop = async ({ file, onError }: any) => {
  const formData = new FormData();
  formData.append('file', file.file as File);

  //上传接口
  try {
    let res = await importTop2k(formData);
    if (res.status == '200') {
      showImportModal.value = false;
      handleGetAPPList();
      handleGetColumns();
      message.success('同步成功');
    } else {
      fileList.value = [];
    }
  } catch (err) {
    fileList.value = [];
    onError();
  }
};
  //同步专项信息
  const customSpecialRequest = async ({
    file,
    data,
    headers,
    withCredentials,
    action,
    onFinish,
    onError,
    onProgress,
  }: UploadCustomRequestOptions) => {
    const formData = new FormData();
    formData.append('file', file.file as File);
    //上传接口
    try {
      let res = await syncSpecial(formData);
      if (res.status == '200') {
        showSpecialModal.value = false;
        handleGetAPPList();
        handleGetColumns();
        message.success('同步');
      } else {
        fileList.value = [];
      }
    } catch (err) {
      fileSpecialList.value = [];
      onError();
    }
  };

  const handleGetColumns = async () => {
    let { data } = await getColumns();
    applicationNameList.value = data['application_name'].map((item) => {
      return {
        label: item,
        value: item,
      };
    });
    companyList.value = data['company'].map((item) => {
      return {
        label: item,
        value: item,
      };
    });
    firstSLAList.value = data['first_sla']
      .sort((a, b) => a - b)
      .map((item) => {
        return {
          label: item,
          value: item,
        };
      });
    roundsList.value = data['rounds']
      .sort((a, b) => a - b)
      .map((item) => {
        return {
          label: item,
          value: item,
        };
      });

    destList.value = data['dest_owner'].map((item) => {
      return {
        label: item
          .split(',')
          .map((item) => (userMap.get(item) ? userMap.get(item) : item))
          .join(','),
        value: item,
      };
    });
    acceptancesList.value = data['acceptance_owner'].map((item) => {
      return {
        label: item
          .split(',')
          .map((item) => (userMap.get(item) ? userMap.get(item) : item))
          .join(','),
        value: item,
      };
    });
  };
  //批量编辑按钮事件
  const handleEditList = () => {
    isListEdit.value = true;
    showListEditModal.value = true;
    handleGetTestRes(checkedRowKeys.value[0]);
  };
  handlequeryUserList();
  handleGetColumns();

  /**
   * 专项信息选中的值只会是通过/不通过，其它的值都是前面回填的默认值，需要置空
   */
  const specialInformationValue = (data) => {
    specialInformationKeys.forEach((v: string) => {
      if (data[v] !== '通过' && data[v] !== '不通过') {
        data[v] = '';
      }
    });
    return data;
  };

  /**
   * 专项信息字段颜色改变
   */
  const setSpecialInformationTH = (value: string) => {
    return h(
      'span',
      {
        style: value === '通过' || value === '不通过' ? '' : 'color: #999; font-style: italic;',
      },
      [h('span', value)]
    );
  };
</script>

<style lang="less" scoped>
  .n-data-table :deep(.n-data-table-th.n-data-table-th--fixed-left::after) {
    width: 0 !important;
  }
  .layout-page-header {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    .n-select {
      min-width: 250px;
    }
  }
  .unfold-icon {
    display: flex;
    align-items: center;
    height: 100%;
    margin-left: -3px;
  }
</style>
