<template>
  <div ref="lineChartDom" id="lineChartDom" class="chart-container"></div>
</template>

<script lang="ts" setup>
import { onMounted } from "vue"
import * as echarts from "echarts"

let { title, yData, xData } = defineProps(['title', 'xData', 'yData'])

let chart

onMounted(() => {
  initChart(title, xData, yData)
  resizeChart()
})

const resizeChart = () => {
  window.addEventListener('resize', () => {
    if (chart) {
      chart.resize()
    }

  })
}

// 基础配置一下Echarts
function initChart(title: string, xData: string[], yData: number[]) {
  chart = echarts.init(document.getElementById("lineChartDom"));
  // 把配置和数据放这里
  chart.setOption({
    title: {
      text: title,
      x: 'center',
      y: 10
    },

    xAxis: {
      type: "category",
      data: xData
    },
    tooltip: {
      trigger: "axis"
    },
    yAxis: {
      type: 'value',
      // name: '代码数量',
      min: 1165,
      max: 1210,
      // interval: 50,
      axisLabel: {
        formatter: '{value}'
      }
    },
    series: [
      {
        data: yData,
        type: "line",
        smooth: true
      }
    ]
  });
}


</script>

<style scoped>
.fut-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
  width: 100%;
  justify-content: space-between;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
