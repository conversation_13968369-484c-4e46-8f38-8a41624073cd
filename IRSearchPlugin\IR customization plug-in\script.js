$(document).ready(() => {
  try {
    let irNumber = location.pathname.match(/(\d+)/)?.[0];
    let plugIgnoreList =
      localStorage.getItem("customeIRPlugIgnoreList")?.split(",") || [];
    let current = document.querySelector(
      'div[nzpopconfirmoverlayclassname="langBoxPop"]'
    )?.innerText;
    let searchText = "";
    let levelList = [];

    marked.setOptions({
      breaks: true,
    });

    window.addEventListener("issueInfoChange", async (e) => {
      this.__issueInfo__ = e.detail;
    });

    window.addEventListener("onAssignIssue", async (e) => {
      try {
        let {
          reqJson,
          currentUser,
          issueHistoryList,
          issueInternalMsgList,
          issueInfo,
        } = e.detail;
        let irNumber = reqJson?.issueID;
        let assignType = reqJson?.assignType;
        let nextHandler = reqJson?.nextHandler;
        let selectedUserType = reqJson?.selectedUserType;
        let levelConfig = {
          1: "L1",
          2: "L2",
          3: "L3",
        };
        let currentL2FlagConfig = {
          0: "L1",
          1: "L2",
          3: "L3",
        };

        let formatLevel = (name, level) => {
          if ((name.startsWith("【Committer")||name.startsWith("【L2.5") )&& level === "L2") {
            return "L2.5";
          }
          return level;
        };
        let statusConfig = {
          0: "草稿",
          1: "待审核",
          3: "处理中",
          4: "已接纳",
          5: "不接纳",
          6: "已解决",
          9: "待验证",
          "-1": "已驳回",
        };

        // 当前处理人
        let sourcePerson = `${issueInfo.issue.currentHandlerCompanyName || ""}${
          issueInfo.issue.currentHandlerCompanyName &&
          issueInfo.issue.currentHandlerAliasName
            ? "-"
            : ""
        }${issueInfo.issue.currentHandlerAliasName || ""}${
          issueInfo.issue?.currentHandlerW3Account
            ? ` ${issueInfo.issue?.currentHandlerW3Account}`
            : ""
        }`;
        let sourceLevel =
          currentL2FlagConfig?.[issueInfo.issue.currentL2Flag] || "";
        sourceLevel = formatLevel(sourcePerson, sourceLevel);

        // 处理人
        let targetLevel = levelConfig?.[assignType] || "";
        let user = e.detail[`${targetLevel}UserInfoList`]?.filter(
          (item) => item.userID === nextHandler
        )?.[0];
        let targetPerson = `${user?.aliasName || ""}${
          user?.w3Account ? ` ${user?.w3Account}` : ""
        }`;
        targetLevel = formatLevel(targetPerson, targetLevel);

        // 操作人
        let operatePerson = `${currentUser.aliasName}${
          currentUser?.w3Account ? ` ${currentUser?.w3Account}` : ""
        }`;
        let operateLevel = levelConfig?.[selectedUserType]
          ? levelConfig?.[selectedUserType]
          : selectedUserType;
        operateLevel = formatLevel(operatePerson, operateLevel);

        let review = "";
        if (sourceLevel === "L2.5") {
          let index = issueHistoryList?.findLastIndex((item) => {
            return item?.issueHistory?.nextHandlerName;
          });
          let time = issueHistoryList?.[index]?.createTime;

          reviewList =
            issueInternalMsgList
              ?.filter((item) => {
                return (
                  new Date(item.createTime) > new Date(time) &&
                  issueInfo.issue?.currentHandlerW3Account === item.w3Account
                );
              })
              ?.map((item) => item.msgContent) || "";
          review = JSON.stringify(reviewList);
        }
        if (issueInfo?.issue?.isSubmittedByHWTest === 1) {
          return;
        }
        chrome.runtime.sendMessage(
          {
            action: "getVersion",
            domain: ".huawei.com",
          },
          async (res) => {
            try {
              let param = {
                irNumber: irNumber,
                sourcePerson, // 当前处理人
                sourceLevel, // 当前处理人级别
                targetPerson, // 下一级处理人
                targetLevel, // 下一级处理人级别
                operatePerson, // 操作人
                operateLevel, // 操作人级别
                updateTime: new Date().toLocaleString().replaceAll("/", "-"), // 操作时间
                review, // L2.5评论
                irStatus:
                  statusConfig?.[reqJson?.status || issueInfo?.issue?.status] ||
                  null, // ir单状态
                version: res.version,
              };
              await issueTransferRecord(param);
              let version = await getVersion();
              if (version && res.version && res.version < version) {
                if (confirm("插件版本已更新，点击确定前往下载最新版本")) {
                  window.open(
                    "https://3ms.huawei.com/km/groups/3958469/blogs/details/16767826?l=zh-cn"
                  );
                }
              }
            } catch (error) {
              console.log(error);
            }
          }
        );
      } catch (error) {}
    });

    let getVersion = async () => {
      let res = await fetch(
        "https://dtse.cbg.huawei.com/board/IRTransport/queryIRPluginVersion",
        {
          method: "post",
          headers: {
            "Content-Type": "application/json",
          },
        }
      ).catch((e) => {
        console.log(e);
      });
      let version = (await res.json())?.data;
      return version;
    };
    let getLeverLIST = async () => {
      let res = await getUserInfo();
      levelList = [];
      if (res.success) {
        res.data.map((item) => {
          if (item.userLevel === 2) {
            const list = `${item.userNo} ${item.userName}`;
            levelList.push({ label: list, value: item.userNo });
          }
        });
      }
    };
    let issueTransferRecord = (param) => {
      return fetch("https://dtse.cbg.huawei.com/board/IRTransport/record", {
        method: "post",
        headers: {
          "Content-Type": "application/json",
        },
        body: param ? JSON.stringify(param) : null,
      }).catch(() => {
        if (confirm("转单记录上报失败,点击确定前往查阅注意事项")) {
          window.open(
            "https://3ms.huawei.com/km/groups/3958469/blogs/details/16767826?l=zh-cn"
          );
        }
      });
    };

    let customPost = (url, param, customHeader = {}, include = false) => {
      return new Promise((resolve, reject) => {
        fetch("http://7.190.6.209:8080/knowledge-admin" + url, {
          method: "post",
          credentials: include ? "include" : "same-origin",
          headers: {
            "Content-Type": "application/json",
            sessionid: localStorage.getItem("customeIRPlugSessionID"),
            ...customHeader,
          },
          body: param ? JSON.stringify(param) : null,
        })
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            resolve(err);
          });
      }).catch((e) => {
        console.log(e);
      });
    };

    let isNeedLogin = async () => {
      let sessionId = localStorage.getItem("customeIRPlugSessionID");
      if (sessionId) {
        try {
          let res = await checkLogin(sessionId);
          return res.status !== 200;
        } catch (error) {
          return true;
        }
      } else {
        return true;
      }
    };

    let checkLogin = (sessionId) => {
      return customPost("/auth/check", null, { sessionid: sessionId }, true);
    };

    let updateType = (bizCode, content, updateType) => {
      return customPost(
        "/knowledge/content/correction",
        {
          bizCode,
          content,
          updateType,
        },
        {},
        true
      );
    };
    let optimizevAdd = (
      irNumber,
      optimizeType,
      problemNo,
      processNo,
      reasonDesc,
      reasonType,
      source,
      urgency
    ) => {
      return customPost(
        "/knowledge/optimize/v2/add",
        {
          irNumber,
          optimizeType,
          problemNo,
          processNo,
          reasonDesc,
          reasonType,
          source,
          urgency,
        },
        {},
        true
      );
    };

    let login = (user, password) => {
      return customPost(
        "/auth/login",
        {
          userNo: user,
          password: password,
        },
        {},
        true
      );
    };

    let search = async (val, pageNumber, checkboxVal) => {
      let isDTS = location.href.includes("/DTSPortal");
      let DTSNum = location.href.match(/.*\/(DTS\d+)\?*.*/)?.[1] || null;
      searchText = val;
      return customPost(
        "/knowledge/search",
        {
          key: val || " ",
          page: pageNumber,
          sourceList: checkboxVal,
          size: 10,
          statusList: null,
          engineList: ["local"],
          from: isDTS ? "DTS" : "ir",
          fromIrNumber: isDTS ? DTSNum : irNumber,
          fromLevel: isDTS
            ? "L1"
            : document.querySelector(
                'div[nzpopconfirmoverlayclassname="langBoxPop"]'
              )?.innerText === "一级运营"
            ? "L1"
            : "L2",
        },
        {},
        true
      );
    };

    let feedback = async (val, item, level, sortNum, list = []) => {
      let logs = [];
      if (item === null) {
        logs = list.map((item, index) => {
          return {
            level: level,
            title: item.title,
            bizCode: item.id,
            sortNum: sortNum + index + 1,
            url: item.url,
            engin: item.engin,
            score: item.score,
          };
        });
      } else {
        logs = [
          {
            level: level,
            title: item.title,
            bizCode: item.id,
            sortNum: sortNum,
            url: item.url,
            engin: item.engin,
            score: item.score,
          },
        ];
      }
      return customPost(
        "/knowledge/log/update",
        {
          key: val || " ",
          logs,
        },
        {},
        true
      );
    };

    let confirmLogin = async () => {
      let user = document
        .querySelector("#costomization-plug-in-login-user")
        ?.value?.trim();
      let password = document
        .querySelector("#costomization-plug-in-login-password")
        ?.value?.trim();

      if (!user || !password) {
        alert("请检查账号和密码");
        return;
      }
      try {
        let res = await login(user, password);
        res.json().then((res) => {
          if (res.success) {
            localStorage.setItem("customeIRPlugSessionID", res.data.sessionId);
            document.body.removeChild(
              document.querySelector("#costomization-plug-in-login-container")
            );
            if (document.querySelector("#costomization-plug-in-container")) {
              document.querySelector(
                "#costomization-plug-in-container"
              ).style.display = "block";
              return;
            }
            popup();
          } else {
            let msg = "";
            if (res.code === "400") {
              msg = "知识库登录失败，请确认密码是否正确";
            } else if (res.code === "B00003") {
              msg = "账号不正确，请联系管理员（谌嘉-30030812）确认是否已有账号";
            }
            alert(msg || res.msg || "请确认工号与密码是否正确");
          }
        });
      } catch {
        alert("操作失败，请参考插件根目录下Readme内注意事项配置浏览器选项");
      }
    };

    let enterLogin = (event) => {
      if (event.keyCode === 13) {
        document
          .querySelector("#costomization-plug-in-login-btn")
          .dispatchEvent(new Event("click"));
      }
    };

    let showLogin = () => {
      if (document.querySelector("#costomization-plug-in-container")) {
        document.querySelector(
          "#costomization-plug-in-container"
        ).style.display = "none";
      }
      let currentUserEmployeeID =
        document.querySelectorAll("#nickNameBox .nickNameValue span")?.[1]
          ?.innerText ||
        document.querySelectorAll(".custom-header .avatar a")?.[0]?.innerText ||
        "";

      let loginHtml = `
        <div style="position: fixed;left: 0;top: 0;z-index: 10000;width: 100vw;height: 100vh;background-color:rgba(0,0,0,.45)"></div>
        <div id="login"
            class='custom-translate'
            style="position: fixed;left: 50%;top: 50%;z-index: 10001;width: 450px;min-height: 200px;max-height: 100vh;overflow-y:auto;padding: 20px; border-radius: 8px;background-color: rgb(255, 255, 255);box-shadow: rgba(0, 0, 4, 0.4) 0px 1px 6px;display: block;">
            <div style="margin-bottom: 20px;">
                <div style="width: 100%;font-size: 22px;display: flex;align-items: center;justify-content: start;margin-bottom: 20px;">
                    <span style="width:100%;text-align: center;">知识库登录</span>
                    <svg id="costomization-plug-in-login-container-cancel" t="1733111927000" style="cursor: pointer;"
                        viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2429" width="20"
                        height="20">
                        <path d="M522.689679 455.703011 897.496052 830.509388C915.99391 849.007246 915.99391 878.9982 897.496052 897.496059 878.998193 915.993917 849.007239 915.993917 830.509381 897.496059L455.703008 522.689682 80.896635 897.496056C62.398777 915.993914 32.407822 915.993914 13.909964 897.496056-4.587894 878.998198-4.587894 849.007243 13.909964 830.509385L388.716338 455.703011 13.909966 80.896635C-4.587892 62.398776-4.587892 32.407822 13.909967 13.909964 32.407825-4.587895 62.398779-4.587894 80.896638 13.909964L455.70301 388.71634 830.509384 13.909966C849.007242-4.587892 878.998196-4.587892 897.496054 13.909966 915.993913 32.407825 915.993913 62.398779 897.496054 80.896637L522.689679 455.703011Z" fill="#000000" p-id="2430"></path>
                    </svg>
                </div>
                <form id="costomization-plug-in-login-container-form">
                    <div style="display: flex;justify-content: center;">
                        <div style="display: flex;justify-content: center;align-items:center;width: 400px;">
                            <label style="width: 100px;" for="costomization-plug-in-login-user">工号</label><input value="${
                              currentUserEmployeeID || ""
                            }" autocomplete="false" id='costomization-plug-in-login-user' placeholder='w600xxxxx' type="text" style=" 
                            box-sizing: border-box;
                            margin: 0;
                            padding: 4px 11px;
                            color: rgba(0, 0, 0, 0.88);
                            font-size: 14px;
                            line-height: 1.5714285714285714;
                            list-style: none;
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
                            position: relative;
                            display: inline-block;
                            width: 100%;
                            min-width: 0;
                            border-radius: 6px;
                            transition: all 0.2s;
                            background: #ffffff;
                            border-width: 1px;
                            border-style: solid;
                            border-color: #d9d9d9;
                            ">
                        </div>
                    </div>

                    <div style="display: flex;justify-content: center;margin-top: 20px;">
                        <div style="display: flex;justify-content: center;align-items:center;width: 400px;">
                            <label style="width: 100px;" for="costomization-plug-in-login-password">密码</label><input autocomplete="false" id='costomization-plug-in-login-password' placeholder='默认123456' type="password" style="    
                            box-sizing: border-box;
                            margin: 0;
                            padding: 4px 11px;
                            color: rgba(0, 0, 0, 0.88);
                            font-size: 14px;
                            line-height: 1.5714285714285714;
                            list-style: none;
                            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
                            position: relative;
                            display: inline-block;
                            width: 100%;
                            min-width: 0;
                            border-radius: 6px;
                            transition: all 0.2s;
                            background: #ffffff;
                            border-width: 1px;
                            border-style: solid;
                            border-color: #d9d9d9;
                            ">
                        </div>
                    </div>
                    <div style="display: flex;justify-content: center;margin-top: 20px;">
                        <button 
                            id='costomization-plug-in-login-btn'
                            style=" 
                            background: #4096ff;
                            color: #fff;
                            width: 400px;    box-sizing: border-box;
                            outline: none;
                            position: relative;
                            display: inline-flex;
                            gap: 8px;
                            align-items: center;
                            justify-content: center;
                            font-weight: 400;
                            white-space: nowrap;
                            text-align: center;
                            background-image: none;
                            border: 1px solid transparent;
                            cursor: pointer;
                            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
                            user-select: none;
                            touch-action: manipulation;
                            font-size: 14px;
                            line-height: 1.5714285714285714;
                            height: 40px;
                            padding: 4px 15px;
                            border-radius: 6px;
                    "><span>登录账号</span></button>
                    </div>
                </form>
            </div>
        </div>`;

      let loginContainer = document.createElement("div");
      loginContainer.id = "costomization-plug-in-login-container";
      loginContainer.innerHTML = loginHtml;
      document.body.appendChild(loginContainer);
      let confirmBtn = document.querySelector(
        "#costomization-plug-in-login-btn"
      );

      confirmBtn.addEventListener("click", confirmLogin);
      confirmBtn.addEventListener("keyup", enterLogin);
      document
        .querySelector("#costomization-plug-in-login-user")
        .addEventListener("keyup", enterLogin);
      document
        .querySelector("#costomization-plug-in-login-password")
        .addEventListener("keyup", enterLogin);
      document
        .querySelector("#costomization-plug-in-login-container-cancel")
        .addEventListener("click", () => {
          document.body.removeChild(loginContainer);
        });
      document
        .querySelector("#costomization-plug-in-login-container-form")
        .addEventListener("submit", (e) => {
          e.preventDefault();
        });
    };

    let editAnswer = async (item) => {
      let currentUserEmployeeID =
        document.querySelectorAll("#nickNameBox .nickNameValue span")?.[1]
          ?.innerText ||
        document.querySelectorAll(".custom-header .avatar a")?.[0]?.innerText ||
        "";
      let reasonDesc = $(
        'textarea[name="costomization-plug-in-modal-detail-edit-textarea"]'
      ).val();
      let optimizeType = $(
        'input[name="costomization-plug-in-modal-detail-edit-radio-group-optimizeType"]:checked'
      ).val();
      var selectElement = document.getElementById(
        "costomization-plug-in-wish-l2"
      );
      let proVal = selectElement.value
      let processNo = optimizeType === "1" ? proVal : currentUserEmployeeID;
      let reasonType = $(
        'input[name="costomization-plug-in-modal-detail-edit-radio-group-reasonType"]:checked'
      ).val();
      let urgency = Number(
        $(
          'input[name="costomization-plug-in-modal-detail-edit-radio-group-urgency"]:checked'
        ).val()
      );
      if (!!reasonDesc) {
        let res = await optimizevAdd(
          item.id,
          optimizeType,
          item.id,
          processNo,
          reasonDesc,
          reasonType,
          0,
          urgency
        );
        res.json().then((res) => {
          if (res.success) {
            alert("太棒啦！感谢您的反馈，流程评审中~");
            $(
              "#costomization-plug-in-modal-detail-edit-container-showEdit"
            ).show();
            $("#costomization-plug-in-modal-detail-edit-container").hide();
          } else {
            alert(res.msg || "反馈失败");
          }
        });
      }
    };

    let showDetail = (item, index) => {
      let modal = document.createElement("div");
      let classificationList = [];
      getLeverLIST();
      item?.firstClassification &&
        classificationList.push(item?.firstClassification);
      item?.secondClassification &&
        classificationList.push(item?.secondClassification);
      item?.classification && classificationList.push(item?.classification);
      modal.innerHTML = `
        <div class='custom-translate' id="costomization-plug-in-modal-detail" style="position: fixed;left: 50%;top: 50%;z-index: 10002;min-height:200px;width: calc(100vw - 40px);min-height: 200px;height: calc(100vh - 40px);overflow-y:auto;padding: 20px; padding: 20px; border-radius: 8px;background-color: rgb(255, 255, 255);box-shadow: rgba(0, 0, 4, 0.4) 0px 1px 6px;display: block;">
            <div style="width: 100%;font-size: 22px;display: flex;align-items: center;justify-content: start;margin-bottom: 20px;">
                <svg id="costomization-plug-in-modal-detail-cancel" style="cursor: pointer;" t="1733801049339" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2432" width="20" height="20"><path d="M398.700049 515.25616L752.123404 161.832805c21.687952-21.687952 21.687952-56.850799 0-78.53875-21.687952-21.687952-56.850799-21.687952-78.53875 0L280.892947 475.986785c-21.687952 21.687952-21.687952 56.850799 0 78.538751L673.584654 947.218266c21.687952 21.687952 56.850799 21.687952 78.53875 0 21.687952-21.687952 21.687952-56.850799 0-78.538751L398.700049 515.25616z" p-id="2433"></path></svg>
                <span style="width:100%;text-align: center;">知识详情</span>
            </div>
            <div id="costomization-plug-in-container-detail-container" style="max-height: calc(100% - 60px);overflow-y: auto;}">
                <div style=" padding: 16px 0px;" id="costomization-plug-in-container-detail-answer-container">
                    <div style="width: 100%;">
                    <div style="display:flex;justify-content:space-between">
                        <div  style="display:flex;font-size: 18px;font-weight: 700; margin-bottom: 10px; color: #333333;">${
                          item?.url
                            ? `<a target="_blank" href='${item?.url}'>${
                                item?.highlight_title || item?.title
                              }</a>`
                            : item?.highlight_title || item?.title
                        }  
                        </div>
                        <a  style="font-size: 18px;font-weight: 700; margin-right:30px" id="costomization-plug-in-modal-detail-text">获取知识链接</a>
                        </div>
                        <div style="font-size: 14px; color: #777;padding: 0 10px;">${
                          classificationList?.length
                            ? `<span  style="margin-left: -10px">【分类】</span>${classificationList.join(
                                "&gt;"
                              )}`
                            : ""
                        }                      
                    </div>
                        ${
                          item?.description
                            ? `<div style="font-size: 14px; width: 100% margin-top: 15px; color: #777;padding: 0 10px;">
                            ${
                              item?.description
                                ? `${
                                    item?.engin === "develop"
                                      ? ""
                                      : `<span style="margin-left: -10px">【问题描述】</span>` +
                                        item?.description.replaceAll(
                                          "<",
                                          "&lt;"
                                        )
                                  }`
                                : ""
                            }
                        </div>`
                            : ""
                        }
                        <div id="costomization-plug-in-container-detail-answer" style="font-size: 14px; color: #333; margin: 0 0 20px 0;padding: 0 10px;"></div>
                    </div>
                </div>
                <div style="width: 100%;height: 40px;display: flex;justify-content: center;height: 32px">
                    <div custom-id=${
                      item.id
                    } style="width: 100px;height: 33px;background-color: #0000000d;display: flex ;align-items: center;justify-content: space-around; border-radius: 33px;">
                      <div  custom-name='high' style="position: relative;flex: 1;text-align: center; cursor: pointer;">
                        <span role="img" aria-label="like" class="anticon anticon-like" style="font-size: 20px" custom-name='high'>
                          <svg custom-name='high' viewBox="64 64 896 896" focusable="false" data-icon="like" width="1em" height="1em" fill="currentColor" aria-hidden="true">
                            <path d="M885.9 533.7c16.8-22.2 26.1-49.4 26.1-77.7 0-44.9-25.1-87.4-65.5-111.1a67.67 67.67 0 00-34.3-9.3H572.4l6-122.9c1.4-29.7-9.1-57.9-29.5-79.4A106.62 106.62 0 00471 99.9c-52 0-98 35-111.8 85.1l-85.9 311H144c-17.7 0-32 14.3-32 32v364c0 17.7 14.3 32 32 32h601.3c9.2 0 18.2-1.8 26.5-5.4 47.6-20.3 78.3-66.8 78.3-118.4 0-12.6-1.8-25-5.4-37 16.8-22.2 26.1-49.4 26.1-77.7 0-12.6-1.8-25-5.4-37 16.8-22.2 26.1-49.4 26.1-77.7-.2-12.6-2-25.1-5.6-37.1zM184 852V568h81v284h-81zm636.4-353l-21.9 19 13.9 25.4a56.2 56.2 0 016.9 27.3c0 16.5-7.2 32.2-19.6 43l-21.9 19 13.9 25.4a56.2 56.2 0 016.9 27.3c0 16.5-7.2 32.2-19.6 43l-21.9 19 13.9 25.4a56.2 56.2 0 016.9 27.3c0 22.4-13.2 42.6-33.6 51.8H329V564.8l99.5-360.5a44.1 44.1 0 0142.2-32.3c7.6 0 15.1 2.2 21.1 6.7 9.9 7.4 15.2 18.6 14.6 30.5l-9.6 198.4h314.4C829 418.5 840 436.9 840 456c0 16.5-7.2 32.1-19.6 43z"></path>
                          </svg>
                        </span>
                      </div>
                      <div class="ant-divider css-1hpnbz2 ant-divider-vertical" role="separator"></div>
                      <div custom-name='low' style="position: relative;flex: 1;text-align: center;cursor: pointer;">
                        <span  id='costomization-plug-in-modal-detail-low-knowledge-feedback-hidden'  custom-name='low' role="img" aria-label="dislike" class="anticon anticon-dislike" style="font-size: 20px"> 
                          <svg custom-name='low' viewBox="64 64 896 896" focusable="false"  data-icon="dislike"   width="1em"   height="1em"   fill="currentColor"   aria-hidden="true" >
                            <path d="M885.9 490.3c3.6-12 5.4-24.4 5.4-37 0-28.3-9.3-55.5-26.1-77.7 3.6-12 5.4-24.4 5.4-37 0-28.3-9.3-55.5-26.1-77.7 3.6-12 5.4-24.4 5.4-37 0-51.6-30.7-98.1-78.3-118.4a66.1 66.1 0 00-26.5-5.4H144c-17.7 0-32 14.3-32 32v364c0 17.7 14.3 32 32 32h129.3l85.8 310.8C372.9 889 418.9 924 470.9 924c29.7 0 57.4-11.8 77.9-33.4 20.5-21.5 31-49.7 29.5-79.4l-6-122.9h239.9c12.1 0 23.9-3.2 34.3-9.3 40.4-23.5 65.5-66.1 65.5-111 0-28.3-9.3-55.5-26.1-77.7zM184 456V172h81v284h-81zm627.2 160.4H496.8l9.6 198.4c.6 11.9-4.7 23.1-14.6 30.5-6.1 4.5-13.6 6.8-21.1 6.7a44.28 44.28 0 01-42.2-32.3L329 459.2V172h415.4a56.85 56.85 0 0133.6 51.8c0 9.7-2.3 18.9-6.9 27.3l-13.9 25.4 21.9 19a56.76 56.76 0 0119.6 43c0 9.7-2.3 18.9-6.9 27.3l-13.9 25.4 21.9 19a56.76 56.76 0 0119.6 43c0 9.7-2.3 18.9-6.9 27.3l-14 25.5 21.9 19a56.76 56.76 0 0119.6 43c0 19.1-11 37.5-28.8 48.4z"></path>
                          </svg>
                        </span>
                        <span id='costomization-plug-in-modal-detail-low-knowledge-feedback' style="display:none;font-size: 20px" custom-name='low' role="img" aria-label="dislike"> 
                        <svg viewBox="64 64 896 896" focusable="false" data-icon="dislike" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M885.9 490.3c3.6-12 5.4-24.4 5.4-37 0-28.3-9.3-55.5-26.1-77.7 3.6-12 5.4-24.4 5.4-37 0-28.3-9.3-55.5-26.1-77.7 3.6-12 5.4-24.4 5.4-37 0-51.6-30.7-98.1-78.3-118.4a66.1 66.1 0 00-26.5-5.4H273v428h.3l85.8 310.8C372.9 889 418.9 924 470.9 924c29.7 0 57.4-11.8 77.9-33.4 20.5-21.5 31-49.7 29.5-79.4l-6-122.9h239.9c12.1 0 23.9-3.2 34.3-9.3 40.4-23.5 65.5-66.1 65.5-111 0-28.3-9.3-55.5-26.1-77.7zM112 132v364c0 17.7 14.3 32 32 32h65V100h-65c-17.7 0-32 14.3-32 32z"></path></svg>
                        </span>
                        </div>
                      </div>
                      </div>
                </div>
                <div id='costomization-plug-in-modal-detail-edit-container'>
                <form id='costomization-plug-in-modal-detail-edit-knowledge-feedback' style="display:none">
                <div style="font-size: 28px;font-weight: normal;">知识反馈</div>
                <div style="margin-top: 10px; margin-bottom:10px;display: flex;align-items: center;gap: 10px;">
                  <span style="color: red;">*</span><span style="width:110px">问题来源：</span>
                    <div style="display: flex;align-items:center;gap: 4px;">
                      <input type="radio" id="option1" checked name="costomization-plug-in-modal-detail-edit-radio-group-source" value="0"> <label for="option1">IR单</label>
                    </div>
                  </div>    
                <div style="display: flex;align-items: center;margin-top:10px; margin-bottom: 10px;gap: 10px;">
                  <span style="color: red;">*</span><span style="width:110px">紧急程度：</span>
                  <div style="display: flex;align-items: center;gap: 4px;">
                    <input type="radio" id="option2" name="costomization-plug-in-modal-detail-edit-radio-group-urgency" value="0"> <label for="option1">P0:致命</label>
                  </div>
                  <div style="display: flex;align-items: center;gap: 4px;">
                    <input type="radio" id="option2" name="costomization-plug-in-modal-detail-edit-radio-group-urgency" value="1"> <label for="option2"> P1:严重</label>
                  </div>
                  <div style="display: flex;align-items: center;gap: 4px;">
                    <input type="radio" id="option3" name="costomization-plug-in-modal-detail-edit-radio-group-urgency" value="2"> <label for="option3">P2:一般</label>
                  </div>
                  <div style="display: flex;align-items: center;gap: 4px;">
                    <input type="radio" id="option4" name="costomization-plug-in-modal-detail-edit-radio-group-urgency" value="3"> <label for="option4"> P3:提示</label>
                  </div>
                </div>             

                <div style="display: flex;align-items:center;margin-top:10px; margin-bottom:10px;gap: 10px;">
                  <span style="color: red;">*</span><span style="width:110px">不准确原因：</span>
                    <div style="display: flex;align-items: center;gap: 4px;">
                      <input type="radio" id="option1" name="costomization-plug-in-modal-detail-edit-radio-group-reasonType" value="0"> <label for="option1">内容过时</label>
                    </div>
                    <div style="display: flex;align-items: center;gap: 4px;">
                      <input type="radio" id="option2" name="costomization-plug-in-modal-detail-edit-radio-group-reasonType" value="1"> <label for="option2">内容错误</label>
                    </div>
                    <div style="display: flex;align-items: center;gap: 4px;">
                      <input type="radio" id="option3" name="costomization-plug-in-modal-detail-edit-radio-group-reasonType" value="2"> <label for="option3">内容表达不清晰</label>
                    </div>
                    <div style="display: flex;align-items: center;gap: 4px;">
                      <input type="radio" id="option4" name="costomization-plug-in-modal-detail-edit-radio-group-reasonType" value="3"> <label for="option4">内容缺失</label>
                    </div>
                    <div style="display: flex;align-items: center;gap: 4px;">
                      <input type="radio" id="option5" name="costomization-plug-in-modal-detail-edit-radio-group-reasonType" value="4"> <label for="option5">示例代码错误</label>
                    </div>
                    <div style="display: flex;align-items: center;gap: 4px;">
                      <input type="radio" id="option6" name="costomization-plug-in-modal-detail-edit-radio-group-reasonType" value="5"> <label for="option6">其他</label>
                    </div>
                </div>

                  <div style="display: flex;align-items: center;margin-top: 10px; margin-bottom: 10px;gap: 10px;">
                    <span style="color: red;">*</span><span style="width:110px">优化类型：</span>
                    <div style="display: flex;align-items: center;gap: 4px;">
                      <input type="radio" id="option1" name="costomization-plug-in-modal-detail-edit-radio-group-optimizeType" value="0"> <label for="option1">自己优化</label>
                    </div>
                    <div style="display: flex;align-items: center;gap: 4px;">
                    <input type="radio" id="option2" name="costomization-plug-in-modal-detail-edit-radio-group-optimizeType" value="1"> <label for="option2">需要他人优化</label>
                    </div>
                    <div style="display:none" id="costomization-plug-in-wish-select">
                      <div style="display: flex;align-items: center;gap: 4px;">
                      <span style="color: red;">*</span><span>L2优化人：</span>
                      <select style="width:300px;" id="costomization-plug-in-wish-l2" name="category"'></select>
                      </div>
                    </div>
                  </div>
                  
                  <div style="display: flex;align-items: center;margin-top: 10px; margin-bottom: 10px;gap: 10px;">
                    <span style="color: red;">*</span><span style="width:110px">优化原因描述：</span>
                    <textarea name="costomization-plug-in-modal-detail-edit-textarea" placeholder="请输入你的回答" style="width: 100%;min-height: 180px;vertical-align: bottom;box-sizing: border-box;margin: 10px 0 0;padding: 4px 11px;color: rgba(0, 0, 0, 0.88);font-size: 14px;list-style: none;border-radius: 6px;"></textarea>
                  </div> 



                  <div style="justify-content:center;display: flex">
                    <button id='costomization-plug-in-modal-detail-edit-cancel' class="costomization-plug-in-modal-button" >取消</button>
                    <button id='costomization-plug-in-modal-detail-edit-reiew'  class="costomization-plug-in-modal-button" disabled>提交审核</button>
                  </div>
                </form>
                </div>
               </div>
        </div>`;
      try {
        modal.addEventListener(
          "click",
          async (event) => {
            try {
              if (event.target.getAttribute("custom-name") === "high") {
                let res = await feedback(
                  searchText,
                  item,
                  event.target.getAttribute("custom-name"),
                  index
                );
                res.json().then((res) => {
                  if (res.success) {
                    plugIgnoreList.push(irNumber);
                    localStorage.setItem(
                      "customeIRPlugIgnoreList",
                      plugIgnoreList
                    );
                    document.removeEventListener(
                      "click",
                      handleDocumentClick,
                      true
                    );
                    document.querySelector(
                      "#costomization-plug-in-container"
                    ).style.display = "none";
                    document.body.removeChild(modal);
                  }
                });
              } else if (event.target.getAttribute("custom-name") === "low") {
                // 展示知识反馈
                var selectElement = document.getElementById(
                  "costomization-plug-in-wish-l2"
                );
                levelList.forEach((item) => {
                  var optionElement = document.createElement("option");
                  optionElement.value = item.value;
                  optionElement.text = item.label;
                  optionElement.style = "color: #000";
                  // 将option添加到select元素中
                  selectElement.appendChild(optionElement);
                });
                $(
                  "#costomization-plug-in-modal-detail-edit-knowledge-feedback"
                ).show();
                $(
                  "#costomization-plug-in-modal-detail-low-knowledge-feedback"
                ).show();
                $(
                  "#costomization-plug-in-modal-detail-low-knowledge-feedback-hidden"
                ).hide();
              }
            } catch (error) {
              document.body.removeChild(modal);
              showLogin();
              return;
            }
          },
          true
        );
        document.body.appendChild(modal);
        document.querySelector(
          "#costomization-plug-in-container-detail-answer"
        ).innerHTML = marked.parse(
          `${
            item?.engin === "develop"
              ? ""
              : '<span style="margin-left: -10px">【问题回复】</span>'
          }${item?.content}`
        );
        document
          .querySelector("#costomization-plug-in-modal-detail-cancel")
          .addEventListener("click", () => {
            document.body.removeChild(modal);
          });
        document
          .querySelector("#costomization-plug-in-modal-detail-text")
          .addEventListener("click", () => {
            navigator.clipboard.writeText(
              `https://dtse.cbg.huawei.com/kowledgeBase/knowledgeSearch/${item?.id}`
            );
          });
        // 详情内复制触发上报
        $("#costomization-plug-in-container-detail-answer-container").on(
          "copy",
          (e) => {
            feedback(searchText, item, "copy", index);
          }
        );
        $("#costomization-plug-in-modal-detail-edit-container-showEdit").click(
          (e) => {
            $("#costomization-plug-in-modal-detail-edit-container").show();
            $(
              'textarea[name="costomization-plug-in-modal-detail-edit-textarea"]'
            ).val(item.ownAnswer || "");
            $(
              "#costomization-plug-in-modal-detail-edit-container-showEdit"
            ).hide();
          }
        );

        $("#costomization-plug-in-modal-detail-edit-cancel").click(() => {
          $(
            "#costomization-plug-in-modal-detail-edit-knowledge-feedback"
          ).hide();
          $(
            "#costomization-plug-in-modal-detail-low-knowledge-feedback"
          ).hide();
          $(
            "#costomization-plug-in-modal-detail-low-knowledge-feedback-hidden"
          ).show();
        });
        $("#costomization-plug-in-modal-detail-edit-container").on(
          "submit",
          (e) => e.preventDefault()
        );
        $("#costomization-plug-in-modal-detail-edit-container").on(
          "input change",
          "input,textarea",
          (e) => {
            let textareaVal = $(
              'textarea[name="costomization-plug-in-modal-detail-edit-textarea"]'
            ).val();
            let radiourgencyVal = $(
              'input[name="costomization-plug-in-modal-detail-edit-radio-group-urgency"]:checked'
            ).val();
            let radioValreasonTypeVal = $(
              'input[name="costomization-plug-in-modal-detail-edit-radio-group-reasonType"]:checked'
            ).val();
            let radiooptimizeTypeVal = $(
              'input[name="costomization-plug-in-modal-detail-edit-radio-group-optimizeType"]:checked'
            ).val();
            if (radiooptimizeTypeVal === "1") {
              $("#costomization-plug-in-wish-select").show();
            } else if (radiooptimizeTypeVal === "0") {
              $("#costomization-plug-in-wish-select").hide();
            }
            if (
              radiourgencyVal &&
              radioValreasonTypeVal &&
              radiooptimizeTypeVal &&
              textareaVal
            ) {
              $("#costomization-plug-in-modal-detail-edit-reiew").attr(
                "disabled",
                false
              );
            } else {
              $("#costomization-plug-in-modal-detail-edit-reiew").attr(
                "disabled",
                true
              );
            }
          }
        );
        $("#costomization-plug-in-modal-detail-edit-reiew").on("click", () => {
          editAnswer(item);
        });
      } catch (error) {
        console.log(error);
      }
    };

    let addWish = async (param) => {
      try {
        return (
          await customPost("/knowledge/feedback", param, {}, true)
        ).json();
      } catch (error) {}
    };
    let getUserInfo = async (param) => {
      try {
        return (await customPost("/auth/search/users", {}, {}, true)).json();
      } catch (error) {}
    };

    // 心愿单弹窗
    let showWishlistModel = (search) => {
      $(document.body).append(`
        <div id="costomization-plug-in-container-add-model-container">
          <div style="position: fixed;left: 0;top: 0;z-index: 10005;width: 100vw;height: 100vh;background-color:rgba(0,0,0,.45)"></div>
          <div id="costomization-plug-in-container-add-model" class='custom-translate'>
            <div style="width: 100%;font-size: 16px;display: flex;align-items: center;justify-content: start;margin-bottom: 20px;font-weight: 700;">
              <span style="width:100%;text-align: left;">一键转心愿单</span>
              <svg id="costomization-plug-in-container-add-model-cancel" t="1733111927000" style="cursor: pointer;"
                  viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2429" width="20"
                  height="20">
                  <path d="M522.689679 455.703011 897.496052 830.509388C915.99391 849.007246 915.99391 878.9982 897.496052 897.496059 878.998193 915.993917 849.007239 915.993917 830.509381 897.496059L455.703008 522.689682 80.896635 897.496056C62.398777 915.993914 32.407822 915.993914 13.909964 897.496056-4.587894 878.998198-4.587894 849.007243 13.909964 830.509385L388.716338 455.703011 13.909966 80.896635C-4.587892 62.398776-4.587892 32.407822 13.909967 13.909964 32.407825-4.587895 62.398779-4.587894 80.896638 13.909964L455.70301 388.71634 830.509384 13.909966C849.007242-4.587892 878.998196-4.587892 897.496054 13.909966 915.993913 32.407825 915.993913 62.398779 897.496054 80.896637L522.689679 455.703011Z" fill="#000000" p-id="2430"></path>
              </svg>
            </div>
            <form id="costomization-plug-in-container-add-model-form">
              <label class="costomization-plug-in-container-add-model-form-item-radio" for="costomization-plug-in-wish-radio"> <input type="radio" id="costomization-plug-in-wish-radio" checked name="radio" value="新增知识需求"> 新增知识需求</label>
              <label class="costomization-plug-in-container-add-model-form-item" for="costomization-plug-in-wish-ir">处理中的IR单号：<input style="margin-top: 10px" autocomplete="false" name="ir" id='costomization-plug-in-wish-ir' placeholder='请填写IR单号' type="text" ></label>
              <label class="costomization-plug-in-container-add-model-form-item" for="costomization-plug-in-wish-category">知识分类：
              <div class="costomization-plug-in-wish-category-container">
                <select
                  style="margin-top: 10px;
                  id="costomization-plug-in-wish-category"
                  name="category"
                >
                  <option value="" selected disabled hidden>
                    请选择知识分类
                  </option>
                  <option style="color: #000" value="故障知识">故障知识</option>
                  <option style="color: #000" value="咨询类知识">
                    咨询类知识
                  </option>
                  <option style="color: #000" value="demo代码">demo代码</option>
                </select>
                <svg
                  class="costomization-plug-in-wish-category-reset hide"
                  id="costomization-plug-in-wish-category-reset"
                  t="1733111927000"
                  style="cursor: pointer"
                  viewBox="0 0 1024 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="2429"
                  width="10"
                  height="10"
                >
                  <path
                    d="M522.689679 455.703011 897.496052 830.509388C915.99391 849.007246 915.99391 878.9982 897.496052 897.496059 878.998193 915.993917 849.007239 915.993917 830.509381 897.496059L455.703008 522.689682 80.896635 897.496056C62.398777 915.993914 32.407822 915.993914 13.909964 897.496056-4.587894 878.998198-4.587894 849.007243 13.909964 830.509385L388.716338 455.703011 13.909966 80.896635C-4.587892 62.398776-4.587892 32.407822 13.909967 13.909964 32.407825-4.587895 62.398779-4.587894 80.896638 13.909964L455.70301 388.71634 830.509384 13.909966C849.007242-4.587892 878.998196-4.587892 897.496054 13.909966 915.993913 32.407825 915.993913 62.398779 897.496054 80.896637L522.689679 455.703011Z"
                    fill="#000000"
                    p-id="2430"
                  ></path>
                </svg>
              </div>
              </label>
              <label class="costomization-plug-in-container-add-model-form-item" for="costomization-plug-in-wish-value"><span style="color: red">* <span style="color: #000">心愿内容：</span></span><textarea style="margin-top: 10px; resize: vertical" autocomplete="false" id='costomization-plug-in-wish-value' placeholder='请填写心愿内容' name="content">${
                search || ""
              }</textarea></label>
              <div class="costomization-plug-in-container-footer">
                <button id="costomization-plug-in-container-add-model-container-cancel" type="button" class="btn"><span>取 消</span></button>
                <button id="costomization-plug-in-container-add-model-container-confirm" type="button" class="btn"><span>确 定</span></button>
              </div>
            </form>
          </div>
        </div>
      `);
      $("#costomization-plug-in-container-add-model-form").on("submit", (e) =>
        e.preventDefault()
      );
      $("#costomization-plug-in-container-add-model-cancel").on("click", () => {
        $("#costomization-plug-in-container-add-model-container").remove();
      });
      $("#costomization-plug-in-container-add-model-container-cancel").on(
        "click",
        () => {
          $("#costomization-plug-in-container-add-model-container").remove();
        }
      );
      $("#costomization-plug-in-container-add-model-container-confirm").on(
        "click",
        async () => {
          let serializedData = $(
            "#costomization-plug-in-container-add-model-form"
          ).serialize();
          let params = new URLSearchParams(serializedData);
          let jsonData = {};
          // 遍历URLSearchParams对象，将键值对添加到JSON对象中
          params.forEach(function (value, key) {
            jsonData[key] = value;
          });
          if (!jsonData.content) {
            alert("心愿内容不能为空！！！");
            return;
          }
          let param = {
            content: jsonData.content,
            category: "新增知识需求",
            fromIrNumber: jsonData.ir,
            from: "search",
            searchContent: search,
            secondCategory: jsonData.category || null,
          };
          let res = await addWish(param);
          if (res.code === "200") {
            $("#costomization-plug-in-container-add-model-container").remove();
            alert("转心愿单成功！！！");
          } else {
            alert(res.msg || "已存在同样的心愿单！！！");
          }
          plugIgnoreList.push(irNumber);
          localStorage.setItem("customeIRPlugIgnoreList", plugIgnoreList);
        }
      );
      $("#costomization-plug-in-wish-category-reset").on("click", () => {
        $("#costomization-plug-in-wish-category").val("");
        $("#costomization-plug-in-wish-category").css("color", "#999999");
        $("#costomization-plug-in-wish-category-reset").addClass("hide");
      });
      $("#costomization-plug-in-wish-category").change(function () {
        if (!$("#costomization-plug-in-wish-category").val()) {
          $("#costomization-plug-in-wish-category").css("color", "#999999");
        } else {
          $("#costomization-plug-in-wish-category").css("color", "#000");
          $("#costomization-plug-in-wish-category-reset").removeClass("hide");
        }
      });
    };

    let popup = () => {
      const popEle = document.querySelector("#costomization-plug-in-container");
      if (popEle) {
        popEle.style.display = "block";
        return;
      }
      let popupContainer = document.createElement("div");
      popupContainer.id = "costomization-plug-in-container";
      popupContainer.innerHTML = `
            <div style="position: fixed;left: 0;top: 0;z-index: 10000;width: 100vw;height: 100vh;background-color:rgba(0,0,0,.45)"></div>
            <div id="costomization-plug-in-modal" class='custom-translate'
                style="position: fixed;left: 50%;top: 50%;z-index: 10001;min-height: 200px;width: calc(100vw - 40px); max-height:  calc(100vh - 40px);overflow-y:auto;padding: 20px; border-radius: 8px;background-color: rgb(255, 255, 255);box-shadow: rgba(0, 0, 4, 0.4) 0px 1px 6px;display: block;">
                <div style="width: 100%;font-size: 22px;display: flex;align-items: center;justify-content: start;margin-bottom: 20px;">
                    <span style="width:100%;text-align: center;">知识检索</span>
                    <svg id="costomization-plug-in-container-cancel" t="1733111927000" style="cursor: pointer;" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2429" width="20" height="20"><path d="M522.689679 455.703011 897.496052 830.509388C915.99391 849.007246 915.99391 878.9982 897.496052 897.496059 878.998193 915.993917 849.007239 915.993917 830.509381 897.496059L455.703008 522.689682 80.896635 897.496056C62.398777 915.993914 32.407822 915.993914 13.909964 897.496056-4.587894 878.998198-4.587894 849.007243 13.909964 830.509385L388.716338 455.703011 13.909966 80.896635C-4.587892 62.398776-4.587892 32.407822 13.909967 13.909964 32.407825-4.587895 62.398779-4.587894 80.896638 13.909964L455.70301 388.71634 830.509384 13.909966C849.007242-4.587892 878.998196-4.587892 897.496054 13.909966 915.993913 32.407825 915.993913 62.398779 897.496054 80.896637L522.689679 455.703011Z" fill="#000000" p-id="2430"></path></svg>
                </div>
                <div id="costomization-plug-in-search-box" style="padding: 20px 0;">
                    <div style="display: flex;line-height: 1.5;font-size: 16px;height: 40px; gap: 20px">
                        <input id="costomization-plug-in-container-input" placeholder="请输入问题描述" type="text" name="title" style="width: 100%;border: 1px solid #0d0c0c;padding: 0 10px 0 10px">
                        <button id="costomization-plug-in-container-search" style="height: 40px;width: 80px;background-color: #0d0c0c;border: 1px solid #0d0c0c;color: #fff;border-radius: 4px;cursor: pointer;">搜索</button>
                    </div>
                    <form id="costomization-plug-in-search-option-container">
                      知识分类：
                      <label for="type-option1" class='costomization-plug-in-search-option-container-label'>
                        <input type="checkbox" style="margin: 0" id="type-option1" name="costomization-plug-in-modal-search-radio-group" checked value="场景化知识">
                        场景化知识
                      </label>
                      <label for="type-option2" class='costomization-plug-in-search-option-container-label'>
                        <input type="checkbox" style="margin: 0" id="type-option2" name="costomization-plug-in-modal-search-radio-group" checked value="问题集">
                        问题集
                      </label>
                      <label for="type-option3" class='costomization-plug-in-search-option-container-label'>
                        <input type="checkbox" style="margin: 0" id="type-option3" name="costomization-plug-in-modal-search-radio-group" value="故障知识">
                        故障知识
                      </label>
                      <label for="type-option4" class='costomization-plug-in-search-option-container-label'>
                        <input type="checkbox" style="margin: 0" id="type-option4" name="costomization-plug-in-modal-search-radio-group" value="2C故障知识">
                        2C故障知识
                      </label>
                      <label for="type-option5" class='costomization-plug-in-search-option-container-label'>
                        <input type="checkbox" style="margin: 0" id="type-option5" name="costomization-plug-in-modal-search-radio-group" value="demo代码">
                        demo代码
                      </label>
                    </form>
                    <button id="costomization-plug-in-search-option-container-category-btn" type="button" style='display:none'><span>一键转心愿单</span></button>
                </div>
            </div>
        `;
      document.body.appendChild(popupContainer);
      let searchBtn = document.querySelector(
        "#costomization-plug-in-container-search"
      );
      let cancelBtn = document.querySelector(
        "#costomization-plug-in-container-cancel"
      );

      $("#costomization-plug-in-search-option-container").on("submit", (e) =>
        e.preventDefault()
      );
      $("#costomization-plug-in-search-option-container").on(
        "change",
        "input",
        (e) => {
          searchBtn.dispatchEvent(new Event("click"));
        }
      );
      $("#costomization-plug-in-container-input").bind(
        "input propertychange",
        (e) => {
          $(
            "#costomization-plug-in-search-option-container-category-btn"
          ).hide();
        }
      );
      $("#costomization-plug-in-search-option-container-category-btn").on(
        "click",
        () => {
          let search = $("#costomization-plug-in-container-input")
            ?.val()
            ?.trim();
          showWishlistModel(search);
        }
      );

      const refreshListData = async (pageNumber) => {
        let pageNum = pageNumber;
        // 是否重新搜索
        let reSearch = false;
        if (!pageNumber) {
          pageNum = 1;
          reSearch = true;
        }

        let input = document.querySelector(
          "#costomization-plug-in-container-input"
        );
        if (!input.value.trim()) {
          return;
        }
        let modal = document.querySelector("#costomization-plug-in-modal");
        let list = [];
        let total = 0;
        try {
          let checkboxVal = [];
          $(
            "input:checkbox[name='costomization-plug-in-modal-search-radio-group']:checked"
          ).each(function () {
            checkboxVal.push(...$(this).val().split(",")); //向数组中添加元素
          });
          let searchRes = await search(
            input.value.trim(),
            pageNum,
            checkboxVal
          );
          let searchResponse = await searchRes.json();
          list = searchResponse.data.dataList;
          total = searchResponse.data.totalCount;
        } catch (error) {
          showLogin();
          return;
        }
        let searchResultContainer = modal.querySelector(
          "#costomization-plug-in-modal-search-result"
        );
        if (searchResultContainer) {
          searchResultContainer.innerHTML = "";
        } else {
          searchResultContainer = document.createElement("div");
          searchResultContainer.id =
            "costomization-plug-in-modal-search-result";
          searchResultContainer.style.width = "100%";
          searchResultContainer.style.overflowY = "auto";
          searchResultContainer.style.maxHeight = "calc(100vh - 285px)";
        }
        let noData = document.createElement("div");
        noData.id = "costomization-plug-in-modal-search-no-data";
        noData.innerHTML = `<div style="width: 100%;text-align: center;color: #000;">暂无数据</div>`;
        let FeedbackResultContainer = document.createElement("div");
        FeedbackResultContainer.innerHTML = `<button id="feedbackAllResult" type="button" style="margin-right: 5px;float: right;color: #fff;background: #1677ff;font-size: 14px;line-height: 1.5714285714285714;height: 32px;padding: 4px 15px;border-radius: 6px;border: 1px solid transparent;margin: 10px">全部未解决</button>`;

        if (!list?.length) {
          searchResultContainer.appendChild(noData);
          searchResultContainer.appendChild(FeedbackResultContainer);
        }
        let pagination = modal.querySelector("#pagination");
        if (pagination) {
          $(function () {
            $("#pagination").pagination("updateItems", total);
            if (reSearch) {
              $("#pagination").pagination("drawPage", pageNum);
              return;
            }
          });
        } else {
          pagination = document.createElement("div");
          pagination.id = "pagination";
          pagination.style.marginTop = "10px";
          pagination.style.display = "flex";
          pagination.style.justifyContent = "center";
          $(function () {
            $("#pagination").pagination({
              items: total,
              itemsOnPage: 10,
              prevText: "上一页",
              nextText: "下一页",
              useAnchors: false,
              onPageClick: (pageNumber) => {
                refreshListData(pageNumber);
              },
            });
          });
        }
        list.forEach((item, index) => {
          let searchResultItem = document.createElement("div");
          let classificationList = [];
          item?.firstClassification &&
            classificationList.push(item?.firstClassification);
          item?.secondClassification &&
            classificationList.push(item?.secondClassification);
          item?.classification && classificationList.push(item?.classification);
          searchResultItem.innerHTML = `
                    <div style="border-bottom: 1px solid rgb(214, 214, 214); padding: 16px 0px;">
                        <div style="width: 100%; ">
                            <div style="display: flex; width: 100%; justify-content: space-between;">
                                <div custom-title='true' style="font-size: 18px;font-weight: 700; margin-bottom: 10px; color: #333333;cursor: pointer;">${
                                  item?.url
                                    ? `<a target="_blank" href='${item?.url}'>${
                                        item?.highlight_title || item?.title
                                      }</a>`
                                    : item?.highlight_title || item?.title
                                }</div>
                            </div>
                            <div style="font-size: 14px; color: #777;">${
                              classificationList?.length
                                ? `【分类】${classificationList.join("&gt;")}`
                                : ""
                            }</div>
                            ${
                              item?.description
                                ? `<div style="font-size: 14px; width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; margin-top: 15px; color: #777;">
                                ${
                                  item?.description
                                    ? `${
                                        item?.engin === "develop"
                                          ? ""
                                          : "【问题描述】"
                                      }${item?.description.replaceAll(
                                        "<",
                                        "&lt;"
                                      )}`
                                    : ""
                                }
                            </div>`
                                : ""
                            }
                            <div style="font-size: 16px; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden; color: #333; margin-top: 15px;">
                                ${
                                  item?.engin === "develop"
                                    ? ""
                                    : "【问题回复】"
                                }${
            item?.highlight_content
              ? `${item?.highlight_content}`
              : item?.content
          }
                            </div>
                        </div>
                        <div style="text-align: end; font-size: 16px; color: rgb(119, 119, 119); margin: 16px 36px 0px;">
                            <span style="margin-right: 20px;">${
                              item?.createTime
                            }</span>
                            <span style="font-size: 14px;">知识来源：${
                              item?.source
                            }</span>
                        </div>
                    </div>
                `;
          searchResultItem.addEventListener(
            "click",
            async (event) => {
              let sortNum = (pageNum - 1) * 10 + index + 1;
              try {
                if (event.target.getAttribute("custom-title")) {
                  feedback(input.value.trim(), item, "no", sortNum);
                  showDetail(item, sortNum);
                  return;
                }
                if (
                  event.target.getAttribute("custom-name") &&
                  !event.target.getAttribute("checked")
                ) {
                  let res = await feedback(
                    input.value.trim(),
                    item,
                    event.target.getAttribute("custom-name"),
                    sortNum
                  );
                  res.json().then((res) => {
                    if (res.success) {
                      plugIgnoreList.push(irNumber);
                      localStorage.setItem(
                        "customeIRPlugIgnoreList",
                        plugIgnoreList
                      );
                      document.removeEventListener(
                        "click",
                        handleDocumentClick,
                        true
                      );
                      document.querySelector(
                        "#costomization-plug-in-container"
                      ).style.display = "none";
                    }
                  });
                }
              } catch (error) {
                showLogin();
                return;
              }
            },
            true
          );
          searchResultContainer.appendChild(searchResultItem);
        });
        let styleTag = document.createElement("style");
        styleTag.innerHTML = `
        #costomization-plug-in-container em {
            color: #f73131;
            font-style: normal;
        }
    `;
        document.head.appendChild(styleTag);
        searchResultContainer.appendChild(FeedbackResultContainer);
        modal.appendChild(searchResultContainer);
        modal.appendChild(pagination);
        $("#costomization-plug-in-search-option-container-category-btn").show();
        if (!list.length) {
          $("#feedbackAllResult").text("未找到结果");
        }
        $("#feedbackAllResult").on("click", async () => {
          if (list?.length) {
            let res = await feedback(
              input.value.trim(),
              null,
              "low",
              (pageNum - 1) * 10,
              list
            );
            let response = await res.json();
            if (!response.success) {
              showLogin();
              return;
            }
          }
          plugIgnoreList.push(irNumber);
          localStorage.setItem("customeIRPlugIgnoreList", plugIgnoreList);
          document.removeEventListener("click", handleDocumentClick, true);
          $("#costomization-plug-in-container").hide();
        });
      };

      searchBtn.addEventListener("click", async () => {
        refreshListData();
      });

      let costomizationPlugInSearchInput = document.querySelector(
        "#costomization-plug-in-container-input"
      );

      costomizationPlugInSearchInput.addEventListener(
        "keyup",
        function (event) {
          if (event.keyCode === 13) {
            searchBtn.dispatchEvent(new Event("click"));
          }
        }
      );

      cancelBtn.addEventListener("click", () => {
        document.querySelector(
          "#costomization-plug-in-container"
        ).style.display = "none";
      });
    };

    let handleDocumentClick = async (event) => {
      plugIgnoreList =
        localStorage.getItem("customeIRPlugIgnoreList")?.split(",") || [];
      // 需求单->issueType:2|bug->issueType:1|华为测试提单->isSubmittedByHWTest:1不需要知识检索
      if (
        [1, 2].includes(this.__issueInfo__?.issue?.issueType) ||
        this.__issueInfo__?.issue?.isSubmittedByHWTest === 1
      ) {
        return;
      }
      current = document.querySelector(
        'div[nzpopconfirmoverlayclassname="langBoxPop"]'
      )?.innerText;
      let label = document.querySelector(
        "nz-modal-container  nz-form-label > label"
      );
      let target = label?.parentNode?.parentNode?.querySelector(
        "nz-form-control nz-select nz-select-item"
      )?.innerText;

      if (
        (label?.innerText === "指派类型" &&
          ["二级运营人员", "三级运营人员"].includes(target) &&
          current === "一级运营") ||
        (label?.innerText === "指派类型" &&
          target === "三级运营人员" &&
          current === "二级运营")
      ) {
        // if (
        //   event.target.innerText === "确定" &&
        //   !plugIgnoreList.includes(irNumber)
        // ) {
        //   event.stopPropagation();
        //   let needLogin = await isNeedLogin();
        //   if (needLogin) {
        //     showLogin();
        //     return;
        //   }
        //   if (document.querySelector("#costomization-plug-in-container")) {
        //     document.querySelector(
        //       "#costomization-plug-in-container"
        //     ).style.display = "block";
        //     return;
        //   }
        //   popup();
        // }
      }
    };

    /**
     * 获取issuce平台csrfToken
     */
    let issuePlatformCsrfToken = "";
    chrome.runtime.sendMessage(
      {
        action: "getCookie",
        domain: ".developer.huawei.com",
      },
      async (res) => {
        issuePlatformCsrfToken = res.csrfToken;
      }
    );
    /**
     * issuce平台公共post请求
     */
    const issueCustomPost = async (param, src = "") => {
      if (!issuePlatformCsrfToken) {
        return undefined;
      }
      let t = new Date().getTime();
      return new Promise((resolve, reject) => {
        fetch(
          src ||
            `https://svc-drcn.developer.huawei.com/codeserver/Common/v1/delegate`,
          {
            method: "post",
            credentials: "include",
            headers: {
              "content-type": "application/json;charset=UTF-8",
              accept: "application/json, text/plain, */*",
              "x-hd-csrf": issuePlatformCsrfToken,
              "x-hd-date": new Date()
                .toISOString()
                .replaceAll(/([\d-:T]*)(\.\d*Z)/g, ($1, b, c) => {
                  return b.replaceAll(/[:-]/g, "") + "Z";
                }),
              "x-hd-serialno": Math.ceil(
                ((t = (9301 * t + 49297) % 233280), (t / 233280) * 10000000)
              ),
            },
            body: JSON.stringify(param),
          }
        )
          .then((res) => res.json())
          .then((res) => resolve(res))
          .catch((err) => reject(err));
      });
    };
    /**
     * 技术中心公共请求post
     */
    const technologyCenterCustomPost = async (url, params) => {
      return new Promise((resolve, reject) => {
        fetch(`https://dtse.cbg.huawei.com/board${url}`, {
          method: "post",
          headers: {
            "Content-Type": "application/json",
          },
          body: params ? JSON.stringify(params) : null,
        })
          .then((res) => res.json())
          .then((res) => resolve(res))
          .catch((err) => reject(err));
      });
    };

    /**
     * 免密登录
     */
    const passwordFreeLogin = async () => {
      try {
        const nickName = document.querySelector(".name").innerText;
        let searchData = { userName: "", userNo: "" };
        let selectedUserType = '';
        // 获取userName  userType
        const { resJson: userNameResJson } = await issueCustomPost({
          reqJson: '{"req":{}}',
          reqType: 0,
          svc: "OpenCommon.DelegateTm.OpenUP_Server4User_getUserRouteInfo",
        });
        if (userNameResJson) {
          const data = JSON.parse(userNameResJson).userRouteInfoList[0];
          searchData.userName = data.realName;
          selectedUserType = data.userType;
        } else {
          return false;
        }
        // 获取userNo
        const { resJson: userNoResJson } = await issueCustomPost({
          reqJson: `{"req":{"nickName":"${nickName}","selectedUserType":${selectedUserType}}}`,
          reqType: 0,
          svc: "PartnerIssueService.Developer.getUserInfo",
        });
        if (userNoResJson) {
          searchData.userNo = JSON.parse(userNoResJson).result.w3Account;
        } else {
          return false;
        }
        // 免密登录
        const { data: loginData } = await technologyCenterCustomPost(
          "/knowledgeBaseManage/login",
          searchData
        );
        if (loginData.sessionId) {
          localStorage.setItem("customeIRPlugSessionID", loginData.sessionId);
          return true;
        } else {
          return false;
        }
      } catch (error) {}
    };

    (() => {
      // 悬浮窗
      let floarContainer = document.createElement("div");
      let floarText = document.createElement("span");
      document.body.appendChild(floarContainer);
      floarContainer.appendChild(floarText);
      floarText.innerText = "WIKI";
      floarText.style.fontFamily = "bold";
      floarText.style.color = "#fff";
      floarContainer.id = "floatWindow-container";
      floarContainer.style.position = "fixed";
      floarContainer.style.width = "50px";
      floarContainer.style.height = "50px";
      floarContainer.style.background =
        "linear-gradient(to bottom right, #0000ff, #90ee90)";
      floarContainer.style.textAlign = "center";
      floarContainer.style.borderRadius = "8px";
      floarContainer.style.bottom = "20px";
      floarContainer.style.right = "20px";
      floarContainer.style.zIndex = "10002";
      floarContainer.style.lineHeight = "50px";
      floarContainer.style.cursor = "pointer";
      floarContainer.addEventListener("click", async () => {
        let needLogin = await isNeedLogin();
        if (needLogin) {
          const loginSuccess = await passwordFreeLogin();
          let needLogin = await isNeedLogin();
          if (loginSuccess && !needLogin) {
            popup();
          } else {
            alert("知识库免密登录失败");
          }
          return;
        }
        popup();
      });
    })();
    if (location.href.includes("/DTSPortal")) {
      return;
    }
    document.removeEventListener("click", handleDocumentClick, true);
    document.addEventListener("click", handleDocumentClick, true);
  } catch (error) {}
});
