# 是否开启mock
VITE_USE_MOCK = true

# 网站根目录
VITE_PUBLIC_PATH = /

# 是否删除console
VITE_DROP_CONSOLE = true

# API
VITE_GLOB_API_URL = 

# 接口前缀
VITE_GLOB_API_URL_PREFIX = /api

# 图片上传地址
VITE_GLOB_UPLOAD_URL=

# 图片前缀地址
VITE_GLOB_IMG_URL=

# 是否启用gzip压缩或brotli压缩
# 可选: gzip | brotli | none
# 如果你需要多种形式，你可以用','来分隔
VITE_BUILD_COMPRESS = 'none'

# 使用压缩时是否删除原始文件，默认为false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false
VITE_APP_BASE_API = /board
# VITE_APP_BASE_API = http://***********:3000/rest
# 知识库
VITE_APP_BASE_KNOWLEDGE_API = http://***********:80/knowledge-admin