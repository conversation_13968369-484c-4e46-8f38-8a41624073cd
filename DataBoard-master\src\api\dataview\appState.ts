import { ewpService as service } from '@/utils/axios';

interface WorkOrderQueryParams {
  orderId?: string;
  appName?: string;
  ewpOwner?: string;
  severity?: string | null;
  status?: string | null;
  currentHandler?: string;
  irOrderId?: string;
  irOrderStatus?: string | null;
  pageNo: number;
  pageSize: number;
}

interface WorkOrderResponse {
  items: any[]; // 替换 'any' 为实际的工单项类型
  total: number;
}

export async function getWorkOrderList(params: any): Promise<any> {
  try {
    const response = await service.post('/management/workOrder/query', params);

    return response;
  } catch (error) {
    console.error('Error fetching work order list:', error);
    throw error;
  }
}

export async function distributeOrder(): Promise<any> {
  try {
    const response = await service.post('/management/workOrder/distributeOrder');
    return response;
  } catch (error) {
    console.error('Error distributeOrder:', error);
    throw error;
  }
}

export async function getDTSResolveState(): Promise<any> {
  try {
    const response = await service.get('/management/overview/view');
    return response;
  } catch (error) {
    console.error('Error fetching work order list:', error);
    throw error;
  }
}

export async function importExcel(file: File): Promise<any> {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await service.post('/management/workOrder/importData', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    console.log('Import response:', response);

    return response;
  } catch (error) {
    console.error('Error importing Excel file:', error);
    throw error;
  }
}

export async function importUpExcel(file: File): Promise<any> {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await service.post('/management/appPublishInfo/importData', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    console.log('Import response:', response);

    return response;
  } catch (error) {
    console.error('Error importing Excel file:', error);
    throw error;
  }
}
export async function updateWorkOrder(data: Partial<any>): Promise<any> {
  try {
    const response = await service.post('/management/workOrder/save', data);
    return response;
  } catch (error) {
    console.error('Error updating work order:', error);
    throw error;
  }
}
export const submitIr = async (data: any) => {
  try {
    const response = await service.post('/management/workOrder/submitIr', data);
    return response;
  } catch (error) {
    0;
    console.error('Error updating work order:', error);
    throw error;
  }
};
export const distributeOrderCcb = async (data: any) => {
  try {
    const response = await service.post('/management/workOrder/distributeOrderCcb', data);
    return response;
  } catch (error) {
    console.error('Error updating work order:', error);
    throw error;
  }
};
