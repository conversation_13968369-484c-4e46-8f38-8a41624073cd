import { SETTINGS } from "./settings.js";

chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
  if (request.action == "getCookie") {
    console.log(request)
    let listener = (details) => {
      console.log(details)
      chrome.webRequest.onCompleted.removeListener(listener);
      chrome.cookies.get(
        {
          url: "https://scmt.rnd.huawei.com",
          name: "prod_cftk",
        },
        function (cookies) {
          console.log(cookies)
          sendResponse({
            csrfToken: cookies.value,
            request: request,
          });
        }
      );
    };
    chrome.webRequest.onCompleted.addListener(listener, {
      urls: [
        "https://scmt.rnd.huawei.com/CompassWeb/cd-cloud-config/v1/get/service/features*",
      ],
    });
  }
  return true;
});