import { VNode, h } from 'vue';
import { NButton, NTag, NSpace } from 'naive-ui';
import { BasicColumn } from '@/components/Table';

export interface ListData {
  field: string;
  appName: string;
  colloquial: string[];
}

export const createColumns = (
  showAction = true,
  handleEdit?: (row: ListData) => void,
  handleDelete?: (row: ListData) => void,
): BasicColumn<ListData>[] => {
  const baseColumns: BasicColumn<ListData>[] = [
    {
      title: '领域',
      key: 'field',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '应用名称',
      key: 'appName',
      width: 200,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '口语化',
      key: 'colloquial',
      render(row) {
        if (row.colloquial?.length > 0) {
          const tagNodeList: VNode[] = []
          row.colloquial.forEach(item => {
            tagNodeList.push(
              h(
                NTag,
                {
                  bordered: false,
                },
                {
                  default: () => item,
                }
              )
            )
          })
          return h(
            NSpace,
            {
              bordered: false,
            },
            {
              default: () => tagNodeList
            },
          );
        }
      },
    },
  ];

  if (showAction) {
    baseColumns.push({
      title: '操作',
      key: 'actions',
      fixed: 'right',
      width: 240,
      render: (row) => {
        return h(
          NSpace,
          {},
          {
            default: () => [
              h(
                NButton,
                {
                  size: 'small',
                  onClick: () => handleEdit?.(row),
                },
                { default: () => '编辑' }
              ),
              h(
                NButton,
                {
                  size: 'small',
                  type: 'error',
                  onClick: () => handleDelete?.(row),
                },
                { default: () => '删除' }
              ),
            ],
          }
        );
      },
    });
  }

  return baseColumns;
};

// 定义查询表单项
export const searchFormItems = [
  {
    field: 'field',
    label: '领域',
    component: 'Select',
    componentProps: {
      options: [
        { label: '4796应用', value: '4796应用' },
        { label: '游戏', value: '游戏' },
      ],
    },
  },
  {
    field: 'appName',
    label: '应用名称',
    component: 'Input',
  },
];
