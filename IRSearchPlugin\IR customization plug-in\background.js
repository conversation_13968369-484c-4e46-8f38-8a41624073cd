const manifest = chrome.runtime.getManifest();

chrome.runtime.onMessage.addListener(function (request, sender, sendResponse) {
  if (request.action == "getVersion") {
    sendResponse({ version: manifest.version });
  }
  if (request.action == "getCookie") {
    let listener = (details) => {
      chrome.webRequest.onCompleted.removeListener(listener);
      chrome.cookies.get(
        {
          url: "https://issuereporter.developer.huawei.com",
          name: "csrfToken",
        },
        function (cookies) {
          sendResponse({
            csrfToken: cookies.value,
            request: request,
          });
        }
      );
      
    };
    chrome.webRequest.onCompleted.addListener(listener, {
      urls: [
        "https://svc-drcn.developer.huawei.com/codeserver/Common/v1/delegate",
      ],
    });
  }
  return true;
});
