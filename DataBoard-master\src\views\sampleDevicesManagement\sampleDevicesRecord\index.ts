import { h, reactive } from 'vue';
export const format = (storageTime: string) => {
  return storageTime
    ? new Date(storageTime).toLocaleString().replace(/\s.*/, '').replaceAll(/\//g, '-')
    : '';
};

export const defaultSearchModel = {
  deviceChannel: null,
  deviceStatus: null,
};

export const defaultModel = {
  serialNumber: null,
  deviceChannel: null,
  region: null,
  storageTime: null,
  acceptanceResponsiblePerson: null,
  applicationName: null,
  deviceCategory: null,
  deviceModel: null,
  deviceNumber: null,
  deviceStatus: null,
  warehouseRecipient: null,
  notes: null,
  availability: null,
  trialNotes: null,
};

export const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page: number) => {
    paginationReactive.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    paginationReactive.pageSize = pageSize;
    paginationReactive.page = 1;
  },
  prefix: (info) => {
    return h('div', null, { default: () => `共 ${info.itemCount} 项` });
  },
});

export const channelList = reactive([
  {
    label: '内购',
    value: '内购',
  },
  {
    label: '伙伴提供',
    value: '伙伴提供',
  },
]);

export const regionList = reactive([
  {
    label: '东莞',
    value: '东莞',
  },
  {
    label: '南京',
    value: '南京',
  },
]);
export const statusList = reactive([
  {
    label: '在库',
    value: '在库',
  },
  {
    label: '外借',
    value: '外借',
  },
  {
    label: '寄回伙伴',
    value: '寄回伙伴',
  },
]);
export const availabilityList = reactive([
  {
    label: '可用',
    value: '可用',
  },
  {
    label: '不可用',
    value: '不可用',
  },
  {
    label: '未试用',
    value: '未试用',
  },
]);
