import { RouteRecordRaw } from 'vue-router';
import { Layout } from '@/router/constant';
import {
  DesktopOutline,
  AnalyticsOutline,
  DocumentTextOutline,
  EarthOutline,
  EyeOutline,
  PulseOutline,
  PieChartOutline,
  ListOutline,
  SearchOutline,
  BugOutline,
  AlertCircleOutline,
} from '@vicons/ionicons5';
import { renderIcon } from '@/utils';
import {
  ExclamationCircleOutlined,
  TableOutlined,
  TeamOutlined,
  ConsoleSqlOutlined,
  CodeOutlined,
  ClusterOutlined,
  FundOutlined,
  ProjectOutlined,
  SketchOutlined,
  GlobalOutlined,
  FileSearchOutlined,
  DesktopOutlined,
  BlockOutlined,
  CommentOutlined,
  CheckSquareOutlined,
  AppstoreAddOutlined,
  HomeOutlined,
  AimOutlined,
  PartitionOutlined,
  AlertOutlined,
  NotificationOutlined,
  RadarChartOutlined,
  DatabaseOutlined,
  ScanOutlined,
  MonitorOutlined,
  DashboardOutlined,
  Bar<PERSON><PERSON>Outlined,
  SoundOutlined,
  EyeOutlined,
  <PERSON>Outlined,
  Line<PERSON>hartOutlined,
  ReadOutlined,
  SettingOutlined,
  SearchOutlined,
  MessageOutlined,
  WarningOutlined,
} from '@vicons/antd';
import { RoleMgmtPathNameEnum } from '@/enums/RoleMgmtPathNameEnum';
import { UserRoleEnum } from '@/enums/UserRoleEnum';

const routes: Array<RouteRecordRaw> = [
  {
    path: '/workplace',
    name: 'workplace',
    redirect: '/workplace',
    component: Layout,
    meta: {
      title: '工作台',
      sort: 2,
      icon: renderIcon(DesktopOutlined),
      permissions: [
        UserRoleEnum.NORMAL_USER,
        UserRoleEnum.FLY_TIGER_MEMBER,
        UserRoleEnum.MANAGER_USER,
      ],
    },
    children: [
      {
        path: '',
        name: 'workplace',
        meta: {
          title: '工作台',
          isRoot: true,
        },
        component: () => import('@/views/workplace/index.vue'),
      },
      {
        path: 'roleManagement',
        name: RoleMgmtPathNameEnum.ALL,
        meta: {
          title: '权限管理',
          permissions: [UserRoleEnum.SUPER_ADMIN, UserRoleEnum.MANAGER_USER],
        },
        component: () => import('@/views/RoleManagement/index.vue'),
      },
    ],
  },
  {
    path: '/departmentOKR',
    name: 'departmentOKR',
    redirect: '/departmentOKR/OKRDataReport',
    component: Layout,
    meta: {
      title: '部门',
      sort: 2,
      icon: renderIcon(ClusterOutlined),
      permissions: [
        UserRoleEnum.NORMAL_USER,
        UserRoleEnum.MANAGER_USER,
        UserRoleEnum.FLY_TIGER_MEMBER,
      ],
    },
    children: [
      {
        path: 'OKRDataReport',
        name: 'OKRDataReport',
        meta: {
          title: '运营看板',
          permissions: [
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.OPERATION_USER,
            UserRoleEnum.TEST_ADMIN,
          ],
        },
        component: () => import('@/views/departmentOKR/index.vue'),
      },
      {
        path: 'statisticalOverview',
        name: 'StatisticalOverview',
        meta: {
          title: ' 交付件统计',
          permissions: [
            UserRoleEnum.NORMAL_USER,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.FLY_TIGER_MEMBER,
          ],
        },
        component: () => import('@/views/technicalDeliverables/statisticalOverview/index.vue'),
      },
      {
        path: 'businessTravelersManage',
        name: 'BusinessTravelersManage',
        meta: {
          title: '差旅管理',
          permissions: [UserRoleEnum.NORMAL_USER, UserRoleEnum.MANAGER_USER],
        },
        component: () => import('@/views/acceptancePrototype/BusinessTravelersManage/index.vue'),
      },
      {
        path: 'departmentRoleManagement',
        name: RoleMgmtPathNameEnum.DEPARTMENT,
        meta: {
          title: '角色管理',
          permissions: [UserRoleEnum.NORMAL_USER, UserRoleEnum.MANAGER_USER],
        },
        component: () => import('@/views/RoleManagement/index.vue'),
      },
    ],
  },
  {
    path: '/orderQuality',
    name: 'OrderQuality',
    redirect: '/orderQuality/orderQualityDataReport',
    component: Layout,
    meta: {
      title: '工单质量',
      sort: 2,
      icon: renderIcon(BlockOutlined),
      permissions: [UserRoleEnum.NORMAL_USER, UserRoleEnum.MANAGER_USER],
    },
    children: [
      {
        path: 'orderQualityDataReport',
        name: 'OrderQualityDataReport',
        meta: {
          title: '运营看板',
          permissions: [
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.OPERATION_USER,
          ],
        },
        component: () => import('@/views/orderQuality/orderQualityDataReport/index.vue'),
      },
      {
        path: 'orderQualityManagement',
        name: 'OrderQualityManagement',
        meta: {
          title: '运营看板管理',
          permissions: [
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.ORDER_QUALITY_ADMIN,
          ],
        },
        component: () => import('@/views/orderQuality/orderQualityManager/main.vue'),
      },
      {
        path: 'statisticalDataDashboard',
        name: 'statisticalDataDashboard',
        meta: {
          title: '技术专家产出统计',
          permissions: [
            UserRoleEnum.NORMAL_USER,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.OPERATION_USER,
          ],
        },
        component: () => import('@/views/orderQuality/statisticalDataDashboard/index.vue'),
      },
      {
        path: 'orderSpotCheck',
        name: 'orderSpotCheck',
        meta: {
          title: '工单抽检',
          permissions: [
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.ORDER_QUALITY_ADMIN,
            UserRoleEnum.ORDER_QUALITY_SPOT_CHECK_LEADER,
            UserRoleEnum.ORDER_QUALITY_SPOT_CHECK_USER,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.NORMAL_USER,
          ],
        },
        component: () => import('@/views/orderQuality/OrderSpotCheck/index.vue'),
      },
      {
        path: 'issueTransferRecord',
        name: 'IssueTransferRecord',
        meta: {
          title: 'IR转单操作记录',
          permissions: [UserRoleEnum.NORMAL_USER, UserRoleEnum.MANAGER_USER],
        },
        component: () => import('@/views/orderQuality/issueTransferRecord/index.vue'),
      },
      {
        path: 'irPlugin',
        name: 'IrPlugin',
        meta: {
          title: '知识库搜索插件',
          permissions: [UserRoleEnum.SUPER_ADMIN, UserRoleEnum.MANAGER_USER],
        },
        component: () => import('@/views/orderQuality/irPlugin/index.vue'),
      },
      {
        path: 'orderQualityRoleManagement',
        name: RoleMgmtPathNameEnum.ORDER_QUALITY,
        meta: {
          title: '角色管理',
          permissions: [UserRoleEnum.NORMAL_USER, UserRoleEnum.MANAGER_USER],
        },
        component: () => import('@/views/RoleManagement/index.vue'),
      },
    ],
  },
  {
    path: '/kowledgeBase',
    name: 'KnowledgeBase',
    redirect: '/kowledgeBase/knowledgeDataReport',
    component: Layout,
    meta: {
      title: '知识库',
      sort: 2,
      icon: renderIcon(ConsoleSqlOutlined),
      permissions: [
        UserRoleEnum.NORMAL_USER,
        UserRoleEnum.FLY_TIGER_MEMBER,
        UserRoleEnum.MANAGER_USER,
      ],
    },
    children: [
      {
        path: 'knowledgeDataReport',
        name: 'KnowledgeDataReport',
        meta: {
          title: '运营看板',
          permissions: [
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.OPERATION_USER,
          ],
        },
        component: () => import('@/views/kowledgeBase/knowledgeDataReport/index.vue'),
      },
      {
        path: 'knowledgeSearch/:id?',
        name: 'knowledgeSearch',
        meta: {
          title: '知识检索',
        },
        component: () => import('@/views/kowledgeBase/knowledgeIframe/index.vue'),
      },
      {
        path: 'knowledgeTodos',
        name: 'knowledgeTodos',
        meta: {
          title: '我的待办',
        },
        component: () => import('@/views/kowledgeBase/knowledgeIframe/index.vue'),
      },
      {
        path: 'myApplication',
        name: 'myApplication',
        meta: {
          title: '我的申请',
        },
        component: () => import('@/views/kowledgeBase/knowledgeIframe/index.vue'),
      },
      {
        path: 'knowledgeCreation',
        name: 'knowledgeCreation',
        meta: {
          title: '知识创作',
        },
        component: () => import('@/views/kowledgeBase/knowledgeIframe/index.vue'),
      },
      {
        path: 'knowledgeMap',
        name: 'knowledgeMap',
        meta: {
          title: '知识地图',
        },
        component: () => import('@/views/kowledgeBase/knowledgeIframe/index.vue'),
      },
      // {
      //   path: 'knowledgeWishMenus',
      //   name: 'knowledgeWishMenus',
      //   meta: {
      //     title: '心愿单',
      //   },
      //   component: () => import('@/views/kowledgeBase/knowledgeIframe/index.vue'),
      // },
      {
        path: 'knowledgeDataDashboard',
        name: 'knowledgeDataDashboard',
        meta: {
          title: '数据看板',
        },
        component: () => import('@/views/kowledgeBase/knowledgeIframe/index.vue'),
      },
      // {
      //   path: 'knowledgeWishContributionList',
      //   name: 'knowledgeWishContributionList',
      //   meta: {
      //     title: '贡献榜',
      //   },
      //   component: () => import('@/views/kowledgeBase/knowledgeIframe/index.vue'),
      // },
      // {
      //   path: 'knowledgeRoleManagement',
      //   name: RoleMgmtPathNameEnum.KNOWLEDGE,
      //   meta: {
      //     title: '角色管理',
      //   },
      //   component: () => import('@/views/RoleManagement/index.vue'),
      // },
    ],
  },
  {
    path: '/faultKnowledgeBaseConstruction',
    name: 'FaultKnowledgeBaseConstruction',
    redirect: '/faultKnowledgeBaseConstruction/breakdownDataReport',
    component: Layout,
    meta: {
      title: '故障知识库建设',
      sort: 2,
      icon: renderIcon(ClusterOutlined),
      hidden: true,
      permissions: [UserRoleEnum.NORMAL_USER, UserRoleEnum.MANAGER_USER],
    },
    children: [
      {
        path: 'breakdownDataReport',
        name: 'BreakdownDataReport',
        meta: {
          title: '运营看板',
          permissions: [
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.OPERATION_USER,
          ],
        },
        component: () =>
          import('@/views/faultKnowledgeBaseConstruction/breakdownDataReport/index.vue'),
      },

      {
        path: 'processOrder',
        name: 'ProcessOrder',
        meta: {
          title: '故障输出',
        },
        component: () => import('@/views/faultKnowledgeBaseConstruction/processOrder/index.vue'),
      },
      {
        path: 'faultKnowRoleManagement',
        name: RoleMgmtPathNameEnum.FAULT_KNOW,
        meta: {
          title: '角色管理',
        },
        component: () => import('@/views/RoleManagement/index.vue'),
      },
    ],
  },
  {
    path: '/sampleCodeManage',
    name: 'SampleCodeManage',
    redirect: '/sampleCodeManage/codeDataReport',
    component: Layout,
    meta: {
      title: '样例代码',
      sort: 2,
      icon: renderIcon(CodeOutlined),
      permissions: [
        UserRoleEnum.NORMAL_USER,
        UserRoleEnum.FLY_TIGER_MEMBER,
        UserRoleEnum.MANAGER_USER,
      ],
    },
    children: [
      {
        path: 'codeDataReport',
        name: 'CodeDataReport',
        meta: {
          title: '运营看板',
          permissions: [
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.OPERATION_USER,
          ],
        },
        component: () => import('@/views/sampleCodeManage/codeDataReport/index.vue'),
      },
      // {
      //   path: 'innovationScenario',
      //   name: 'InnovationScenario',
      //   meta: {
      //     title: '创新场景',
      //     hidden: true,
      //   },
      //   component: () => import('@/views/sampleCodeManage/innovationScenario/index.vue'),
      // },
      {
        path: 'sampleCode',
        name: 'SampleCode',
        meta: {
          title: '样例代码',
        },
        component: () => import('@/views/sampleCodeManage/sampleCode/index.vue'),
      },
      {
        path: 'customizedDemo/:todo?',
        name: 'CustomizedDemo',
        meta: {
          title: 'Demo创作',
          permissions: [
            UserRoleEnum.NORMAL_USER,
            UserRoleEnum.FLY_TIGER_MEMBER,
            UserRoleEnum.MANAGER_USER,
          ],
        },
        component: () => import('@/views/sampleCodeManage/CustomizedDemo/index.vue'),
      },
      {
        path: 'codeOutgoingMgmt',
        name: 'codeOutgoingMgmt',
        meta: {
          title: 'Demo管理',
          permissions: [
            UserRoleEnum.NORMAL_USER,
            UserRoleEnum.FLY_TIGER_MEMBER,
            UserRoleEnum.MANAGER_USER,
          ],
        },
        component: () => import('@/views/sampleCodeManage/codeOutgoing/index.vue'),
      },
      {
        path: 'codeDataReportManager',
        name: 'CodeDataReportManager',
        meta: {
          title: '运营看板管理',
          permissions: [
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.SAMPLE_CODE_ADMIN,
          ],
        },
        component: () => import('@/views/sampleCodeManage/codeDataReportManager/index.vue'),
      },
      {
        path: 'codeRoleManagement',
        name: RoleMgmtPathNameEnum.SAMPLE_CODE,
        meta: {
          title: '角色管理',
        },
        component: () => import('@/views/RoleManagement/index.vue'),
      },
    ],
  },
  {
    path: '/developerCommunity',
    name: 'DeveloperCommunity',
    redirect: '/developerCommunity/communityDataReport',
    component: Layout,
    meta: {
      title: '开发者社区',
      sort: 2,
      icon: renderIcon(CommentOutlined),
      permissions: [
        UserRoleEnum.MANAGER_USER,
        UserRoleEnum.SUPER_ADMIN,
        UserRoleEnum.OPERATION_USER,
        UserRoleEnum.DEVELOPER_COMMUNITY_ADMIN,
      ],
    },
    children: [
      {
        path: 'communityDataReport',
        name: 'CommunityDataReport',
        meta: {
          title: '运营看板',
          permissions: [
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.OPERATION_USER,
          ],
        },
        component: () => import('@/views/developerCommunity/communityDataReport/index.vue'),
      },
      {
        path: 'issueManagement',
        name: 'issueManagement',
        meta: {
          title: '社区运营管理台',
          permissions: [
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.OPERATION_USER,
            UserRoleEnum.DEVELOPER_COMMUNITY_ADMIN,
          ],
        },
        component: () => import('@/views/developerCommunity/issuePage/index.vue'),
      },
      {
        path: 'auditManagement',
        name: 'auditManagement',
        meta: {
          title: '审核管理',
          permissions: [
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.OPERATION_USER,
            UserRoleEnum.DEVELOPER_COMMUNITY_ADMIN,
          ],
        },
        component: () => import('@/views/developerCommunity/auditManagement/index.vue'),
      },
      {
        path: RoleMgmtPathNameEnum.DEVELOPER_COMMUNITY,
        name: RoleMgmtPathNameEnum.DEVELOPER_COMMUNITY,
        meta: {
          title: '角色管理',
          permissions: [
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.DEVELOPER_COMMUNITY_ADMIN,
          ],
        },
        component: () => import('@/views/RoleManagement/index.vue'),
      },
    ],
  },
  {
    path: '/listingProtection',
    name: 'ListingProtection',
    redirect: '/listingProtection/problemHandle',
    component: Layout,
    meta: {
      title: '舆情保障',
      sort: 2,
      icon: renderIcon(SoundOutlined),
    },
    children: [
      {
        path: 'screenDisplay',
        name: 'ScreenDisplay',
        meta: {
          title: '运营看板',
          permissions: [
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.OPERATION_USER,
          ],
          icon: renderIcon(LineChartOutlined),
        },
        component: () => import('@/views/listingProtection/screenDisplay/index.vue'),
      },
      {
        path: 'console',
        name: `console`,
        meta: {
          title: '主控台',
          icon: renderIcon(DesktopOutlined),
          permissions: [
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.LISTING_PROTECTION_ADMIN,
            UserRoleEnum.MANAGER_USER,
          ],
          affix: true,
        },
        component: () => import('@/views/dashboard/console/console.vue'),
      },
      {
        path: 'problemHandle',
        name: 'ProblemHandle',
        meta: {
          title: '舆情管理',
          icon: renderIcon(BellOutlined),
          permissions: [
            UserRoleEnum.NORMAL_USER,
            UserRoleEnum.FLY_TIGER_MEMBER,
            UserRoleEnum.MANAGER_USER,
          ],
        },
        component: () => import('@/views/publicOpinionManagement/problemHandle/index.vue'),
      },
      {
        path: 'process',
        name: 'Process',
        component: () => import('@/views/dataview/myOrder/components/process.vue'),
        meta: {
          title: '问题处理',
          hidden: true,
          permissions: [
            UserRoleEnum.NORMAL_USER,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.LISTING_PROTECTION_ADMIN,
          ],
        },
      },
      {
        path: 'ecologicalTracking',
        name: 'EcologicalTracking',
        meta: {
          title: '生态舆情跟踪',
          keepAlive: true,
          icon: renderIcon(EyeOutline),
          permissions: [UserRoleEnum.NORMAL_USER, UserRoleEnum.MANAGER_USER],
        },
        component: () => import('@/views/dataview/ecologicalTracking/index.vue'),
      },
      {
        path: 'myTodo',
        name: 'MyTodo',
        meta: {
          title: '我的待办',
          keepAlive: true,
          icon: renderIcon(CheckSquareOutlined),
          permissions: [
            UserRoleEnum.NORMAL_USER,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.ONLY_VIEW_PUBLIC_OPINION,
          ],
        },
        component: () => import('@/views/dataview/myOrder/index.vue'),
      },
      {
        path: '/mycheck',
        name: 'MyCheck',
        meta: {
          title: '工单抽检',
          permissions: [
            UserRoleEnum.NORMAL_USER,
            UserRoleEnum.FLY_TIGER_MEMBER,
            UserRoleEnum.MANAGER_USER,
          ],
          icon: renderIcon(FileSearchOutlined),
        },
        component: () => import('@/views/dataview/myOrder/myCheck/index.vue'),
      },
      {
        path: '/irmanagement',
        name: 'IrManagement',
        meta: {
          title: 'IR单管理',
          permissions: [
            UserRoleEnum.NORMAL_USER,
            UserRoleEnum.FLY_TIGER_MEMBER,
            UserRoleEnum.MANAGER_USER,
          ],
          icon: renderIcon(MessageOutlined),
        },
        component: () => import('@/views/dataview/irManagement/index.vue'),
      },
      {
        path: 'problemHandleDetail',
        name: 'ProblemHandleDetail',
        meta: {
          title: '舆情管理详情',
          hidden: true,
          permissions: [
            UserRoleEnum.NORMAL_USER,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.LISTING_PROTECTION_ADMIN,
          ],
        },
        component: () => import('@/views/publicOpinionManagement/problemHandle/detail.vue'),
      },
      {
        path: '/dataview',
        name: 'Dataview',
        meta: {
          title: '数据看板',
          icon: renderIcon(PieChartOutline),
          permissions: [
            UserRoleEnum.NORMAL_USER,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.LISTING_PROTECTION_ADMIN,
          ],
        },
        children: [
          {
            path: 'targetDashboard',
            name: 'TargetDashboard',
            meta: {
              title: '指标看板',
              permissions: [
                UserRoleEnum.NORMAL_USER,
                UserRoleEnum.MANAGER_USER,
                UserRoleEnum.LISTING_PROTECTION_ADMIN,
              ],
            },
            component: () => import('@/views/dataview/targetDashboard/index.vue'),
          },
          {
            path: 'appstate',
            name: 'Appstate',
            meta: {
              title: '应用状态',
              permissions: [
                UserRoleEnum.NORMAL_USER,
                UserRoleEnum.MANAGER_USER,
                UserRoleEnum.LISTING_PROTECTION_ADMIN,
              ],
            },
            component: () => import('@/views/dataview/appState/index.vue'),
          },
          {
            path: 'appanalysis',
            name: 'appAnalysis',
            meta: {
              title: '应用分析报表',
              permissions: [
                UserRoleEnum.NORMAL_USER,
                UserRoleEnum.MANAGER_USER,
                UserRoleEnum.LISTING_PROTECTION_ADMIN,
              ],
            },
            component: () => import('@/views/dataview/appAnalysis/index.vue'),
          },
          {
            path: '/appdetail',
            name: 'AppDetail',
            meta: {
              title: '应用详情',
              hidden: true,
              permissions: [
                UserRoleEnum.NORMAL_USER,
                UserRoleEnum.MANAGER_USER,
                UserRoleEnum.LISTING_PROTECTION_ADMIN,
              ],
            },
            component: () => import('@/views/dataview/appDetail/index.vue'),
          },
          {
            path: 'dtsResolveState',
            name: 'DtsResolveState',
            meta: {
              title: '工单解决状态',
              permissions: [
                UserRoleEnum.NORMAL_USER,
                UserRoleEnum.MANAGER_USER,
                UserRoleEnum.LISTING_PROTECTION_ADMIN,
              ],
            },
            component: () => import('@/views/dataview/dtsResolveState/index.vue'),
          },
          {
            path: 'futAPPList',
            name: 'futAPPList',
            meta: {
              title: 'FUT应用列表',
              permissions: [
                UserRoleEnum.NORMAL_USER,
                UserRoleEnum.MANAGER_USER,
                UserRoleEnum.LISTING_PROTECTION_ADMIN,
              ],
            },
            component: () => import('@/views/dataview/futAPPList/index.vue'),
          },
          {
            path: 'clusteringDashboard',
            name: 'clusteringDashboard',
            meta: {
              title: '聚类指标看板',
              permissions: [
                UserRoleEnum.NORMAL_USER,
                UserRoleEnum.MANAGER_USER,
                UserRoleEnum.LISTING_PROTECTION_ADMIN,
              ],
            },
            component: () => import('@/views/dataview/clusteringDashboard/index.vue'),
          },
        ],
      },
      {
        path: '/opinionmonitoring',
        name: 'opinionMonitoring',
        meta: {
          title: '舆情监控',
          icon: renderIcon(PulseOutline),
          permissions: [
            UserRoleEnum.NORMAL_USER,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.LISTING_PROTECTION_ADMIN,
          ],
        },
        children: [
          {
            path: 'allQuestions',
            name: 'AllQuestions',
            meta: {
              title: '全渠道监控',
              permissions: [
                UserRoleEnum.NORMAL_USER,
                UserRoleEnum.MANAGER_USER,
                UserRoleEnum.LISTING_PROTECTION_ADMIN,
              ],
            },
            component: () => import('@/views/dataview/futQuestions/index.vue'),
          },
          {
            path: 'top38',
            name: 'top38',
            meta: {
              title: 'TOP38渠道监控',
              permissions: [
                UserRoleEnum.NORMAL_USER,
                UserRoleEnum.MANAGER_USER,
                UserRoleEnum.LISTING_PROTECTION_ADMIN,
              ],
            },
            component: () => import('@/views/dataview/top38/index.vue'),
          },
          {
            path: 'betaState',
            name: 'BetaState',
            meta: {
              title: 'BETA渠道监控',
              permissions: [
                UserRoleEnum.NORMAL_USER,
                UserRoleEnum.MANAGER_USER,
                UserRoleEnum.LISTING_PROTECTION_ADMIN,
              ],
            },
            component: () => import('@/views/dataview/betaState/index.vue'),
          },
          {
            path: 'futQuestions',
            name: 'FutQuestions',
            meta: {
              title: 'FUT渠道监控',
              permissions: [
                UserRoleEnum.NORMAL_USER,
                UserRoleEnum.MANAGER_USER,
                UserRoleEnum.LISTING_PROTECTION_ADMIN,
              ],
            },
            component: () => import('@/views/dataview/futQuestions/index.vue'),
            props: { source: 'FUT' },
          },
          {
            path: 'nssview',
            name: 'Nssview',
            meta: {
              title: 'NSS渠道监控',
              permissions: [
                UserRoleEnum.NORMAL_USER,
                UserRoleEnum.MANAGER_USER,
                UserRoleEnum.LISTING_PROTECTION_ADMIN,
              ],
            },
            component: () => import('@/views/dataview/nssView/index.vue'),
          },
          {
            path: 'AGview',
            name: 'AGview',
            meta: {
              title: 'AG渠道监控',
              permissions: [
                UserRoleEnum.NORMAL_USER,
                UserRoleEnum.MANAGER_USER,
                UserRoleEnum.LISTING_PROTECTION_ADMIN,
              ],
            },
            component: () => import('@/views/dataview/AGview/index.vue'),
          },
          {
            path: 'internetQuestions',
            name: 'internetQuestions',
            meta: {
              title: '互联网渠道监控',
              permissions: [
                UserRoleEnum.NORMAL_USER,
                UserRoleEnum.MANAGER_USER,
                UserRoleEnum.LISTING_PROTECTION_ADMIN,
              ],
            },
            component: () => import('@/views/dataview/internetQuestions/index.vue'),
          },
          {
            path: 'serviceQuestions',
            name: 'serviceQuestions',
            meta: {
              title: '服务渠道监控',
              permissions: [
                UserRoleEnum.NORMAL_USER,
                UserRoleEnum.MANAGER_USER,
                UserRoleEnum.LISTING_PROTECTION_ADMIN,
              ],
            },
            component: () => import('@/views/dataview/serviceAndOtherQuestions/service.vue'),
          },
          {
            path: 'otherQuestions',
            name: 'otherQuestions',
            meta: {
              title: '其他渠道监控',
              permissions: [
                UserRoleEnum.NORMAL_USER,
                UserRoleEnum.MANAGER_USER,
                UserRoleEnum.LISTING_PROTECTION_ADMIN,
              ],
            },
            component: () => import('@/views/dataview/serviceAndOtherQuestions/other.vue'),
          },
        ],
      },
      {
        path: '/faulttree',
        name: 'Faulttree',
        redirect: '/faulttree/treelist',
        meta: {
          title: '故障树管理',
          icon: renderIcon(BugOutline),
          keepAlive: true,
          permissions: [UserRoleEnum.NORMAL_USER, UserRoleEnum.MANAGER_USER],
        },
        children: [
          // {
          //   path: 'treelist',
          //   name: 'TreeList',
          //   meta: {
          //     title: '故障场景',
          //     keepAlive: true,
          //     permissions: [UserRoleEnum.NORMAL_USER],
          //   },
          //   component: () => import('@/views/dataview/faultScenes/index.vue'),
          // },
          // {
          //   path: 'categorymanage',
          //   name: 'MategoryManage',
          //   meta: {
          //     title: '故障分类',
          //     keepAlive: true,
          //     permissions: [UserRoleEnum.NORMAL_USER],
          //   },
          //   component: () => import('@/views/dataview/faultScenes/categoryManage.vue'),
          // },
          {
            path: 'knowData',
            name: 'knowData',
            meta: {
              title: '故障知识指标统计',
              keepAlive: true,
              permissions: [UserRoleEnum.NORMAL_USER, UserRoleEnum.MANAGER_USER],
            },
            component: () => import('@/views/dataview/knowData/index.vue'),
          },
        ],
      },
      {
        path: '/ecoknowledge',
        name: 'Ecoknowledge',
        redirect: '/ecoknowledge/knowledge',
        meta: {
          title: '生态知识',
          icon: renderIcon(SketchOutlined),
          keepAlive: true,
          permissions: [UserRoleEnum.NORMAL_USER, UserRoleEnum.MANAGER_USER],
        },
        children: [
          {
            path: 'knowledge',
            name: 'knowledge',
            component: () => import('@/views/dataview/knowledge/index.vue'),
            meta: {
              title: '知识管理',
              permissions: [UserRoleEnum.NORMAL_USER, UserRoleEnum.MANAGER_USER],
            },
          },
        ],
      },
      {
        path: '/dataconfig',
        name: 'dataconfig',
        meta: {
          title: '数据配置页面',
          icon: renderIcon(SettingOutlined),
          keepAlive: true,
          permissions: [UserRoleEnum.NORMAL_USER, UserRoleEnum.MANAGER_USER],
        },
        children: [
          {
            path: 'phoneconfig',
            name: 'phoneConfig',
            meta: {
              title: '机型数据配置',
              keepAlive: true,
              permissions: [UserRoleEnum.NORMAL_USER, UserRoleEnum.MANAGER_USER],
            },
            component: () => import('@/views/dataview/phoneConfig/index.vue'),
          },
          {
            path: 'personManage',
            name: 'personManage',
            meta: {
              title: '人员数据配置',
              keepAlive: true,
              permissions: [UserRoleEnum.NORMAL_USER, UserRoleEnum.MANAGER_USER],
            },
            component: () => import('@/views/dataview/personManage/index.vue'),
          },
          {
            path: 'representEmployeeManage',
            name: 'representEmployeeManage',
            meta: {
              title: '代表处人员配置',
              keepAlive: true,
              permissions: [UserRoleEnum.NORMAL_USER, UserRoleEnum.MANAGER_USER],
            },
            component: () => import('@/views/dataview/representEmployeeManage/index.vue'),
          },
        ],
      },
      // {
      //   path: 'guaranteeDataReport',
      //   name: 'GuaranteeDataReport',
      //   meta: {
      //     title: '数据报表',
      //   },
      //   component: () => import('@/views/listingProtection/guaranteeDataReport/index.vue'),
      // },
      // {
      //   path: 'betaProblemProgress',
      //   name: 'BetaProblemProgress',
      //   meta: {
      //     title: 'Beta问题进展',
      //   },
      //   component: () => import('@/views/listingProtection/betaProblemProgress/index.vue'),
      // },
      // {
      //   path: 'universalityProblemProgress',
      //   name: 'UniversalityProblemProgress',
      //   meta: {
      //     title: '共性问题进展',
      //   },
      //   component: () => import('@/views/listingProtection/universalityProblemProgress/index.vue'),
      // },
      {
        path: 'listingProtectionRoleManagement',
        name: RoleMgmtPathNameEnum.LISTING_PROTECTION,
        meta: {
          title: '角色管理',
          permissions: [UserRoleEnum.NORMAL_USER, UserRoleEnum.MANAGER_USER],
          icon: renderIcon(TeamOutlined),
        },
        component: () => import('@/views/RoleManagement/index.vue'),
      },
    ],
  },
  {
    path: '/acceptancePrototype',
    name: 'AcceptancePrototype',
    redirect: '/AcceptancePrototype',
    component: Layout,
    meta: {
      title: '应用验收',
      sort: 2,
      icon: renderIcon(CheckSquareOutlined),
      permissions: [
        UserRoleEnum.SUPER_ADMIN,
        UserRoleEnum.TEST_ADMIN,
        UserRoleEnum.TEST_USER,
        UserRoleEnum.MANAGER_USER,
        UserRoleEnum.NORMAL_USER,
      ],
    },
    children: [
      {
        path: 'testQualityDataReport',
        name: 'TestQualityDataReport',
        meta: {
          title: '运营看板',
        },
        component: () => import('@/views/testQualityDataReport/index.vue'),
      },
      {
        path: 'applicationAcceptance',
        name: 'ApplicationAcceptance',
        meta: {
          title: '质量评估',
        },
        component: () => import('@/views/acceptancePrototype/ApplicationAcceptance/index.vue'),
      },
      {
        path: 'statisticsTesting',
        name: 'StatisticsTesting',
        meta: {
          title: '测试中保障',
          permissions: [
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.TEST_ADMIN,
            UserRoleEnum.TEST_USER,
            UserRoleEnum.MANAGER_USER,
          ],
        },
        component: () => import('@/views/acceptancePrototype/StatisticsTesting/index.vue'),
      },
      {
        path: 'statisticsTested',
        name: 'StatisticsTested',
        meta: {
          title: '测试完成统计',
          permissions: [
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.TEST_ADMIN,
            UserRoleEnum.TEST_USER,
            UserRoleEnum.MANAGER_USER,
          ],
        },
        component: () => import('@/views/acceptancePrototype/StatisticsTested/index.vue'),
      },
      {
        path: 'verticalDomain',
        name: 'VerticalDomain',
        meta: {
          title: '垂域共性功能底表',
        },
        component: () => import('@/views/acceptancePrototype/VerticalDomain/index.vue'),
      },
      {
        path: '/industryDetail',
        name: 'IndustryDetail',
        meta: {
          title: '功能底表详情',
          hidden: true,
        },
        component: () => import('@/views/acceptancePrototype/IndustryDetail/index.vue'),
      },
      {
        path: '/testAppdetail',
        name: 'TestAppdetail',
        meta: {
          title: '应用详情',
          hidden: true,
        },
        component: () => import('@/views/acceptancePrototype/TestAppdetail/index.vue'),
      },
      {
        path: 'testQualityManagement',
        name: 'TestQualityManagement',
        meta: {
          title: '运营看板管理',
          permissions: [
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.TEST_ADMIN,
          ],
        },
        component: () =>
          import('@/views/acceptancePrototype/operationDashboardManagement/index.vue'),
      },
      {
        path: 'testRoleManagement',
        name: RoleMgmtPathNameEnum.TEST,
        meta: {
          title: '角色管理',
          permissions: [
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.TEST_ADMIN,
            UserRoleEnum.TEST_USER,
            UserRoleEnum.PROTOTYPE_ADMIN,
            UserRoleEnum.DEVICE_MANAGER,
            UserRoleEnum.MANAGER_USER,
          ],
        },
        component: () => import('@/views/RoleManagement/index.vue'),
      },
    ],
  },
  {
    path: '/prototypeManagement',
    name: 'PrototypeManagement',
    redirect: '/UrgeSignature',
    component: Layout,
    meta: {
      title: '样机管理',
      sort: 2,
      icon: renderIcon(AppstoreAddOutlined),
      permissions: [
        UserRoleEnum.SUPER_ADMIN,
        UserRoleEnum.PROTOTYPE_ADMIN,
        UserRoleEnum.DEVICE_MANAGER,
        UserRoleEnum.OPERATION_USER,
        UserRoleEnum.NORMAL_USER,
        UserRoleEnum.MANAGER_USER,
      ],
    },
    children: [
      {
        path: 'urgeSignature',
        name: 'UrgeSignature',
        meta: {
          title: '样机邮件管理',
        },
        component: () => import('@/views/acceptancePrototype/UrgeSignature/index.vue'),
      },
      {
        path: 'sampleDevicesRecord',
        name: 'SampleDevicesRecord',
        meta: {
          title: '依赖设备库房管理',
        },
        component: () => import('@/views/sampleDevicesManagement/SampleDevicesRecord/index.vue'),
      },
      {
        path: 'sampleDevidesUsageRegistration',
        name: 'SampleDevidesUsageRegistration',
        meta: {
          title: '依赖设备借用归还登记',
        },
        component: () =>
          import('@/views/sampleDevicesManagement/sampleDevidesUsageRegistration/index.vue'),
      },
      {
        path: 'prototypeRoleManagement',
        name: RoleMgmtPathNameEnum.PROTOTYPE,
        meta: {
          title: '角色管理',
        },
        component: () => import('@/views/RoleManagement/index.vue'),
      },
    ],
  },
  {
    path: '/allianceCommunity',
    name: 'AllianceCommunity',
    meta: {
      title: '联盟社区',
      icon: renderIcon(GlobalOutlined),
      sort: 2,
      permissions: [UserRoleEnum.SUPER_ADMIN, UserRoleEnum.ALLIANCE_COMMINITY_USER],
    },
    component: Layout,
    children: [
      {
        path: 'wiseoperKnowledgeView',
        name: 'WiseoperKnowledgeView',
        meta: {
          title: '社区wiseoper',
        },
        component: () => import('@/views/wiseoperKnowledgeView/index.vue'),
      },
    ],
  },
  {
    path: '/searchOperations',
    name: 'searchOperations',
    redirect: '/searchAnalysis/searchAnalysis',
    meta: {
      title: '搜索运营',
      icon: renderIcon(FileSearchOutlined),
      sort: 2,
      permissions: [
        UserRoleEnum.SUPER_ADMIN,
        UserRoleEnum.MANAGER_USER,
        UserRoleEnum.OPERATION_USER,
      ],
    },
    component: Layout,
    children: [
      {
        path: 'SearchAnalysis',
        name: 'SearchAnalysis',
        meta: {
          title: '运营看板',
          permissions: [
            UserRoleEnum.SUPER_ADMIN,
            UserRoleEnum.MANAGER_USER,
            UserRoleEnum.OPERATION_USER,
          ],
        },
        component: () => import('@/views/searchAnalysis/index.vue'),
      },
    ],
  },
];

export default routes;
