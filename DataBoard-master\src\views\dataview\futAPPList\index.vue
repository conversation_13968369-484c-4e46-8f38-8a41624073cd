<template>
  <div>
    <n-card>
      <n-form
        :model="searchForm"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        @submit.prevent="handleSearch"
      >
        <n-grid :cols="24" :x-gap="24">
          <n-grid-item v-for="item in searchFormItems" :key="item.field" :span="6">
            <n-form-item :label="item.label" :path="item.field">
              <n-input
                v-if="item.component === 'Input'"
                v-model:value="searchForm[item.field]"
                clearable
                @keyup.enter="handleSearch"
              />
              <n-select
                v-else-if="item.component === 'Select'"
                v-model:value="searchForm[item.field]"
                :options="item.componentProps.options"
                clearable
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        <div class="form-actions">
          <n-space>
            <n-button @click="resetForm">重置</n-button>
            <n-button type="primary" attr-type="submit">查询</n-button>
          </n-space>
        </div>
      </n-form>
    </n-card>

    <n-card style="margin-top: 24px">
      <n-space align="center" style="margin-bottom: 16px" v-if="canEdit">
        <n-button type="primary" @click="showAddModal = true">新增</n-button>
      </n-space>

      <n-data-table
        remote
        :bordered="false"
        :single-line="false"
        striped
        :columns="columns"
        :data="tableData"
        :pagination="paginationReactive"
        :loading="loading"
        :scroll-x="1800"
        max-height="510"
      />
    </n-card>

    <n-modal v-model:show="showEditModal" preset="card" title="编辑应用" style="width: 600px">
      <n-form
        :model="editingAPP"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="领域" path="field">
          <n-select
            v-model:value="editingAPP.field"
            :options="
              [
                { label: '4796应用', value: '4796应用' },
                { label: '游戏', value: '游戏' },
              ]
            "
          />
        </n-form-item>
        <n-form-item label="应用名称" path="appName">
          <n-input v-model:value="editingAPP.appName" />
        </n-form-item>
        <n-form-item label="口语化" path="colloquial">
          <n-dynamic-tags v-model:value="editingAPP.colloquial" />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showEditModal = false">取消</n-button>
          <n-button type="primary" @click="saveEdit">保存</n-button>
        </n-space>
      </template>
    </n-modal>

    <n-modal v-model:show="showAddModal" preset="card" title="新增应用" style="width: 600px">
      <n-form
        ref="formRef"
        :model="newAPP"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="领域" path="field">
          <n-select
            v-model:value="newAPP.field"
            :options="
              [
                { label: '4796应用', value: '4796应用' },
                { label: '游戏', value: '游戏' },
              ]
            "
          />
        </n-form-item>
        <n-form-item label="应用名称" path="appName">
          <n-input v-model:value="newAPP.appName" />
        </n-form-item>
        <n-form-item label="口语化" path="colloquial">
          <n-dynamic-tags v-model:value="newAPP.colloquial" />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showAddModal = false">取消</n-button>
          <n-button type="primary" @click="saveNewAPP">保存</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>
<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { type UserInfoType, useUserStore } from '@/store/modules/user';
  import {
    NCard,
    NForm,
    NFormItem,
    NInput,
    NSelect,
    NButton,
    NSpace,
    NGrid,
    NGridItem,
    NDataTable,
    useMessage,
    FormRules,
    useDialog,
  } from 'naive-ui';
  import { createColumns, searchFormItems, ListData } from './columns';
  import {
    getFUTAPPList,
    addFUTAPP,
    updateFUTAPP,
    deleteFUTAPP,
  } from '@/api/dataview/futAPPList';
  import type { FormInst } from 'naive-ui';
  import { filterObjectValues } from '@/utils';

  // 获取权限
  const userStore = useUserStore();
  const userInfo: UserInfoType = userStore.getUserInfo || {};
  const futAccounts = [
    'g********', '********', '郭晶', // 郭晶
    'h********', '********', '黄幸平', // 黄幸平
    'w********', '********', '王书恒', // 王书恒
    'x********', '********', '邢后亮', // 邢后亮
    'z********', '********', '朱宏坤', // 朱宏坤
    'z********', '********', '宗昊瑾', // 宗昊瑾
    'j********', '********', '姜智衡', // 姜智衡
    'c********', '********', '曹荣幽', // 曹荣幽
    'l********', '********', '刘威', // 刘威
  ];

  const canEdit = futAccounts.includes(userInfo.userName)
  const message = useMessage();
  const dialog = useDialog();

  const rules: FormRules = {
    field: [
      {
        required: true,
        message: '请输入领域',
      },
    ],
    appName: [
      {
        required: true,
        message: '请输入应用名称',
      },
    ],
  };
  const searchForm = reactive({
    field: '',
    appName: '',
  });

  const tableData = ref<ListData[]>([]);
  const loading = ref(false);
  const paginationReactive = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 50, 100],
    itemCount: 0,
    prefix({ itemCount }) {
      return `Total：${itemCount}`;
    },
    onChange: (page: number) => {
      paginationReactive.page = page;
      fetchData();
    },
    onUpdatePageSize: (pageSize: number) => {
      paginationReactive.pageSize = pageSize;
      paginationReactive.page = 1;
      fetchData();
    },
  });

  const handleSearch = () => {
    paginationReactive.page = 1;
    fetchData();
  };

  const resetForm = () => {
    Object.keys(searchForm).forEach((key) => {
      searchForm[key] = '';
    });
    fetchData();
  };

  interface FUTAPPData extends ListData {
    irOrderStatus: string;
    _id: string;
  }

  const fetchData = async () => {
    loading.value = true;
    try {
      const params: any = {
        ...filterObjectValues(searchForm),
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
      };

      const res = await getFUTAPPList(params);
      tableData.value = res.records;
      paginationReactive.itemCount = res.total;
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      loading.value = false;
    }
  };

  const showEditModal = ref(false);
  const editingAPP = ref<Partial<ListData>>({} as Partial<ListData>);

  const handleEdit = (row: ListData) => {
    editingAPP.value = { ...row };
    if (editingAPP.value.colloquial === null) {
      editingAPP.value.colloquial = []
    }
    showEditModal.value = true;
  };

  const saveEdit = async () => {
    try {
      await updateFUTAPP(editingAPP.value);
      message.success('应用更新成功');
      showEditModal.value = false;
      fetchData();
    } catch (error: any) {
      message.error('应用更新失败');
    }
  };

  const showAddModal = ref(false);
  const newAPP = ref<FUTAPPData>({
    id: '' // 后台 save 要求必传
  } as FUTAPPData);

  const formRef = ref<FormInst | null>(null);

  const saveNewAPP = async () => {
    try {
      await formRef.value?.validate();
      await addFUTAPP(newAPP.value);
      message.success('应用创建成功！');
      showAddModal.value = false;
      newAPP.value = {
        id: ''
      } as FUTAPPData;
      fetchData();
    } catch (error: any) {
      message.error('应用创建失败！');
    }
  };

  const handleDelete = async (row: ListData) => {
    dialog.warning({
      title: '提示',
      content: '确定删除此应用？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          await deleteFUTAPP(row.id);
          message.success('删除成功');
          fetchData();
        } catch (error) {
          message.error('删除失败');
        }
      },
    })
  };

  const columns = createColumns(canEdit, handleEdit, handleDelete);

  onMounted(() => {
    fetchData();
  });
</script>

<style scoped>
  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 12px;
  }
</style>
