<template>
  <div class="dashboard-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <n-icon size="24" class="title-icon">
          <Settings />
        </n-icon>
        数据统计分析平台
      </h1>
      <p class="page-subtitle">实时监控工单状态，数据驱动决策</p>
    </div>

    <n-card class="main-card">
      <n-tabs type="line" animated trigger="click" @update:value="changeTab" class="custom-tabs">
        <n-tab-pane name="futDataState" tab="FUT数据统计">
          <!-- 顶部工具栏 -->
          <div class="toolbar">
            <div class="toolbar-left">
              <h3 class="section-title">FUT数据统计</h3>
              <p class="section-desc">查看FUT相关数据的统计分析</p>
            </div>
            <div class="toolbar-right">
              <n-space>
                <n-date-picker
                  v-for="item in searchFormItemsFUT"
                  :key="item.field"
                  clearable
                  v-model:value="searchFormItemsFUT[item.field]"
                  type="daterange"
                  :is-date-disabled="disablePreviousDate"
                  @confirm="onConfirmFut"
                  placeholder="选择日期范围"
                  class="compact-picker"
                />
                <n-button @click="resetForm" quaternary>重置</n-button>
                <n-button type="primary" @click="handleSearch">查询</n-button>
                <n-button type="primary" @click="handleExportFutRes" :loading="exportLoading"
                  >导出</n-button
                >
              </n-space>
            </div>
          </div>

          <!-- 数据概览卡片 -->
          <!-- <div class="stats-grid">
            <div class="stat-card" v-for="(item, index) in futBetaData.slice(0, 4)" :key="index">
              <div class="stat-icon">📊</div>
              <div class="stat-content">
                <div class="stat-label">{{ item.lable }}</div>
                <div class="stat-value">{{ item.total || 0 }}</div>
                <div class="stat-change">
                  <span class="before">公测前: {{ item.beforeOpen || 0 }}</span>
                  <span class="after">公测后: {{ item.afterOpen || 0 }}</span>
                </div>
              </div>
            </div>
          </div> -->

          <!-- 公测前后数据对比卡片 -->
          <div class="comparison-section">
            <div class="section-header">
              <h4>📊 公测前后数据对比</h4>
              <div class="total-summary" v-if="getTotalData()">
                <div class="total-item">
                  <span class="total-label">总量:</span>
                  <span class="total-value">{{ getTotalData()?.total || 0 }}</span>
                </div>
              </div>
            </div>
            <div class="comparison-grid" v-loading="loadingFUTBetaRes">
              <div
                class="comparison-card"
                v-for="(item, index) in futBetaData.filter((item) => !item.lable?.includes('总量'))"
                :key="index"
              >
                <div class="comparison-header">
                  <div class="comparison-icon">
                    <span v-if="item.lable?.includes('待分析')">🔍</span>
                    <span v-else-if="item.lable?.includes('待定界')">🎯</span>
                    <span v-else-if="item.lable?.includes('待输出')">📝</span>
                    <span v-else-if="item.lable?.includes('待锁定')">🔒</span>
                    <span v-else-if="item.lable?.includes('待修复')">🔧</span>
                    <span v-else-if="item.lable?.includes('待回归')">🔄</span>
                    <span v-else-if="item.lable?.includes('已闭环')">✅</span>
                    <span v-else-if="item.lable?.includes('总量')">📊</span>
                    <span v-else>📋</span>
                  </div>
                  <div class="comparison-title">{{ item.lable }}</div>
                </div>

                <div class="comparison-content">
                  <div class="comparison-stats">
                    <div class="stat-item before">
                      <div class="stat-label">公测前</div>
                      <div class="stat-value">{{ item.beforeOpen || 0 }}</div>
                    </div>
                    <div class="stat-divider">→</div>
                    <div class="stat-item after">
                      <div class="stat-label">公测后</div>
                      <div class="stat-value">{{ item.afterOpen || 0 }}</div>
                    </div>
                  </div>

                  <div class="comparison-change">
                    <div
                      class="change-value"
                      :class="getChangeClass(item.beforeOpen, item.afterOpen)"
                    >
                      {{ getChangeText(item.beforeOpen, item.afterOpen) }}
                    </div>
                    <div class="change-total"> 总计: {{ item.total || 0 }} </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 详细统计数据 -->
          <div class="detail-section">
            <div class="section-header">
              <h4>📋 详细统计数据</h4>
            </div>
            <n-data-table
              :single-line="false"
              :loading="loadingFUTRes"
              :columns="futColumns"
              :data="fuTtableData"
              class="clean-table"
              size="small"
            />
          </div>
        </n-tab-pane>
        <n-tab-pane tab="BetaClub单数据统计"><statisticsTable /></n-tab-pane>
        <n-tab-pane name="DTSResolveState" tab="DTS单数据统计">
          <!-- 顶部工具栏 -->
          <div class="toolbar">
            <div class="toolbar-left">
              <h3 class="section-title">DTS单数据统计</h3>
              <p class="section-desc">查看DTS单状态分布和趋势分析</p>
            </div>
            <div class="toolbar-right">
              <n-space>
                <n-date-picker
                  v-for="item in searchFormItemsDTSdistribution"
                  :key="item.field"
                  clearable
                  v-model:value="searchFormItemsDTSdistribution[item.field]"
                  type="daterange"
                  :is-date-disabled="disablePreviousDate"
                  @confirm="onConfirmResolve"
                  placeholder="选择日期范围"
                  class="compact-picker"
                />
                <n-button @click="resetForm" quaternary>重置</n-button>
                <n-button type="primary" @click="handleSearch">查询</n-button>
                <n-button type="primary" @click="handleExportRes" :loading="exportLoading"
                  >导出</n-button
                >
              </n-space>
            </div>
          </div>

          <!-- 趋势图表 -->
          <div class="chart-section">
            <div class="section-header">
              <h4>📈 趋势分析</h4>
            </div>
            <div ref="trendChart" class="chart"></div>
          </div>

          <!-- 数据表格 -->
          <div class="">
            <div class="section-header">
              <h4>📋 状态分布数据</h4>
            </div>
            <n-data-table
              remote
              :columns="DTSResolveStateColumns"
              :data="DTSResolveStateData"
              :pagination="paginationReactive"
              :bordered="false"
              :single-line="false"
              :loading="loadingRes"
              size="small"
              class="clean-table"
            />
          </div>
        </n-tab-pane>
        <n-tab-pane name="bugStatus" tab="工单解决状态分布">
          <BugStatusDistribution />
        </n-tab-pane>

        <n-tab-pane name="DTSSolveStates" tab="每日工单定界情况">
          <!-- 表单查询部分 -->
          <n-card>
            <n-form
              :model="searchForm"
              label-placement="left"
              label-width="auto"
              require-mark-placement="right-hanging"
              @submit.prevent="handleSearch"
            >
              <n-grid :cols="24" :x-gap="24">
                <n-grid-item
                  v-for="item in searchFormItemsDTSResolveStates"
                  :key="item.field"
                  :span="9"
                >
                  <n-form-item :label="item.label" :path="item.field">
                    <n-date-picker
                      v-if="item.component === 'DateRangePicker'"
                      clearable
                      v-model:value="searchFormItemsDTSResolveStates[item.field]"
                      type="daterange"
                      :is-date-disabled="disablePreviousDate"
                      @confirm="onConfirm"
                    />
                    <n-select
                      v-else-if="item.component === 'Select'"
                      v-model:value="searchForm[item.field]"
                      :options="item.componentProps.options"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
              <div class="form-actions">
                <n-space>
                  <n-button @click="resetForm">重置</n-button>
                  <n-button type="primary" attr-type="submit">查询</n-button>
                </n-space>
              </div>
            </n-form>
          </n-card>

          <!-- 折线图部分 -->
          <n-card class="table-container">
            <div ref="dailyChart" class="chart"></div>
          </n-card>

          <!-- 新数据表格 -->
          <n-card class="table-container">
            <n-table striped :single-line="false" :loading="loadingAllDTS">
              <thead>
                <tr>
                  <th rowspan="1">日期</th>
                  <th v-for="item in DTSSolveStateDataAll" :key="item.modifier" colspan="1">
                    {{ item.modifier }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(item, index) in DTSSolveStateDataAll[0].dtsDailyModifyDateCntPo"
                  :key="item.modifier"
                >
                  <td
                    >{{
                      formatDate(DTSSolveStateDataAll[0].dtsDailyModifyDateCntPo[index].modifyDate)
                    }}
                  </td>
                  <template v-for="(subItem, subIndex) in DTSSolveStateDataAll" :key="subIndex">
                    <td>
                      <div>
                        {{ formatData(subItem.dtsDailyModifyDateCntPo[index].modifyCntByDay) }}
                      </div>
                    </td>
                  </template>
                </tr>
              </tbody>
            </n-table>
          </n-card>

          <!-- 表格 -->
          <n-card class="table-container hidden">
            <!-- 添加导出按钮 -->
            <div class="table-header">
              <n-button type="primary" @click="handleExport" :loading="exportLoading">
                导出数据
              </n-button>
            </div>
            <n-table striped :single-line="false" :loading="loadingDTS">
              <thead>
                <tr>
                  <th rowspan="2">日期</th>
                  <th v-for="item in DTSSolveStateData" :key="item.modifier" colspan="2">
                    {{ item.modifier }}
                  </th>
                </tr>
                <tr>
                  <template v-for="_ in DTSSolveStateData" :key="_.modifier">
                    <th>新增</th>
                    <th>合计</th>
                  </template>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(item, index) in DTSSolveStateData[0].dtsDailyModifyDateCntPo"
                  :key="item.modifier"
                >
                  <td>{{
                    formatDate(DTSSolveStateData[0].dtsDailyModifyDateCntPo[index].modifyDate)
                  }}</td>
                  <!-- <template v-if="item.dtsDailyModifyDateCntPo"> -->
                  <template v-for="(subItem, subIndex) in DTSSolveStateData" :key="subIndex">
                    <!-- <td v-if="!subItem.dtsDailyModifyDateCntPo[index].modifyCntByDay"> 0 </td> -->
                    <td
                      :class="
                        subItem.dtsDailyModifyDateCntPo[index].modifyCntByDay > 9
                          ? 'add-count-green'
                          : 'add-count-red'
                      "
                    >
                      <div
                        :class="
                          subItem.dtsDailyModifyDateCntPo[index].modifyCntByDay > 9
                            ? 'add-count-bckgreen'
                            : 'add-count-bckred'
                        "
                      >
                        {{
                          formatData(
                            subItem.dtsDailyModifyDateCntPo[index].modifyCntByDay,
                            subItem.dtsDailyModifyDateCntPo[index]
                          )
                        }}
                      </div>
                    </td>

                    <td class="total-count-blue">
                      <div class="total-count-bckblue">
                        {{ formatData(subItem.dtsDailyModifyDateCntPo[index].totalModifyCnt) }}
                      </div>
                    </td>
                  </template>
                  <!-- </template> -->
                </tr>
                <tr>
                  <td>新增合计</td>
                  <template v-for="(item, index) in DTSSolveStateData" :key="index">
                    <td
                      colspan="2"
                      :class="item.addModifyCnt > 9 ? 'add-count-green' : 'add-count-red'"
                    >
                      <div
                        :class="item.addModifyCnt > 9 ? 'add-count-bckgreen' : 'add-count-bckred'"
                      >
                        {{ item.addModifyCnt }}
                      </div>
                    </td>
                  </template>
                </tr>
              </tbody>
            </n-table>
          </n-card>
        </n-tab-pane>
        <n-tab-pane name="dailyWorkStatistics" tab="日工作量统计">
          <!-- 表单查询部分 -->
          <n-card>
            <n-form
              :model="searchForm"
              label-placement="left"
              label-width="auto"
              require-mark-placement="right-hanging"
              @submit.prevent="handleSearch"
            >
              <n-grid :cols="24" :x-gap="24">
                <n-grid-item v-for="item in searchDailyWorkStatistics" :key="item.field" :span="8">
                  <n-form-item :label="item.label" :path="item.field">
                    <n-date-picker
                      v-if="item.component === 'DateRangePicker'"
                      clearable
                      v-model:value="searchDailyWorkStatistics[item.field]"
                      type="date"
                      :is-date-disabled="disablePreviousDate"
                      @update-value="onConfirmDaily"
                    />
                    <n-select
                      v-else-if="item.component === 'Select'"
                      v-model:value="searchForm[item.field]"
                      :options="item.componentProps.options"
                      clearable
                      filterable
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
              <div class="form-actions">
                <n-space>
                  <n-button @click="resetForm">重置</n-button>
                  <n-button type="primary" attr-type="submit">查询</n-button>
                </n-space>
              </div>
            </n-form>
          </n-card>
          <!-- 数据表格 -->
          <n-card class="table-container">
            <n-data-table
              remote
              :columns="dailyWorkStatisticsColumns"
              :data="dailyWorkStatisticsData"
              :bordered="true"
              :single-line="false"
              :loading="loadingDaily"
              @update:sorter="handleDailySorterChange"
              size="small"
            />
          </n-card>
        </n-tab-pane>
        <n-tab-pane name="testReturnState" tab="测试回归状态人员统计分布">
          <!-- 折线图部分 -->
          <n-card class="table-container">
            <div ref="testNumChart" class="chart"></div>
          </n-card>
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { ewpService as service } from '@/utils/axios';
  import { NCard, NButton, NIcon, NSpace } from 'naive-ui';
  import {
    searchFormItemsDTSResolveStates,
    searchFormItemsDTSdistribution,
    futColumns,
    searchFormItemsFUT,
    searchDailyWorkStatistics,
  } from './columns';
  import * as echarts from 'echarts';
  import { h, watch } from 'vue';
  import statisticsTable from '../betaClubDataView/index.vue';
  import BugStatusDistribution from './components/BugStatusDistribution.vue';
  import { Settings } from '@vicons/ionicons5';
  const dailySortState = ref({
    sortField: 'total',
    sortOrder: 'desc',
  });

  let tabName = ref('futDataState');

  const searchForm = reactive({
    orderId: '',
    appName: '',
    ewpOwner: '',
    severity: null,
    status: null,
    currentHandler: '',
    irOrderId: '',
    irOrderStatus: null,
    top: '',
    name: null,
  });
  let DTSSolveStateData = reactive([
    {
      dtsDailyModifyDateCntPo: null,
      modifier: '',
    },
  ]);
  let DTSSolveStateDataAll = reactive([
    {
      dtsDailyModifyDateCntPo: null,
      modifier: '',
    },
  ]);
  let DTSSolveStateDataTotal = 0;
  const loading = ref(false);
  const loadingDTS = ref(false);
  const loadingAllDTS = ref(false);
  const loadingRes = ref(false);
  const loadingDaily = ref(false);
  const loadingFUTRes = ref(false);
  const loadingFUTBetaRes = ref(false);
  const loadingTestNum = ref(false);
  const paginationReactive = reactive({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    showSizePicker: true,
    pageSizes: [10, 20, 50],
    prefix({ itemCount }) {
      return `Total：${itemCount}`;
    },
    onChange: (page: number) => {
      paginationReactive.page = page;
      if (tabName.value === 'DTSSolveStates') {
        fetchDTSResolveStateData();
        fetchDTSResolveStateDataAll();
      } else if (tabName.value === 'DTSResolveState') {
        fetchDTSdistributionData();
      } else if (tabName.value === 'dailyWorkStatistics') {
        fetchDailyWorkStatisticsData();
      }
    },
    onUpdatePageSize: (pageSize: number) => {
      paginationReactive.pageSize = pageSize;
      paginationReactive.page = 1;
      if (tabName.value === 'DTSSolveStates') {
        fetchDTSResolveStateData();
        fetchDTSResolveStateDataAll();
      } else if (tabName.value === 'DTSResolveState') {
        fetchDTSdistributionData();
      } else if (tabName.value === 'dailyWorkStatistics') {
        fetchDailyWorkStatisticsData();
      }
    },
  });

  const handleDailySorterChange = (sorter) => {
    if (sorter) {
      const { columnKey, order } = sorter;
      dailySortState.value.sortField = columnKey;
      dailySortState.value.sortOrder = order === 'ascend' ? 'asc' : 'desc';
    } else {
      dailySortState.value.sortField = 'total';
      dailySortState.value.sortOrder = 'desc';
    }
    fetchDailyWorkStatisticsData();
  };

  const futBetaColumns = [
    {
      title: '',
      key: 'lable',
      colSpan: (rowData, rowIndex) => (rowIndex === 8 ? 3 : 1),
      render(row) {
        if (row.lable == '总量') {
          // 使用 render 函数来自定义渲染内容，并添加类名
          return h('div', { class: 'centered-cell' }, row.lable);
        } else {
          return h('div', row.lable);
        }
      },
    },
    {
      title: '公测前',
      key: 'beforeOpen',
    },
    {
      title: '公测后',
      key: 'afterOpen',
    },
    {
      title: '总计',
      key: 'total',
    },
  ];

  // 请求公测前后数据参数
  const futBetaData = ref([
    {
      lable: '待分析',
      beforeOpen: '',
      afterOpen: '',
      total: '',
    },
    {
      lable: '待提单',
      beforeOpen: '',
      afterOpen: '',
      total: '',
    },
    {
      lable: '已挂起',
      beforeOpen: '',
      afterOpen: '',
      total: '',
    },
    {
      lable: '问题定界',
      beforeOpen: '',
      afterOpen: '',
      total: '',
    },
    {
      lable: '待锁定',
      beforeOpen: '',
      afterOpen: '',
      total: '',
    },
    {
      lable: '待修复',
      beforeOpen: '',
      afterOpen: '',
      total: '',
    },
    {
      lable: '待回归',
      beforeOpen: '',
      afterOpen: '',
      total: '',
    },
    {
      lable: '已闭环',
      beforeOpen: '',
      afterOpen: '',
      total: '',
    },
    {
      lable: '总量',
      beforeOpen: '',
      afterOpen: '',
      total: '',
    },
  ] as any);
  const futBetaDataLink = 'futOrder/overviewTotal';
  /**
   * 请求fut公测前后数据方法
   * @param link 请求的链接
   */
  async function getFUTBetaDataList(link): Promise<any> {
    try {
      const response = await service.get(`/management/${link}`);
      return response;
    } catch (error) {
      console.error('Error fetching work order list:', error);
      throw error;
    }
  }

  //  请求FUT公测前和数据
  const fetchFUTBetaData = async () => {
    loadingFUTBetaRes.value = true;
    try {
      const data = await getFUTBetaDataList(futBetaDataLink);
      futBetaData.value[0].beforeOpen = data.toBeAnalyzedBefore;
      futBetaData.value[0].afterOpen = data.toBeAnalyzedAfter;
      futBetaData.value[0].total = data.toBeAnalyzedTotal;
      futBetaData.value[1].beforeOpen = data.toBeCreatedBefore;
      futBetaData.value[1].afterOpen = data.toBeCreatedAfter;
      futBetaData.value[1].total = data.toBeCreatedTotal;
      futBetaData.value[2].beforeOpen = data.pendingBefore;
      futBetaData.value[2].afterOpen = data.pendingAfter;
      futBetaData.value[2].total = data.pendingTotal;
      futBetaData.value[3].beforeOpen = data.toBeLocatedBefore;
      futBetaData.value[3].afterOpen = data.toBeLocatedAfter;
      futBetaData.value[3].total = data.toBeLocatedTotal;
      futBetaData.value[4].beforeOpen = data.toBeLockedBefore;
      futBetaData.value[4].afterOpen = data.toBeLockedAfter;
      futBetaData.value[4].total = data.toBeLockedTotal;
      futBetaData.value[5].beforeOpen = data.toBeRepairedBefore;
      futBetaData.value[5].afterOpen = data.toBeRepairedAfter;
      futBetaData.value[5].total = data.toBeRepairedTotal;
      futBetaData.value[6].beforeOpen = data.toBeReturnedBefore;
      futBetaData.value[6].afterOpen = data.toBeReturnedAfter;
      futBetaData.value[6].total = data.toBeReturnedTotal;
      futBetaData.value[7].beforeOpen = data.closedBefore;
      futBetaData.value[7].afterOpen = data.closedAfter;
      futBetaData.value[7].total = data.closedTotal;
      futBetaData.value[8].total = data.total;
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loadingFUTBetaRes.value = false;
    }
  };

  // 请求fut数据统计参数
  const fuTtableData = ref([] as any);
  const futDataParams = {
    startTime: '',
    endTime: '',
  };

  interface futDataParams {
    startTime?: string;
    endTime?: string;
  }

  const futDataLink = 'futOrder/overview';
  /**
   * 请求fut数据统计方法
   * @param link 请求的链接
   * @param params 入参
   */
  async function getFUTDataList(link, params: futDataParams): Promise<any> {
    try {
      const response = await service.post(`/management/${link}`, params);
      return response;
    } catch (error) {
      console.error('Error fetching work order list:', error);
      throw error;
    }
  }

  //  请求FUT数据统计
  const fetchFUTallData = async () => {
    fuTtableData.value = [];
    loadingFUTRes.value = true;
    try {
      const queryParams = {
        startTime: futDataParams.startTime,
        endTime: futDataParams.endTime,
      };
      const data = await getFUTDataList(futDataLink, queryParams);
      const dateKeys = Object.keys(data).sort(function (a, b) {
        return new Date(a) - new Date(b);
      });
      dateKeys.forEach((dateKey) => {
        const currentRowData = {
          date: dateKey,
        };
        Object.keys(data[dateKey]).forEach((stateKey) => {
          currentRowData[stateKey] = data[dateKey][stateKey];
        });
        fuTtableData.value.push(currentRowData);
      });
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loadingFUTRes.value = false;
    }
  };

  /**
   * 请求柱状图数据方法
   * @param link 请求的链接
   * @param params 入参
   */

  const testReturnStateLink = 'workOrder/testerDistribution';

  const testReturnStateName = ref([] as any);
  const testReturnStateTotal = ref([] as any);

  // 测试人员测试回归状态问题单数量柱状图数据处理
  const fetchTestNumData = async () => {
    loadingTestNum.value = true;
    try {
      const data = await service.post(`/management/${testReturnStateLink}`);
      // data.unshift({name: '名字', total: null});
      console.log(data, 'data');
      for (let i = 0; i < data.length; i++) {
        testReturnStateName.value.push(data[i].name);
        testReturnStateTotal.value.push(data[i].total);
      }
      initTestNumChart();
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loadingTestNum.value = false;
    }
  };

  // 测试人员测试回归状态问题单数量柱状图
  const testNumChart = ref(null);
  const initTestNumChart = () => {
    console.log(testNumChart.value, 'chartValue');
    if (!testNumChart.value) return;
    const chart = echarts.init(testNumChart.value, 'dark');
    const option = {
      backgroundColor: '#f0f0f0',
      title: {
        text: '测试人员回归测试问题单统计',
        textStyle: {
          fontSize: 18, // 调整字体大小
          color: '#333', // 调整字体颜色
        },
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.7)', //设置背景颜色为深色，这里使用的是 RGBA 格式，其中 A 为透明度
        textStyle: {
          color: '#fff', //设置文字颜色为白色，以便在深色背景上更清晰可见
        },
      },
      xAxis: {
        type: 'category',
        data: testReturnStateName.value,
        axisLabel: {
          show: true,
          interval: 0, // 强制显示所有标签
        },
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '回归测试问题单数',
          type: 'bar',
          color: '#0558ea',
          data: testReturnStateTotal.value,
          align: 'right',
          label: {
            show: true, // 显示数据
            position: 'top', // 数据显示在柱子顶部
            fontSize: 14, // 字体大小
            color: '#180202', // 字体颜色
          },
        },
      ],
    };
    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
  };

  const trendChartData = ref([] as any);

  // 接口请求获取折线图数据参数
  const trendChartDataParams = {
    startDate: '',
    endDate: '',
    pageNo: 0,
    pageSize: 0,
  };

  interface trendChartDataParams {
    startDate?: string;
    endDate?: string;
    pageNo?: number;
    pageSize?: number;
  }

  const trendChartDataLink = 'overview/dtsview';
  /**
   * 请求折线图数据方法
   * @param link 请求的链接
   * @param params 入参
   */
  async function getTrendChartDataList(link, params: trendChartDataParams): Promise<any> {
    try {
      const response = await service.post(`/management/${link}`, params);
      return response;
    } catch (error) {
      console.error('Error fetching work order list:', error);
      throw error;
    }
  }
  const toBeAnalyzedData = ref([] as any);
  const toBeLocatedData = ref([] as any);
  const toBeOutputKnowledgeData = ref([] as any);
  const toBeLockedData = ref([] as any);
  const toBeRepairedData = ref([] as any);
  const toBeReturnedData = ref([] as any);
  const closedData = ref([] as any);
  const fetchTrendChartData = async () => {
    loadingRes.value = true;
    try {
      const queryParams = {
        startDate: '',
        endDate: '',
        pageNo: 0,
        pageSize: 0,
      };
      const data = await getTrendChartDataList(trendChartDataLink, queryParams);
      trendChartData.value = data;
      // trendChartData.value = trendChartDataCeshi.value;
      for (let i = 0; i < trendChartData.value.length; i++) {
        toBeAnalyzedData.value.push(trendChartData.value[i].toBeAnalyzed);
        toBeLocatedData.value.push(trendChartData.value[i].toBeLocated);
        toBeOutputKnowledgeData.value.push(trendChartData.value[i].toBeOutputKnowledge);
        toBeLockedData.value.push(trendChartData.value[i].toBeLocked);
        toBeRepairedData.value.push(trendChartData.value[i].toBeRepaired);
        toBeReturnedData.value.push(trendChartData.value[i].toBeReturned);
        closedData.value.push(trendChartData.value[i].closed);
      }
      console.log(trendChartData.value);
      initTrendChart();
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loadingRes.value = false;
    }
  };

  // DTS单状态分布折线图
  const trendChart = ref(null);
  const initTrendChart = () => {
    if (!trendChart.value) return;
    const chart = echarts.init(trendChart.value, 'dark');
    const option = {
      backgroundColor: '#f0f0f0',
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.7)', //设置背景颜色为深色，这里使用的是 RGBA 格式，其中 A 为透明度
        textStyle: {
          color: '#fff', //设置文字颜色为白色，以便在深色背景上更清晰可见
        },
      },
      legend: { data: ['待分析', '待定界', '待输出知识', '待锁定', '待修复', '待回归', '已闭环'] },
      grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: trendChartData.value.map((item) => formatDate(item.date)),
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '待分析',
          type: 'line',
          stack: 'Total',
          data: toBeAnalyzedData.value,
          smooth: true,
        },
        {
          name: '待定界',
          type: 'line',
          stack: 'Total',
          data: toBeLocatedData.value,
          smooth: true,
        },
        {
          name: '待输出知识',
          type: 'line',
          stack: 'Total',
          smooth: true,
          data: toBeOutputKnowledgeData.value,
        },
        {
          name: '待锁定',
          type: 'line',
          stack: 'Total',
          smooth: true,
          data: toBeLockedData.value,
        },
        {
          name: '待修复',
          type: 'line',
          stack: 'Total',
          smooth: true,
          data: toBeRepairedData.value,
        },
        {
          name: '待回归',
          type: 'line',
          stack: 'Total',
          data: toBeReturnedData.value,
          smooth: true,
        },
        {
          name: '已闭环',
          type: 'line',
          stack: 'Total',
          smooth: true,
          data: closedData.value,
        },
      ],
    };
    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
  };

  /**
   * 请求折线图数据方法
   * @param link 请求的链接
   * @param params 入参
   */

  // 接口请求获取折线图数据参数
  interface dailyChartDataParams {
    startDate?: string;
    endDate?: string;
    pageNo?: number;
    pageSize?: number;
  }

  const dailyChartDataLink = 'dtsDailyModify/lineChart';

  async function getDailyChartDataList(link, params: dailyChartDataParams): Promise<any> {
    try {
      const response = await service.post(`/management/${link}`, params);
      return response;
    } catch (error) {
      console.error('Error fetching work order list:', error);
      throw error;
    }
  }

  const dailyChartData = ref([] as any);

  const topBoundarySumData = ref([] as any);
  const topModifySumData = ref([] as any);
  const allBoundarySumData = ref([] as any);
  const allModifySumData = ref([] as any);

  // 每日定界折线图数据处理
  const fetchDailyChartData = async () => {
    loadingRes.value = true;
    try {
      const queryParams = {
        startDate: '',
        endDate: '',
        pageNo: 0,
        pageSize: 0,
        top: searchForm.top,
      };
      const data = await getDailyChartDataList(dailyChartDataLink, queryParams);
      dailyChartData.value = data;
      console.log(dailyChartData.value[0]?.flag, 'dailyChartData');
      for (let i = 0; i < dailyChartData.value.length; i++) {
        topBoundarySumData.value.push(dailyChartData.value[i].topBoundarySum);
        topModifySumData.value.push(dailyChartData.value[i].topModifySum);
        dailyChartData.value[i]?.flag &&
          allBoundarySumData.value.push(dailyChartData.value[i].allBoundarySum);
        dailyChartData.value[i]?.flag &&
          allModifySumData.value.push(dailyChartData.value[i].allModifySum);
      }
      initDailyChart();
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loadingRes.value = false;
    }
  };

  // 每日定界折线图
  const dailyChart = ref(null);
  const initDailyChart = () => {
    console.log(allBoundarySumData.value, '折线图');
    if (!dailyChart.value) return;
    const chart = echarts.init(dailyChart.value, 'dark');
    const option = {
      backgroundColor: '#f0f0f0',
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.7)', //设置背景颜色为深色，这里使用的是 RGBA 格式，其中 A 为透明度
        textStyle: {
          color: '#fff', //设置文字颜色为白色，以便在深色背景上更清晰可见
        },
      },
      legend: { data: ['TOP2000 测试提单', 'TOP2000 当日定界', '全量 测试提单', '全量 当日定界'] },
      grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: dailyChartData.value.map((item) => formatDate(item.date)),
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: 'TOP2000 测试提单',
          type: 'line',
          color: '#f80606',
          symbol: 'circle',
          symbolSize: 6,
          data: topModifySumData.value,
          smooth: true,
        },
        {
          name: 'TOP2000 当日定界',
          type: 'line',
          color: '#4af307',
          symbol: 'circle',
          symbolSize: 6,
          data: topBoundarySumData.value,
          smooth: true,
        },
        {
          name: '全量 测试提单',
          type: 'line',
          smooth: true,
          color: '#af4e30',
          symbol: 'circle',
          symbolSize: 6,
          data: allModifySumData.value,
        },
        {
          name: '全量 当日定界',
          type: 'line',
          smooth: true,
          color: '#07f37d',
          symbol: 'circle',
          symbolSize: 6,
          data: allBoundarySumData.value,
        },
      ],
    };
    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
  };

  const DTSQueryParams = {
    startDate: '',
    endDate: '',
    top: '',
  };

  /**
   * 每日工单定界情况接口入参数据
   */
  interface DTSSolveQueryParams {
    startDate?: string;
    endDate?: string;
    modifier?: string | null;
    pageNo: number;
    pageSize: number;
  }

  const DTSSolveStatesLink = 'dtsDailyModify/dtsDailyModify';

  const DTSSolveColumns = () => {
    return [
      {
        title: '日期',
        key: 'date',
        resizable: true,
        width: 400,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '责任人',
        key: 'name',
        resizable: true,
        width: 200,
        ellipsis: {
          tooltip: true,
        },
        children: [
          {
            title: '合计',
            key: 'total',
          },
          {
            title: '新增',
            key: 'add',
          },
        ],
      },
    ];
  };

  /**
   * 请求每日工单定界情况
   * @param link 请求的链接
   * @param params 入参
   */
  async function getDataList(link, params: DTSSolveQueryParams): Promise<any> {
    try {
      const response = await service.post(`/management/${link}`, params);
      return response;
    } catch (error) {
      console.error('Error fetching work order list:', error);
      throw error;
    }
  }

  /**
   * 每日工单定界情况接口入参数据
   */
  interface DTSSolveQueryAllParams {
    startDate?: string;
    endDate?: string;
    modifier?: string | null;
    pageNo: number;
    pageSize: number;
  }

  const DTSSolveStatesAllLink = 'dtsDailyModify/tableQuery';

  /**
   * 请求每日工单TOP2000和全量情况
   * @param link 请求的链接
   * @param params 入参
   */
  async function getAllDataList(link, params: DTSSolveQueryAllParams): Promise<any> {
    try {
      const response = await service.post(`/management/${link}`, params);
      return response;
    } catch (error) {
      console.error('Error fetching work order list:', error);
      throw error;
    }
  }

  const handleSearch = () => {
    if (tabName.value === 'futDataState') {
      fetchFUTallData();
    } else if (tabName.value === 'DTSSolveStates') {
      if (searchForm.top === 'TOP2000') {
        allBoundarySumData.value = [];
        allModifySumData.value = [];
        fetchDailyChartData();
      }
      fetchDTSResolveStateData();
      fetchDTSResolveStateDataAll();
    } else if (tabName.value === 'DTSResolveState') {
      fetchDTSdistributionData();
    } else if (tabName.value === 'dailyWorkStatistics') {
      fetchDailyWorkStatisticsData();
    }
  };

  /** * DTS单状态分布接口入参数据 */
  const DTSResolveQueryParams = {
    startTime: '',
    endTime: '',
    pageNo: 0,
    pageSize: 10,
  };

  /**
   *  DTS单状态分布接口入参数据
   */
  interface DTSResolveQueryParams {
    startTime?: string;
    endTime?: string;
    pageNo: number;
    pageSize: number;
  }

  const DTSResolveStatesLink = 'overview/dtspage';

  /**
   * 请求DTS单状态分布
   * @param link 请求的链接
   * @param params 入参
   */
  async function getResolveDataList(link, params: DTSResolveQueryParams): Promise<any> {
    try {
      const response = await service.post(`/management/${link}`, params);
      return response;
    } catch (error) {
      console.error('Error fetching work order list:', error);
      throw error;
    }
  }
  // DTS单状态分布表格数据
  const DTSResolveStateData = ref([] as any);
  //  请求DTS单状态分布表格数据
  const fetchDTSdistributionData = async () => {
    loadingRes.value = true;
    try {
      const queryParams = {
        startTime: DTSResolveQueryParams.startTime,
        endTime: DTSResolveQueryParams.endTime,
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
      };
      const data = await getResolveDataList(DTSResolveStatesLink, queryParams);
      paginationReactive.itemCount = data.total;
      DTSResolveStateData.value = data.records;
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loadingRes.value = false;
    }
  };

  /** * 日工作量统计接口入参数据 */
  const dailyWorkQueryParams = {
    date: '',
    name: '',
    pageNo: 0,
    pageSize: 10,
  };

  /**
   * 日工作量统计接口入参数据
   */
  interface dailyWorkQueryParams {
    date?: string;
    name?: string;
    pageNo: number;
    pageSize: number;
  }

  const dailyWorkStatisticsLink = 'dtsDailyModify/data';

  /**
   * 请求日工作量统计
   * @param link 请求的链接
   * @param params 入参
   */
  async function getDailyWorkDataList(link, params: dailyWorkQueryParams): Promise<any> {
    try {
      const response = await service.post(`/management/${link}`, params);
      return response;
    } catch (error) {
      console.error('Error fetching work order list:', error);
      throw error;
    }
  }
  // 日工作量表格数据
  const dailyWorkStatisticsData = ref([] as any);
  //  请求日工作量表格数据
  const fetchDailyWorkStatisticsData = async () => {
    loadingDaily.value = true;
    try {
      const queryParams = {
        date: dailyWorkQueryParams.date,
        name: searchForm.name,
        sortField: dailySortState.value.sortField,
        sortOrder: dailySortState.value.sortOrder,
        // pageNo: paginationReactive.page,
        // pageSize: paginationReactive.pageSize,
      };
      const data = await getDailyWorkDataList(dailyWorkStatisticsLink, queryParams);
      paginationReactive.itemCount = data.total;
      dailyWorkStatisticsData.value = data;
      console.log(dailyWorkStatisticsData.value, 'dailyData');
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loadingDaily.value = false;
    }
  };

  const resetForm = () => {
    if (tabName.value === 'futDataState') {
      Object.keys(futDataParams).forEach((key) => {
        futDataParams[key] = '';
      });
      searchFormItemsFUT.value.submitTime = null;
      fetchFUTallData();
    } else if (tabName.value === 'DTSSolveStates') {
      Object.keys(DTSQueryParams).forEach((key) => {
        DTSQueryParams[key] = '';
      });
      searchForm.top = '';
      searchFormItemsDTSResolveStates.value.submitTime = null;
      fetchDTSResolveStateData();
      fetchDTSResolveStateDataAll();
      fetchDailyChartData();
    } else if (tabName.value === 'DTSResolveState') {
      Object.keys(DTSResolveQueryParams).forEach((key) => {
        DTSResolveQueryParams[key] = '';
      });
      searchFormItemsDTSdistribution.value.submitTime = null;
      paginationReactive.page = 1;
      fetchDTSdistributionData();
    } else if (tabName.value === 'dailyWorkStatistics') {
      Object.keys(dailyWorkQueryParams).forEach((key) => {
        dailyWorkQueryParams[key] = '';
      });
      searchForm.name = null;
      searchDailyWorkStatistics.value.date = null;
      paginationReactive.page = 1;
      // dailySortState.value.sortField = 'total';
      // dailySortState.value.sortOrder = 'desc';
      fetchDailyWorkStatisticsData();
    }
  };

  const fetchDTSResolveStateData = async () => {
    loadingDTS.value = true;
    try {
      const queryParams = {
        startDate: DTSQueryParams.startDate,
        endDate: DTSQueryParams.endDate,
        top: searchForm.top,
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
      };
      const data = await getDataList(DTSSolveStatesLink, queryParams);
      let dataLength = [];
      data.forEach((element) => {
        dataLength.push(element.dtsDailyModifyDateCntPo.length);
      });
      dataLength.sort((a, b) => b - a);
      let dataLengthMax = dataLength[0];
      let dataObj = {};
      DTSSolveStateData = data;
      DTSSolveStateData.forEach((item) => {
        for (let i = 0; i < dataLengthMax; i++) {
          if (item.dtsDailyModifyDateCntPo.length < dataLengthMax) {
            dataObj.modifyDate = '0';
            dataObj.modifyCntByDay = 0;
            dataObj.totalModifyCnt = 0;
            item.dtsDailyModifyDateCntPo.push(dataObj);
          }
        }
      });
      DTSSolveStateData.sort((a, b) => b.addModifyCnt - a.addModifyCnt);
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loadingDTS.value = false;
    }
  };

  const fetchDTSResolveStateDataAll = async () => {
    loadingAllDTS.value = true;
    try {
      const queryParams = {
        startDate: DTSQueryParams.startDate,
        endDate: DTSQueryParams.endDate,
        // top: searchForm.top,
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
      };
      const data = await getAllDataList(DTSSolveStatesAllLink, queryParams);
      let dataLength = [];
      data.forEach((element) => {
        dataLength.push(element.dtsDailyModifyDateCntPo.length);
      });
      dataLength.sort((a, b) => b - a);
      let dataLengthMax = dataLength[0];
      let dataObj = {};
      DTSSolveStateDataAll = data;
      // DTSSolveStateDataAll.forEach((item) => {
      //   for (let i = 0; i < dataLengthMax; i++) {
      //     if (item.dtsDailyModifyDateCntPo.length < dataLengthMax) {
      //       dataObj.modifyDate = '0';
      //       dataObj.modifyCntByDay = 0;
      //       dataObj.totalModifyCnt = 0;
      //       item.dtsDailyModifyDateCntPo.push(dataObj);
      //     }
      //   }
      // });
      // DTSSolveStateDataAll.sort((a, b) => b.addModifyCnt - a.addModifyCnt);
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loadingAllDTS.value = false;
    }
  };

  const formatDate = (dateString: string) => {
    return dateString.split('T')[0];
  };

  const formatData = (dataNumber: number, data: object) => {
    return dataNumber.toString();
  };

  // 计算变化百分比和样式
  const getChangeText = (before: number, after: number) => {
    if (!before || before === 0) {
      return after > 0 ? `+${after}` : '0';
    }
    const change = after - before;
    if (change > 0) {
      return `+${change} `;
    } else if (change < 0) {
      return `${change} `;
    } else {
      return '无变化';
    }
  };

  const getChangeClass = (before: number, after: number) => {
    if (!before || before === 0) {
      return after > 0 ? 'positive' : 'neutral';
    }
    const change = after - before;
    if (change > 0) {
      return 'positive';
    } else if (change < 0) {
      return 'negative';
    } else {
      return 'neutral';
    }
  };

  // 获取总量数据
  const getTotalData = () => {
    return futBetaData.value.find((item) => item.lable?.includes('总量'));
  };

  const disablePreviousDate = (ts: number) => {
    return ts > Date.now();
  };

  const changeTab = (name: string) => {
    tabName.value = name;
  };

  const onConfirm = (v: number | number[] | null) => {
    if (v) {
      DTSQueryParams.startDate = timestampToDate(v[0]);
      DTSQueryParams.endDate = timestampToDate(v[1]);
    }
  };

  const onConfirmResolve = (v: number | number[] | null) => {
    if (v) {
      DTSResolveQueryParams.startTime = timestampToDate(v[0]);
      DTSResolveQueryParams.endTime = timestampToDate(v[1]);
    }
  };

  const onConfirmDaily = (v: number | null) => {
    if (v) {
      dailyWorkQueryParams.date = timestampToDate(v);
    }
  };

  const onConfirmFut = (v: number | number[] | null) => {
    if (v) {
      futDataParams.startTime = timestampToDate(v[0]);
      futDataParams.endTime = timestampToDate(v[1]);
    }
  };

  /**
              时间戳转换年月日
          */

  const timestampToDate = (timestamp: number): string => {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const exportLoading = ref(false);

  const handleExportRes = () => {
    return new Promise((resolve, reject) => {
      const req = new XMLHttpRequest();
      exportLoading.value = true;
      const queryParams = {
        startTime: DTSResolveQueryParams.startTime,
        endTime: DTSResolveQueryParams.endTime,
      };
      req.open(
        'POST',
        `http://${window.location.host}/ewp/management/overview/exportOverviewData`,
        true
      );
      req.responseType = 'blob';
      req.setRequestHeader('Content-Type', 'application/json');
      req.setRequestHeader('env', 'dev');
      req.onload = function () {
        const data = req.response;
        console.log('data:', data);
        if (data.size === 0) {
          exportLoading.value = false;
          reject(new Error('No data to export'));
          return;
        }
        const blob = new Blob([data]);
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = 'DTS单状态分布.xls';
        a.href = blobUrl;
        a.click();
        exportLoading.value = false;
        resolve(true);
      };
      req.send(JSON.stringify(queryParams));
    });
  };
  const handleExportFutRes = () => {
    return new Promise((resolve, reject) => {
      const req = new XMLHttpRequest();
      exportLoading.value = true;
      const queryParams = {
        startTime: futDataParams.startTime,
        endTime: futDataParams.endTime,
      };
      req.open(
        'POST',
        `http://${window.location.host}/ewp/management/futOrder/exportOverviewData`,
        true
      );
      req.responseType = 'blob';
      req.setRequestHeader('Content-Type', 'application/json');
      req.setRequestHeader('env', 'dev');
      req.onload = function () {
        const data = req.response;
        console.log('data:', data);
        if (data.size === 0) {
          exportLoading.value = false;
          reject(new Error('No data to export'));
          return;
        }
        const blob = new Blob([data]);
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = 'FUT数据统计.xls';
        a.href = blobUrl;
        a.click();
        exportLoading.value = false;
        resolve(true);
      };
      req.send(JSON.stringify(queryParams));
    });
  };
  const handleExport = () => {
    return new Promise((resolve, reject) => {
      const req = new XMLHttpRequest();
      exportLoading.value = true;
      const queryParams = {
        startDate: DTSQueryParams.startDate,
        endDate: DTSQueryParams.endDate,
      };
      req.open(
        'POST',
        `http://${window.location.host}/ewp/management/dtsDailyModify/exportData`,
        true
      );
      req.responseType = 'blob';
      req.setRequestHeader('Content-Type', 'application/json');
      req.setRequestHeader('env', 'dev');
      req.onload = function () {
        const data = req.response;
        console.log('data:', data);
        if (data.size === 0) {
          exportLoading.value = false;
          reject(new Error('No data to export'));
          return;
        }
        const blob = new Blob([data]);
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = '每日工单定界情况.xlsx';
        a.href = blobUrl;
        a.click();
        exportLoading.value = false;
        resolve(true);
      };
      req.send(JSON.stringify(queryParams));
    });
  };

  // DTS单状态分布表格表头
  const DTSResolveStateColumns = [
    {
      title: '日期',
      key: 'date',
      render(row) {
        return timestampToDate(row.date);
      },
    },
    { title: '待分析', key: 'toBeAnalyzed' },
    { title: '待定界', key: 'toBeLocated' },
    { title: '待输出知识', key: 'toBeOutputKnowledge' },
    { title: '待锁定', key: 'toBeLocked' },
    { title: '待修复', key: 'toBeRepaired' },
    { title: '待回归', key: 'toBeReturned' },
    { title: '已闭环', key: 'closed' },
    { title: '全量', key: 'total' },
  ];

  // 日工作量统计表格
  const dailyWorkStatisticsColumns = [
    {
      title: '姓名',
      key: 'name',
      // width: 400,
      align: 'center',
      sorter: true,
    },
    {
      title: '定界',
      key: 'modify',
      align: 'center',
      sorter: true,
    },
    {
      title: '锁定',
      key: 'lock',
      align: 'center',
      sorter: true,
    },
    {
      title: '待定界',
      key: 'notModify',
      align: 'center',
      sorter: true,
    },
    {
      title: '待定界（缺失）',
      key: 'notModifyOfLack',
      align: 'center',
      sorter: true,
    },
    {
      title: '知识生产',
      key: 'knowledgeCreate',
      align: 'center',
      sorter: true,
    },
    {
      title: '提知识优化',
      key: 'knowledgeSubmit',
      align: 'center',
      sorter: true,
    },
    {
      title: '知识优化',
      key: 'knowledgeOptimize',
      align: 'center',
      sorter: true,
    },
  ];

  onMounted(() => {
    fetchFUTBetaData();
    fetchFUTallData();
    fetchTrendChartData();
    fetchDTSResolveStateData();
    fetchDTSResolveStateDataAll();
    fetchDTSdistributionData();
    fetchDailyWorkStatisticsData();
  });
  // 监听tab标签变化，重新绘制echarts图
  watch(tabName, () => {
    if (tabName.value == 'DTSResolveState') {
      fetchTrendChartData();
    } else if (tabName.value == 'DTSSolveStates') {
      fetchDailyChartData();
    } else if (tabName.value == 'testReturnState') {
      fetchTestNumData();
    }
  });
</script>

<style lang="less" scoped>
  // 整体容器样式
  .dashboard-container {
    min-height: 100vh;
    background: #f5f7fa;
    padding: 24px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
      sans-serif;
  }

  // 页面标题样式
  .page-header {
    margin-bottom: 24px;
    padding: 0 4px;

    .page-title {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
      display: flex;
      align-items: center;
      gap: 12px;
      color: #1f2937;

      .title-icon {
        color: #3b82f6;
        background: #eff6ff;
        padding: 8px;
        border-radius: 8px;
      }
    }

    .page-subtitle {
      font-size: 14px;
      color: #6b7280;
      margin: 0;
      font-weight: 400;
    }
  }

  // 主卡片样式
  .main-card {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    overflow: hidden;
  }

  // 自定义标签页样式
  .custom-tabs {
    :deep(.n-tabs-nav) {
      background: #ffffff;
      border-bottom: 1px solid #e5e7eb;
      padding: 0 24px;
    }

    :deep(.n-tabs-tab) {
      font-weight: 500;
      font-size: 14px;
      padding: 12px 16px;
      color: #6b7280;
      border-radius: 6px 6px 0 0;
      transition: all 0.2s ease;
      margin-right: 4px;

      &:hover {
        background: #f9fafb;
        color: #374151;
      }

      &.n-tabs-tab--active {
        background: #ffffff;
        color: #3b82f6;
        border-bottom: 2px solid #3b82f6;
        font-weight: 600;
      }
    }

    :deep(.n-tabs-pane) {
      padding: 0;
    }
  }

  // 工具栏样式
  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 24px;
    background: #ffffff;
    border-bottom: 1px solid #f3f4f6;

    .toolbar-left {
      .section-title {
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
        margin: 0 0 4px 0;
      }

      .section-desc {
        font-size: 14px;
        color: #6b7280;
        margin: 0;
      }
    }

    .toolbar-right {
      .compact-picker {
        :deep(.n-input) {
          border-radius: 6px;
          border: 1px solid #d1d5db;
          font-size: 14px;
        }
      }
    }
  }

  // 数据概览卡片网格
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    padding: 24px;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;

    .stat-card {
      background: #ffffff;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
      border: 1px solid #e5e7eb;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 16px;

      &:hover {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
        transform: translateY(-1px);
      }

      .stat-icon {
        font-size: 32px;
        background: #eff6ff;
        padding: 12px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 56px;
        height: 56px;
      }

      .stat-content {
        flex: 1;

        .stat-label {
          font-size: 14px;
          color: #6b7280;
          margin-bottom: 4px;
        }

        .stat-value {
          font-size: 24px;
          font-weight: 700;
          color: #1f2937;
          margin-bottom: 8px;
        }

        .stat-change {
          display: flex;
          gap: 12px;
          font-size: 12px;

          .before {
            color: #ef4444;
          }

          .after {
            color: #10b981;
          }
        }
      }
    }
  }

  // 内容布局
  .content-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    padding: 24px;

    .content-left,
    .content-right {
      background: #ffffff;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
      border: 1px solid #e5e7eb;
      overflow: hidden;
    }
  }

  // 图表和表格区域样式
  .chart-section,
  .table-section {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    margin: 0 24px 16px 24px;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
    }
  }

  // 区域标题
  .section-header {
    padding: 16px 20px;
    border-bottom: 1px solid #f3f4f6;
    background: #f9fafb;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h4 {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
      margin: 0;
    }

    .total-summary {
      display: flex;
      align-items: center;
      gap: 16px;
      font-size: 14px;

      .total-item {
        display: flex;
        align-items: center;
        gap: 6px;

        .total-label {
          color: #6b7280;
          font-weight: 500;
        }

        .total-value {
          color: #1f2937;
          font-weight: 700;
          font-size: 18px;
        }
      }

      .total-change {
        display: flex;
        gap: 12px;
        font-size: 12px;

        .total-before {
          color: #ef4444;
          font-weight: 500;
        }

        .total-after {
          color: #10b981;
          font-weight: 500;
        }
      }
    }
  }

  // 对比区域样式
  .comparison-section {
    padding: 24px;
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
  }

  .detail-section {
    padding: 24px;
    background: #ffffff;
  }

  // 对比卡片网格
  .comparison-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
    margin-top: 16px;
  }

  .comparison-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    transition: all 0.2s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);

    &:hover {
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
      transform: translateY(-1px);
    }
  }

  .comparison-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;

    .comparison-icon {
      font-size: 24px;
      background: #eff6ff;
      padding: 8px;
      border-radius: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 40px;
      height: 40px;
    }

    .comparison-title {
      font-size: 16px;
      font-weight: 600;
      color: #1f2937;
    }
  }

  .comparison-content {
    .comparison-stats {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;

      .stat-item {
        text-align: center;
        flex: 1;

        .stat-label {
          font-size: 12px;
          color: #6b7280;
          margin-bottom: 4px;
        }

        .stat-value {
          font-size: 20px;
          font-weight: 700;
          color: #1f2937;
        }

        &.before .stat-value {
          color: #ef4444;
        }

        &.after .stat-value {
          color: #10b981;
        }
      }

      .stat-divider {
        font-size: 18px;
        color: #9ca3af;
        margin: 0 16px;
      }
    }

    .comparison-change {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 12px;
      border-top: 1px solid #f3f4f6;

      .change-value {
        font-size: 14px;
        font-weight: 600;

        &.positive {
          color: #10b981;
        }

        &.negative {
          color: #ef4444;
        }

        &.neutral {
          color: #6b7280;
        }
      }

      .change-total {
        font-size: 14px;
        color: #6b7280;
        font-weight: 500;
      }
    }
  }

  // 卡片头部样式
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #1f2937;

    .header-icon {
      color: #3b82f6;
      background: #eff6ff;
      padding: 6px;
      border-radius: 6px;
    }

    .header-title {
      font-size: 16px;
    }
  }

  // 表单样式
  .search-form {
    .form-item {
      :deep(.n-form-item-label) {
        font-weight: 500;
        color: #374151;
        font-size: 14px;
      }

      .date-picker {
        :deep(.n-input) {
          border-radius: 6px;
          border: 1px solid #d1d5db;
          transition: all 0.2s ease;

          &:hover {
            border-color: #9ca3af;
          }

          &:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
          }
        }
      }
    }
  }

  // 按钮样式
  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    gap: 12px;

    .reset-btn {
      background: #ffffff;
      border: 1px solid #d1d5db;
      color: #374151;
      border-radius: 6px;
      padding: 8px 16px;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.2s ease;

      &:hover {
        background: #f9fafb;
        border-color: #9ca3af;
      }
    }

    .search-btn,
    .export-btn {
      background: #3b82f6;
      border: 1px solid #3b82f6;
      color: #ffffff;
      border-radius: 6px;
      padding: 8px 16px;
      font-weight: 500;
      font-size: 14px;
      transition: all 0.2s ease;

      &:hover {
        background: #2563eb;
        border-color: #2563eb;
      }
    }
  }

  // 清洁表格样式
  .clean-table {
    :deep(.n-data-table-th) {
      background: #f9fafb;
      font-weight: 600;
      color: #374151;
      border-bottom: 1px solid #e5e7eb;
      font-size: 14px;
      padding: 12px 16px;
    }

    :deep(.n-data-table-td) {
      transition: all 0.2s ease;
      font-size: 14px;
      color: #1f2937;
      padding: 12px 16px;
      border-bottom: 1px solid #f9fafb;
    }

    :deep(.n-data-table-tr:hover .n-data-table-td) {
      background: #f9fafb;
    }

    :deep(.n-data-table-tr:nth-child(even)) {
      background: #ffffff;
    }

    :deep(.n-data-table) {
      border: none;
    }

    :deep(.n-data-table-wrapper) {
      border-radius: 0;
    }
  }

  .thead-float {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
  }

  .table-container {
    width: 100%;
    overflow-x: auto;
    white-space: nowrap;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);

    table {
      width: 1000px;
      text-align: center;
      border-radius: 8px;
      overflow: hidden;

      .add-count-red {
        color: #ef4444;
        font-weight: 500;
      }

      .add-count-green {
        color: #10b981;
        font-weight: 500;

        .add-count-bckgreen {
          width: 100%;
          height: 100%;
          background: #d1fae5;
          border-radius: 4px;
          padding: 2px 6px;
        }
      }

      .total-count-blue {
        color: #3b82f6;
        font-weight: 500;
      }
    }
  }

  .table-header {
    margin-bottom: 16px;
    display: flex;
    justify-content: flex-end;
  }

  .chart {
    height: 400px;
    width: 100%;
    border-radius: 8px;
    background: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  }

  :deep .centered-cell {
    text-align: center !important;
    font-weight: 600;
    color: #3b82f6;
  }

  .submit-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 16px 0;
    padding: 16px;
    background: #f9fafb;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
  }

  // 响应式设计
  @media (max-width: 1024px) {
    .content-layout {
      grid-template-columns: 1fr;
    }

    .stats-grid {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .comparison-grid {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
  }

  @media (max-width: 768px) {
    .dashboard-container {
      padding: 16px;
    }

    .page-header .page-title {
      font-size: 20px;
      flex-direction: column;
      gap: 8px;
    }

    .toolbar {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .toolbar-right {
        :deep(.n-space) {
          flex-wrap: wrap;
        }
      }
    }

    .stats-grid {
      grid-template-columns: 1fr;
      padding: 16px;
    }

    .content-layout,
    .chart-table-layout {
      padding: 16px;
      gap: 16px;
    }

    .section-header {
      padding: 12px 16px;
    }
  }
</style>
