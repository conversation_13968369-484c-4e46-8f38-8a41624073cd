import { h, ref } from 'vue';
import { NButton, NTag, NSelect, NTooltip } from 'naive-ui';
import { formatDateTime } from '@/utils';
import { allIssueType, allUserType } from '@/api/feedback';
import {getPersonOptionsOnlyName} from "@/views/dataview/personManage/staffCommonUtils";
import {DTS_HANDLE_TAG} from "@/views/dataview/personManage/tagContant";

interface ComponentStyle {
  width: string;
  textAlign: string;
}

interface ComponentProps {
  style: ComponentStyle;
  clearable?: boolean;
  placeholder?: string;
  options?: Array<{ label: string; value: any }>;
  filterable?: boolean;
}

interface SearchFormItem {
  field: string;
  label: string;
  component: 'Input' | 'Select' | 'DateRangePicker';
  componentProps?: ComponentProps;
}

// 修改 createColumns 的返回类型
export const createColumns = (handleAppNameClick?: (row: any) => void): any => {
  const baseColumns = [
    {
      type: 'selection',
    },
    {
      title: '登记日期',
      key: 'registerDate',
      resizable: true,
      width: 120,
      render(row) {
        return formatDateTime(row.registerDate);
      },
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '吐槽单号',
      key: 'orderId',
      resizable: true,
      width: 120,
      ellipsis: {
        tooltip: true,
      },
      render: (row) => {
        return h(
          NTag,
          {
            type: 'primary',
            bordered: false,
            style: {
              cursor: 'pointer',
            },
          },
          {
            default: () => row.orderId,
          }
        );
      },
    },
    {
      title: '问题类型',
      key: 'type',
      resizable: true,
      width: 120,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '吐槽内容',
      key: 'description',
      resizable: true,
      width: 200,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '发生时间',
      key: 'createTime',
      resizable: true,
      width: 120,
      render(row) {
        return formatDateTime(row.createTime);
      },
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '产品名称',
      key: 'productName',
      resizable: true,
      width: 120,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '产品版本',
      key: 'productVersion',
      resizable: true,
      width: 120,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '应用名称',
      key: 'appName',
      resizable: true,
      width: 120,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '应用责任人',
      key: 'ewpOwner',
      resizable: true,
      width: 120,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: 'TOP2000',
      key: 'top',
      resizable: true,
      width: 100,
      render: (row) => (row.top ? '是' : '否'),
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '是否新问题',
      key: 'isNew',
      resizable: true,
      width: 100,
      render: (row) => (row.isNew ? '是' : '否'),
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: 'DTS单',
      key: 'dtsOrder',
      resizable: true,
      width: 120,
      render: (row) => {
        if (!row.dtsOrder) return null;
        return h(
          NTag,
          {
            type: 'info',
            border: false,
            style: {
              cursor: 'pointer',
            },
            onClick: () => {
              window.open(
                `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${row.dtsOrder}`,
                '_blank'
              );
            },
          },
          {
            default: () => row.dtsOrder,
          }
        );
      },
    },
    {
      title: '是否已登记内部表',
      key: 'isRegister',
      resizable: true,
      width: 140,
      render: (row) => (row.isRegister ? '是' : '否'),
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '备注',
      key: 'remark',
      resizable: true,
      width: 200,
      ellipsis: {
        tooltip: true,
      },
    },
  ];

  return baseColumns;
};

// 创建一个通用的样式对象
const commonStyle: ComponentProps = {
  style: {
    width: '300px',
    textAlign: 'left',
  },
  clearable: true,
  placeholder: '请输入',
};

// 为 Select 组件创建特定样式
const selectStyle: ComponentProps = {
  ...commonStyle,
  style: {
    ...commonStyle.style,
  },
  filterable: true,
  placeholder: '请选择',
};

const allUserTypeOptions = ref<Array<{ label: string; value: string }>>([]);
const getUserIssueType = async () => {
  try {
    const response = await allUserType();
    if (response && Array.isArray(response)) {
      allUserTypeOptions.value = response.map((item) => ({
        label: item,
        value: item,
      }));
    } else {
      allUserTypeOptions.value = [];
    }
  } catch (error) {
    allUserTypeOptions.value = [];
  }
};

//应用责任人
const dtsAccountOptions = await getPersonOptionsOnlyName(DTS_HANDLE_TAG);

export const getSearchFormItems = (): SearchFormItem[] => [
  {
    field: 'registerDate',
    label: '登记日期',
    component: 'DateRangePicker',
  },
  {
    field: 'orderId',
    label: '吐槽单号',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'appName',
    label: '应用名称',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'type',
    label: '问题类型',
    component: 'Input',
  },
  {
    field: 'description',
    label: '吐槽内容',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'createTime',
    label: '发生时间',
    component: 'DateRangePicker',
  },
  {
    field: 'productName',
    label: '产品名称',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'productVersion',
    label: '产品版本',
    component: 'Input',
    componentProps: commonStyle,
  },

  {
    field: 'ewpOwner',
    label: '应用责任人',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: dtsAccountOptions,
    },
  },
  {
    field: 'top',
    label: 'TOP2000',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
  },
  {
    field: 'isNew',
    label: '是否新问题',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
  },
  {
    field: 'dtsOrder',
    label: 'DTS单',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'isRegister',
    label: '是否已登记内部表',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
    },
  },
  // {
  //   field: 'remark',
  //   label: '备注',
  //   component: 'Input',
  //   componentProps: commonStyle,
  // },
];
