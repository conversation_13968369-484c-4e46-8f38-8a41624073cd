import type { EcologicalRecord } from './columns';
import * as mockAPI from './api.mock';

export const getEcologicalData = mockAPI.getEcologicalData;
export const createEcologicalData = mockAPI.createEcologicalData;
export const updateEcologicalData = mockAPI.updateEcologicalData;
export const deleteEcologicalData = mockAPI.deleteEcologicalData;
export const importPuraXHistoricalData = mockAPI.importPuraXHistoricalData;
export const syncPuraXAutoDetectedIssues = mockAPI.syncPuraXAutoDetectedIssues;

/*
import { http } from '@/utils/http';

// Interface for the response data
interface EcologicalDataResponse {
  items: EcologicalRecord[];
  total: number;
}

// Interface for filter parameters
interface EcologicalDataParams {
  page?: number;
  pageSize?: number;
  registrationDate?: [number, number] | null;
  ewpOwner?: string | null;
  problemBelonging?: string | null;
  representative?: string | null;
  appName?: string | null;
  problemDescription?: string | null;
  problemLevel?: string | null;
  source?: string | null;
  dtsTicket?: string | null;
  status?: string | null;
  sourceCloseLoop?: string | null;
  currentHandler?: string | null;
  affectedProducts?: string | null;
  planCloseLoopTime?: [number, number] | null;
}

export function getEcologicalData(params: EcologicalDataParams): Promise<EcologicalDataResponse> {
  return http.request({
    url: '/api/ecological-tracking',
    method: 'GET',
    params
  });
}

export function createEcologicalData(data: Omit<EcologicalRecord, 'id'>): Promise<EcologicalRecord> {
  return http.request({
    url: '/api/ecological-tracking',
    method: 'POST',
    data
  });
}

export function updateEcologicalData(data: EcologicalRecord): Promise<EcologicalRecord> {
  return http.request({
    url: `/api/ecological-tracking/${data.id}`,
    method: 'PUT',
    data
  });
}

export function deleteEcologicalData(id: string): Promise<void> {
  return http.request({
    url: `/api/ecological-tracking/${id}`,
    method: 'DELETE'
  });
}

export function importPuraXHistoricalData(file: File): Promise<{ success: boolean; message: string }> {
  const formData = new FormData();
  formData.append('file', file);
  
  return http.request({
    url: '/api/ecological-tracking/import/pura-x',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

export function getPuraXAutoDetectedIssues(): Promise<EcologicalDataResponse> {
  return http.request({
    url: '/api/ecological-tracking/pura-x-auto',
    method: 'GET'
  });
}

export function syncPuraXAutoDetectedIssues(): Promise<{ success: boolean; message: string }> {
  return http.request({
    url: '/api/ecological-tracking/sync-pura-x',
    method: 'POST'
  });
}
*/ 
