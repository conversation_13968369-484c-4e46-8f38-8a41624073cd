<template>
  <div>
    <!-- DTSE Leader展示 -->
    <n-card style="margin-bottom: 12px" title="我的待办" v-if="isDtseLeader">
      <n-data-table
        remote
        :bordered="false"
        :single-line="false"
        striped
        :columns="todoColumns"
        :data="todoTableData"
        :loading="todoLoading"
      />
    </n-card>
    <n-card style="margin-bottom: 12px">
      <n-form label-placement="left" label-width="100px" label-align="left">
        <n-grid x-gap="30" :cols="3" style="margin-bottom: 12px">
          <n-gi v-if="isShowNameSearch">
            <n-form-item label="姓名">
              <n-select :options="userList" v-model:value="filters.name" filterable clearable />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="日期">
              <n-date-picker v-model:value="filters.dateList" type="daterange" />
              <n-tooltip placement="bottom" trigger="hover">
                <template #trigger>
                  <n-icon size="22" style="margin-left: 8px">
                    <HelpCircleOutline />
                  </n-icon>
                </template>
                <span>
                  社区问题闭环数，社区交叉审核贡献，驻场开发天数不支持按时间查询，显示结果为交付总量
                </span>
              </n-tooltip>
            </n-form-item>
          </n-gi>
        </n-grid>
      </n-form>
      <n-space justify="center">
        <n-button type="primary" @click="handleGetOutputList">查询 </n-button>
        <n-button type="default" @click="handleResetFilter">重置 </n-button>
      </n-space>
    </n-card>
    <div class="layout-page-header">
      <n-space>
        <n-button secondary strong type="warning" @click="handleGetOutputList()">
          <template #icon>
            <n-icon>
              <Refresh />
            </n-icon>
          </template>
          刷新
        </n-button>
        <n-button
          v-if="isShowNameSearch"
          secondary
          strong
          type="primary"
          @click="exportOutputList(getTimes())"
        >
          导出
        </n-button>
        <!-- <n-button v-if="isShowButton" secondary strong type="primary" @click="mrHandle()">
          导入MR检视意见数量
        </n-button> -->
        <!-- <n-button
          v-if="isShowButton"
          secondary
          strong
          type="primary"
          @click="codeDevelopmentHandle()"
        >
          导入工程代码开发行数
        </n-button> -->
        <n-button
          v-if="isShowButton"
          secondary
          strong
          type="primary"
          @click="showCommunityProblemData = true"
        >
          导入社区问题数据
        </n-button>
        <n-button secondary strong type="primary" @click="queryOnsiteRecordInfo"
          >驻场信息
        </n-button>
        <n-button v-if="isShowButton" secondary strong type="primary" @click="showVoice = true">
          导入开发者声音
        </n-button>
        <n-alert :showIcon="false" type="info" class="alert-info"> 当前数据跨{{quarterNumber}}个季度，审核分上限{{reviewNubmber}}分，总分上限{{totalNubmber}}分。</n-alert>
      </n-space>
    </div>
    <n-data-table
      :loading="isLoading"
      :columns="columns"
      :data="tableData"
      :pagination="pagination"
      :scroll-x="2500"
      :bordered="false"
      :single-line="false"
      :row-props="rowProps"
      :max-height="700"
      virtual-scroll
      style="margin-top: 20px"
    />
    <n-modal v-model:show="showMRReviewNumber">
      <n-card
        style="width: 600px"
        title="批量导入"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <div style="margin-bottom: 20px">最近一次导入时间：{{ latestMrReviewTime }}</div>
        <n-upload
          action="#"
          :custom-request="importMRReviewNumber"
          :multiple="true"
          :default-file-list="fileList"
          accept=".xls,.xlsx,"
          :max="1"
          directory-dnd
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <Add />
              </n-icon>
            </div>
            <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
          </n-upload-dragger>
        </n-upload>
      </n-card>
    </n-modal>

    <n-modal v-model:show="showCodeDevelopmentNumber">
      <n-card
        style="width: 600px"
        title="批量导入"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <div style="margin-bottom: 20px">最近一次导入时间：{{ latestMrCodeLineTime }}</div>
        <n-upload
          action="#"
          :custom-request="importCodeDevelopmentNumber"
          :multiple="true"
          :default-file-list="fileList"
          accept=".xls,.xlsx,"
          :max="1"
          directory-dnd
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <Add />
              </n-icon>
            </div>
            <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
          </n-upload-dragger>
        </n-upload>
      </n-card>
    </n-modal>

    <n-modal v-model:show="showCommunityProblemData">
      <n-card
        style="width: 600px"
        title="批量导入"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-upload
          action="#"
          :custom-request="importCommunityProblemData"
          :multiple="true"
          :default-file-list="fileList"
          accept=".xls,.xlsx,"
          :max="1"
          directory-dnd
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <Add />
              </n-icon>
            </div>
            <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
          </n-upload-dragger>
        </n-upload>
      </n-card>
    </n-modal>
    <n-modal v-model:show="showVoice">
      <n-card
        style="width: 600px"
        title="导入开发者声音"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-upload
          action="#"
          :custom-request="importVoiceData"
          :multiple="true"
          :default-file-list="fileList"
          accept=".xls,.xlsx,"
          :max="1"
          directory-dnd
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <Add />
              </n-icon>
            </div>
            <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
          </n-upload-dragger>
        </n-upload>
      </n-card>
    </n-modal>
    <n-modal v-model:show="showResidentInfo">
      <n-card
        style="width: 1000px"
        title="驻场信息"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-button type="primary" @click="addLine"> 新增 </n-button>
        <n-data-table
          style="margin-top: 16px"
          remote
          :bordered="false"
          :single-line="false"
          striped
          :columns="residentColumns"
          :data="residentInfoData"
          :loading="residentInfoLoading"
        />
        <n-space justify="center" style="margin-top: 12px" v-if="ifAdd">
          <n-button type="primary" @click="handleCreate">提交 </n-button>
          <n-button type="default" @click="ifAdd = false">取消 </n-button>
        </n-space>
      </n-card>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { Add, Refresh, Close, GridOutline, HelpCircleOutline } from '@vicons/ionicons5';
  import { DownOutlined, UpOutlined } from '@vicons/antd';
  import {
    NIcon,
    NButton,
    NTag,
    useDialog,
    UploadCustomRequestOptions,
    useMessage,
    NTooltip,
    NGradientText,
    NFlex,
    NInput,
    NSelect,
    NDatePicker,
  } from 'naive-ui';
  import { h, ref, reactive, onMounted, computed } from 'vue';
  import { formatDateTime } from '@/utils';
  import { getDefalutFiltersModel, getFiltersModel, statusMap, remarkOptions } from './index';
  import { useUserStore } from '@/store/modules/user';
  import { getEmployeeList, queryUser } from '@/api/system/usermanage';
  import {
    getOutputList,
    exportOutputList,
    mRReviewNumberImport,
    codeDevelopmentNumberImport,
    communityProblemDataImport,
    residentDevelopmentDaysImport,
    latestMrReview,
    latestMrCodeLine,
    createOnsiteRecord,
    queryOnsiteRecord,
    updateOnsiteRecord,
    queryToBeApproved,
    deleteOnsiteRecord,
    importVod,
    updateRemark,
  } from '@/api/dataview/statisticalOverview';
  import { UserRoleEnum } from '@/enums/UserRoleEnum';
  import { useEventBus } from '@/hooks/useEventBus';
  const userStore = useUserStore();

  let roles = userStore.getUserInfo.roles;
  let role = '2_admin';
  const isAdmin = roles.includes('2_admin') || userStore.getUserInfo.isSuperAdmin;
  const isShowButton =
    roles.includes(UserRoleEnum.SUPER_ADMIN) || roles.includes(UserRoleEnum.MANAGER_USER); // 超级管理员/主管
  const isDtseLeader = roles.includes(UserRoleEnum.DTSE_LEADER);
  const isShowNameSearch =
    roles.includes(UserRoleEnum.SUPER_ADMIN) ||
    roles.includes(UserRoleEnum.DEPARTMENT_ADMIN) ||
    roles.includes(UserRoleEnum.MANAGER_USER); // 部门管理员/主管
  let currentHandler = userStore.getUserInfo.userName;

  const collapse = ref(false);
  const filters = ref(getDefalutFiltersModel());
  const dialog = useDialog();
  const message = useMessage();
  const isLoading = ref(false);
  const residentInfoLoading = ref(false);
  const showMRReviewNumber = ref(false); // 导入MR检视意见数量
  const latestMrReviewTime = ref('123456'); // mr最近的检视时间
  const showCodeDevelopmentNumber = ref(false); // 导入工程代码开发行数
  const latestMrCodeLineTime = ref(''); // 工程代码行数最近的检视时间
  const showCommunityProblemData = ref(false); // 导入社区问题数据
  const showVoice = ref(false); // 导入开发者声音
  const showResidentDevelopmentDays = ref(false); // 导入驻场开发天数
  const showResidentInfo = ref(false); // 驻场信息
  const ifAdd = ref(false);
  const isEdit = ref(false);
  const leaderList = ref([]);
  const userList = ref([]);
  const commonCol = [
    {
      title: '起始时间',
      key: 'startDate',
      render(row, index) {
        if (!row.isEdit) {
          return row.startDate;
        }
        return h(NDatePicker, {
          value: row.startDate,
          type: 'date',
          format: 'yyyy/MM/dd',
          size: 'small',
          onUpdateValue(v) {
            residentInfoData.value[index]['startDate'] = formatDateTime(v);
          },
        });
      },
    },
    {
      title: '结束时间',
      key: 'endDate',
      render(row, index) {
        if (!row.isEdit) {
          return row.endDate;
        }
        return h(NDatePicker, {
          value: row.endDate,
          type: 'date',
          format: 'yyyy/MM/dd',
          size: 'small',
          onUpdateValue(v) {
            residentInfoData.value[index]['endDate'] = formatDateTime(v);
          },
        });
      },
    },
    {
      title: '驻场应用',
      key: 'appName',
      render(row, index) {
        if (!row.isEdit) {
          return row.appName;
        }
        return h(NInput, {
          value: row.appName,
          type: 'text',
          size: 'small',
          onUpdateValue(v) {
            residentInfoData.value[index]['appName'] = v;
          },
        });
      },
    },
    {
      title: '代码量',
      key: 'codeLines',
      render(row, index) {
        if (!row.isEdit) {
          return row.codeLines;
        }
        return h(NInput, {
          value: row.codeLines,
          type: 'text',
          size: 'small',
          onUpdateValue(v) {
            residentInfoData.value[index]['codeLines'] = Number(v);
          },
        });
      },
    },
    {
      title: '状态',
      key: 'status',
      render(row, index) {
        if (!row.isEdit) {
          return statusMap[row.status];
        }
        return h(NInput, {
          // 审批中传0 通过传1   驳回2
          value: statusMap[row.status],
          disabled: true,
          type: 'text',
          size: 'small',
        });
      },
    },
    {
      title: '审核人',
      key: 'leaderName',
      render(row, index) {
        if (!row.isEdit) {
          return row.leaderName;
        }
        return h(NSelect, {
          value: row.leaderName,
          options: leaderList.value,
          filterable: true,
          style: { width: '130px' },
          size: 'small',
          onChange(v) {
            residentInfoData.value[index]['leader'] = v;
            const fileList = leaderList.value.filter((item) => {
              return item.value == v;
            });
            residentInfoData.value[index]['leaderName'] = fileList[0].label;
          },
        });
      },
    },
  ];
  const col = [
    {
      title: '姓名/工号',
      key: 'name',
      width: 110,
      fixed: 'left',
      render: (row) => {
        return h('span', {}, row.name ? `${row.name}/${row.account}` : '');
      },
    },
    {
      title: '人员属性',
      key: 'remark',
      width: 110,
      fixed: 'left',
      render(row) {
        if (row.remark === '计分规则') {
          return h('span', {}, '计分规则');
        } else {
          return h(NSelect, {
            value: row.remark,
            clearable: true,
            filterable: true,
            disabled: !isShowButton,
            options: ref(remarkOptions).value,
            size: 'small',
            title: row.ownerFunction,
            style: { width: '100%' },
            async onUpdateValue(v) {
              row.remark = v || undefined;
              try {
                let res = await updateRemark({
                  account: `${row.account}`,
                  remark: v || '',
                });
                if (res.status == '200') {
                  message.success('修改成功！');
                  handleGetOutputList();
                } else {
                  message.warning('修改失败！');
                }
              } catch (e) {}
            },
          });
        }
      },
    },
    {
      title: '问题处理交付件',
      key: 'account',
      align: 'center',
      resizable: true,
      children: [
        {
          title: 'L1问题',
          key: 'l1Problem',
          width: 60,
          resizable: true,
          ellipsis: false,
        },
        {
          title: 'L2问题数量',
          key: 'l2Problem',
          width: 60,
          resizable: true,
          ellipsis: false,
        },
        {
          title: '社区问题数量',
          key: 'communityProblem',
          width: 60,
          resizable: true,
          ellipsis: false,
        },
        {
          title: '开发者声音收集',
          key: 'vod',
          width: 70,
          resizable: true,
          ellipsis: false,
        },
        {
          title: '消费者舆情问题',
          key: 'ewpProblem',
          width: 70,
          resizable: true,
          ellipsis: false,
        },
      ],
    },
    {
      title: '知识沉淀交付件',
      key: 'account',
      align: 'center',
      children: [
        {
          title: '知识优化',
          key: 'optimizeCount',
          resizable: true,
          ellipsis: false,
          width: 70,
        },
        {
          title: 'FAQ',
          key: 'createFaqCount',
          resizable: true,
          ellipsis: false,
          width: 60,
        },
        {
          title: '其他场景化知识',
          key: 'createNonFaqCount',
          resizable: true,
          ellipsis: false,
          width: 70,
        },
        {
          title: '样例demo分数',
          key: 'demo',
          width: 70,
          resizable: true,
          ellipsis: false,
        },
        {
          title: '垂域解决方案',
          key: 'verticalSolution',
          width: 70,
          resizable: true,
          ellipsis: false,
        },
      ],
    },
    {
      title: '审核贡献（最高80）',
      key: 'account',
      align: 'center',
      children: [
        {
          title: '社区问题交叉审核',
          key: 'communityReview',
          width: 80,
          resizable: true,
          ellipsis: false,
        },
        {
          title: 'MR检视意见',
          key: 'mrReview',
          width: 60,
          resizable: true,
          ellipsis: false,
        },
        {
          title: '知识生产L2审核',
          key: 'l2ReviewCount',
          resizable: true,
          ellipsis: false,
          width: 80,
        },
        {
          title: '开发者声音审核',
          key: 'vodReview',
          resizable: true,
          ellipsis: false,
          width: 80,
        },
        {
          title: 'IR工单质量审核',
          key: 'irQualityReview',
          resizable: true,
          ellipsis: false,
          width: 70,
        },
        {
          title: '知识生产L2.5审核',
          key: 'l25ReviewCount',
          resizable: true,
          ellipsis: false,
          width: 80,
        },
        {
          title: '知识生产Committer审核',
          key: 'committerReviewCount',
          resizable: true,
          ellipsis: false,
          width: 100,
        },
        {
          title: 'IR问题L2.5审核',
          key: 'irReview',
          width: 80,
          resizable: true,
          ellipsis: false,
        },
        {
          title: '样例demo评审',
          key: 'demoInspection',
          width: 80,
          resizable: true,
          ellipsis: false,
        },
        {
          title: '白盒评价审核',
          key: 'whiteCaseReview',
          width: 70,
          resizable: true,
          ellipsis: false,
        },
      ],
    },
    {
      title: '额外计分项',
      key: 'account',
      align: 'center',
      children: [
        {
          title: '驻场开发天数',
          key: 'onSiteDays',
          width: 70,
          resizable: true,
          ellipsis: false,
        },
        {
          title: '工程代码开发',
          key: 'projectDevelop',
          width: 70,
          resizable: true,
          ellipsis: false,
        },
      ],
    },
    {
      title: '最终分数（最高160）',
      key: 'amount',
      resizable: true,
      ellipsis: false,
      width: 80,
      sorter: (row1, row2) => row1.amount - row2.amount,
    },
  ];
  const todoColumns = computed(() => {
    const col = [
      {
        title: '提交人',
        key: 'name',
        width: 190,
        render: (row) => {
          return h('span', {}, `${row.name}/${row.account}`);
        },
      },
    ];
    const configCol = [
      {
        title: '操作',
        key: 'config',
        render(row, index) {
          return h(NFlex, { wrap: false }, () => [
            h(
              NButton,
              {
                text: true,
                strong: true,
                type: 'info',
                size: 'tiny',
                disabled: ifAdd.value,
                onClick: () => {
                  handleUpdate(row, 'approved');
                },
              },
              { default: () => '通过' }
            ),
            h(
              NButton,
              {
                text: true,
                strong: true,
                type: 'error',
                size: 'tiny',
                // disabled: !isEdit.value,
                onClick: async () => {
                  handleUpdate(row, 'reject');
                },
              },
              { default: () => '驳回' }
            ),
          ]);
        },
      },
    ];
    return col.concat(commonCol).concat(configCol);
  });
const residentColumns = computed(() => {
  const col = [
    {
      title: '操作',
      key: 'config',
      render(row, index) {
        return h(NFlex, { wrap: false }, () => [
          h(
            NButton,
            {
              text: true,
              strong: true,
              type: 'info',
              size: 'tiny',
              disabled: ifAdd.value || row.status === 1,
              onClick: () => {
                row.isEdit = !row.isEdit;
                !row.isEdit && handleUpdate(row, null);
              },
            },
            { default: () => (row.isEdit ? '提交' : '编辑') }
          ),
          h(
            NButton,
            {
              text: true,
              strong: true,
              type: 'error',
              size: 'tiny',
              onClick: async () => {
                dialog.info({
                  title: '提示',
                  content: '确定要删除该条信息吗？',
                  positiveText: '确定',
                  negativeText: '取消',
                  onPositiveClick: async () => {
                    let data = [];
                    data.push(row.id);
                    let res = await deleteOnsiteRecord(data);
                    if (res.status == '200') {
                      message.success('删除成功');
                      queryOnsiteRecordInfo();
                    } else {
                      message.warning('删除失败！');
                    }
                  },
                  onNegativeClick: () => {},
                });
              },
            },
            { default: () => '删除' }
          ),
        ]);
      },
    },
  ];
  return commonCol.concat(col);
});
const todoTableData = ref([]);
const todoLoading = ref(false);
const eventBus = useEventBus();
onMounted(async () => {
  let { data } = await queryUser({
    pageNum: 1,
    pageSize: 1000,
    user: {
      roles: ['8_dtse_leader'],
    },
  });
  leaderList.value = data.data.map((item) => {
    return {
      label: item.userName,
      value: item.account,
    };
  });
  handleToBeApproved();
});
const columns = ref(col);
const residentInfoData = ref([{}]);
// 查询待办
const handleToBeApproved = async () => {
  todoLoading.value = true;
  let res = await queryToBeApproved();
  try {
    if (res.status == '200') {
      todoLoading.value = false;
      todoTableData.value = res.data;
    }
  } catch (error) {
    todoLoading.value = false;
    onError();
  }
};
// 查询驻场信息
const queryOnsiteRecordInfo = async () => {
  ifAdd.value = false;
  showResidentInfo.value = true;
  residentInfoLoading.value = true;
  let res = await queryOnsiteRecord();
  try {
    if (res.status == '200') {
      residentInfoLoading.value = false;
      residentInfoData.value = res.data;
      residentInfoData.value.forEach((item) => (item.isEdit = false));
    }
  } catch (error) {
    residentInfoLoading.value = false;
    onError();
  }
};
// 新增驻场信息
const handleCreate = async () => {
  const list = ['appName', 'codeLines', 'endDate', 'leaderName', 'startDate', 'status', 'leader'];
  let data = [];
  residentInfoData.value.forEach((item) => {
    if (item.isEdit) {
      let obj = {};
      list.forEach((key) => {
        obj[key] = item[key];
      });
      data.push(obj);
    }
  });
  let res = await createOnsiteRecord(data);
  if (res.status == '200') {
    message.success('新增成功');
    queryOnsiteRecordInfo();
  }
};
// 编辑驻场信息
const handleUpdate = async (row, result) => {
  let obj = {};
  const list = [
    'id',
    'appName',
    'codeLines',
    'endDate',
    'leader',
    'leaderName',
    'startDate',
    'status',
  ];
  list.forEach((key) => {
    if (key === 'status') {
      obj[key] = result === 'approved' ? 1 : result === 'reject' ? 2 : 0;
    } else {
      if (result) {
        obj[key] = row[key];
      } else {
        obj[key] = row[key];
      }
    }
  });
  let res = await updateOnsiteRecord(obj);
  if (res.status == '200') {
    message.success(result ? '审批成功！' : '修改信息成功！');
    if (result) {
      handleToBeApproved();
      eventBus.emit('updateMenu');
    } else {
      queryOnsiteRecordInfo();
    }
  }
};
const pagination = reactive({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  itemCount: 0,
  prefix({ itemCount }) {
    return `总条数 ${itemCount-1 < 0 ? 0 : itemCount-1}`;
  },
  onChange: (page: number) => {
    pagination.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
  },
});
const tableData = ref([]);
const quarterNumber = ref(0);
const reviewNubmber = computed(() => {
  return (quarterNumber.value)*80
});
const totalNubmber =computed(() => {
  return (quarterNumber.value)*160
});;
const handleResetFilter = () => {
    filters.value = getFiltersModel();
    handleGetOutputList();
  };
  const rowProps = (row) => {
    return {
      class: row.remark === '计分规则' ? 'rowGreen' : '',
    };
  };
  // 获取开始时间、结束时间
  const getTimes = () => {
    let params = {
      startDate: '',
      endDate: '',
    };
    for (let key in filters.value) {
      if (key === 'dateList' && filters.value[key]) {
        params.startDate = new Date(filters.value[key][0]).toLocaleDateString().replaceAll('/', '-');
        params.endDate = new Date(filters.value[key][1]).toLocaleDateString().replaceAll('/', '-');
      }
      if (filters.value[key]) {
        params[key] = filters.value[key];
      }
    }
    return params;
  };
// 新增驻场信息
const addLine = async () => {
  ifAdd.value = true;
  residentInfoData.value.splice(residentInfoData.value.length, 0, {
    startDate: null,
    endDate: null,
    appName: '',
    codeLines: '',
    leaderName: '',
    status: 0,
    config: '',
    isEdit: true,
  });
};
const handleGetOutputList = async () => {
  const data = { userAccout: userStore.getAccount };
  Object.assign(data, getTimes());
  let res = await getOutputList(data);

  if (res.status == '200') {
    quarterNumber.value = res.data.quarters||0
    const list = [
      {
        remark: '计分规则',
        // 问题处理交付件
        l1Problem: '1分/个',
        l2Problem: '4分/个',
        communityProblem: '2分/个',
        vod: '0.5分/个',
        ewpProblem: '2分/个',
        // 知识沉淀交付件
        optimizeCount: '1分/个',
        createFaqCount: '2分/个',
        createNonFaqCount: '6分/个',
        demo: 'demo实际分数',
        verticalSolution: '160分/个',
        //
        communityReview: '0.25分/个',
        mrReview: '0.25分/个',
        l2ReviewCount: '0.5分/个',
        vodReview: '0.5分/个',
        irQualityReview: '0.25分/个',
        l25ReviewCount: '1分/个',
        committerReviewCount: '1分/个',
        demoInspection: '1分/个',
        irReview: '0.5分/个',
        whiteCaseReview: '1分/个',
      },
    ];
    tableData.value = list.concat(res.data.outputList);
  }
};
const handlequeryUserList = async () => {
  let { data } = await getEmployeeList();
  userList.value = data.map((item) => {
    return {
      label: item.userName + '/' + item.account,
      value: item.userName,
    };
  });
};
{
  handlequeryUserList();
  handleGetOutputList();
}

const mrHandle = async () => {
try {
  let res = await latestMrReview();
  showMRReviewNumber.value = true;
  if (res.status == '200') {
    console.log(res.data);
    latestMrReviewTime.value = res.data;
  } else {
    latestMrReviewTime.value = '';
  }
} catch (error) {
  onError();
}
};

const codeDevelopmentHandle = async () => {
  try {
    let res = await latestMrCodeLine();
    showCodeDevelopmentNumber.value = true;
    if (res.status == '200') {
      latestMrCodeLineTime.value = res.data;
    } else {
      latestMrCodeLineTime.value = '';
    }
  } catch (error) {
    onError();
  }
};

//导入MR检视意见数量
const importMRReviewNumber = async ({ file, onError }: UploadCustomRequestOptions) => {
  const formData = new FormData();
  formData.append('file', file.file as File);
  try {
    let res = await mRReviewNumberImport(formData);
    if (res.status == '200') {
      showMRReviewNumber.value = false;
      handleGetOutputList();
      message.success('导入成功');
    } else {
    }
  } catch (err) {
    onError();
  }
};

// 导入工程代码开发行数
const importCodeDevelopmentNumber = async ({ file, onError }: UploadCustomRequestOptions) => {
  const formData = new FormData();
  formData.append('file', file.file as File);
  try {
    let res = await codeDevelopmentNumberImport(formData);
    if (res.status == '200') {
      showCodeDevelopmentNumber.value = false;
      handleGetOutputList();
      message.success('导入成功');
    } else {
    }
  } catch (err) {
    onError();
  }
};

// 导入社区问题数据
const importCommunityProblemData = async ({ file, onError }: UploadCustomRequestOptions) => {
  const formData = new FormData();
  formData.append('file', file.file as File);
  try {
    let res = await communityProblemDataImport(formData);
    if (res.status == '200') {
      showCommunityProblemData.value = false;
      handleGetOutputList();
      message.success('导入成功');
    } else {
    }
  } catch (err) {
    onError();
  }
};
// 导入开发者声音
const importVoiceData = async ({ file, onError }: UploadCustomRequestOptions) => {
  const formData = new FormData();
  formData.append('file', file.file as File);
  try {
    let res = await importVod(formData);
    if (res.status == '200') {
      showVoice.value = false;
      handleGetOutputList();
      message.success('导入成功');
    } else {
    }
  } catch (err) {
    onError();
  }
};
// 导入驻场开发天数
const importResidentDevelopmentDays = async ({ file, onError }: UploadCustomRequestOptions) => {
  const formData = new FormData();
  formData.append('file', file.file as File);
  try {
    let res = await residentDevelopmentDaysImport(formData);
    if (res.status == '200') {
      showResidentDevelopmentDays.value = false;
      handleGetOutputList();
      message.success('导入成功');
    } else {
    }
  } catch (err) {
    onError();
  }
};
  </script>

  <style lang="less" scoped>
  .layout-page-header {
    margin-top: 20px;
    .n-select {
      min-width: 250px;
    }
  }
  :deep(.alert-info .n-alert-body ){
    height: 34px !important;
    padding:8px
  }
  :deep(.n-data-table .n-data-table-tbody .rowGreen .n-data-table-td) {
    background-color: #daf0e4 !important;
    color: #18a058 !important;
    padding-top: 2px !important;
    padding-bottom: 2px !important;
  }
  </style>
