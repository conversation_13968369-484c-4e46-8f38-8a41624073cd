<script setup lang="ts">
  import { ref, reactive, computed, onMounted, onUnmounted } from 'vue';
  import {
    NDataTable,
    NSpace,
    NButton,
    NInput,
    NSelect,
    NDatePicker,
    NForm,
    NFormItem,
    NModal,
    NTag,
    NCard,
    NGrid,
    NGridItem,
    NIcon,
    NPopconfirm,
    NUpload,
    NUploadDragger,
    NTabs,
    NTabPane,
    NEmpty,
    NInputNumber,
    NCollapse,
    NCollapseItem,
    FormRules,
    FormInst, useMessage,
  } from 'naive-ui';
  import { SearchOutlined, EditOutlined, UploadOutlined, SyncOutlined } from '@vicons/antd';
  import { columns, type EcologicalRecord } from './columns';
  import {
    createEcologicalData,
    getEcologicalData,
    updateEcologicalData,
    importPuraXHistoricalData,
    syncPuraXAutoDetectedIssues,
  } from './api';
  import { useEventBus } from '@/hooks/useEventBus';
  import { usePermission } from '@/composables/usePermission';
  import {
    deleteEcologicalData,
    getItemsFromAppName,
  } from '@/views/dataview/ecologicalTracking/api.mock';
  import { useUserStore } from '@/store/modules/user';
  import {getPersonOptionsOnlyName} from "@/views/dataview/ecologicalTracking/api.mock";
  import {DTS_HANDLE_TAG} from "@/views/dataview/personManage/tagContant";
  import ProblemProgressModal from "@/views/dataview/myOrder/components/ProblemProgressModal.vue";
  import dayjs from "dayjs";
  import {addQuestion} from "@/api/dataview/serviceAndOtherQuestions";

  const eventBus = useEventBus();

  const { hasEwpPermission, hasPmPermission } = usePermission();

  const exportLoading = ref(false);
  const loading = ref(false);
  const formRef = ref<FormInst | null>(null);
  const sortState = ref({
    sortField: 'ewpOwner',
    sortOrder: 'desc',
  });
  const data = ref<EcologicalRecord[]>([]);
  const pagination = reactive({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    showSizePicker: true,
    pageSizes: [10, 20, 30, 50],
    prefix({ itemCount }) {
      return `总条数 ${itemCount}`;
    },
    onChange: (page: number) => {
      pagination.page = page;
      loadData();
    },
    onUpdatePageSize: (pageSize: number) => {
      pagination.pageSize = pageSize;
      pagination.page = 1;
      loadData();
    },
  });

  const dynamicRules = reactive<FormRules>({
    appName: [{ required: true, message: '请输入应用名称' }],
    defeatDescription: [{ required: true, message: '请输入问题描述' }],
    defeatLevel: [{ required: true, message: '请选择问题级别' }],
    source: [{ required: true, message: '请选择来源' }],
    device: [{ required: true, message: '请选择设备型号' }],
    registrationDate: [{ required: true, message: '请输入登记日期' }],
  });

  const activeTab = ref('all');
  const userStore = useUserStore();
  const userInfo: any = userStore.getUserInfo || {};
  const onlyMine = ref(true);
  const selectedRows = ref<any[]>([]);
  const checkedRowKeys = ref<(string | number)[]>([]);
  const filterModel = reactive({
    registrationDate: null,
    ewpOwner: userInfo.roles.includes('5_admin') ? userInfo.userName : null,
    defeatAttribution: null,
    representativeOffice: null,
    appName: null,
    defeatDescription: null,
    defeatLevel: null,
    defeatLevels: [],
    source: null,
    sourceOwner: null,
    dtsOrderId: null,
    orderStatus: null,
    sourceClosed: null,
    currentHandler: userInfo.roles.includes('5_admin') ? null : extractNumbers(userInfo.account),
    affectedProduct: null,
    affectedProducts: [],
    solutionPlanTime: null,
    device: null as string | null,
    keyDefeat: null,
  });

  const handleFiltersChange = (filters: any) => {
    // 合并筛选条件到搜索表单
    Object.assign(filterModel, filters);
    // 重置到第一页
    pagination.page = 1;
    // 重新获取数据
    filterData();
  };
  function extractNumbers(str) {
    // 使用正则表达式匹配所有数字部分
    const numbers = str.match(/\d+/g);
    return numbers ? numbers.join('') : ''; // 将匹配到的数字拼接成字符串
  }

  // 只看我
  const handleOnlyMineChange = (value: boolean) => {
    if (value) {
      if (userInfo.roles.includes('5_admin')) {
        filterModel.ewpOwner = userInfo.userName;
      } else {
        filterModel.currentHandler = extractNumbers(userInfo.account);
      }
    } else {
      filterModel.ewpOwner = null;
      filterModel.currentHandler = null;
    }
    handleFilter();
  };

  // 勾选
  const handleCheckedRowKeysChange = (keys: (string | number)[]) => {
    const newSelectedRows = keys
      .map((key) => {
        const existingRow = selectedRows.value.find((row) => row.id === key);
        if (existingRow) {
          return existingRow;
        }
        return data.value.find((row) => row.id === key);
      })
      .filter(Boolean) as EcologicalRecord[];

    selectedRows.value = newSelectedRows;
    checkedRowKeys.value = keys;
  };

  const problemLevelOptions = [
    { label: '极高', value: '极高' },
    { label: '高', value: '高' },
    { label: '中', value: '中' },
    { label: '低', value: '低' },
  ];

  const sourceOptions = [
    { label: 'VOC预警', value: 'VOC预警' },
    { label: '预录单', value: '预录单' },
    { label: '热线/互联网', value: '热线/互联网' },
    { label: '门店反馈', value: '门店反馈' },
    { label: '巡店', value: '巡店' },
    { label: 'SVIP/VIP', value: 'SVIP/VIP' },
    { label: 'BetaClub/反馈助手', value: 'BetaClub/反馈助手' },
    { label: '代表处', value: '代表处' },
    { label: 'UEF', value: 'UEF' },
    { label: '其它(电话/微信/mail/eSapce等)', value: '其它(电话/微信/mail/eSapce等)' },
    { label: 'AG', value: 'AG' },
  ];

  const statusOptions = [
    // { label: '待分单', value: '待分单' },
    { label: '待定界', value: '待定界' },
    { label: '待锁定', value: '待锁定' },
    // { label: '待审核', value: '待审核' },
    // { label: '待归档', value: '待归档' },
    { label: '待回归', value: '待回归' },
    // { label: '已锁定', value: '已锁定' },
    { label: '已闭环', value: '已闭环' },
    { label: '空', value: '-' },
  ];

  const sourceCloseLoopOptions = [
    { label: '是', value: '是' },
    { label: '否', value: '否' },
    { label: '无需闭环', value: '无需闭环' },
  ];

  const trueFalseOptions = [
    { label: '是', value: '是' },
    { label: '否', value: '否' },
  ];

  const problemBelongingOptions = [
    { label: '三方应用', value: '三方应用' },
    { label: '系统问题', value: '系统问题' },
    { label: '非问题', value: '非问题' },
  ];

  const deviceModelOptions = [
    { label: 'Pura X', value: 'Pura X' },
    { label: 'nova 14', value: 'nova 14' },
    { label: 'Pura 80', value: 'Pura 80' },
    { label: 'PC', value: 'PC' },
    // { label: 'Mate X5', value: 'Mate X5' },
    // { label: 'ALL', value: '' },
  ];

  const affectedProductsOptions = [
    { label: 'Pura X', value: 'Pura X' },
    { label: 'nova 14', value: 'nova 14' },
    { label: 'Pura 80', value: 'Pura 80' },
    { label: 'PC', value: 'PC' },
    // { label: 'Mate X6', value: 'Mate X6' },
    // { label: 'Mate X5', value: 'Mate X5' },
    { label: 'ALL', value: 'ALL' },
  ];

  const showEditModal = ref(false);
  const editMode = ref<'ewp' | 'pm'>('ewp');
  const editingRecord = ref<Partial<EcologicalRecord> | null>(null);

  const showImportModal = ref(false);
  const importFile = ref<File | null>(null);
  const importLoading = ref(false);
  const ewpOptions = ref([]);

  const filterCollapsed = ref(['basic']);

  const message = useMessage();

  const handlePaginationChange = (page: number) => {
    pagination.page = page;
    loadData();
  };

  const handlePaginationSizeChange = (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.page = 1;
    loadData();
  };

  // 查询接口
  const loadData = async () => {
    loading.value = true;

    try {
      const params = {
        ...filterModel,
        startRegistrationDate: (filterModel.registrationDate ?? []).map((item) =>
          timestampToDate(item)
        )[0],
        endRegistrationDate: (filterModel.registrationDate ?? []).map((item) =>
          timestampToDate(item)
        )[1],
        // registrationDate
        // solutionPlanTime
        startSolutionPlanTime: (filterModel.solutionPlanTime ?? []).map((item) =>
          timestampToDate(item)
        )[0],
        endSolutionPlanTime: (filterModel.solutionPlanTime ?? []).map((item) =>
          timestampToDate(item)
        )[1],
        // affectedProduct: (filterModel.affectedProduct ?? []).split(','),
        device: filterModel.device ? filterModel.device.toString() : '',
        defeatLevels:
          filterModel.defeatLevels?.length === 0 || !filterModel.defeatLevels
            ? filterModel.defeatLevel
            : filterModel.defeatLevels,
        pageNo: pagination.page,
        pageSize: pagination.pageSize,
        sortField: sortState.value.sortField,
        sortOrder: sortState.value.sortOrder,
      } as any;

      const response = await getEcologicalData(params);
      data.value = response.records;
      pagination.itemCount = response.total;
      console.log(response, data.value, 'response data');
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      loading.value = false;
    }
  };

  const filterData = async () => {
    loading.value = true;

    try {
      const params = {
        ...filterModel,
        startRegistrationDate: (filterModel.registrationDate ?? []).map((item) =>
          timestampToDate(item)
        )[0],
        endRegistrationDate: (filterModel.registrationDate ?? []).map((item) =>
          timestampToDate(item)
        )[1],
        startSolutionPlanTime: (filterModel.solutionPlanTime ?? []).map((item) =>
          timestampToDate(item)
        )[0],
        endSolutionPlanTime: (filterModel.solutionPlanTime ?? []).map((item) =>
          timestampToDate(item)
        )[1],
        affectedProducts: filterModel.affectedProduct,
        defeatLevels: filterModel.defeatLevel,
        device: filterModel.device ? filterModel.device.toString() : '',
        pageNo: pagination.page,
        pageSize: pagination.pageSize,
        sortField: sortState.value.sortField,
        sortOrder: sortState.value.sortOrder,
      } as any;

      const response = await getEcologicalData(params);
      data.value = response.records;
      pagination.itemCount = response.total;
      console.log(response, data.value, 'response data');
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      loading.value = false;
    }
  };

  const handleFilter = () => {
    pagination.page = 1;
    loadData();
  };

  // 查询重置按钮
  const resetFilter = () => {
    Object.keys(filterModel).forEach((key) => {
      filterModel[key as keyof typeof filterModel] = null;
    });
    filterModel.device = activeTab.value === 'all' ? '' : activeTab.value;
    if (onlyMine.value) {
      if (userInfo.roles.includes('5_admin')) {
        filterModel.ewpOwner = userInfo.userName;
      } else {
        filterModel.currentHandler = extractNumbers(userInfo.account);
      }
    }
    loadData();
    // 重置选中状态
    checkedRowKeys.value = [];
    selectedRows.value = [];
  };

  // 删除
  const handleDelete = async (id: string) => {
    await deleteEcologicalData(id);
    setTimeout(() => {
      loadData();
    }, 1000);
  };

  // 打开modal页
  const openEditModal = (data: { row: Partial<EcologicalRecord>; mode: 'ewp' | 'pm' }) => {
    const { row, mode } = data;
    if (mode === 'ewp' && !hasEwpPermission.value) return;
    if (mode === 'pm' && !hasPmPermission.value) return;

    editingRecord.value = { ...row };
    editMode.value = mode;
    showEditModal.value = true;
  };

  // 新增、编辑保存
  const saveEditedRecord = async () => {
    if(!editingRecord.value.device){
      editingRecord.value.device = filterModel.device;
    }
    formRef.value?.validate(async (errors) => {
      if (!editingRecord.value) return;

      try {
        if (!errors) {
          if (editingRecord.value.id) {
            const changeRecord = {
              ...editingRecord.value,
              affectedProduct: editingRecord.value.affectedProducts.toString(),
              registrationDate: editingRecord.value.registrationDate
                ? timestampToDate(editingRecord.value.registrationDate)
                : timestampToDate(Date.now()),
              solutionPlanTime: editingRecord.value.solutionPlanTime
                ? timestampToDate(editingRecord.value.solutionPlanTime)
                : '',
            };
            await updateEcologicalData(changeRecord as Omit<EcologicalRecord, 'id'>);
          } else {
            const newRecord = {
              ...editingRecord.value,
              dataSource: '手动登记',
              affectedProduct: editingRecord.value?.affectedProducts?.toString(),
              registrationDate: editingRecord.value.registrationDate
                ? timestampToDate(editingRecord.value.registrationDate)
                : timestampToDate(Date.now()),
              solutionPlanTime: editingRecord.value.solutionPlanTime
                ? timestampToDate(editingRecord.value.solutionPlanTime)
                : '',
              domainLeader: editingRecord.value.domainLeader || '李国良 00578453',
              orderProgress: editingRecord.value.orderProgress || '【问题根因】XXXX\n【解决方案】XXXX',
              opinionVolume: editingRecord.value.opinionVolume || 1,
              keyDefeat: editingRecord.value.keyDefeat || '否',
              sourceClosed: editingRecord.value.sourceClosed || '否',
              device: editingRecord.value.device || filterModel.device,
            };
            await createEcologicalData(newRecord as EcologicalRecord);
          }

          showEditModal.value = false;
          loadData();
        }
      } catch (error) {
        message.error(error.message === '不能为空' ? `当前处理人${error.message}` : error.message);
        console.error('Failed to save record:', error.message);
      }
    });
  };

  const isPmEditable = (field: string) => {
    const pmEditableFields = [
      'issueReportTicket',
      'progress',
      'currentHandler',
      'affectedProducts',
      'planCloseLoopTime',
    ];
    return pmEditableFields.includes(field);
  };

  // 切换tab页
  const handleTabChange = (tabName: string) => {
    activeTab.value = tabName;
    resetFilter();

    if (tabName === 'Pura X') {
      filterModel.device = 'Pura X';
    } else if (tabName === 'nova 14') {
      filterModel.device = 'nova 14';
    } else if (tabName === 'Pura 80') {
      filterModel.device = 'Pura 80';
    } else if (tabName === 'PC') {
      filterModel.device = 'PC';
    }  else {
      filterModel.device = '';
    }
    setTimeout(() => {
      loadData();
    }, 1000);
  };

  const handleImportFileChange = (options: { file: File }) => {
    importFile.value = options.file;
    return false;
  };

  // 导入px数据
  const importPuraXData = async () => {
    if (!importFile.value) return;

    importLoading.value = true;
    try {
      await importPuraXHistoricalData(importFile.value);
      showImportModal.value = false;
      loadData();
    } catch (error) {
      console.error('Failed to import Pura X data:', error);
    } finally {
      importLoading.value = false;
    }
  };

  // 同步px数据
  const syncPuraXData = async (tab: string) => {
    try {
      await syncPuraXAutoDetectedIssues(tab);
      loadData();
    } catch (error) {
      console.error('Failed to sync Pura X data:', error);
    }
  };

  // 同步nova数据
  const syncNovaData = async (tab: string) => {
    try {
      await syncPuraXAutoDetectedIssues(tab);
      loadData();
    } catch (error) {
      console.error('Failed to sync nova 14 data:', error);
    }
  };

  // 同步p80数据
  const syncPura80Data = async (tab: string) => {
    try {
      await syncPuraXAutoDetectedIssues(tab);
      loadData();
    } catch (error) {
      console.error('Failed to sync Pura 80 data:', error);
    }
  };

  // 同步PC数据
  const syncPCData = async (tab: string) => {
    try {
      await syncPuraXAutoDetectedIssues(tab);
      loadData();
    } catch (error) {
      console.error('Failed to sync PC data:', error);
    }
  };

  // 根据输入的应用名自动匹配责任人、代表处、应用PM
  const selectItems = async () => {
    const [username, represent, appPM] = await getItemsFromAppName(editingRecord.value.appName);
    if (username) {
      editingRecord.value.ewpOwner = username;
    }
    if (represent) {
      editingRecord.value.representativeOffice = represent;
    }
    if (appPM) {
      editingRecord.value.currentHandler = appPM;
    }
  };

  // 导出
  const handleExport = () => {
    return new Promise((resolve, reject) => {
      const req = new XMLHttpRequest();
      const params = {
        ...filterModel,
        startRegistrationDate: (filterModel.registrationDate ?? []).map((item) =>
          timestampToDate(item)
        )[0],
        endRegistrationDate: (filterModel.registrationDate ?? []).map((item) =>
          timestampToDate(item)
        )[1],
        startSolutionPlanTime: (filterModel.solutionPlanTime ?? []).map((item) =>
          timestampToDate(item)
        )[0],
        endSolutionPlanTime: (filterModel.solutionPlanTime ?? []).map((item) =>
          timestampToDate(item)
        )[1],
        device: filterModel.device ? filterModel.device.toString() : '',
        pageNo: pagination.page,
        pageSize: pagination.pageSize,
        sortField: sortState.value.sortField,
        sortOrder: sortState.value.sortOrder,
      } as any;
      exportLoading.value = true;
      req.open(
        'POST',
        `http://${window.location.host}/ewp/management/special-item/orders/sheet`,
        true
      );
      // req.setRequestHeader('env','dev');
      req.responseType = 'blob';
      req.setRequestHeader('Content-Type', 'application/json');
      req.onload = function () {
        const data = req.response;
        const blob = new Blob([data]);
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = `生态舆情跟踪导出${
          filterModel.device ? filterModel.device.toString() : ''
        }.xlsx`;
        a.href = blobUrl;
        a.click();
        exportLoading.value = false;
        resolve(true);
      };
      req.send(JSON.stringify(params));
    });
  };

  // 生成表格模板
  const generateTemplates = () => {
    if (selectedRows.value.length === 0) {
      message.warning('请先选择要生成模板的行');
      return;
    }
    const templates = selectedRows.value.map(
      (row) =>
        `【问题描述】${
          row.defeatDescription + '\n'
        }【DTS链接】https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${row.dtsOrderId}
【分析结论】xxxx（来自故障知识获取的定界信息）
【修改建议】xxxx（来自故障知识获取的定界信息）
【应用PM】 @xxxx
【问题跟踪平台】https://dtse.cbg.huawei.com/listingProtection/ecologicalTracking
请执行：点击表格中【应用PM更新】按钮更新进展
【生态舆情保障运作规范】https://3ms.huawei.com/km/groups/3945040/blogs/details/19122126?l=zh-cn${'\n'}`
    );

    const content = templates.join('\n');
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard.writeText(content).then(() => {
        message.success('模板已生成并复制到剪贴板');
        // 重置选择状态
        checkedRowKeys.value = [];
        selectedRows.value = [];
      });
    } else {
      // 创建text area
      let textArea = document.createElement('textarea');
      textArea.value = content;
      // 使text area不在viewport，同时设置不可见
      textArea.style.position = 'absolute';
      textArea.style.opacity = '0';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      return new Promise((res, rej) => {
        // 执行复制命令并移除文本框
        document.execCommand('copy') ? res(null) : rej();
        textArea.remove();
        message.success('模板已生成并复制到剪贴板');
        // 重置选择状态
        checkedRowKeys.value = [];
        selectedRows.value = [];
      });
    }
  };

  /**
   时间戳转换年月日
   */

  const timestampToDate = (timestamp: number): string => {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 排序
  const handleSorterChange = (sorter) => {
    if (sorter) {
      const { columnKey, order } = sorter;
      sortState.value.sortField = columnKey;
      sortState.value.sortOrder = order === 'ascend' ? 'asc' : 'desc';
    } else {
      sortState.value.sortField = 'ewpOwner';
      sortState.value.sortOrder = 'desc';
    }
    loadData();
  };

  //问题进展
  const progressModel = () =>{
    const showProblemModal = ref(false);
    const currentProblem = ref({
      orderId: null,
      appName: '',
      description: ''
    })
    const selectData = ref({});
    const afterSubmit = async (data) =>{
      //更新最新进展
      selectData.value.orderProgress = dayjs(data.createTime).format('MM/DD')+"："+data.problemProcess;
      await updateEcologicalData(selectData.value);
    }
    const showModel = (row) =>{
      selectData.value = row;
      currentProblem.value.orderId = 'ecological'+row.id;
      currentProblem.value.appName = row.appName;
      currentProblem.value.description = row.defeatDescription;
      showProblemModal.value = true;
    }
    return {
      showProblemModal,
      currentProblem,
      selectData,
      afterSubmit,
      showModel
    }
  }

  const {showProblemModal,
    currentProblem,
    selectData,
    afterSubmit,
    showModel} = progressModel();

  onMounted(async () => {
    eventBus.on('showModel', showModel);
    eventBus.on('openEditModal', openEditModal);
    loadData();
    ewpOptions.value = await getPersonOptionsOnlyName(DTS_HANDLE_TAG);
  });

  onUnmounted(() => {
    eventBus.off('showModel', showModel);
    eventBus.off('openEditModal', openEditModal);
  });
</script>

<template>
  <div class="ecological-tracking">
    <n-tabs
      v-model:value="activeTab"
      type="card"
      @update:value="handleTabChange"
      class="device-tabs"
    >
      <n-tab-pane name="all" tab="全部问题">
        <n-card :title="'筛选条件'" class="filter-card">
          <n-collapse v-model:expanded-names="filterCollapsed">
            <n-collapse-item name="basic" title="基础筛选">
              <n-grid :cols="4" :x-gap="16" :y-gap="16">
                <n-grid-item>
                  <n-form-item label="登记日期">
                    <n-date-picker
                      v-model:value="filterModel.registrationDate"
                      type="daterange"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="问题描述">
                    <n-input
                      v-model:value="filterModel.defeatDescription"
                      placeholder="请输入问题描述"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="DTS单状态">
                    <n-select
                      v-model:value="filterModel.orderStatus"
                      :options="statusOptions"
                      placeholder="请选择状态"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="设备型号">
                    <n-select
                      v-model:value="filterModel.device"
                      :options="deviceModelOptions"
                      placeholder="请选择设备型号"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-collapse-item>
            <n-collapse-item name="advanced" title="高级筛选">
              <n-grid :cols="4" :x-gap="16" :y-gap="16">
                <n-grid-item>
                  <n-form-item label="是否是关键问题">
                    <n-select
                      v-model:value="filterModel.keyDefeat"
                      :options="trueFalseOptions"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="EWP责任人">
                    <n-select
                      v-model:value="filterModel.ewpOwner"
                      placeholder="请选择EWP责任人"
                      :options="ewpOptions"
                      filterable
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="问题归属">
                    <n-select
                      v-model:value="filterModel.defeatAttribution"
                      :options="problemBelongingOptions"
                      placeholder="请选择问题归属"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="代表处">
                    <n-input
                      v-model:value="filterModel.representativeOffice"
                      placeholder="请输入代表处"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="应用名称">
                    <n-input
                      v-model:value="filterModel.appName"
                      placeholder="请输入应用名称"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="问题级别">
                    <n-select
                      v-model:value="filterModel.defeatLevels"
                      :options="problemLevelOptions"
                      placeholder="请选择问题级别"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="来源">
                    <n-select
                      v-model:value="filterModel.source"
                      :options="sourceOptions"
                      placeholder="请选择来源"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="来源登记人">
                    <n-input
                      v-model:value="filterModel.sourceOwner"
                      placeholder="请输入"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="DTS单">
                    <n-input
                      v-model:value="filterModel.dtsOrderId"
                      placeholder="请输入DTS单"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="来源是否闭环">
                    <n-select
                      v-model:value="filterModel.sourceClosed"
                      :options="sourceCloseLoopOptions"
                      placeholder="请选择来源是否闭环"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="当前处理人">
                    <n-input
                      v-model:value="filterModel.currentHandler"
                      placeholder="请输入当前处理人"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="影响品类/产品">
                    <n-select
                      v-model:value="filterModel.affectedProducts"
                      :options="affectedProductsOptions"
                      placeholder="请选择影响品类/产品"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="计划闭环时间">
                    <n-date-picker
                      v-model:value="filterModel.solutionPlanTime"
                      type="daterange"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-collapse-item>
          </n-collapse>
          <div class="filter-buttons">
            <n-space>
              <n-button type="primary" @click="handleFilter">
                <template #icon>
                  <n-icon><search-outlined /></n-icon>
                </template>
                查询
              </n-button>
              <n-button @click="resetFilter">重置</n-button>
            </n-space>
          </div>
        </n-card>
      </n-tab-pane>

      <n-tab-pane name="Pura X" tab="Pura X机型问题">
        <n-card :title="'Pura X筛选条件'" class="filter-card">
          <n-collapse v-model:expanded-names="filterCollapsed">
            <n-collapse-item name="basic" title="基础筛选">
              <n-grid :cols="4" :x-gap="16" :y-gap="16">
                <n-grid-item>
                  <n-form-item label="登记日期">
                    <n-date-picker
                      v-model:value="filterModel.registrationDate"
                      type="daterange"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="问题描述">
                    <n-input
                      v-model:value="filterModel.defeatDescription"
                      placeholder="请输入问题描述"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="DTS单状态">
                    <n-select
                      v-model:value="filterModel.orderStatus"
                      :options="statusOptions"
                      placeholder="请选择状态"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-collapse-item>
            <n-collapse-item name="advanced" title="高级筛选">
              <n-grid :cols="4" :x-gap="16" :y-gap="16">
                <n-grid-item>
                  <n-form-item label="是否是关键问题">
                    <n-select
                      v-model:value="filterModel.keyDefeat"
                      :options="trueFalseOptions"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="EWP责任人">
                    <n-select
                      v-model:value="filterModel.ewpOwner"
                      placeholder="请选择EWP责任人"
                      :options="ewpOptions"
                      filterable
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="问题归属">
                    <n-select
                      v-model:value="filterModel.defeatAttribution"
                      :options="problemBelongingOptions"
                      placeholder="请选择问题归属"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="代表处">
                    <n-input
                      v-model:value="filterModel.representativeOffice"
                      placeholder="请输入代表处"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="应用名称">
                    <n-input
                      v-model:value="filterModel.appName"
                      placeholder="请输入应用名称"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="问题级别">
                    <n-select
                      v-model:value="filterModel.defeatLevels"
                      :options="problemLevelOptions"
                      placeholder="请选择问题级别"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="来源">
                    <n-select
                      v-model:value="filterModel.source"
                      :options="sourceOptions"
                      placeholder="请选择来源"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="来源登记人">
                    <n-input
                      v-model:value="filterModel.sourceOwner"
                      placeholder="请输入"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="DTS单">
                    <n-input
                      v-model:value="filterModel.dtsOrderId"
                      placeholder="请输入DTS单"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="来源是否闭环">
                    <n-select
                      v-model:value="filterModel.sourceClosed"
                      :options="sourceCloseLoopOptions"
                      placeholder="请选择来源是否闭环"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="当前处理人">
                    <n-input
                      v-model:value="filterModel.currentHandler"
                      placeholder="请输入当前处理人"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="影响品类/产品">
                    <n-select
                      v-model:value="filterModel.affectedProducts"
                      :options="affectedProductsOptions"
                      placeholder="请选择影响品类/产品"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="计划闭环时间">
                    <n-date-picker
                      v-model:value="filterModel.solutionPlanTime"
                      type="daterange"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-collapse-item>
          </n-collapse>
          <div class="filter-buttons">
            <n-space>
              <n-button type="primary" @click="handleFilter">
                <template #icon>
                  <n-icon><search-outlined /></n-icon>
                </template>
                查询
              </n-button>
              <n-button @click="resetFilter">重置</n-button>
              <n-button type="success" @click="syncPuraXData(activeTab)">
                <template #icon>
                  <n-icon><sync-outlined /></n-icon>
                </template>
                同步最新Pura X问题
              </n-button>
            </n-space>
          </div>
        </n-card>
      </n-tab-pane>

      <n-tab-pane name="nova 14" tab="nova 14机型问题">
        <n-card :title="'nova 14筛选条件'" class="filter-card">
          <n-collapse v-model:expanded-names="filterCollapsed">
            <n-collapse-item name="basic" title="基础筛选">
              <n-grid :cols="4" :x-gap="16" :y-gap="16">
                <n-grid-item>
                  <n-form-item label="登记日期">
                    <n-date-picker
                      v-model:value="filterModel.registrationDate"
                      type="daterange"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="问题描述">
                    <n-input
                      v-model:value="filterModel.defeatDescription"
                      placeholder="请输入问题描述"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="DTS单状态">
                    <n-select
                      v-model:value="filterModel.orderStatus"
                      :options="statusOptions"
                      placeholder="请选择状态"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-collapse-item>
            <n-collapse-item name="advanced" title="高级筛选">
              <n-grid :cols="4" :x-gap="16" :y-gap="16">
                <n-grid-item>
                  <n-form-item label="是否是关键问题">
                    <n-select
                      v-model:value="filterModel.keyDefeat"
                      :options="trueFalseOptions"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="EWP责任人">
                    <n-select
                      v-model:value="filterModel.ewpOwner"
                      placeholder="请选择EWP责任人"
                      :options="ewpOptions"
                      filterable
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="问题归属">
                    <n-select
                      v-model:value="filterModel.defeatAttribution"
                      :options="problemBelongingOptions"
                      placeholder="请选择问题归属"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="代表处">
                    <n-input
                      v-model:value="filterModel.representativeOffice"
                      placeholder="请输入代表处"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="应用名称">
                    <n-input
                      v-model:value="filterModel.appName"
                      placeholder="请输入应用名称"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="问题级别">
                    <n-select
                      v-model:value="filterModel.defeatLevels"
                      :options="problemLevelOptions"
                      placeholder="请选择问题级别"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="来源">
                    <n-select
                      v-model:value="filterModel.source"
                      :options="sourceOptions"
                      placeholder="请选择来源"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="来源登记人">
                    <n-input
                      v-model:value="filterModel.sourceOwner"
                      placeholder="请输入"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="DTS单">
                    <n-input
                      v-model:value="filterModel.dtsOrderId"
                      placeholder="请输入DTS单"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="来源是否闭环">
                    <n-select
                      v-model:value="filterModel.sourceClosed"
                      :options="sourceCloseLoopOptions"
                      placeholder="请选择来源是否闭环"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="当前处理人">
                    <n-input
                      v-model:value="filterModel.currentHandler"
                      placeholder="请输入当前处理人"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="影响品类/产品">
                    <n-select
                      v-model:value="filterModel.affectedProducts"
                      :options="affectedProductsOptions"
                      placeholder="请选择影响品类/产品"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="计划闭环时间">
                    <n-date-picker
                      v-model:value="filterModel.solutionPlanTime"
                      type="daterange"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-collapse-item>
          </n-collapse>
          <div class="filter-buttons">
            <n-space>
              <n-button type="primary" @click="handleFilter">
                <template #icon>
                  <n-icon><search-outlined /></n-icon>
                </template>
                查询
              </n-button>
              <n-button @click="resetFilter">重置</n-button>
              <n-button type="success" @click="syncNovaData(activeTab)">
                <template #icon>
                  <n-icon><sync-outlined /></n-icon>
                </template>
                同步最新nova 14问题
              </n-button>
            </n-space>
          </div>
        </n-card>
      </n-tab-pane>

      <n-tab-pane name="Pura 80" tab="Pura 80机型问题">
        <n-card :title="'Pura 80筛选条件'" class="filter-card">
          <n-collapse v-model:expanded-names="filterCollapsed">
            <n-collapse-item name="basic" title="基础筛选">
              <n-grid :cols="4" :x-gap="16" :y-gap="16">
                <n-grid-item>
                  <n-form-item label="登记日期">
                    <n-date-picker
                      v-model:value="filterModel.registrationDate"
                      type="daterange"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="问题描述">
                    <n-input
                      v-model:value="filterModel.defeatDescription"
                      placeholder="请输入问题描述"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="DTS单状态">
                    <n-select
                      v-model:value="filterModel.orderStatus"
                      :options="statusOptions"
                      placeholder="请选择状态"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-collapse-item>
            <n-collapse-item name="advanced" title="高级筛选">
              <n-grid :cols="4" :x-gap="16" :y-gap="16">
                <n-grid-item>
                  <n-form-item label="是否是关键问题">
                    <n-select
                      v-model:value="filterModel.keyDefeat"
                      :options="trueFalseOptions"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="EWP责任人">
                    <n-select
                      v-model:value="filterModel.ewpOwner"
                      placeholder="请选择EWP责任人"
                      :options="ewpOptions"
                      filterable
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="问题归属">
                    <n-select
                      v-model:value="filterModel.defeatAttribution"
                      :options="problemBelongingOptions"
                      placeholder="请选择问题归属"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="代表处">
                    <n-input
                      v-model:value="filterModel.representativeOffice"
                      placeholder="请输入代表处"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="应用名称">
                    <n-input
                      v-model:value="filterModel.appName"
                      placeholder="请输入应用名称"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="问题级别">
                    <n-select
                      v-model:value="filterModel.defeatLevels"
                      :options="problemLevelOptions"
                      placeholder="请选择问题级别"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="来源">
                    <n-select
                      v-model:value="filterModel.source"
                      :options="sourceOptions"
                      placeholder="请选择来源"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="来源登记人">
                    <n-input
                      v-model:value="filterModel.sourceOwner"
                      placeholder="请输入"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="DTS单">
                    <n-input
                      v-model:value="filterModel.dtsOrderId"
                      placeholder="请输入DTS单"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="来源是否闭环">
                    <n-select
                      v-model:value="filterModel.sourceClosed"
                      :options="sourceCloseLoopOptions"
                      placeholder="请选择来源是否闭环"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="当前处理人">
                    <n-input
                      v-model:value="filterModel.currentHandler"
                      placeholder="请输入当前处理人"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="影响品类/产品">
                    <n-select
                      v-model:value="filterModel.affectedProducts"
                      :options="affectedProductsOptions"
                      placeholder="请选择影响品类/产品"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="计划闭环时间">
                    <n-date-picker
                      v-model:value="filterModel.solutionPlanTime"
                      type="daterange"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-collapse-item>
          </n-collapse>
          <div class="filter-buttons">
            <n-space>
              <n-button type="primary" @click="handleFilter">
                <template #icon>
                  <n-icon><search-outlined /></n-icon>
                </template>
                查询
              </n-button>
              <n-button @click="resetFilter">重置</n-button>
              <n-button type="success" @click="syncPura80Data(activeTab)">
                <template #icon>
                  <n-icon><sync-outlined /></n-icon>
                </template>
                同步最新Pura 80问题
              </n-button>
            </n-space>
          </div>
        </n-card>
      </n-tab-pane>

      <n-tab-pane name="PC" tab="PC机型问题">
        <n-card :title="'PC筛选条件'" class="filter-card">
          <n-collapse v-model:expanded-names="filterCollapsed">
            <n-collapse-item name="basic" title="基础筛选">
              <n-grid :cols="4" :x-gap="16" :y-gap="16">
                <n-grid-item>
                  <n-form-item label="登记日期">
                    <n-date-picker
                      v-model:value="filterModel.registrationDate"
                      type="daterange"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="问题描述">
                    <n-input
                      v-model:value="filterModel.defeatDescription"
                      placeholder="请输入问题描述"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="DTS单状态">
                    <n-select
                      v-model:value="filterModel.orderStatus"
                      :options="statusOptions"
                      placeholder="请选择状态"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-collapse-item>
            <n-collapse-item name="advanced" title="高级筛选">
              <n-grid :cols="4" :x-gap="16" :y-gap="16">
                <n-grid-item>
                  <n-form-item label="是否是关键问题">
                    <n-select
                      v-model:value="filterModel.keyDefeat"
                      :options="trueFalseOptions"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="EWP责任人">
                    <n-select
                      v-model:value="filterModel.ewpOwner"
                      placeholder="请选择EWP责任人"
                      :options="ewpOptions"
                      filterable
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="问题归属">
                    <n-select
                      v-model:value="filterModel.defeatAttribution"
                      :options="problemBelongingOptions"
                      placeholder="请选择问题归属"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="代表处">
                    <n-input
                      v-model:value="filterModel.representativeOffice"
                      placeholder="请输入代表处"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="应用名称">
                    <n-input
                      v-model:value="filterModel.appName"
                      placeholder="请输入应用名称"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="问题级别">
                    <n-select
                      v-model:value="filterModel.defeatLevels"
                      :options="problemLevelOptions"
                      placeholder="请选择问题级别"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="来源">
                    <n-select
                      v-model:value="filterModel.source"
                      :options="sourceOptions"
                      placeholder="请选择来源"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="来源登记人">
                    <n-input
                      v-model:value="filterModel.sourceOwner"
                      placeholder="请输入"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="DTS单">
                    <n-input
                      v-model:value="filterModel.dtsOrderId"
                      placeholder="请输入DTS单"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="来源是否闭环">
                    <n-select
                      v-model:value="filterModel.sourceClosed"
                      :options="sourceCloseLoopOptions"
                      placeholder="请选择来源是否闭环"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="当前处理人">
                    <n-input
                      v-model:value="filterModel.currentHandler"
                      placeholder="请输入当前处理人"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="影响品类/产品">
                    <n-select
                      v-model:value="filterModel.affectedProducts"
                      :options="affectedProductsOptions"
                      placeholder="请选择影响品类/产品"
                      clearable
                      multiple
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="计划闭环时间">
                    <n-date-picker
                      v-model:value="filterModel.solutionPlanTime"
                      type="daterange"
                      clearable
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
            </n-collapse-item>
          </n-collapse>
          <div class="filter-buttons">
            <n-space>
              <n-button type="primary" @click="handleFilter">
                <template #icon>
                  <n-icon><search-outlined /></n-icon>
                </template>
                查询
              </n-button>
              <n-button @click="resetFilter">重置</n-button>
              <n-button type="success" @click="syncPCData(activeTab)">
                <template #icon>
                  <n-icon><sync-outlined /></n-icon>
                </template>
                同步最新PC问题
              </n-button>
            </n-space>
          </div>
        </n-card>
      </n-tab-pane>
    </n-tabs>

    <n-card
      :title="activeTab === 'all' ? '生态监控' :`${activeTab}生态监控`"
      class="data-table-card"
    >
      <template #header>
        <div style="display: flex; align-items: center; width: 100%">
          <span class="card-title" style="margin-right: 16px; font-weight: bold; font-size: 16px">
            {{ activeTab === 'all' ? '生态监控' :`${activeTab}生态监控` }}
          </span>
        </div>
      </template>
      <template #header-extra>
        <n-space style="align-items: center">
          <n-button type="success" :disabled="selectedRows.length === 0" @click="generateTemplates">
            生成通报模板 ({{ selectedRows.length }})
          </n-button>
          <n-button type="success" secondary :loading="exportLoading" @click="handleExport">
            导出Excel
          </n-button>
          <n-button
            v-if="hasEwpPermission"
            type="primary"
            @click="openEditModal({ row: {}, mode: 'ewp' })"
          >
            手动登记问题
          </n-button>
          <n-switch
            v-model:value="onlyMine"
            @update:value="handleOnlyMineChange"
            style="margin-left: 12px"
          >
            <template #checked>只看我</template>
            <template #unchecked>全部</template>
          </n-switch>
        </n-space>
      </template>

      <n-data-table
        :columns="columns(loadData)"
        :data="data"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        :checked-row-keys="checkedRowKeys"
        @update:sorter="handleSorterChange"
        @update:filters="handleFiltersChange"
        @update:checked-row-keys="handleCheckedRowKeysChange"
        striped
        bordered
        :scroll-x="3400"
        :max-height="5000"
        virtual-scroll
        remote
      />
      <!-- <n-empty v-if="!loading && !data.length" description="暂无数据" /> -->
    </n-card>

    <n-modal
      v-model:show="showEditModal"
      style="width: 800px"
      preset="card"
      :title="editMode === 'ewp' ? 'EWP责任人编辑' : '应用PM编辑'"
      :mask-closable="false"
    >
      <n-form
        :model="editingRecord"
        label-placement="left"
        label-width="160"
        v-if="editingRecord"
        :rules="dynamicRules"
        :rule-props="{ immediate: true }"
        ref="formRef"
      >
        <template v-if="editMode === 'ewp'">
          <n-form-item label="登记日期" path="registrationDate">
            <n-date-picker
              v-model:value="editingRecord.registrationDate"
              type="date"
              :disabled="editMode === 'pm'"
            />
          </n-form-item>
          <n-form-item label="是否是关键问题">
            <n-select
              v-model:value="editingRecord.keyDefeat"
              :options="trueFalseOptions"
              :default-value="'否'"
            />
          </n-form-item>
          <n-form-item label="应用名称" path="appName">
            <n-input v-model:value="editingRecord.appName" @change="selectItems" />
          </n-form-item>
          <n-form-item label="EWP责任人">
            <n-select
              v-model:value="editingRecord.ewpOwner"
              placeholder="请选择EWP责任人"
              :options="ewpOptions"
              filterable
              clearable
            />
          </n-form-item>
          <n-form-item label="问题归属">
            <n-select
              v-model:value="editingRecord.defeatAttribution"
              :options="problemBelongingOptions"
            />
          </n-form-item>
          <n-form-item label="代表处">
            <n-input v-model:value="editingRecord.representativeOffice" />
          </n-form-item>
          <n-form-item label="问题描述" path="defeatDescription">
            <n-input v-model:value="editingRecord.defeatDescription" type="textarea" />
          </n-form-item>
          <n-form-item label="问题级别" path="defeatLevel">
            <n-select v-model:value="editingRecord.defeatLevel" :options="problemLevelOptions" />
          </n-form-item>
          <n-form-item label="来源" path="source">
            <n-select v-model:value="editingRecord.source" :options="sourceOptions" />
          </n-form-item>
          <!--          <n-form-item label="声量">-->
          <!--            <n-input-number v-model:value="editingRecord.opinionVolume" :min="1" />-->
          <!--          </n-form-item>-->
          <n-form-item label="知识ID">
            <n-input v-model:value="editingRecord.knowledgeId" />
          </n-form-item>
          <n-form-item label="DTS单">
            <n-input v-model:value="editingRecord.dtsOrderId" />
          </n-form-item>
          <!--          <n-form-item label="状态">-->
          <!--            <n-select v-model:value="editingRecord.orderStatus" :options="statusOptions" />-->
          <!--          </n-form-item>-->
          <n-form-item label="来源是否闭环">
            <n-select
              v-model:value="editingRecord.sourceClosed"
              :options="sourceCloseLoopOptions"
              :default-value="'否'"
            />
          </n-form-item>
          <n-form-item label="设备型号" path="device">
            <n-select
              v-model:value="editingRecord.device"
              :options="deviceModelOptions"
              placeholder="请选择设备型号"
              :default-value="filterModel.device"
            />
          </n-form-item>
        </template>

        <n-form-item label="IssueRreport单">
          <n-input v-model:value="editingRecord.irOrderId" />
        </n-form-item>
        <n-form-item label="进展">
          <n-input
            placeholder="请在表格内双击【最新进展】框填写"
            disabled
            type="textarea"
            :rows="3"
          />
<!--            :default-value="'【问题根因】XXXX\n【解决方案】XXXX'"-->
<!--            v-model:value="editingRecord.orderProgress"-->
        </n-form-item>
        <n-form-item label="数据来源" v-if="editMode === 'ewp'">
          <n-input
            v-model:value="editingRecord.dataSource"
            :default-value="'手动登记'"
            disabled
            style="font-weight: bold"
          />
        </n-form-item>
        <n-form-item label="当前处理人">
          <n-input v-model:value="editingRecord.currentHandler" />
        </n-form-item>
        <n-form-item label="领域主管" v-if="editMode === 'ewp'">
          <n-input v-model:value="editingRecord.domainLeader" :default-value="'李国良 00578453'" />
        </n-form-item>
        <n-form-item label="影响品类/产品">
          <n-select
            v-model:value="editingRecord.affectedProducts"
            :options="affectedProductsOptions"
            multiple
          />
        </n-form-item>
        <n-form-item label="计划闭环时间">
          <n-date-picker v-model:value="editingRecord.solutionPlanTime" type="date" clearable />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showEditModal = false">取消</n-button>
          <n-button type="primary" @click="saveEditedRecord">保存</n-button>
        </n-space>
      </template>
    </n-modal>

    <n-modal
      v-model:show="showImportModal"
      style="width: 600px"
      preset="card"
      title="导入历史Pura X问题"
      :mask-closable="false"
    >
      <n-upload>
        <n-upload-dragger
          :custom-request="handleImportFileChange"
          :default-upload="false"
          :max="1"
          :accept="'.xlsx,.csv'"
        >
          <div style="padding: 20px 0">
            <n-icon size="48" :depth="3">
              <upload-outlined />
            </n-icon>
            <div style="margin-top: 12px"> 点击或者拖动文件到该区域来导入 </div>
            <div style="margin-top: 12px"> 支持格式：.xlsx, .csv </div>
          </div>
        </n-upload-dragger>
      </n-upload>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showImportModal = false">取消</n-button>
          <n-button
            type="primary"
            @click="importPuraXData"
            :loading="importLoading"
            :disabled="!importFile"
          >
            导入
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <ProblemProgressModal
      v-model:show="showProblemModal"
      :order-id="currentProblem?.orderId || ''"
      :current-problem="currentProblem"
      :after-submit="afterSubmit"
    />
  </div>
</template>

<style lang="less" scoped>
  .ecological-tracking {
    padding: 16px;

    .device-tabs {
      margin-bottom: 16px;
    }

    .filter-card {
      margin-bottom: 16px;

      .filter-buttons {
        margin-top: 16px;
        display: flex;
        justify-content: flex-end;
      }

      :deep(.n-collapse-item) {
        .n-collapse-item__header {
          font-weight: 500;
        }
      }
    }

    .data-table-card {
      .n-data-table {
        max-width: 100%;
        overflow-x: auto;
      }
    }
  }
</style>
