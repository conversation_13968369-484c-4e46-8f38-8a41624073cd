<template>
  <div class="know-data-container">
    <!-- 顶部统计卡片 -->
    <n-grid :x-gap="12" :y-gap="8" :cols="4">
      <n-grid-item v-for="(stat, index) in statistics" :key="index">
        <n-card class="stat-card" :class="stat.type">
          <div class="stat-content">
            <div class="stat-icon">
              <n-icon size="24" :component="stat.icon" />
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stat.value }}{{ stat.unit }}</div>
              <div class="stat-label">
                <span>
                  {{ stat.label }}
                </span>
                <n-tooltip placement="bottom" trigger="hover">
                  <template #trigger>
                    <n-icon
                      color="#0e7a0d"
                      size="19px"
                      v-show="stat.label !== '知识总数'"
                      style="cursor: pointer; margin-left: 2px; padding-top: 4px"
                    >
                      <QuestionCircleTwotone />
                    </n-icon>
                  </template>
                  <span>{{ stat.info }}</span>
                </n-tooltip>
              </div>
            </div>
          </div>
          <n-progress
            v-show="stat.label !== '知识总数'"
            type="line"
            :percentage="stat.trend"
            processing
            :height="20"
            :border-radius="4"
          >
            {{ stat.trend }}{{ stat.unit }}
          </n-progress>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 趋势图表区域 -->
    <div class="chart-section">
      <n-card title="指标趋势分析" class="trend-chart">
        <template #header>
          <div class="chart-header">
            <!-- <div class="chart-title">
              <n-icon size="18"><TrendingUpOutline /></n-icon>
              <span>指标趋势分析</span>
              <n-tag type="info" size="small" class="time-tag">
                {{ timeRangeOptions.find((item) => item.value === timeRange)?.label }}
              </n-tag>
            </div> -->
            <!-- <div class="chart-toolbar">
              <n-space align="center">
                <n-radio-group v-model:value="chartType" size="small">
                  <n-radio-button value="line">
                    <template #default>
                      <n-icon><TrendingUpOutline /></n-icon> 折线图
                    </template>
                  </n-radio-button>
                  <n-radio-button value="bar">
                    <template #default>
                      <n-icon><BarChartOutline /></n-icon> 柱状图
                    </template>
                  </n-radio-button>
                </n-radio-group>
                <n-select
                  v-model:value="timeRange"
                  :options="timeRangeOptions"
                  size="small"
                  style="width: 120px"
                />
                <n-button-group>
                  <n-button size="small" @click="refreshData" :loading="isRefreshing">
                    <template #icon>
                      <n-icon><RefreshOutline /></n-icon>
                    </template>
                    刷新
                  </n-button>
                  <n-button size="small" @click="exportData">
                    <template #icon>
                      <n-icon><DownloadOutline /></n-icon>
                    </template>
                    导出
                  </n-button>
                </n-button-group>
              </n-space>
            </div> -->
          </div>
        </template>
        <div class="chart-container">
          <div ref="chartRef" class="trend-chart-inner"></div>
          <div class="chart-legend">
            <div
              v-for="(item, index) in chartLegend"
              :key="index"
              class="legend-item"
              :class="{ 'legend-item-disabled': !item.active }"
              @click="toggleLegend(index)"
            >
              <span class="legend-color" :style="{ backgroundColor: item.color }"></span>
              <span class="legend-label">{{ item.name }}</span>
              <!-- <span class="legend-value">{{ item.value }}%</span> -->
            </div>
          </div>
        </div>
      </n-card>
    </div>

    <!-- 详细数据表格 -->
    <n-card title="知识库使用率详情" class="data-table">
      <template #header-extra>
        <!-- <n-input-group>
          <n-input placeholder="根据知识ID搜索..." v-model:value="searchText">
            <template #prefix>
              <n-icon><SearchOutline /></n-icon>
            </template>
          </n-input>
          <n-button type="primary" ghost>
            <template #icon>
              <n-icon><FilterOutline /></n-icon>
            </template>
            筛选
          </n-button>
        </n-input-group> -->
      </template>
      <n-data-table
        :columns="columns"
        :data="displayTableData"
        :bordered="false"
        striped
        :scroll-x="6000"
        :loading="loading"
      />
    </n-card>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch, h } from 'vue';
  import * as echarts from 'echarts';
  import { NIcon, NSpace, NButton } from 'naive-ui';
  import {
    BookOutline,
    BulbOutline,
    CheckmarkCircleOutline,
    ServerOutline,
    TrendingUpOutline,
    BarChartOutline,
    RefreshOutline,
    DownloadOutline,
    SearchOutline,
    FilterOutline,
  } from '@vicons/ionicons5';

  import { QuestionCircleTwotone } from '@vicons/antd';

  import {
    getIndicatorTrend,
    getLatestIndicator,
    getUsageRateDetail,
  } from '@/api/dataview/knowData';

  // 定义类型
  interface DailyData {
    date: string;
    modifyCntByDay: number;
    rate: number;
  }

  interface PersonData {
    modifier: string;
    addModifyCnt: number;
    faultTreeDailyUsageRatePos: DailyData[];
  }

  interface DisplayData {
    date: string;
    index: number;
  }

  const loading = ref(false);
  // 统计数据
  const statistics = ref([
    {
      label: '使用率/截止到前一天',
      value: '--',
      unit: '%',
      icon: BookOutline,
      trend: 0,
      type: 'usage',
      info: '使用知识定界问题单数量 / 完成定界问题单总数 * 100%',
    },
    {
      label: '命中率/截止到前一天',
      value: '--',
      unit: '%',
      icon: BulbOutline,
      trend: 0,
      type: 'hit',
      info: '(有知识且知识可以解决问题 + 有知识但知识无法解决问题) / 完成定界问题单总数 * 100%',
    },
    {
      label: '覆盖率/截止到前一天',
      value: '--',
      unit: '%',
      icon: CheckmarkCircleOutline,
      trend: 0,
      type: 'accuracy',
      info: '有知识且知识可以解决问题  / 完成定界问题单总数 * 100%',
    },
    {
      label: '知识总数',
      value: '--',
      unit: '',
      icon: ServerOutline,
      type: 'total',
      info: '总知识数量',
    },
  ]);

  // 时间范围选择
  const timeRange = ref('7d');
  const timeRangeOptions = [
    { label: '最近7天', value: '7d' },
    { label: '最近30天', value: '30d' },
    { label: '最近90天', value: '90d' },
    { label: '自定义', value: 'custom' },
  ];

  // 图表相关
  const chartRef = ref<HTMLElement | null>(null);
  let chart: echarts.ECharts | null = null;

  // 表格相关
  const searchText = ref('');
  // 表格数据
  const tableData = ref<PersonData[]>();
  const fetchLatestIndicator = async () => {
    let res = await getLatestIndicator();
    statistics.value[0].trend = Number(res.usageRate) || 0;
    statistics.value[0].value = res.usageRate || '--';
    statistics.value[1].trend = Number(res.hitRate) || 0;
    statistics.value[1].value = res.hitRate || '--';
    statistics.value[2].trend = Number(res.accuracy) || 0;
    statistics.value[2].value = res.accuracy || '--';
    statistics.value[3].trend = Number(res.knowNum) || 0;
    statistics.value[3].value = res.knowNum || '--';
  };
  const fetchIndicatorTrend = async () => {
    let res = await getIndicatorTrend();
    updateChartWithTrendData(res);
  };
  const fetchUsageRateDetail = async () => {
    loading.value = true;
    let res = await getUsageRateDetail();
    tableData.value = res;
    initTableData();
    loading.value = false;
  };
  // 构建表格列
  type ColumnType = {
    title: string;
    key: string;
    fixed?: string;
    render?: (row: DisplayData) => any;
  };

  const columns = ref<ColumnType[]>([
    {
      title: '日期',
      key: 'date',
      fixed: 'left',
    },
  ]);

  // 用于美化显示使用率的样式
  const getUsageStyle = () => {
    return {
      style: {
        padding: '4px 8px',
        backgroundColor: 'rgba(64, 158, 255, 0.1)',
        borderRadius: '4px',
        color: '#409EFF',
        fontWeight: 'bold',
      },
    };
  };

  // 用于展示的表格数据
  const displayTableData = ref<DisplayData[]>([]);

  // 初始化表格列和数据
  const initTableData = () => {
    // 清空现有列，保留第一列
    if (columns.value.length > 1) {
      columns.value = columns.value.slice(0, 1);
    }

    // 添加人名作为表头
    if (tableData.value && tableData.value.length > 0) {
      tableData.value.forEach((person) => {
        columns.value.push({
          title: person.modifier,
          key: person.modifier,
          render: (row: DisplayData) => {
            const personData = tableData.value?.find((p) => p.modifier === person.modifier);
            if (
              personData &&
              personData.faultTreeDailyUsageRatePos &&
              personData.faultTreeDailyUsageRatePos[row.index]
            ) {
              const dailyData = personData.faultTreeDailyUsageRatePos[row.index];
              const rate = dailyData.rate;
              // 当值为100%时使用绿色样式
              const isMax = String(rate).includes('100');
              return h(
                'span',
                {
                  style: {
                    padding: '4px 8px',
                    backgroundColor: isMax ? 'rgba(103, 194, 58, 0.1)' : 'rgba(64, 158, 255, 0.1)',
                    borderRadius: '4px',
                    color: isMax ? '#67C23A' : '#409EFF',
                    fontWeight: 'bold',
                  },
                },
                `${dailyData.rate}`
              );
            }
            return '-';
          },
        });
      });

      // 准备展示数据
      const displayData: DisplayData[] = [];

      // 确保数据存在且有长度
      if (
        tableData.value[0] &&
        tableData.value[0].faultTreeDailyUsageRatePos &&
        tableData.value[0].faultTreeDailyUsageRatePos.length > 0
      ) {
        // 从数据中取出日期
        for (let i = 0; i < tableData.value[0].faultTreeDailyUsageRatePos.length; i++) {
          const dateStr = tableData.value[0].faultTreeDailyUsageRatePos[i].date;
          const dateObj = new Date(dateStr);
          const month = dateObj.getMonth() + 1;
          const day = dateObj.getDate();

          displayData.push({
            date: `${month}月${day}日`,
            index: i,
          });
        }

        displayTableData.value = displayData;
      }
    }
  };

  // 新增状态
  const chartType = ref('line');
  const isRefreshing = ref(false);
  const chartLegend = ref([
    { name: '使用率', value: 85.6, color: '#409EFF', active: true },
    { name: '命中率', value: 92.3, color: '#67C23A', active: true },
    { name: '覆盖率', value: 88.9, color: '#E6A23C', active: true },
  ]);

  // 切换图例显示状态
  const toggleLegend = (index: number) => {
    chartLegend.value[index].active = !chartLegend.value[index].active;
    updateChart();
  };

  // 更新图表数据
  const updateChartWithTrendData = (trendData: any[]) => {
    if (!chartRef.value || !chart) return;

    let dates: string[] = [];
    let usageRates: number[] = [];
    let hitRates: number[] = [];
    let accuracyRates: number[] = [];

    // 处理日期和使用率数据
    if (trendData && trendData.length > 0) {
      const usageData = trendData[0].faultTreeDailyUsageRatePos;
      const hitData = trendData[2].faultTreeDailyUsageRatePos;
      const accuracyData = trendData[1].faultTreeDailyUsageRatePos;

      dates = usageData.map((item: any) => {
        const date = new Date(item.date);
        const month = date.getMonth() + 1;
        const day = date.getDate();
        return `${month}月${day}日`;
      });

      usageRates = usageData.map((item: any) => {
        // 去掉百分号并转换为数字
        return parseFloat(item.rate.replace('%', ''));
      });

      hitRates = hitData.map((item: any) => {
        return parseFloat(item.rate.replace('%', ''));
      });

      accuracyRates = accuracyData.map((item: any) => {
        return parseFloat(item.rate.replace('%', ''));
      });
    }

    const option = {
      color: ['#409EFF', '#67C23A', '#E6A23C'],
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: '#eee',
        borderWidth: 1,
        textStyle: {
          color: '#333',
        },
        padding: [10, 15],
        formatter: (params: any) => {
          let result = `<div style="font-weight: bold; margin-bottom: 8px;">${params[0].axisValue}</div>`;
          params.forEach((item: any) => {
            if (chartLegend.value[item.seriesIndex]?.active !== false) {
              result += `
                <div style="display: flex; align-items: center; margin: 4px 0;">
                  <span style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background: ${item.color}; margin-right: 8px;"></span>
                  <span style="flex: 1;">${item.seriesName}</span>
                  <span style="font-weight: bold;">${item.value}%</span>
                </div>
              `;
            }
          });
          return result;
        },
      },
      grid: {
        top: 30,
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: chartType.value === 'bar',
        data:
          dates.length > 0
            ? dates
            : ['4月5日', '4月6日', '4月7日', '4月8日', '4月9日', '4月10日', '4月11日'],
        axisLine: {
          lineStyle: {
            color: '#ddd',
          },
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#eee',
          },
        },
        axisLabel: {
          formatter: '{value}%',
          color: '#666',
        },
      },
      series: [
        {
          name: '使用率',
          type: chartType.value,
          smooth: true,
          showSymbol: false,
          symbolSize: 6,
          lineStyle: {
            width: 3,
          },
          emphasis: {
            focus: 'series',
          },
          data: usageRates.length > 0 ? usageRates : [82, 85, 88, 84, 86, 83, 85],
        },
        {
          name: '命中率',
          type: chartType.value,
          smooth: true,
          showSymbol: false,
          symbolSize: 6,
          lineStyle: {
            width: 3,
          },
          emphasis: {
            focus: 'series',
          },
          data: hitRates.length > 0 ? hitRates : [90, 92, 91, 94, 89, 93, 92],
        },
        {
          name: '覆盖率',
          type: chartType.value,
          smooth: true,
          showSymbol: false,
          symbolSize: 6,
          lineStyle: {
            width: 3,
          },
          emphasis: {
            focus: 'series',
          },
          data: accuracyRates.length > 0 ? accuracyRates : [88, 86, 89, 87, 90, 88, 89],
        },
      ].map((series, index) => ({
        ...series,
        showSymbol: chartType.value === 'line',
        symbolSize: 8,
        emphasis: {
          scale: true,
          focus: 'series',
        },
        itemStyle: {
          borderRadius: 6,
        },
        lineStyle:
          chartType.value === 'line'
            ? {
                width: 3,
                shadowColor: 'rgba(0,0,0,0.2)',
                shadowBlur: 10,
              }
            : undefined,
        show: chartLegend.value[index].active,
        areaStyle:
          chartType.value === 'line'
            ? {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: chartLegend.value[index].color + '40', // 40 为透明度
                  },
                  {
                    offset: 1,
                    color: chartLegend.value[index].color + '05', // 05 为透明度
                  },
                ]),
              }
            : undefined,
      })),
    };

    chart.setOption(option);
  };

  // 更新图表
  const updateChart = () => {
    if (!chartRef.value || !chart) return;

    const option = {
      color: ['#409EFF', '#67C23A', '#E6A23C'],
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderColor: '#eee',
        borderWidth: 1,
        textStyle: {
          color: '#333',
        },
        padding: [10, 15],
        formatter: (params: any) => {
          let result = `<div style="font-weight: bold; margin-bottom: 8px;">${params[0].axisValue}</div>`;
          params.forEach((item: any) => {
            if (chartLegend.value[item.seriesIndex]?.active !== false) {
              result += `
                <div style="display: flex; align-items: center; margin: 4px 0;">
                  <span style="display: inline-block; width: 10px; height: 10px; border-radius: 50%; background: ${item.color}; margin-right: 8px;"></span>
                  <span style="flex: 1;">${item.seriesName}</span>
                  <span style="font-weight: bold;">${item.value}%</span>
                </div>
              `;
            }
          });
          return result;
        },
      },
      grid: {
        top: 30,
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: chartType.value === 'bar',
        data: ['4月5日', '4月6日', '4月7日', '4月8日', '4月9日', '4月10日', '4月11日'],
        axisLine: {
          lineStyle: {
            color: '#ddd',
          },
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: 'value',
        min: 75,
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#eee',
          },
        },
        axisLabel: {
          formatter: '{value}%',
          color: '#666',
        },
      },
      series: [
        {
          name: '使用率',
          type: chartType.value,
          smooth: true,
          showSymbol: false,
          symbolSize: 6,
          lineStyle: {
            width: 3,
          },
          emphasis: {
            focus: 'series',
          },
          data: [82, 85, 88, 84, 86, 83, 85],
        },
        {
          name: '命中率',
          type: chartType.value,
          smooth: true,
          showSymbol: false,
          symbolSize: 6,
          lineStyle: {
            width: 3,
          },
          emphasis: {
            focus: 'series',
          },
          data: [90, 92, 91, 94, 89, 93, 92],
        },
        {
          name: '覆盖率',
          type: chartType.value,
          smooth: true,
          showSymbol: false,
          symbolSize: 6,
          lineStyle: {
            width: 3,
          },
          emphasis: {
            focus: 'series',
          },
          data: [88, 86, 89, 87, 90, 88, 89],
        },
      ].map((series, index) => ({
        ...series,
        showSymbol: chartType.value === 'line',
        symbolSize: 8,
        emphasis: {
          scale: true,
          focus: 'series',
        },
        itemStyle: {
          borderRadius: 6,
        },
        lineStyle:
          chartType.value === 'line'
            ? {
                width: 3,
                shadowColor: 'rgba(0,0,0,0.2)',
                shadowBlur: 10,
              }
            : undefined,
        show: chartLegend.value[index].active,
        areaStyle:
          chartType.value === 'line'
            ? {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: chartLegend.value[index].color + '40', // 40 为透明度
                  },
                  {
                    offset: 1,
                    color: chartLegend.value[index].color + '05', // 05 为透明度
                  },
                ]),
              }
            : undefined,
      })),
    };

    chart.setOption(option);
  };

  // 监听图表类型变化
  watch(chartType, () => {
    updateChart();
  });

  // 刷新数据
  const refreshData = async () => {
    isRefreshing.value = true;
    try {
      // 模拟数据刷新
      await new Promise((resolve) => setTimeout(resolve, 1000));
      updateChart();
    } finally {
      isRefreshing.value = false;
    }
  };

  // 导出数据
  const exportData = () => {
    // TODO: 实现数据导出逻辑
  };

  // 初始化图表
  const initChart = () => {
    if (chartRef.value) {
      chart = echarts.init(chartRef.value);
      updateChart();
    }
  };

  onMounted(() => {
    initChart();

    window.addEventListener('resize', () => {
      chart?.resize();
    });
    fetchLatestIndicator();
    fetchIndicatorTrend();
    fetchUsageRateDetail();
  });
</script>

<style scoped lang="less">
  .know-data-container {
    padding: 16px;
    background: #f5f7fa;
    min-height: 100vh;

    .stat-card {
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .stat-content {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .stat-icon {
          margin-right: 16px;
          width: 48px;
          height: 48px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .stat-info {
          flex: 1;

          .stat-value {
            font-size: 24px;
            font-weight: bold;
            line-height: 1.2;
          }

          .stat-label {
            font-size: 14px;
            color: #666;
          }
        }
      }

      :deep(.n-progress) {
        margin-top: 8px;

        .n-progress-content {
          background-color: rgba(0, 0, 0, 0.05);
        }

        .n-progress-graph-line-rail {
          height: 8px !important;
          border-radius: 4px;
        }

        .n-progress-graph-line-fill {
          height: 8px !important;
          border-radius: 4px;
        }

        .n-progress-text {
          font-weight: bold;
          font-size: 12px;
          margin-left: 8px;
        }
      }

      &.usage {
        .stat-icon {
          background: rgba(64, 158, 255, 0.1);
          color: #409eff;
        }

        :deep(.n-progress-graph-line-fill) {
          background-color: #409eff;
        }
      }

      &.hit {
        .stat-icon {
          background: rgba(103, 194, 58, 0.1);
          color: #67c23a;
        }

        :deep(.n-progress-graph-line-fill) {
          background-color: #67c23a;
        }
      }

      &.accuracy {
        .stat-icon {
          background: rgba(230, 162, 60, 0.1);
          color: #e6a23c;
        }

        :deep(.n-progress-graph-line-fill) {
          background-color: #e6a23c;
        }
      }

      &.total {
        .stat-icon {
          background: rgba(144, 147, 153, 0.1);
          color: #909399;
        }

        :deep(.n-progress-graph-line-fill) {
          background-color: #909399;
        }
      }
    }

    .chart-section {
      margin: 16px 0;

      .trend-chart {
        background: white;

        .chart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          border-bottom: 1px solid #f0f0f0;

          .chart-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            font-weight: 500;

            i {
              color: #409eff;
            }

            .time-tag {
              margin-left: 8px;
            }
          }

          .chart-toolbar {
            display: flex;
            align-items: center;
            gap: 16px;
          }
        }

        .chart-container {
          position: relative;
          padding: 16px;

          .trend-chart-inner {
            height: 320px;
          }

          .chart-legend {
            display: flex;
            justify-content: center;
            gap: 24px;
            margin-top: 12px;

            .legend-item {
              display: flex;
              align-items: center;
              gap: 8px;
              cursor: pointer;
              padding: 4px 12px;
              border-radius: 4px;
              transition: all 0.3s;

              &:hover {
                background: #f5f7fa;
              }

              &.legend-item-disabled {
                opacity: 0.5;
              }

              .legend-color {
                width: 12px;
                height: 12px;
                border-radius: 50%;
              }

              .legend-label {
                color: #666;
              }

              .legend-value {
                font-weight: 500;
                margin-left: 4px;
              }
            }
          }
        }
      }
    }

    .data-table {
      margin-top: 16px;
    }
  }
</style>
