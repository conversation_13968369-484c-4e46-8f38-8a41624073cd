<template>
  <n-space vertical>
    <n-card>
      <n-spin :show="statisticLoading">
        <n-grid x-gap="20" y-gap="20" :cols="3">
          <n-gi>
            <div class="title">总出差人次</div>
            {{ statistic?.totalTravelCount || '-' }}
          </n-gi>
          <n-gi>
            <div class="title">总出差城市数</div>
            {{ statistic?.totalCityCount || '-' }}
          </n-gi>
          <n-gi>
            <div class="title">单人累计出差最长天数</div>
            {{ statistic?.longestDay || '-' }}
          </n-gi>
          <n-gi span="3">
            <div class="title">出差人次最多城市（TOP3）</div>
            <div v-if="statistic?.top3City?.length">
              <n-grid :cols="3" :x-gap="20" :y-gap="20">
                <n-gi v-for="(item, index) in statistic.top3City">
                  {{ item.city }}：{{ item.count }}次
                </n-gi>
              </n-grid>
              <template v-if="!statistic?.top3City?.length">-</template>
            </div>
          </n-gi>
          <n-gi span="3">
            <div class="title">出差人员天数排行榜（TOP5）</div>
            <div v-if="statistic?.top10Person?.length">
              <n-grid :cols="5" :x-gap="20" :y-gap="4">
                <n-gi v-for="item in statistic.top10Person.slice(0, 5)">
                  {{ item.name }}：{{ item.travelDays }}天
                </n-gi>
              </n-grid>
              <template v-if="!statistic?.top10Person?.length">-</template>
            </div>
          </n-gi>
        </n-grid>
      </n-spin>
    </n-card>
    <n-card>
      <n-form label-placement="left" label-width="auto" label-align="right" :model="filters">
        <n-grid x-gap="20" :cols="4" style="margin-bottom: 12px">
          <n-gi>
            <n-form-item label="统计维度" path="travelTime">
              <n-radio-group
                v-model:value="filters.isMonth"
                name="radiobuttongroup2"
                size="medium"
                @change="handleMouthChange()"
              >
                <n-radio-button :value="true"> 月份 </n-radio-button>
                <n-radio-button :value="false"> 年份 </n-radio-button>
              </n-radio-group>
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="时间" path="travelTime">
              <n-date-picker
                v-model:value="filters.travelTime"
                :type="filters.isMonth ? 'month' : 'year'"
                :style="{ width: '100%' }"
                :is-date-disabled="disablePreviousDate"
                clearable
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="人员类别" path="personType">
              <n-select
                v-model:value="filters.personType"
                :options="personTypeList"
                filterable
                clearable
                multiple
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="主管" path="leaderList">
              <n-select
                v-model:value="filters.leaderList"
                :options="leaderList"
                filterable
                multiple
                clearable
              />
            </n-form-item>
          </n-gi>
        </n-grid>
      </n-form>
      <n-space justify="center">
        <n-button secondary strong type="primary" @click="searchData(true)"> 查询 </n-button>
        <n-button secondary strong type="default" @click="refreshSearch()"> 重置 </n-button>
      </n-space>
    </n-card>
    <n-spin :show="loading">
      <n-card class="chart-card">
        <n-scrollbar x-scrollable x-placement="top">
          <div ref="timeDimensionChartRef" class="chart-container"></div>
        </n-scrollbar>
      </n-card>
    </n-spin>

    <n-spin :show="loading">
      <n-card class="chart-card">
        <n-scrollbar x-scrollable x-placement="top">
          <div ref="provinceDimensionChartRef" class="chart-container"></div>
        </n-scrollbar>
      </n-card>
    </n-spin>
  </n-space>
</template>
<script lang="ts" setup>
  import { Add, Refresh, Close, GridOutline } from '@vicons/ionicons5';
  import { NButton, useDialog, useMessage } from 'naive-ui';
  import { h, ref, reactive, onBeforeMount, onMounted, onUnmounted, defineProps, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import {
    formatDateTime,
    getChartFilters,
    districts,
    emergencyContactModelTemplate,
    relationList,
    getDateList,
    getMonthList,
  } from '../index';
  import {
    getTimeDimensionChart,
    getProvinceDimensionChart,
    getStatistic,
  } from '@/api/dataview/businessTravelersManage';
  import { useUserStore } from '@/store/modules/user';
  import { getEmployeeList } from '@/api/system/usermanage';
  import * as echarts from 'echarts';
  import { debounce, cloneDeep } from 'lodash-es';

  const timeDimensionChartRef = ref<HTMLElement | null>(null);
  const provinceDimensionChartRef = ref<HTMLElement | null>(null);
  const isSearch = ref(false);
  const loading = ref(true);
  const statistic = ref(reactive({}));
  const statisticLoading = ref(true);
  const contactModel = ref({ ...emergencyContactModelTemplate });
  const areaList = districts.map((item) => {
    return item?.sortName || item.name;
  });
  const dateList = getDateList(Date.now());
  const monthList = getMonthList(Date.now());
  const userStore = useUserStore();
  const props = defineProps({
    leaderList: {
      default: () => [],
      required: true,
    },
  });
  watch(
    () => props.leaderList,
    () => {
      leaderList.value = props.leaderList;
    }
  );
  const leaderList = ref(props.leaderList);
  const userNo = userStore.getUserInfo.account;
  const userList = ref([]);
  const hasContact = ref(false);
  const filters = ref(reactive(getChartFilters()));
  const searFilters = ref(reactive(getChartFilters()));
  let timeDimensionChart;
  let provinceDimensionChart;

  const personTypeList = [
    {
      label: 'OD',
      value: 'OD',
    },
    {
      label: '自有',
      value: '自有',
    },
  ];

  const disablePreviousDate = (ts: number) => {
    return ts > Date.now();
  };

  const initTimeDimensionChart = () => {
    timeDimensionChart = echarts.init(timeDimensionChartRef.value);
    let option = {
      grid: {
        top: 60,
        left: 40,
        right: 60,
        bottom: 120,
      },
      title: {
        top: 10,
        left: 'center',
        text: '在外人数统计（时间）',
      },
      label: {
        show: true,
        position: 'top',
        formatter: (params) => (params.value ? params.value : ''),
      },
      tooltip: {
        show: true,
      },
      xAxis: {
        type: 'category',
        name: '时间',
        nameGap: 20,
        nameTextStyle: {
          align: 'left',
          verticalAlign: 'top',
        },
        data: dateList,
        axisTick: {
          alignWithLabel: true,
          show: true,
        },
        axisLabel: {
          interval: '0',
          rotate: 40,
          margin: 20,
        },
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 10,
        name: '在外人数',
        nameGap: 20,
        nameTextStyle: {
          align: 'center',
        },
        axisLine: {
          show: true,
        },
      },
      series: [
        {
          data: [],
          type: 'bar',
          barMaxWidth: 20,
          barMinWidth: 15,
          barCategoryGap: 40,
          large: true,
        },
      ],
      dataZoom: [
        {
          id: 'dataZoomX',
          type: 'slider',
          xAxisIndex: [0],
          filterMode: 'filter',
          moveHandleSize: 0,
          bottom: 5,
          showDetail: false,
          brushSelect: false,
        },
      ],
    };
    timeDimensionChart.setOption(option);
  };

  const initProvinceDimensionChart = () => {
    provinceDimensionChart = echarts.init(provinceDimensionChartRef.value);
    let option = {
      grid: {
        top: 60,
        left: 40,
        right: 60,
        bottom: 80,
      },
      title: {
        top: 10,
        left: 'center',
        text: '在外人数统计（省份）',
      },
      label: {
        show: true,
        position: 'top',
        formatter: (params) => (params.value ? params.value : ''),
      },
      xAxis: {
        type: 'category',
        name: '省份',
        nameGap: 20,
        nameTextStyle: {
          align: 'left',
          verticalAlign: 'top',
        },
        axisTick: {
          alignWithLabel: true,
          show: false,
        },
        axisLabel: {
          interval: '0',
          width: 20,
          overflow: 'break',
          showMaxLabel: true,
          alignMaxLabel: 'center',
        },
        data: areaList,
      },
      yAxis: {
        type: 'value',
        name: '在外人数',
        nameGap: 20,
        nameTextStyle: {
          align: 'center',
        },
        min: 0,
        max: 10,
        axisLine: {
          show: true,
        },
      },
      tooltip: {
        show: true,
      },
      series: [
        {
          data: [],
          type: 'bar',
          barMaxWidth: 20,
          barMinWidth: 15,
          barGap: 10,
          barCategoryGap: 10,
        },
      ],
      dataZoom: [
        {
          id: 'dataZoomX',
          type: 'slider',
          xAxisIndex: [0],
          filterMode: 'none',
          // endValue: 10,
          moveHandleSize: 0,
          bottom: 5,
          showDetail: false,
          brushSelect: false,
        },
      ],
    };
    provinceDimensionChart.setOption(option);
  };

  const chartResize = debounce(() => {
    timeDimensionChart.resize();
    provinceDimensionChart.resize();
  });

  onMounted(async () => {
    initDefaultStatistic();
    await searchData();
    initTimeDimensionChart();
    initProvinceDimensionChart();
    window.addEventListener('resize', chartResize);
  });

  const initDefaultStatistic = async () => {
    statisticLoading.value = true;
    try {
      let res = await getStatistic();
      statistic.value = res.data;
    } catch (err) {}
    statisticLoading.value = false;
  };

  onUnmounted(() => {
    window.removeEventListener('resize', chartResize);
  });

  const handleMouthChange = () => {
    filters.value.travelTime = +new Date();
  };

  const searchData = debounce(async (isSearch) => {
    loading.value = true;
    if (isSearch) {
      searFilters.value = cloneDeep(filters.value);
    }
    let searForm = searFilters.value;
    let params = {
      personType: searForm.personType,
      year: new Date(searForm.travelTime).getFullYear(),
      month: new Date(searForm.travelTime).getMonth() + 1,
      leaderList: searForm.leaderList,
    };
    try {
      let isMonth = searFilters.value.isMonth;
      let statisticList = await Promise.all([
        getTimeDimensionChart(params, isMonth),
        getProvinceDimensionChart(params, isMonth),
      ]);
      updateTimeDimensionChartData(statisticList[0].data, isMonth);
      updateProvinceDimensionChartData(statisticList[1].data);
    } catch (e) {
      console.log(e);
    }
    loading.value = false;
  }, 300);

  const updateTimeDimensionChartData = (chartData, isMonth) => {
    let xAxisData = isMonth
      ? getDateList(searFilters.value.travelTime)
      : getMonthList(searFilters.value.travelTime);
    let seriesMap = {};
    chartData.forEach((item) => {
      seriesMap = {
        ...seriesMap,
        ...item,
      };
    });
    let seriesList = xAxisData.map((item) => {
      return isMonth ? seriesMap[item] : seriesMap[new Date(item).getMonth() + 1];
    });
    let max = Math.max(...seriesList) + 2 <= 5 ? 5 : Math.max(...seriesList) + 2;
    timeDimensionChart.setOption({
      xAxis: {
        data: xAxisData,
        axisLabel: { rotate: isMonth ? 40 : 0 },
      },
      yAxis: { max: max },
      series: { data: seriesList },
      grid: { bottom: isMonth ? 120 : 80 },
    });
  };

  const updateProvinceDimensionChartData = (chartData) => {
    let seriesList = areaList.map((item, index) => {
      return chartData?.[item] || chartData?.[districts?.[index]?.name] || 0;
    });
    let max = Math.max(...seriesList) + 2 <= 5 ? 5 : Math.max(...seriesList) + 2;
    provinceDimensionChart.setOption({
      yAxis: { max: max },
      series: { data: seriesList },
    });
  };

  const refreshSearch = debounce(() => {
    filters.value = reactive(getChartFilters());
    searFilters.value = reactive(getChartFilters());
    searchData();
  }, 300);
</script>

<style lang="less">
  @keyframes scroll-y {
    from {
      transform: translateY(25px);
    }
    to {
      transform: translateY(calc(-100%));
    }
  }
</style>
<style lang="less" scoped>
  .chart-card {
    overflow-x: auto;
  }
  .chart-container {
    width: 100%;
    min-width: 1000px;
    height: 500px;
  }
  .title {
    margin-bottom: 4px;
  }

  @media (max-width: 768px) {
    .charts {
      flex-direction: column;
    }
  }

  .marquee {
    height: 25px;
    overflow: hidden;
  }
</style>
