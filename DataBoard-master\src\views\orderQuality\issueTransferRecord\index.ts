import { reactive } from 'vue';

export const defaultSearchModel = {
  irNumber: null,
  operatePerson: null,
  operateLevel: null,
  outputLevel: null,
  sourcePerson: null,
  sourceLevel: null,
  targetPerson: null,
  targetLevel: null,
  review: null,
  updateTime: null,
  closeTime: null,
  irStatus: null,
  irType: null,
};
export const sampleDevidesUsageRegistrationColumn = [
  {
    title: '曾经处理人',
    key: 'sourcePerson',
    width: 130,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '曾经处理人运营级别',
    key: 'sourceLevel',
    width: 160,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 600px;overflow-y: auto',
      },
    },
  },
  {
    title: '目标处理人',
    key: 'targetPerson',
    width: 130,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '目标处理人运营级别',
    key: 'targetLevel',
    width: 140,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '指派操作人',
    key: 'operatePerson',
    width: 130,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '操作人运营级别',
    key: 'operateLevel',
    width: 130,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    render: (row) => {
      let level = row.operateLevel;
      try {
        let levelConfig = {
          Viewer: '数据查看员',
          Auditor: '审核员',
          SuperOperations: '超级运营',
        };
        level = levelConfig?.[level] || level || '';
      } catch (e) { }
      return level;
    },
  },
  {
    title: 'L2.5评论',
    key: 'review',
    width: 180,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render: (row) => {
      let review = row.review;
      try {
        if (review) {
          review = JSON.parse(review).join('、');
        }
      } catch (e) { }
      return review || '';
    },
  },
  {
    title: '转单操作时间',
    key: 'updateTime',
    width: 170,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    render: (row) => {
      let time;
      try {
        time = row.updateTime ? formatTime(row.updateTime) : '';
      } catch (e) { }
      return time || '';
    },
  },
];
// 单号，类型，创建时间，关单时间，状态，最高运营级别
export const outColumns = [
  {
    title: 'IR单号',
    key: 'irNumber',
    fixed: 'left',
  },
  {
    title: '最高运营级别',
    key: 'outputLevel',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '闭环人',
    key: 'outputPerson',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '类型',
    key: 'irType',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '创建时间',
    key: 'createTime',
    resizable: true,
    render: (row) => {
      let time;
      try {
        time = row.createTime ? formatTime(row.createTime) : '';
      } catch (e) { }
      return time || '';
    },
  },
  {
    title: '关单时间',
    key: 'closeTime',
    ellipsis: {
      tooltip: true,
    },
    resizable: true,
    render: (row) => {
      let time;
      try {
        time = row.closeTime ? formatTime(row.closeTime) : '';
      } catch (e) { }
      return time || '';
    },
  },
  {
    title: 'IR单状态',
    key: 'irStatus',
    fixed: 'right',
  },
];
export const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 150,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  prefix({ itemCount }) {
    return `总条数 ${itemCount}`;
  },
  onChange: (page: number) => {
    paginationReactive.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    paginationReactive.pageSize = pageSize;
    paginationReactive.page = 1;
  },
});

export const formatTime = (time) => {
  try {
    const date = new Date(time);
    let Y = date.getFullYear();
    let M = `00${date.getMonth() + 1}`.slice(-2);
    let D = `00${date.getDate()}`.slice(-2);
    let h = `00${date.getHours()}`.slice(-2);
    let m = `00${date.getMinutes()}`.slice(-2);
    let s = `00${date.getSeconds()}`.slice(-2);
    return `${Y}/${M}/${D} ${h}:${m}:${s}`;
  } catch (error) { }
};
