export const applicationTypeList = [
  {
    label: '应用',
    value: '应用',
  },
  {
    label: '元服务',
    value: '元服务',
  },
];
export const firstLevelAcceptanceList = [
  {
    label: '验收测试团队验收',
    value: '验收测试团队验收',
  },
  {
    label: '四组一队现场验收',
    value: '四组一队现场验收',
  },
  {
    label: '伙伴书面举证',
    value: '伙伴书面举证',
  },
  {
    label: '其他',
    value: '其他',
  },
];
export const secondLevelAcceptanceList = [
  {
    label: '录屏',
    value: '录屏',
  },
  {
    label: '书面举证',
    value: '书面举证',
  },
  {
    label: '现场验收',
    value: '现场验收',
  },
  {
    label: 'Block',
    value: 'Block',
  },
  {
    label: '其他',
    value: '其他',
  },
];
export const functionEnhancementPriorityList = [
  {
    label: '高',
    value: '高',
  },
  {
    label: '中',
    value: '中',
  },
  {
    label: '其他',
    value: '其他',
  },
];
export const applyVerticalDomainList = [
  {
    label: '便捷生活',
    value: '便捷生活',
  },
  {
    label: '出行导航',
    value: '出行导航',
  },
  {
    label: '儿童',
    value: '儿童',
  },
  {
    label: '购物比价',
    value: '购物比价',
  },
  {
    label: '教育',
    value: '教育',
  },
  {
    label: '金融理财',
    value: '金融理财',
  },
  {
    label: '旅游住宿',
    value: '旅游住宿',
  },
  {
    label: '美食',
    value: '美食',
  },
  {
    label: '母婴',
    value: '母婴',
  },
  {
    label: '拍摄美化',
    value: '拍摄美化',
  },
  {
    label: '汽车',
    value: '汽车',
  },
  {
    label: '商务',
    value: '商务',
  },
  {
    label: '社交通讯',
    value: '社交通讯',
  },
  {
    label: '实用工具',
    value: '实用工具',
  },
  {
    label: '新闻阅读',
    value: '新闻阅读',
  },
  {
    label: '行业通用',
    value: '行业通用',
  },
  {
    label: '影音娱乐',
    value: '影音娱乐',
  },
  {
    label: '运动健康',
    value: '运动健康',
  },
  {
    label: '主题个性',
    value: '主题个性',
  },
  {
    label: '游戏',
    value: '游戏',
  },
  {
    label: '待确定',
    value: '待确定',
  },
];
export const modelTemplate = 
{
  functionEnhancementPercentage: '',
  applicationName: '', //应用名称  *
  company: '', //公司名称 *
  applicationType: '', //应用类型 *
  kcpStage: '', //KCP阶段
  label: ['4796','垂域专精','企业内部办公应用'], //标签
  applyVerticalDomainList:['便捷生活','出行导航', '儿童', '购物比价', '教育', '金融理财', '旅游住宿', '美食', '母婴', '拍摄美化', '汽车', '商务', '社交通讯', '实用工具', '新闻阅读', '行业通用','影音娱乐','运动健康','主题个性','待确定'], //应用垂域
  provinceList: null, //省份
  destList: null, //dest人员
  acceptances: null, //验收责任人
  applicationPackageName: '', //应用包名
  releaseConclusion: '', // 上架结论 *
  latestTestConclusion: '', //最新测试结论 *
  lastTestTimeList: null, //最新转测时间
  lastCompletionTime: null, //测试完成时间
  rounds: '', //轮次 *
  firstTestTime: null, //首轮转测时间
  firstCompletionTime: null, //首轮完成时间
  firstSLA: '', //首轮SLA, int类型 *
  isFunctionEnhancements: '', //是否功能增强 *
  isDataInheritance: '', //是否数据继承 *
  involvedFoldingScreen: '', //是否涉及折叠屏 *
  pad: '', //是否涉及pad *
  pc: '', //是否涉及pc *
  innovativeSpecial: '', //创新特性 * 是否NA
  standardApp: '', // A标应用， 是否NA *
  hwAccountLogin: '', //华为账号登录 是否NA *
  hwAccountOneKeyLogin: '', //华为账号一键登录 是否NA *
  permissionSecurityPicker: '', //权限安全picker 是否NA  *
  digitalCashier: '',
  physicalCashier: '',
  remark: '', //备注
  firstSlaList: [],
  roundsList: [],
  involvedFoldingScreenList: [],
  standardAppList: [],
  permissionSecurityPickerList: [],
  digitalCashierList: [],
  physicalCashierList: [],
  isFunctionEnhancementsList: [],
  padList: [],
  pcList: [],
  hwAccountLoginList: [],
  hwAccountOneKeyLoginList: [], //华为账号一键登录 是否NA *
  innovativeSpecialList: [],
  isDataInheritanceList: [],
  applicationNameList: null,
  releaseConclusionList: [],
  lastTestConclusionList: [],
  isServiceCompanyUndertakes: '',
  isServiceCompanyUndertakesList: [],
  latestTestConclusionList: [],
  functionEnhancementPriorityList: [],
  kcpStageList: [],
  firstLevelAcceptanceList: [],
  secondLevelAcceptanceList: []
};
export const testResModelTemplate = {
  testStartDate: null,
  testEndDate: null,
  userNo: '',
  testConclusion: '',
};
export function formatDateTime(date, format) {
  date = new Date(date);
  const o = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12, // 小时
    'H+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
    a: date.getHours() < 12 ? '上午' : '下午', // 上午/下午
    A: date.getHours() < 12 ? 'AM' : 'PM', // AM/PM
  };
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      );
    }
  }
  return format;
}
export function getDefaultProblemHandleFilters() {
  return { ...modelTemplate };
}

export const checkList = [
  {
    label: '通过',
    value: '通过',
  },
  {
    label: '不通过',
    value: '不通过',
  },
];
export const testResList = [
  {
    label: 'KCP5：通过',
    value: 'KCP5：通过',
  },
  {
    label: 'KCP5：不通过',
    value: 'KCP5：不通过',
  },
  {
    label: 'KCP5：驳回',
    value: 'KCP5：驳回',
  },
  {
    label: 'KCP5：测试中',
    value: 'KCP5：测试中',
  },
  {
    label: 'KCP5：带风险通过',
    value: 'KCP5：带风险通过',
  },
  {
    label: 'KCP6：通过',
    value: 'KCP6：通过',
  },
  {
    label: 'KCP6：不通过',
    value: 'KCP6：不通过',
  },
  {
    label: 'KCP6：驳回',
    value: 'KCP6：驳回',
  },
  {
    label: 'KCP6：测试中',
    value: 'KCP6：测试中',
  },
  {
    label: 'KCP6：带风险通过',
    value: 'KCP6：带风险通过',
  },
  {
    label: 'KCP7：通过',
    value: 'KCP7：通过',
  },
  {
    label: 'KCP7：不通过',
    value: 'KCP7：不通过',
  },
  {
    label: 'KCP7：驳回',
    value: 'KCP7：驳回',
  },
  {
    label: 'KCP7：测试中',
    value: 'KCP7：测试中',
  },
  {
    label: 'KCP7：带风险通过',
    value: 'KCP7：带风险通过',
  },
];
export const labelList = [
  {
    label: '4796',
    value: '4796',
  },
  {
    label: '生态丰富度5165',
    value: '生态丰富度5165',
  },
  {
    label: '垂域专精',
    value: '垂域专精',
  },
  {
    label: '心愿单',
    value: '心愿单',
  },
  {
    label: '企业内部办公应用',
    value: '企业内部办公应用',
  },
  {
    label: '头部互联网',
    value: '头部互联网',
  },
  {
    label: '区域重点',
    value: '区域重点',
  },
  {
    label: '行业总部',
    value: '行业总部',
  },
];
export const provinceList = [
  { label: '安徽', value: '安徽' },
  { label: '北京', value: '北京' },
  { label: '福建', value: '福建' },
  { label: '甘肃', value: '甘肃' },
  { label: '广东', value: '广东' },
  { label: '广西（含海南）', value: '广西（含海南）' },
  { label: '贵州', value: '贵州' },
  { label: '河北', value: '河北' },
  { label: '河南', value: '河南' },
  { label: '黑龙江', value: '黑龙江' },
  { label: '湖北', value: '湖北' },
  { label: '湖南', value: '湖南' },
  { label: '吉林', value: '吉林' },
  { label: '江苏', value: '江苏' },
  { label: '江西', value: '江西' },
  { label: '辽宁', value: '辽宁' },
  { label: '内蒙古', value: '内蒙古' },
  { label: '青海', value: '青海' },
  { label: '山东', value: '山东' },
  { label: '山西', value: '山西' },
  { label: '陕西（含宁夏）', value: '陕西（含宁夏）' },
  { label: '上海', value: '上海' },
  { label: '深圳', value: '深圳' },
  { label: '四川', value: '四川' },
  { label: '天津', value: '天津' },
  { label: '香港', value: '香港' },
  { label: '新疆', value: '新疆' },
  { label: '行业系统部', value: '行业系统部' },
  { label: '云南', value: '云南' },
  { label: '运营商', value: '运营商' },
  { label: '浙江', value: '浙江' },
  { label: '终端云', value: '终端云' },
  { label: '重庆', value: '重庆' },
  { label: '西藏', value: '西藏' },
];
export const isServiceList = [
  {
    label: '是',
    value: '是',
  },
  {
    label: '否',
    value: '否',
  },
];
export const releaseConclusionList = [
  {
    label: '驳回',
    value: '驳回',
  },
  {
    label: '不通过',
    value: '不通过',
  },
  {
    label: '裁决同意公开测试上架',
    value: '裁决同意公开测试上架',
  },
  {
    label: '裁决同意正式上架',
    value: '裁决同意正式上架',
  },
  {
    label: '允许公开测试上架',
    value: '允许公开测试上架',
  },
  {
    label: '允许正式上架',
    value: '允许正式上架',
  },
];
export const kcpStageList = [
  {
    label: '启动',
    value: '启动',
  },
];
for (let i = 1; i < 9; i++) {
  kcpStageList.push({
    label: 'KCP' + i,
    value: 'KCP' + i,
  });
}

// 专项信息keys
export const specialInformationKeys = [
  'isDataInheritance', // 数据继承
  'involvedFoldingScreen', // 折叠屏
  'pad', // PAD
  'pc', // PC
  'innovativeSpecial', // 创新特性
  'standardApp', // A标应用
  'hwAccountLogin', // 华为账号登录
  'hwAccountOneKeyLogin', // 华为账号一键登录
  'permissionSecurityPicker', // 权限安全picker
  'digitalCashier', // 数字商品收银台
  'physicalCashier', // 实物商品收银台
];
