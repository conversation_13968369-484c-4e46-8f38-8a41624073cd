<template>
  <div ref="lineBarChartDom" id="lineBarChartDom" class="chart-container"></div>
</template>

<script lang="ts" setup>
import { onMounted } from "vue"
import * as echarts from "echarts"
let chart

onMounted(() => {
  initChart()
  resizeChart()
})

const resizeChart = () => {
  window.addEventListener('resize', () => {
    if (chart) {
      chart.resize()
    }

  })
}

// 基础配置一下Echarts
function initChart() {
  chart = echarts.init(document.getElementById("lineBarChartDom"));
  // 把配置和数据放这里
  chart.setOption({
    title: {
      text: '样例代码数量和使用数',
      x: 'center',
      y: 10
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    // toolbox: {
    //   feature: {
    //     dataView: { show: true, readOnly: false },
    //     magicType: { show: true, type: ['line', 'bar'] },
    //     restore: { show: true },
    //     saveAsImage: { show: true }
    //   }
    // },
    legend: {
      data: ['代码数量', '使用数'],
      x: 'center',
      y: 'bottom'
    },
    xAxis: [
      {
        type: 'category',
        data: ['23-10.6', '23-10.13', '23-10.20', '23-10.27', '23-11.03'],
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '代码数量',
        min: 680,
        max: 900,
        // interval: 22,
        axisLabel: {
          formatter: '{value}'
        }
      },
      {
        type: 'value',
        name: '使用数',
        min: 290,
        max: 410,
        // interval: 22,
        axisLabel: {
          formatter: '{value} '
        }
      }
    ],
    series: [
      {
        name: '代码数量',
        type: 'bar',
        barWidth: '30%',
        tooltip: {
          valueFormatter: function (value) {
            return value;
          }
        },
        data: [
          757, 764, 774, 883, 861
        ]
      },
      {
        name: '使用数',
        type: 'line',
        yAxisIndex: 1,
        tooltip: {
          valueFormatter: function (value) {
            return value
          }
        },
        data: [320, 346, 356, 362, 367]
      }
    ]
  });

}


</script>

<style scoped>
.fut-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
  width: 100%;
  justify-content: space-between;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
