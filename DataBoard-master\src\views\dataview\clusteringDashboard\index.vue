<template>
  <div>
    <n-card style="margin-bottom: 16px">
      <!-- 表单查询部分 -->
      <n-card>
            <n-form
              :model="searchForm"
              label-placement="left"
              label-width="auto"
              require-mark-placement="right-hanging"
              @submit.prevent="handleSearch"
            >
              <n-grid :cols="24" :x-gap="24">
                <n-grid-item
                  v-for="item in searchFormItemsDTSResolveStates"
                  :key="item.field"
                  :span="8"
                >
                  <n-form-item :label="item.label" :path="item.field">
                    <n-date-picker
                      clearable
                      v-model:value="searchFormItemsDTSResolveStates[item.field]"
                      type="daterange"
                      :is-date-disabled="disablePreviousDate"
                      @confirm="onConfirm"
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
              <div class="form-actions">
                <n-space>
                  <n-button @click="resetForm">重置</n-button>
                  <n-button type="primary" attr-type="submit">查询</n-button>
                </n-space>
              </div>
            </n-form>
          </n-card>

      <!-- 折线图部分 -->
      <n-card >
        <div class="chart-container">
          <div ref="accuracyChart" class="chart"></div>
          <div ref="compressChart" class="chart"></div>
        </div>
      </n-card>

      <!-- 表格 -->
      <n-card class="table-container">
        <n-data-table
          remote
          :columns="tableColumns"
          :data="clusteringDashData"
          :bordered="true"
          :single-line="false"
          :loading="loadingClustering"
          size="small"
        />
      </n-card>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { ewpService as service } from '@/utils/axios';
  import { NCard, NButton } from 'naive-ui';
  import { searchFormItemsDTSResolveStates } from './columns';
  import * as echarts from 'echarts';

  const searchForm = reactive({
    status: null,
    top: '',
  });

  let clusteringDashData = reactive([
    {
      date:'',
      top10accuracy:'',
      top20accuracy:'',
      top50accuracy:'',
      top10compressibility:'',
      top20compressibility:'',
      top50compressibility:'',
    },
  ]);

  const loadingClustering = ref(false);
  const loadingRes = ref(false);
  const paginationReactive = reactive({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    showSizePicker: true,
    pageSizes: [10, 20, 50],
    prefix({ itemCount }) {
      return `Total：${itemCount}`;
    },
    onChange: (page: number) => {
      paginationReactive.page = page;
      fetchClusteringDashData();
    },
    onUpdatePageSize: (pageSize: number) => {
      paginationReactive.pageSize = pageSize;
      paginationReactive.page = 1;
      fetchClusteringDashData();
    },
  });

  /**
   * 请求折线图数据方法
   * @param link 请求的链接
   * @param params 入参
   */

    // 接口请求获取折线图数据参数
  interface chartDataParams {
    startDate?: string;
    endDate?: string;
    pageNo?: number;
    pageSize?: number;
  }

  const chartDataLink = 'futAccuracy/query';

  async function getChartDataList(link, params: chartDataParams): Promise<any> {
    try {
      const response = await service.post(`/management/${link}`, params);
      return response;
    } catch (error) {
      console.error('Error fetching work order list:', error);
      throw error;
    }
  }

  const chartData = ref([] as any);
  const accuracyData = ref([] as any);
  const compressibilityData = ref([] as any);

  // 折线图数据处理
  const fetchChartData = async () => {
    loadingRes.value = true;
    try {
      const queryParams = {
        startDate: '',
        endDate: '',
        pageNo: 0,
        pageSize: 0,
      };
      const data = await getChartDataList(chartDataLink, queryParams);
      chartData.value = data;
      for (let i = 0; i < chartData.value.length; i++) {
        accuracyData.value.push(formatDataNum(chartData.value[i].accuracy));
        compressibilityData.value.push(formatDataNum(chartData.value[i].compressibility));
      }
      initAccuracyChart();
      initCompressChart();
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loadingRes.value = false;
    }
  };

  // 准确率折线图
  const accuracyChart = ref(null);
  const initAccuracyChart = () => {
    if (!accuracyChart.value) return;
    const chart = echarts.init(accuracyChart.value, 'dark');
    const option = {
      backgroundColor: '#f0f0f0',
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.7)', //设置背景颜色为深色，这里使用的是 RGBA 格式，其中 A 为透明度
        textStyle: {
          color: '#fff', //设置文字颜色为白色，以便在深色背景上更清晰可见
        },
      },
      legend: {data: ['准确率']},
      grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true},
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: chartData.value.map((item) => formatDate(item.date)),
      },
      yAxis: {type: 'value'},
      series: [
        {
          name: '准确率',
          type: 'line',
          data: accuracyData.value,
          smooth: true,
        },
      ],
    };
    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
  };

  // 压缩率折线图
  const compressChart = ref(null);
  const initCompressChart = () => {
    if (!compressChart.value) return;
    const chart = echarts.init(compressChart.value, 'dark');
    const option = {
      backgroundColor: '#f0f0f0',
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(0, 0, 0, 0.7)', //设置背景颜色为深色，这里使用的是 RGBA 格式，其中 A 为透明度
        textStyle: {
          color: '#fff', //设置文字颜色为白色，以便在深色背景上更清晰可见
        },
      },
      legend: {data: ['压缩率']},
      grid: {left: '3%', right: '4%', bottom: '3%', containLabel: true},
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: chartData.value.map((item) => formatDate(item.date)),
      },
      yAxis: {type: 'value'},
      series: [
        {
          name: '压缩率',
          type: 'line',
          color: '#FF5733',
          data: compressibilityData.value,
          smooth: true,
        },
      ],
    };
    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
  };

  const clusteringQueryParams = {
    startDate: '',
    endDate: '',
    top: '',
  };

  const tableColumns= [
    {
      title: '日期',
      key: 'date',
      align: 'center',
      render(row) {
        return formatDate(row.date);
      },
    },
    {
      title: '准确率',
      key: 'accuracy',
      align: 'center',
      children: [
        {
          title: 'TOP10',
          key: 'top10accuracy',
          align: 'center',
          render(row) {
            return formatData(row.top10accuracy);
          },
        },
        {
          title: 'TOP20',
          key: 'top20accuracy',
          align: 'center',
          render(row) {
            return formatData(row.top20accuracy);
          },
        },
        {
          title: 'TOP50',
          key: 'top50accuracy',
          align: 'center',
          render(row) {
            return formatData(row.top50accuracy);
          },
        },
      ],
    },
    {
      title: '压缩率',
      key: 'compress',
      align: 'center',
      children: [
        {
          title: 'TOP10',
          key: 'top10compressibility',
          align: 'center',
          render(row) {
            return formatData(row.top10compressibility);
          },
        },
        {
          title: 'TOP20',
          key: 'top20compressibility',
          align: 'center',
          render(row) {
            return formatData(row.top20compressibility);
          },
        },
        {
          title: 'TOP50',
          key: 'top50compressibility',
          align: 'center',
          render(row) {
            return formatData(row.top50compressibility);
          },
        },
      ],
    },
  ];

  /**
   * 接口入参数据
   */
  interface clusteringSolveQueryParams {
    startDate?: string;
    endDate?: string;
    pageNo: number;
    pageSize: number;
  }

  const clusteringSolveStateLink = 'futAccuracy/tableQuery';

  /**
   * 请求每日工单定界情况
   * @param link 请求的链接
   * @param params 入参
   */
  async function getDataList(link, params: clusteringSolveQueryParams): Promise<any> {
    try {
      const response = await service.post(`/management/${link}`, params);
      return response;
    } catch (error) {
      console.error('Error fetching work order list:', error);
      throw error;
    }
  }

  const handleSearch = () => {
      fetchClusteringDashData();
      // fetchChartData();
  };

  const resetForm = () => {
      Object.keys(clusteringQueryParams).forEach((key) => {
        clusteringQueryParams[key] = '';
      });
      searchFormItemsDTSResolveStates.value.submitTime = null;
      fetchClusteringDashData();
      fetchChartData();
  };

  const fetchClusteringDashData = async () => {
    loadingClustering.value = true;
    try {
      const queryParams = {
        startDate: clusteringQueryParams.startDate,
        endDate: clusteringQueryParams.endDate,
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
      };
      const data = await getDataList(clusteringSolveStateLink, queryParams);
      clusteringDashData = data;
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loadingClustering.value = false;
    }
  };

  const formatDate = (dateString: string) => {
    return dateString.split('T')[0];
  };

  const formatDataNum = (dataNumber: number) => {
    return (dataNumber * 100).toFixed(2);
  };

  const formatData = (dataNumber: number) => {
    return `${(dataNumber * 100).toFixed(2)}%`;
  };

  const disablePreviousDate = (ts: number) => {
    return ts > Date.now();
  };

  const onConfirm = (v: number | number[] | null) => {
    if (v) {
      clusteringQueryParams.startDate = timestampToDate(v[0]);
      clusteringQueryParams.endDate = timestampToDate(v[1]);
    }
  };

  /**
   时间戳转换年月日
   */

  const timestampToDate = (timestamp: number): string => {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  onMounted(() => {
    fetchClusteringDashData();
    fetchChartData();
  });
</script>

<style lang="less" scoped>
  .thead-float {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
  }
  .table-container {
    width: 100%; /* 设置容器宽度 */
    overflow-x: auto; /* 横向滚动条自动显示 */
    white-space: nowrap; /* 防止内容换行 */
    table {
      width: 1000px; /* 设置表格宽度大于容器宽度以触发滚动 */
      text-align: center;
    }
  }
  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 12px; /* 减小上边距 */
  }
  .chart {
    height: 300px;
    width: 50%;
    margin-left: 20px;
  }
  .chart-container {
    display: flex;
    gap: 1rem;
    flex-wrap: nowrap;
  }
</style>
