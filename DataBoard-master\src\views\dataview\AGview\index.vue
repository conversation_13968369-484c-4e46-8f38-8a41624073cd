<template>
  <div class="app-container">
    <div class="filter-container">
      <n-card>
        <n-form
          :model="searchForm"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          @submit.prevent="handleSearch"
        >
          <n-grid :cols="24" :x-gap="24">
            <n-grid-item v-for="item in visibleSearchFormItems" :key="item.field" :span="6">
              <n-form-item :label="item.label" :path="item.field">
                <n-input
                  clearable
                  v-if="item.component === 'Input'"
                  v-model:value="searchForm[item.field]"
                  @keyup.enter="handleSearch"
                />
                <n-select
                  clearable
                  v-else-if="item.component === 'Select'"
                  v-model:value="searchForm[item.field]"
                  :options="item.componentProps.options"
                  :multiple="item.field === 'createTime'"
                />
                <n-date-picker
                  v-else-if="item.component === 'DateRangePicker'"
                  v-model:value="searchForm[item.field]"
                  type="daterange"
                  clearable
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>
          <div class="form-actions">
            <n-space>
              <n-button @click="resetForm">重置</n-button>
              <n-button type="primary" attr-type="submit">查询</n-button>
              <n-button @click="toggleExpandForm">
                {{ isExpanded ? '收起' : '展开' }}
                <template #icon>
                  <n-icon>
                    <chevron-down v-if="!isExpanded" />
                    <chevron-up v-else />
                  </n-icon>
                </template>
              </n-button>
            </n-space>
          </div>
        </n-form>
      </n-card>
    </div>

    <n-card style="margin-top: 12px">
      <n-space align="center" style="margin-bottom: 10px; margin-top: -9px">
        <n-space>
          <n-button @click="onRelateDTS"> 关联DTS单 </n-button>
        </n-space>
      </n-space>
      <n-data-table
        remote
        :bordered="false"
        :single-line="false"
        striped
        @update:filters="handleFiltersChange"
        :columns="columns"
        :data="tableData"
        :pagination="paginationReactive"
        :loading="loading"
        :scroll-x="2000"
        :row-key="(row) => row.id"
        :checked-row-keys="checkedRowKeys"
        @update:checked-row-keys="handleCheckedRowKeysChange"
        :max-height="477"
      />
    </n-card>
    <!-- 关联DTS单 -->
    <n-modal v-model:show="showRelateModal">
      <n-card
        style="width: 600px"
        title="请输入DTS单号"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-input v-model:value="relateDTS" />
        <template #footer>
          <n-space justify="end">
            <n-button type="primary" @click="onRelateClick" :disabled="!relateDTS">保存</n-button>
            <n-button @click="showRelateModal = false">取消</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script lang="ts">
  export default {
    name: 'Dtsstate',
  };
</script>

<script lang="ts" setup>
  import { ref, reactive, onMounted, computed, h } from 'vue';
  import {
    NCard,
    NForm,
    NFormItem,
    NInput,
    NSelect,
    NButton,
    NSpace,
    NGrid,
    NGridItem,
    NIcon,
    useMessage,
  } from 'naive-ui';
  import { ChevronDown, ChevronUp } from '@vicons/ionicons5';
  import { createColumns, getSearchFormItems } from './columns';
  import { filterObjectValues } from '@/utils';
  import { getAgFeedback, batchSave } from '@/api/dataview/ag';

  const searchForm = reactive({});
  const tableData = ref<any[]>([]);
  const loading = ref(false);
  const message = useMessage();
  const searchFormItems = computed(() => getSearchFormItems());
  const paginationReactive = reactive({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    prefix({ itemCount }) {
      return `Total：${itemCount}`;
    },
    onChange: (page: number) => {
      paginationReactive.page = page;
      fetchData();
    },
    onUpdatePageSize: (pageSize: number) => {
      paginationReactive.pageSize = pageSize;
      paginationReactive.page = 1;
      fetchData();
    },
  });

  // 表格选择框
  const selectedRows = ref<Record<string, any>[]>([]);
  const checkedRowKeys = ref<(string | number)[]>([]);
  const handleCheckedRowKeysChange = (keys: (string | number)[]) => {
    const newSelectedRows = keys
      .map((key) => {
        const existingRow = selectedRows.value.find((row) => row.id === key);
        if (existingRow) {
          return existingRow;
        }
        return tableData.value.find((row) => row.id === key);
      })
      .filter(Boolean) as ListData[];

    selectedRows.value = newSelectedRows;
    checkedRowKeys.value = keys;
  };

  // 关联DTS单模态框
  const showRelateModal = ref(false);

  // 关联DTS单
  const relateDTS = ref('');

  const isExpanded = ref(false);
  const visibleSearchFormItems = computed(() => {
    return isExpanded.value ? searchFormItems.value : searchFormItems.value.slice(0, 4);
  });

  const handleSearch = () => {
    paginationReactive.page = 1;
    fetchData();
  };

  const resetForm = () => {
    Object.keys(searchForm).forEach((key) => {
      searchForm[key] = '';
    });
    fetchData();
  };

  const toggleExpandForm = () => {
    isExpanded.value = !isExpanded.value;
  };
  // 修改 formatDate 函数
  const formatDate = (date: number | Date | null): string => {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };
  const fetchData = async () => {
    loading.value = true;

    try {
      const queryParams = {
        ...filterObjectValues(searchForm),
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
      };
      if (searchForm.createTime) {
        const [start, end] = searchForm.createTime;
        queryParams.startTime = formatDate(start);
        queryParams.endTime = formatDate(end);
      }
      if (searchForm.clusterTime) {
        const [start, end] = searchForm.clusterTime;
        queryParams.startClusterTime = formatDate(start);
        queryParams.endClusterTime = formatDate(end);
      }
      const { total, records } = await getAgFeedback(queryParams);
      tableData.value = records;
      paginationReactive.itemCount = total;
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loading.value = false;
    }
  };

  const handleFiltersChange = (filters: any) => {
    // 合并筛选条件到搜索表单
    Object.assign(searchForm, filters);
    // 重置到第一页
    paginationReactive.page = 1;
    // 重新获取数据
    fetchData();
  };

  const columns = computed(() => createColumns(handleAppNameClick));
  const handleAppNameClick = (appName: string) => {
    // 更新搜索表单中的 appName 字段
    searchForm.appName = appName;
    // 触发搜索
    handleSearch();
  };

  // 点击关联DTS单
  const onRelateDTS = () => {
    if (selectedRows.value.length === 0) {
      return;
    }
    showRelateModal.value = true;
  };

  // 关联DTS单保存
  const onRelateClick = async () => {
    try {
      const data: Record<string, any> = [];
      selectedRows.value.forEach((item) => {
        item.dtsUrl = relateDTS.value;
        data.push(item);
      });
      await batchSave(data);
      message.success('关联成功');
      showRelateModal.value = false;
      fetchData();
      // 重置选择状态
      checkedRowKeys.value = [];
      selectedRows.value = [];
    } catch (error) {
      message.error('关联失败');
      console.log(error);
    }
  };

  onMounted(() => {
    fetchData();
  });
</script>

<style scoped>
  /* 修改表单项样式 */
  :deep(.n-form-item) {
    display: flex;
    margin-right: 0;
    margin-bottom: 18px;
  }

  :deep(.n-form-item-label) {
    width: 90px !important;
    text-align: right;
  }

  :deep(.n-form-item-blank) {
    flex: 1;
  }

  /* 统一输入框和选择框的宽度和对齐方式 */
  :deep(.n-input),
  :deep(.n-select) {
    /* width: 300px !important; */
  }

  /* 确保输入内容左对齐 */
  :deep(.n-input__input-el),
  :deep(.n-select-option__content) {
    text-align: left !important;
  }

  /* 确保选择框的内容左对齐 */
  :deep(.n-base-selection-label) {
    text-align: left !important;
  }

  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 12px;
  }

  .setting-icon {
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
    color: #666;
  }

  .setting-icon:hover {
    background-color: #f5f5f5;
    color: #2080f0;
    transform: rotate(30deg);
  }

  .bold-font {
    font-weight: 700;
  }

  .gradient-text {
    font-size: 14px;
    background: -webkit-linear-gradient(90deg, red 0%, green 50%, blue 100%); /* Chrome, Safari */
    background: linear-gradient(90deg, red 0%, green 50%, blue 100%); /* 标准语法 */
    -webkit-background-clip: text; /* Chrome, Safari */
    background-clip: text;
    -webkit-text-fill-color: transparent; /* Chrome, Safari */
    color: transparent;
  }

  .statistics-container {
    margin: 24px 0;
  }

  .stat-card {
    background-color: #fff;
    transition: all 0.3s;
    height: 100%;
  }

  .stat-card:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .stat-content {
    text-align: center;
  }

  .stat-label {
    color: #666;
    font-size: 14px;
    margin-bottom: 8px;
  }

  .stat-value {
    color: #2080f0;
    font-size: 24px;
    font-weight: bold;
  }
</style>
