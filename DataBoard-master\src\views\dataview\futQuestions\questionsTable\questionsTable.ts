import { h } from 'vue';
import { NTag, NButton, NSpace, NBadge, NIcon } from 'naive-ui';
import { ewpService as service } from '@/utils/axios';
import { Link, Attach } from '@vicons/ionicons5';
import { UserInfoType, useUserStore } from '@/store/modules/user';
import { formatDateTime } from '@/utils';
import { getStaffList } from '@/views/dataview/personManage/staff';
import { getMaxIntNumber } from '@/utils/commonUtils';
import {
  FUT_ADMIN_TAG,
  FUT_HANDLE_TAG,
  TEST_EMPLOYEE_TAG,
} from '@/views/dataview/personManage/tagContant';
import {
  getPersonNamesFromTag,
  getPersonOptionsOnlyName,
} from '@/views/dataview/personManage/staffCommonUtils';

const urlPrefixMap = {
  fut: 'https://fut.rnd.huawei.com/futinternal/#!services/jalor/workflow/instance/',
  BETA: 'https://betaclub.huawei.com/beta/products/#/ProblemDetail?quesId=',
};

const userStore = useUserStore();
const userInfo: UserInfoType = userStore.getUserInfo || {};

//测试人员options
const testAccountsOptions = await getPersonOptionsOnlyName(TEST_EMPLOYEE_TAG);

const futAccounts = await getPersonNamesFromTag(FUT_HANDLE_TAG);

const testerAccounts = await getPersonNamesFromTag(TEST_EMPLOYEE_TAG);

const futAdminAccounts = await getPersonNamesFromTag(FUT_ADMIN_TAG);
// 定义查询表单项
export const searchFormItems = [
  {
    field: 'orderId',
    label: '单号',
    component: 'Input',
  },
  {
    field: 'submitTime',
    label: '用户提单时间',
    component: 'Date',
  },
  {
    field: 'clusterTime',
    label: '聚类时间',
    component: 'Date',
  },
  {
    field: 'field',
    label: '领域',
    component: 'Select',
    componentProps: {
      options: [
        { label: '4796应用', value: '4796,垂域专精477' },
        { label: '游戏', value: '游戏' },
        { label: '其他', value: '其他' },
        { label: '未命中-225应用', value: '225' },
        { label: '未命中-一方', value: '一方' },
        { label: '未命中', value: '未命中' },
      ],
    },
  },
  {
    field: 'appName',
    label: '应用',
    component: 'Input',
  },
  {
    field: 'sceneName',
    label: '场景名称',
    component: 'Input',
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    multiple: true,
    componentProps: {
      options: [
        { label: '待处理', value: 'INIT' },
        { label: '待提单', value: 'Analyzed(dts)' },
        { label: '已挂起', value: 'Analyzed(pending)' },
        { label: '问题定界', value: 'OPEN' },
        { label: '待锁定', value: 'UNLOCKED' },
        { label: '待修复', value: 'TRACKING' },
        { label: '待回归', value: 'REGRESSION' },
        { label: '观察声量', value: 'ObserveVolume' },
        { label: '已闭环', value: 'CLOSED' },
      ],
    },
  },
  {
    field: 'dtsUrl',
    label: 'DTS单',
    component: 'Input',
  },
  {
    field: 'problemDescription',
    label: '问题描述',
    component: 'Input',
  },
  {
    field: 'testOwner',
    label: '测试责任人',
    component: 'Select',
    componentProps: {
      filterable: true,
      options: testAccountsOptions,
    },
  },
  {
    field: 'pendingReason',
    label: '备注',
    component: 'Input',
  },
  {
    field: 'topName',
    label: 'TOP排序',
    component: 'Select',
    componentProps: {
      options: [
        { label: '按TOP分类排序', value: 'topKindName' },
        { label: '按TOP场景排序', value: 'topSceneName' },
        { label: '按TOP应用排序', value: 'topAppName' },
      ],
    },
  },
  {
    field: 'clustersRules',
    label: '聚类规则',
    component: 'Select',
    componentProps: {
      options: [
        { label: '单日批量', value: 'day' },
        { label: '单周批量', value: 'week' },
        { label: '单月批量', value: 'month' },
      ],
    },
  },
  {
    field: 'autoLink',
    label: '自动关联DTS',
    component: 'Select',
    componentProps: {
      options: [
        { label: '未确认', value: 'Y' },
        { label: '已确认', value: 'C' },
      ],
    },
    multiple: true,
  },
  {
    field: 'kind',
    label: '分类',
    component: 'Input',
  },

  {
    field: 'top',
    label: 'TOP2000',
    component: 'Select',
    componentProps: {
      options: [{ label: 'TOP2000', value: 'TOP2000' }],
    },
  },
  {
    field: 'source',
    label: '来源',
    component: 'Select',
    componentProps: {
      options: [
        { label: 'FUT', value: 'FUT' },
        { label: 'AG', value: 'AG' },
        { label: 'NSS', value: 'NSS' },
        { label: 'BETA', value: 'BETA' },
      ],
    },
    multiple: true,
  },
  {
    field: 'createTime',
    label: '创建时间',
    component: 'Date',
  },
  {
    field: 'checkBox',
    component: 'Checkbox',
    children: [
      {
        field: 'parentOnly',
        label: '只看父单',
        component: 'CheckBox',
      },
      {
        field: 'experienceFracture',
        label: '体验断裂',
        component: 'CheckBox',
      },
      {
        field: 'isIncorrectAnalysis',
        label: '关联DTS错误',
        component: 'CheckBox',
      },
    ],
  },
];


// 1.从缓存读取列配置
// 2.测试、FUT人员特有列
export const getColumns = (
  handleQuestion,
  handleEdit,
  handleDelete,
  handleKindClick,
  handleQuickAssociate
) => {
  const allColumns = [
    {
      title: '来源',
      key: 'source',
      width: 60,
    },
    {
      title: '单号',
      key: 'orderId',
      className: 'cell-link',
      width: 120,
      render: (rowData) => {
        const canJump = !!urlPrefixMap[rowData.source];
        const type = canJump ? 'info' : 'default';
        const attrObject: Record<string, any> = {
          type: type,
          bordered: false,
        };
        if (canJump) {
          attrObject.onClick = () => {
            if (!canJump) {
              return;
            }
            window.open(
              'javascript:window.name',
              `<script>location.replace('${urlPrefixMap[rowData.source]}${rowData.orderId}')</script>`
            );
          };
        }
        return rowData.parentOrderId === '0'
          ? // 父单
          h(
            NBadge,
            {
              type: 'info',
            },
            {
              default: () =>
                h(
                  // 可以点击跳转的FUT单号
                  NTag,
                  attrObject,
                  {
                    default: () => rowData.orderId,
                  }
                ),
              value: () =>
                h(
                  // 父单的标记
                  NIcon,
                  {
                    component: Link,
                    size: '16',
                    onClick: () => {
                      const dom = document.createElement('input');
                      dom.value = rowData.orderId;
                      document.body.appendChild(dom);
                      dom.select();
                      document.execCommand('copy');
                      document.body.removeChild(dom);
                    },
                  }
                ),
            }
          )
          : // 子单及未关联单
          h(NTag, attrObject, {
            default: () => rowData.orderId,
          });
      },
    },
    {
      title: '用户提单时间',
      key: 'submitTime',
      width: 110,
      render: (rowData) => String(rowData.submitTime).split('T')[0].replaceAll('-', '/'),
    },
    {
      title: '聚类时间',
      key: 'clusterTime',
      width: 110,
      render: (rowData) => {
        if (rowData.clusterTime) {
          return String(rowData.clusterTime).split('T')[0].replaceAll('-', '/');
        }
        return '';
      },
    },
    {
      title: '创建时间',
      key: 'createTime',
      width: 110,
      render: (rowData) => {
        if (rowData.createTime) {
          return String(rowData.createTime).split('T')[0].replaceAll('-', '/');
        }
        return '';
      },
    },
    {
      title: '领域',
      key: 'field',
      width: 90,
    },
    {
      title: '应用',
      key: 'appName',
      width: 100,
      render: (row) => {
        return h(
          NTag,
          {
            bordered: false,
            style: {
              cursor: 'pointer',
            },
          },
          [
            // 问题单号标签
            h(
              NTag,
              {
                bordered: false,
              },
              {
                default: () => row.appName,
              }
            ),
            // TOP标记
            row.top
              ? h(
                NTag,
                {
                  type: 'warning',
                  size: 'tiny',
                  bordered: false,
                  style: {
                    position: 'absolute',
                    right: '-8px',
                    top: '-8px',
                    padding: '0 4px',
                    transform: 'scale(0.8)',
                    fontSize: '12px',
                    lineHeight: '16px',
                    minWidth: '16px',
                    textAlign: 'center',
                    zIndex: 1,
                    color: 'red',
                  },
                },
                { default: () => row.top.toUpperCase() }
              )
              : null,
          ]
        );
      },
    },
    {
      title: '场景名称',
      key: 'sceneName',
      width: 130,
    },
    {
      title: '分类',
      key: 'kind',
      width: 120,
      sorter: true,
    },
    {
      title: '问题描述',
      key: 'problemDescription',
      width: 350,
    },
    {
      title: 'DTS描述',
      key: 'dtsDescription',
      width: 350,
    },
    {
      title: '问题级别',
      key: 'level',
      width: 90,
    },
    {
      title: '舆情数量',
      key: 'publicOpinionNum',
      width: 90,
    },
    {
      title: '测试处理时间',
      key: 'testProcessTime',
      width: 110,
      render: (rowData) => formatDateTime(rowData.testProcessTime),
    },
    {
      title: '关联DTS时间',
      key: 'associateDtsTime',
      width: 110,
      render: (rowData) => formatDateTime(rowData.associateDtsTime),
    },
    {
      title: '进展&措施',
      key: 'progressAndMeasure',
      width: 300,
    },
    {
      title: '产品',
      key: 'model',
      width: 120,
    },
    {
      title: '状态',
      key: 'status',
      width: 120,
      render: (rowData) => {
        return {
          INIT: '待处理',
          'Analyzed(dts)': '待提单',
          'Analyzed(pending)': '已挂起',
          OPEN: '问题定界',
          UNLOCKED: '待锁定',
          TRACKING: '待修复',
          REGRESSION: '待回归',
          ObserveVolume: '观察声量',
          CLOSED: '已闭环',
        }[rowData.status];
      },
    },
    {
      title: 'DTS单状态',
      key: 'dtsStatus',
      width: 170,
    },
    {
      title: '责任人',
      key: 'responsiblePerson',
      width: 100,
    },
    {
      title: '领域责任人',
      key: 'areaResponsiblePerson',
      width: 100,
    },
    {
      title: '计划上线时间',
      key: 'plannedLaunchTime',
      width: 110,
    },
    {
      title: '备注',
      key: 'pendingReason',
      width: 110,
    },
    {
      title: '相似DTS单',
      key: 'autoLinkDtsUrl',
      width: 90,
      render: (row) => {
        const { autoLinkDtsUrl } = row;
        const dtsReg = /(http.+)?(DTS\d{8})/;
        const matched = dtsReg.exec(autoLinkDtsUrl);
        // 0：非DTS单 1：DTSXXXXXXXX 2：完整DTS链接
        const type = matched === null ? 0 : matched[1] ? 2 : 1;
        return type === 0
          ? autoLinkDtsUrl
          : h(
            NTag,
            {
              type: 'info',
              bordered: false,
              onClick: async () => {
                const dtsUrlPrefix = 'https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/';
                window.open(
                  type === 1 ? `${dtsUrlPrefix}${autoLinkDtsUrl}` : autoLinkDtsUrl,
                  '_blank'
                );
              },
            },
            {
              default: () => (type === 1 ? autoLinkDtsUrl : autoLinkDtsUrl.split('/').pop()),
            }
          );
      },
    },
    {
      title: '系统版本',
      key: 'systemVersion',
      width: 320,
    },
    {
      title: '应用版本',
      key: 'dboxAppVersion',
      width: 80,
    },
    {
      title: '测试责任人',
      key: 'testOwner',
      width: 70,
    },
    {
      title: '问题类型',
      key: 'questionType',
      width: 90,
    },
    {
      title: '单日声量',
      key: 'dayVocNum',
      width: 90,
    },
    {
      title: '单周声量',
      key: 'weekVocNum',
      width: 90,
    },
    {
      title: '单月声量',
      key: 'monthVocNum',
      width: 90,
    },
    {
      title: '自动关联DTS',
      key: 'autoLink',
      width: 120,
      render: (rowData) => {
        return (
          {
            Y: '未确认',
            C: '已确认',
          }[rowData.autoLink] || '否'
        );
      },
    },
  ];

  // DTS 单列
  const reasonIndex = allColumns.findIndex((item) => item.key === 'pendingReason');
  const dtsColumn = {
    title: 'DTS单',
    key: 'dtsUrl',
    width: 90,
    render: (row) => {
      const { dtsUrl, autoLinkDtsUrl } = row;
      // DTS 单为空，自动关联 DTS 单不为空，则提供一键关联功能
      if (!dtsUrl && autoLinkDtsUrl) {
        return h(
          NTag,
          {
            type: 'info',
            style: { cursor: 'pointer' },
            onClick: () => {
              handleQuickAssociate(row);
            },
          },
          {
            default: () => '一键关联',
          }
        );
      }
      const dtsReg = /(http.+)?(DTS\d{8})/;
      const matched = dtsReg.exec(dtsUrl);
      // 0：非DTS单 1：DTSXXXXXXXX 2：完整DTS链接
      const type = matched === null ? 0 : matched[1] ? 2 : 1;
      return type === 0
        ? dtsUrl
        : h(
            NTag,
            {
              type: 'info',
              bordered: false,
              onClick: async () => {
                const dtsUrlPrefix = 'https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/';
                window.open(type === 1 ? `${dtsUrlPrefix}${dtsUrl}` : dtsUrl, '_blank');
              },
            },
            {
              default: () => (type === 1 ? dtsUrl : dtsUrl.split('/').pop()),
            }
          );
    },
  };
  allColumns.splice(reasonIndex + 1, 0, dtsColumn);

  // 操作列
  if (
    !allColumns.some((item) => item.key === 'action') &&
    futAccounts.includes(userInfo.userName)
  ) {
    allColumns.push({
      title: '操作',
      key: 'actions',
      fixed: 'right',
      width: 140,
      render: (row) => {
        return h(
          'div',
          { style: { display: 'flex', gap: '8px' } },
          {
            default: () => {
              const buttonList: any[] = [];
              //测试处理时间为空，显示处理按钮
              if (row.testProcessTime) {
                buttonList.push(
                  h(
                    NButton,
                    {
                      size: 'small',
                      type: 'primary',
                      onClick: () => handleEdit?.(row),
                    },
                    { default: () => '编辑' }
                  )
                );
              } else {
                buttonList.push(
                  h(
                    NButton,
                    {
                      size: 'small',
                      onClick: () => handleQuestion?.(row),
                    },
                    { default: () => '处理' }
                  )
                );
              }
              buttonList.push(
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error',
                    onClick: () => handleDelete?.(row),
                  },
                  { default: () => '删除' }
                )
              );
              return buttonList;
            },
          }
        );
      },
    });
  }
  // 勾选框
  if (
    !allColumns.some((item) => item.type === 'selection') &&
    testerAccounts.includes(userInfo.userName)
  ) {
    allColumns.unshift({
      type: 'selection',
    });
  }
  // 分类
  const kindColumn = allColumns.find((item) => item.key === 'kind');
  if (kindColumn) {
    kindColumn.render = (row) => {
      return h(
        'span',
        {
          style: {
            color: '#2080f0',
            cursor: 'pointer',
          },
          onClick: () => {
            handleKindClick(row.kind);
          },
        },
        {
          default: () => row.kind,
        }
      );
    };
  }

  let columnsSetting = localStorage.getItem('__4796_admin_fut_columns_setting');
  columnsSetting = columnsSetting ? JSON.parse(columnsSetting) : {};
  allColumns.forEach((item) => {
    item.show = columnsSetting[item.key]?.show === 'false' ? false : true;
  });
  return allColumns;
};

export const fetchData = async (
  params: Record<string, any> = {
    pageNo: 1,
    pageSize: 20,
  }
): Promise<{ records: Record<string, any>[]; total: number; size: number }> => {
  return service.post('/management/futOrder/query', params);
};

export const updateData = async (params) => {
  return service.post('/management/futOrder/save', params);
};

export const deleteData = async (orderId) => {
  return service.post('/management/futOrder/delete', { orderId });
};

export const isTester = testerAccounts.includes(userInfo.userName);

export const isFUTAdmin = futAdminAccounts.includes(userInfo.userName);

// 关联DTS单
export const relateParentOrder = async (checkedRows, parentData) => {
  checkedRows.forEach((item) => {
    item.parentOrderId = parentData.orderId;
  });
  parentData.parentOrderId = 0;
  const data = checkedRows.concat(parentData);
  await batchSave(data);
};

export const batchSave = async (data) => {
  return service.post('/management/futOrder/batchSave', data);
};

export const questionTypeOptions = [
  { label: '应用需求', value: '应用需求' },
  { label: '功能需求', value: '功能需求' },
  { label: '数据丢失', value: '数据丢失' },
];

export const exportData = (
  config: { url: string; fileName: string },
  params: Record<string, any> = {}
) => {
  return new Promise((resolve, reject) => {
    const req = new XMLHttpRequest();
    req.open('POST', config.url, true);
    req.responseType = 'blob';
    req.setRequestHeader('Content-Type', 'application/json');
    req.onload = function () {
      const data = req.response;
      const blob = new Blob([data]);
      const blobUrl = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.download = config.fileName;
      a.href = blobUrl;
      a.click();
      resolve(true);
    };
    req.send(JSON.stringify(params));
  });
};

// 更新FUT
export const updateFUT = async (updateDate) => {
  return service.post('/management/futOrder/downloadData', { maxSubmitTime: updateDate });
};

// 自动绑单上传
export const autoBindDTSUpload = async (params) => {
  return service.post('/management/futOrder/uploadFutAnalyzeData', params);
};

// 自动绑单下载
export const autoBindDTSDownload = async (params) => {
  return service.post('/management/futOrder/downloadFutAnalyzedData', params);
};
