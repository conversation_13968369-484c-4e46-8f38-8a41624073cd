<template>
  <div class="process-container">
    <!-- 左侧问题详情面板 -->
    <n-layout-sider
      bordered
      collapse-mode="width"
      :collapsed="isPanelCollapsed"
      :collapsed-width="64"
      :width="320"
      show-trigger
      @collapse="isPanelCollapsed = true"
      @expand="isPanelCollapsed = false"
      class="side-panel"
      style="background-color: #fff"
    >
      <n-scrollbar class="full-height">
        <n-card class="side-card">
          <template #header>
            <n-space align="center" justify="space-between">
              <span class="text-16 font-500">问题详情</span>
              <n-button @click="goBack" class="back-button">
                <template #icon>
                  <n-icon><arrow-back /></n-icon>
                </template>
                返回
              </n-button>
            </n-space>
          </template>

          <n-scrollbar>
            <div class="detail-list">
              <n-descriptions bordered :column="1" size="small">
                <n-descriptions-item label="问题单号">
                  <n-button text tag="a" @click="toDts" target="_blank" type="primary">
                    {{ problemInfo.orderId }}
                  </n-button>
                  <n-button
                    size="small"
                    quaternary
                    style="margin-left: 10px"
                    @click="copyLink(problemInfo.orderId)"
                  >
                    <template #icon>
                      <n-icon><copy-outline /></n-icon>
                    </template>
                    复制单号
                  </n-button>
                </n-descriptions-item>
                <n-descriptions-item label="严重程度">
                  <n-tag :type="getSeverityType(problemInfo.level)">
                    {{ problemInfo.level }}
                  </n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="EWP状态">
                  <n-tag type="info">
                    {{ curState }}
                  </n-tag>
                </n-descriptions-item>
                <n-descriptions-item label="L1处理人" v-if="problemInfo.preHandler">
                  {{ problemInfo.preHandler }}
                </n-descriptions-item>
                <n-descriptions-item label="当前级别">
                  {{ problemInfo.currentLevel }}
                </n-descriptions-item>
                <n-descriptions-item label="EWP责任人">
                  {{ problemInfo.ewpOwner }}
                </n-descriptions-item>
                <n-descriptions-item label="当前处理人">
                  {{ problemInfo.currentHandler }}
                </n-descriptions-item>
                <n-descriptions-item label="问题单创建人">
                  {{ problemInfo.creator }}
                </n-descriptions-item>
                <n-descriptions-item label="创建时间">
                  {{ formatDate(problemInfo.createTime) }}
                </n-descriptions-item>
                <n-descriptions-item label="最后处理时间">
                  {{ formatDate(problemInfo.statusChangeTime) }}
                </n-descriptions-item>
                <n-descriptions-item label="当前状态">
                  <n-tag :type="getStatusType(problemInfo.status)">
                    {{ problemInfo.status }}
                  </n-tag>
                </n-descriptions-item>

                <n-descriptions-item label="betaclub链接">
                  <n-button text tag="a" @click="toBeta" target="_blank" type="primary">
                    {{ problemInfo.betaOrder }}
                  </n-button>
                </n-descriptions-item>
              </n-descriptions>

              <!-- 添加快捷操作按钮组 -->
              <div class="quick-actions">
                <n-space vertical>
                  <n-divider class="quick-divider" />

                  <div class="related-actions">
                    <n-space vertical size="small">
                      <span class="section-title">快捷操作</span>
                      <n-space wrap :size="12">
                        <n-tag
                          v-for="action in quickActions"
                          :key="action.key"
                          :type="action.type"
                          :bordered="false"
                          clickable
                          @click="handleQuickAction(action)"
                          class="action-tag"
                        >
                          {{ action.label }}
                        </n-tag>
                      </n-space>
                    </n-space>
                  </div>
                </n-space>
              </div>
            </div>
          </n-scrollbar>
        </n-card>
      </n-scrollbar>
    </n-layout-sider>

    <!-- 主内容区域 -->
    <n-layout class="main-content">
      <n-layout-header>
        <!-- 步骤条卡片 -->
        <n-card class="step-card" :class="{ closed: isClosed }">
          <n-alert type="info" :show-icon="false" class="description-alert">
            问题描述：{{ problemInfo.description }}
          </n-alert>
          <n-steps :current="current" size="large">
            <n-step
              v-for="(item, index) of getProcessList()"
              :key="index"
              :title="item.title"
              :status="getStepStatus(index)"
            >
              <template #icon>
                <div class="step-icon">{{ index + 1 }}</div>
              </template>
            </n-step>
          </n-steps>
        </n-card>
      </n-layout-header>

      <!-- 内容区域 -->
      <n-scrollbar class="content-scrollbar" style="margin-top: 10px">
        <n-card class="content-card">
          <div class="step-content">
            <!-- 定界表单 -->
            <template v-if="current === 1">
              <n-grid :cols="24" :x-gap="24">
                <n-gi :span="16">
                  <n-card title="问题定界" class="form-card">
                    <n-form
                      ref="delimitFormRef"
                      :model="model.delimit"
                      :rules="rules.delimit"
                      label-placement="top"
                      label-width="120"
                      require-mark-placement="right-hanging"
                    >
                      <n-grid :cols="2" :x-gap="24">
                        <n-form-item-gi label="故障分类" path="module">
                          <n-select
                            v-model:value="model.delimit.module"
                            :options="moduleOptions"
                            clearable
                            placeholder="选择故障分类"
                          />
                        </n-form-item-gi>
                        <n-form-item-gi label="问题归属" path="attribution">
                          <n-select
                            v-model:value="model.delimit.attribution"
                            :options="attributionOptions"
                          />
                        </n-form-item-gi>
                        <!-- 新增转单选项 -->
                        <n-form-item-gi label="转单处理人" path="transferTo">
                          <n-select
                            v-model:value="model.delimit.transferTo"
                            :options="
                              model.delimit.module
                                ? l2Options[model.delimit.module]?.filter(
                                    (staff) => staff.level === 'L2'
                                  ) || []
                                : allL1Staff
                            "
                            :placeholder="
                              model.delimit.module ? '选择该模块的L2处理人' : '选择处理人'
                            "
                            clearable
                            filterable
                          />
                          <n-button
                            style="margin-left: 10px"
                            :type="'info'"
                            @click="handleTransfer"
                            :disabled="!model.delimit.transferTo"
                          >
                            {{ '转单' }}
                          </n-button>
                        </n-form-item-gi>
                        <n-form-item-gi
                          v-if="problemInfo.top !== 'TOP38'"
                          label="知识id"
                          path="knowId"
                        >
                          <n-input v-model:value="model.delimit.knowId" />
                        </n-form-item-gi>
                        <n-form-item-gi label="是否重复问题" path="isDuplicated">
                          <n-radio-group v-model:value="model.delimit.isDuplicated">
                            <n-space>
                              <n-radio :value="true">是</n-radio>
                              <n-radio :value="false">否</n-radio>
                            </n-space>
                          </n-radio-group>
                        </n-form-item-gi>
                      </n-grid>

                      <n-form-item-gi
                        v-if="model.delimit.isDuplicated"
                        label="重复问题单号"
                        path="duplicateProblemID"
                        width="100px"
                      >
                        <n-input v-model:value="model.delimit.duplicateProblemID" />
                      </n-form-item-gi>

                      <n-form-item label="原因分析" path="reason">
                        <n-card size="small" class="editor-card">
                          <div class="editor-container">
                            <Toolbar
                              class="editor-toolbar"
                              :editor="editorRef"
                              :defaultConfig="toolbarConfig"
                              mode="default"
                              style="border-bottom: 1px solid #ccc"
                            />
                            <Editor
                              class="editor-content"
                              v-model="valueHtml"
                              :defaultConfig="editorConfig"
                              mode="default"
                              @on-created="handleCreated"
                              style="min-height: 300px"
                            />
                          </div>
                        </n-card>
                      </n-form-item>

                      <!-- 添加操作按钮到表单内部 -->
                      <div
                        class="action-buttons"
                        style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #f0f0f0"
                      >
                        <n-space justify="center">
                          <n-button
                            v-if="current === 1 && (curState === '待锁定' || curState === '已锁定')"
                            type="warning"
                            @click="returnToLockPlan"
                          >
                            返回锁定
                          </n-button>
                          <n-button
                            v-if="curState === '待定界'"
                            type="success"
                            @click="handleSaveDelimit"
                          >
                            保存
                          </n-button>
                          <n-button type="primary" @click="handleDelimit"> 提交定界 </n-button>
                        </n-space>
                      </div>
                    </n-form>
                  </n-card>
                </n-gi>

                <n-gi :span="8">
                  <n-space vertical :size="16">
                    <!-- 知识推荐卡片 -->
                    <n-card title="知识推荐" size="small" class="know-card">
                      <!-- 添加搜索区域 -->
                      <div class="search-area">
                        <!-- 添加问题描述选词区域 -->
                        <div class="description-select">
                          <div class="label">从问题描述中选择关键词：</div>
                          <div class="text-wrapper">
                            <template v-for="(word, index) in splitDescription" :key="index">
                              <n-button
                                :class="['word-btn', { selected: index === curIndex }]"
                                text
                                @click="toggleWord(word, index)"
                              >
                                {{ word }}
                              </n-button>
                            </template>
                          </div>
                        </div>

                        <!-- 原有的搜索框部分 -->
                        <n-input-group>
                          <n-input
                            v-model:value="searchKeyword"
                            placeholder="输入关键词搜索"
                            @keydown.enter="handleSearch"
                          >
                            <template #suffix>
                              <n-button quaternary circle type="primary" @click="handleSearch">
                                <template #icon>
                                  <n-icon><search /></n-icon>
                                </template>
                              </n-button>
                            </template>
                          </n-input>
                        </n-input-group>
                      </div>
                      <n-scrollbar style="max-height: 575px; overflow: auto">
                        <n-list hoverable clickable>
                          <n-list-item v-for="item in knowledgeList" :key="item.id">
                            <n-thing
                              :title="item.desc"
                              :description="
                                item.expanded ? '' : item.content?.substring(0, 200) + '...'
                              "
                              style="overflow: hidden; margin-bottom: 10px"
                            >
                              <template #default v-if="item.expanded">
                                <n-card size="small" class="knowledge-content-card">
                                  <n-scrollbar style="max-height: 400px">
                                    <div
                                      class="markdown-content"
                                      v-html="renderMarkdown(item.content)"
                                    ></div>
                                  </n-scrollbar>
                                </n-card>
                              </template>

                              <template #footer>
                                <n-space justify="space-between">
                                  <n-space>
                                    <n-tag size="small" type="success" @click="copyLink(item.id)"
                                      >知识ID: {{ item.id }}</n-tag
                                    >
                                  </n-space>
                                  <n-space>
                                    <n-button
                                      text
                                      type="primary"
                                      @click.stop="toggleKnowledgeExpand(item)"
                                    >
                                      {{ item.expanded ? '收起' : '展开' }}
                                    </n-button>
                                    <n-button
                                      text
                                      type="info"
                                      @click.stop="handleKnowledgeClick(item)"
                                    >
                                      查看详情
                                    </n-button>
                                  </n-space>
                                </n-space>
                              </template>
                            </n-thing>
                          </n-list-item>
                        </n-list>
                      </n-scrollbar>
                    </n-card>
                  </n-space>
                </n-gi>
              </n-grid>
            </template>

            <!-- 锁定计划表单 -->
            <template v-if="current === 2">
              <!-- 修改为可折叠的定界信息汇总卡片 -->
              <n-collapse
                :default-expanded-names="['delimit-summary']"
                v-model:expanded-names="expandedNames"
                class="delimit-collapse"
              >
                <n-collapse-item title="定界信息汇总" name="delimit-summary">
                  <template #header-extra>
                    <n-space size="small">
                      <n-tag size="small" type="info">{{ model.delimit.module }}</n-tag>
                      <n-tag size="small" type="primary">点击展开查看</n-tag>
                    </n-space>
                  </template>
                  <n-descriptions bordered :column="2" size="small">
                    <n-descriptions-item label="故障分类">
                      <n-tag type="info">{{ model.delimit.module }}</n-tag>
                    </n-descriptions-item>
                    <n-descriptions-item label="问题归属">
                      <n-tag type="info">{{ model.delimit.attribution }}</n-tag>
                    </n-descriptions-item>
                    <n-descriptions-item label="知识ID">
                      <span @click="toKnowledgeClick(model.delimit.knowId)">{{
                        model.delimit.knowId || '无'
                      }}</span>
                    </n-descriptions-item>
                    <n-descriptions-item label="是否重复问题">
                      <n-tag :type="model.delimit.isDuplicated ? 'warning' : 'success'">
                        {{ model.delimit.isDuplicated ? '是' : '否' }}
                      </n-tag>
                    </n-descriptions-item>
                    <n-descriptions-item v-if="model.delimit.isDuplicated" label="重复问题单号">
                      {{ model.delimit.duplicateProblemID }}
                    </n-descriptions-item>
                  </n-descriptions>

                  <n-divider>原因分析</n-divider>
                  <div v-html="model.delimit.reason" class="reason-summary"></div>
                </n-collapse-item>
              </n-collapse>

              <n-card title="锁定计划" class="form-card" style="margin-top: 16px">
                <n-form
                  ref="planFormRef"
                  :model="model.plan"
                  :rules="rules.plan"
                  label-placement="left"
                  label-width="120"
                  require-mark-placement="right-hanging"
                >
                  <n-grid :cols="3" :x-gap="24">
                    <!-- <n-form-item-gi label="下一步处理方式" path="nextMethod">
                      <n-select v-model:value="model.plan.nextMethod" :options="nextOptions" />
                    </n-form-item-gi> -->
                    <n-form-item-gi label="提交审核人" path="next">
                      <n-select v-model:value="model.plan.next" :options="handleOptions" />
                    </n-form-item-gi>
                    <n-form-item-gi label="计划锁定时间" path="time">
                      <n-date-picker v-model:value="model.plan.time" type="date" clearable />
                    </n-form-item-gi>
                  </n-grid>

                  <n-form-item label="解决方案" path="solution">
                    <n-input
                      type="textarea"
                      v-model:value="model.plan.solution"
                      :autosize="{ minRows: 4, maxRows: 8 }"
                    />
                  </n-form-item>

                  <!-- 添加操作按钮到表单内部 -->
                  <div
                    class="action-buttons"
                    style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #f0f0f0"
                  >
                    <n-space justify="center">
                      <n-button secondary @click="prev"> 上一步 </n-button>
                      <n-button type="primary" @click="handleClose"> 提交锁定 </n-button>
                    </n-space>
                  </div>
                </n-form>
              </n-card>
            </template>

            <!-- 审核修改表单 -->
            <template v-if="current === 3">
              <!-- 折叠的定界信息汇总 -->
              <n-collapse
                :default-expanded-names="['delimit-summary']"
                v-model:expanded-names="expandedNames"
                class="delimit-collapse"
              >
                <n-collapse-item title="定界信息汇总" name="delimit-summary">
                  <template #header-extra>
                    <n-space size="small">
                      <n-tag size="small" type="info">{{ model.delimit.module }}</n-tag>
                      <n-tag size="small" type="primary">点击展开查看</n-tag>
                    </n-space>
                  </template>
                  <n-descriptions bordered :column="2" size="small">
                    <n-descriptions-item label="故障分类">
                      <n-tag type="info">{{ model.delimit.module }}</n-tag>
                    </n-descriptions-item>
                    <n-descriptions-item label="问题归属">
                      <n-tag type="info">{{ model.delimit.attribution }}</n-tag>
                    </n-descriptions-item>
                    <n-descriptions-item label="知识ID">
                      <span @click="toKnowledgeClick(model.delimit.knowId)">{{
                        model.delimit.knowId || '无'
                      }}</span>
                    </n-descriptions-item>
                    <n-descriptions-item label="是否重复问题">
                      <n-tag :type="model.delimit.isDuplicated ? 'warning' : 'success'">
                        {{ model.delimit.isDuplicated ? '是' : '否' }}
                      </n-tag>
                    </n-descriptions-item>
                    <n-descriptions-item v-if="model.delimit.isDuplicated" label="重复问题单号">
                      {{ model.delimit.duplicateProblemID }}
                    </n-descriptions-item>
                  </n-descriptions>

                  <n-divider>原因分析</n-divider>
                  <div v-html="model.delimit.reason" class="reason-summary"></div>
                </n-collapse-item>
              </n-collapse>

              <!-- 折叠的锁定计划信息 -->
              <n-collapse
                :default-expanded-names="[]"
                v-model:expanded-names="expandedPlanNames"
                class="delimit-collapse"
                style="margin-top: 16px"
              >
                <n-collapse-item title="锁定计划信息" name="plan-summary">
                  <template #header-extra>
                    <n-space size="small">
                      <n-tag size="small" type="success">锁定计划</n-tag>
                      <n-tag size="small" type="primary">点击展开查看</n-tag>
                    </n-space>
                  </template>
                  <n-descriptions bordered :column="2" size="small">
                    <n-descriptions-item label="提交审核人">
                      {{ model.plan.next }}
                    </n-descriptions-item>
                    <n-descriptions-item label="计划锁定时间">
                      {{ formatDate(model.plan.time) }}
                    </n-descriptions-item>
                  </n-descriptions>

                  <n-divider>解决方案</n-divider>
                  <div class="solution-summary">{{ model.plan.solution }}</div>
                </n-collapse-item>
              </n-collapse>

              <n-card
                title="审核意见"
                class="form-card"
                style="margin-top: 16px"
                v-if="current === 3"
              >
                <n-form
                  ref="reviewFormRef"
                  :model="model.review"
                  :rules="rules.review"
                  label-placement="left"
                  label-width="120"
                  require-mark-placement="right-hanging"
                >
                  <n-form-item label="审核意见" path="reviewResult">
                    <n-input
                      type="textarea"
                      :readonly="curState === '待归档'"
                      v-model:value="model.review.reviewResult"
                      :autosize="{ minRows: 4, maxRows: 8 }"
                      placeholder="请输入审核意见..."
                    />
                  </n-form-item>

                  <n-form-item label="下一步处理人" path="nextHandler">
                    <n-input v-model:value="model.review.nextHandler" />
                  </n-form-item>

                  <!-- 添加操作按钮到表单内部 -->
                  <div
                    class="action-buttons"
                    style="margin-top: 16px; padding-top: 16px; border-top: 1px solid #f0f0f0"
                    v-if="curState === '待审核'"
                  >
                    <n-space justify="center">
                      <n-button type="warning" @click="handleReject"> 审核驳回 </n-button>
                      <n-button type="primary" @click="handleApprove"> 审核通过 </n-button>
                    </n-space>
                  </div>
                </n-form>
              </n-card>
            </template>
          </div>
        </n-card>
      </n-scrollbar>
    </n-layout>

    <!-- 智能问答悬浮按钮 -->
    <div class="ai-assistant-btn hidden" @click="toggleAiAssistant">
      <n-icon class="ai-icon" size="24">
        <chatbubble-ellipses-outline />
      </n-icon>
      <span class="ai-text">智能问答</span>
    </div>

    <!-- 智能问答侧边栏 -->
    <div class="ai-assistant-drawer" :class="{ 'drawer-open': showAiAssistant }">
      <div class="drawer-header">
        <span class="drawer-title"
          >智能助手（如空白，请先<span style="color: blue" @click="toHuaWeiDev">登录</span>）</span
        >
        <n-button circle quaternary @click="toggleAiAssistant">
          <template #icon
            ><n-icon><close /></n-icon
          ></template>
        </n-button>
      </div>
      <div class="drawer-content">
        <iframe
          width="100%"
          height="100%"
          id="customerService"
          src="https://developer.huawei.com/consumer/cn/customerService/#/bot-dev-top/faq-top/faq-talk-top"
        ></iframe>
      </div>
      <!-- 添加侧边关闭区域 -->
      <div class="drawer-close-handle" @click="toggleAiAssistant">
        <n-icon size="20"><chevron-forward /></n-icon>
      </div>
    </div>

    <!-- 遮罩层 -->
    <div class="drawer-mask" :class="{ 'mask-visible': showAiAssistant }"></div>

    <!-- 引导遮罩和提示 -->
    <template v-if="showGuide">
      <div class="guide-mask">
        <div
          class="highlight-area"
          :style="{
            left: `${highlightPosition.x}px`,
            top: `${highlightPosition.y}px`,
            width: `${highlightPosition.width}px`,
            height: `${highlightPosition.height}px`,
          }"
        >
          <div class="guide-tooltip" :style="getTooltipStyle()">
            <p class="guide-title">{{ currentGuideStep.title }}</p>
            <p class="guide-content">{{ currentGuideStep.content }}</p>
            <n-space justify="space-between" style="margin-top: 12px">
              <n-button size="small" type="primary" @click="nextGuideStep">
                {{ isLastStep ? '完成' : '下一步' }}
              </n-button>
              <n-button size="small" @click="skipGuide">跳过</n-button>
            </n-space>
          </div>
        </div>
      </div>
    </template>

    <!-- 添加查看日志弹窗 -->
    <n-modal v-model:show="showLogModal" preset="card" title="操作日志" style="width: 800px">
      <n-timeline>
        <n-timeline-item
          v-for="(item, index) in operationLogs"
          :key="index"
          :type="getTimelineItemType(item)"
          :title="getTimelineTitle(item)"
          :time="getTimelineTime(item)"
          :content="getTimelineContent(item)"
          line-type="dashed"
        >
          <template #icon>
            <n-icon :component="getTimelineIcon(item)" />
          </template>
          <div class="timeline-item-content">
            <n-space vertical size="small">
              <n-space align="center">
                <span class="timeline-label">处理环节:</span>
                <n-tag :type="getProcessTagType(item)">{{ item.dealStep || '未知环节' }}</n-tag>
              </n-space>
              <n-space align="center" v-if="item.retentionTime">
                <span class="timeline-label">滞留时间:</span>
                <span>{{ formatretentionTime(item.retentionTime) }}</span>
              </n-space>
              <n-space align="center" v-if="item.handlerCn">
                <span class="timeline-label">操作人:</span>
                <span>{{ item.handlerCn }}</span>
              </n-space>
              <n-space align="center" v-if="item.dealerCn">
                <span class="timeline-label">下一步处理人:</span>
                <span>{{ item.dealerCn }}</span>
              </n-space>
            </n-space>
          </div>
        </n-timeline-item>
      </n-timeline>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
  import {
    ref,
    computed,
    onMounted,
    watch,
    shallowRef,
    onBeforeUnmount,
    nextTick,
    reactive,
  } from 'vue';
  import { useRouter } from 'vue-router';
  import {
    moduleOptions,
    attributionOptions,
    getProcessConfigList,
    getDefaultInfo,
    analysisTemplates,
  } from './processModel';
  import { ArrowBack, Search } from '@vicons/ionicons5';
  import {
    InformationCircleOutline,
    CopyOutline,
    CheckmarkDoneOutline,
    AlertCircleOutline,
    CloseCircleOutline,
    ChatbubbleEllipsesOutline,
    Close,
    ChevronForward,
    // 添加时间轴图标
    TimeOutline,
    CheckmarkCircleOutline,
    HourglassOutline,
    PersonOutline,
    SwapHorizontalOutline,
  } from '@vicons/ionicons5';
  import { useMessage } from 'naive-ui';
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
  import '@wangeditor/editor/dist/css/style.css';
  import {
    addWishOrder,
    delimit,
    saveDelimit,
    delimitView,
    getDataList,
    lock,
    lockView,
    getProcessLog,
    reviewOrder,
    reviewView,
  } from '@/api/dataview/myOrder';
  import { batchCreateIR, DTSTurnTo } from '@/api/dataview/irManagement';
  import { getWorkOrderList, submitIr } from '@/api/dataview/appState';
  import { marked } from 'marked';
  import { getStaffList } from '@/views/dataview/personManage/staff';

  const message = useMessage();

  const router = useRouter();
  let orderId = router.currentRoute.value.query.orderId as string;
  if (orderId) {
    localStorage.setItem('orderId', orderId);
  } else {
    const storedOrderId = localStorage.getItem('orderId');
    if (storedOrderId) {
      orderId = storedOrderId;
    }
  }
  console.log('orderId:', orderId);
  const info = ref<any>({});

  // 将problemInfo修改为computed属性，以确保在info更新时自动更新
  const problemInfo = computed(() => {
    return {
      title: info.value?.description || '',
      currentLevel: info.value?.currentLevel || '',
      preHandler: info.value?.preHandler || '',
      orderId: info.value?.orderId || orderId || '',
      description: info.value?.description || '',
      level: info.value?.severity || '一般',
      status: info.value?.status || '',
      source: info.value?.source || '',
      sceneName: info.value?.sceneName || '',
      submitTime: info.value?.createTime || '',
      appName: info.value?.appName || '',
      module: info.value?.module || '',
      progress: info.value?.progress || '',
      creator: info.value?.creator || '',
      createTime: info.value?.createTime || '',
      currentHandler: info.value?.currentHandler || '',
      acceptanceOwner: info.value?.acceptanceOwner || '',
      betaOrder: info.value?.betaOrder || '无',
      deviceType: info.value?.deviceType || '未识别',
      systemVersion: info.value?.systemVersion || '未识别',
      opinionVolume: info.value?.opinionVolume || 0,
      top: info.value?.top,
      statusChangeTime: info.value?.statusChangeTime,
      ewpOwner: info.value?.ewpOwner,
    };
  });
  const getProcessList = () => {
    return getProcessConfigList(problemInfo.value.level);
  };
  function goBack() {
    router.push({ name: 'MyTodo' });
  }
  const formatDate = (date: number | Date | null): string => {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };
  const current = ref(1);
  const expendedNames = ref();
  const isClosed = ref(false);
  const showLogModal = ref(false);

  const toHuaWeiDev = () => {
    window.open(
      'https://developer.huawei.com/consumer/cn/customerService/#/bot-dev-top/faq-top/faq-talk-top'
    );
  };
  // 增加计算属性，判断是否应该显示知识ID输入框
  const shouldShowKnowIdField = computed(() => {
    const excludedModules = [
      '应用缺失',
      '功能缺失',
      '其他',
      '非问题-单双一致',
      '非问题-规格设计',
      '非问题-小概率',
      '非问题-用户操作不当',
      '非问题-应用下架且无计划',
    ];
    return !excludedModules.includes(model.value.delimit.module || '');
  });
  const handleOptions = ref<Array<{ label: string; value: string }>>([]);

  const fetchHandleOptions = async () => {
    try {
      const res = await getStaffList({
        pageNo: 1,
        pageSize: 100,
        tagIds: '21',
      });
      console.log('res:', res);
      if (res && res.records) {
        handleOptions.value = res.records.map((item: any) => ({
          label: `${item.name} ${item.id}`,
          value: `${item.pinyin} ${item.id}`,
        }));
        model.value.plan.next = getRandomHandler();
      }
    } catch (error) {
      message.error('获取审核人列表失败');
    }
  };

  onMounted(() => {
    fetchHandleOptions();
    // ... existing code ...
  });

  // 添加随机选择责任人的函数
  const getRandomHandler = () => {
    const availableHandlers = handleOptions.value.filter(
      (handler) => handler.value !== problemInfo.value.currentHandler
    );
    if (availableHandlers.length === 0) {
      return problemInfo.value.currentHandler;
    }
    const randomIndex = Math.floor(Math.random() * availableHandlers.length);
    return availableHandlers[randomIndex].value;
  };
  // 在模型中使用传入的数据
  const model = ref({
    delimit: {
      isDuplicated: false,
      resModule: null,
      knowId: '',
      faultTreeStatus: null as string | null,
      reason: analysisTemplates['常规模板'],
      current: info.value?.currentHandler || '沈力',
      nextMethod: null as string | null,
      next: null as string | null,
      module: null as string | null,
      attribution: '三方应用',
      duplicateProblemID: null as string | null,
      transferTo: null as string | null,
    },
    plan: {
      nextMethod: null as string | null,
      next: '',
      time: null as Date | null,
      solution: null as string | null,
    },
    // 新增审核数据模型
    review: {
      reviewResult: 'pls',
      nextHandler: 'lichengfang 30052277',
      status: false,
    },
  });

  // 添加L1/L2处理人数据
  const l2Options = reactive<
    Record<string, Array<{ label: string; value: string; tags?: string[]; level?: string }>>
  >({});

  // 所有的L1处理人
  const allL1Staff = ref<Array<{ label: string; value: string; tags?: string[]; level?: string }>>(
    []
  );

  // 获取L2处理人数据
  const fetchL2StaffByModule = async (moduleType: string) => {
    try {
      // 调用API获取L2人员列表
      const response = await getStaffList({
        pageNo: 1,
        pageSize: 100,
        tagIds: '16',
      });

      if (response && response.records) {
        const matchingStaff = response.records.filter((staff) => {
          return (
            staff.tags &&
            staff.tags.some(
              (tag) =>
                tag.name.toLowerCase() === moduleType.toLowerCase() ||
                moduleType.toLowerCase().includes(tag.name.toLowerCase()) ||
                tag.name.toLowerCase().includes(moduleType.toLowerCase())
            )
          );
        });

        const getStaffLevel = (staff: any): string => {
          if (!staff.tags) return 'L1';

          const hasL2Tag = staff.tags.some(
            (tag: any) => tag.id === '16' || tag.id === '19' || tag.name.includes('L2')
          );

          return hasL2Tag ? 'L2' : 'L1';
        };

        const staffToUse = matchingStaff.length > 0 ? matchingStaff : response.records;

        const staffOptions = staffToUse
          .map((staff) => {
            const staffLevel = getStaffLevel(staff);
            return {
              label: `${staff.name} ${staff.id} (${staffLevel} - ${moduleType})`,
              value: `${staff.pinyin} ${staff.id}`,
              tags: staff.tags ? staff.tags.map((tag) => tag.name) : [],
              level: staffLevel,
            };
          })
          .filter((staff) => staff.level === 'L2'); // 只保留L2级别的人员

        l2Options[moduleType] = staffOptions;
      }
    } catch (error) {
      console.error('获取L2人员失败:', error);
      message.error('获取L2人员失败');
    }
  };

  const initAllL2Staff = async () => {
    try {
      const response = await getStaffList({
        pageNo: 1,
        pageSize: 100,
        tagIds: '16,19',
      });

      if (response && response.records) {
        const tagToModuleMap: Record<string, string[]> = {
          兼容性: ['兼容性'],
          UX体验: ['UX体验'],
          功能故障: ['功能故障'],
          稳定性: ['稳定性'],
          性能功耗: ['性能功耗'],
          安全隐私: ['安全隐私'],
          功能缺失: ['功能缺失'],
          应用缺失: ['应用缺失'],
        };

        const getStaffLevel = (staff: any): string => {
          if (!staff.tags) return 'L1';

          const hasL2Tag = staff.tags.some(
            (tag: any) => tag.id === '16' || tag.id === '19' || tag.name.includes('L2')
          );

          return hasL2Tag ? 'L2' : 'L1';
        };

        if (moduleOptions.value && moduleOptions.value.length > 0) {
          moduleOptions.value.forEach((item: any) => {
            if (item && typeof item === 'object' && 'value' in item) {
              l2Options[item.value] = [];
            }
          });

          const l1StaffList: Array<{
            label: string;
            value: string;
            tags?: string[];
            level?: string;
          }> = [];

          // 遍历所有人员
          response.records.forEach((staff) => {
            if (!staff.tags) return;

            const staffLevel = getStaffLevel(staff);

            if (staffLevel === 'L1') {
              l1StaffList.push({
                label: `${staff.name} ${staff.id} (${staffLevel})`,
                value: `${staff.pinyin} ${staff.id}`,
                tags: staff.tags.map((t) => t.name),
                level: staffLevel,
              });
            }

            staff.tags.forEach((tag) => {
              // 查找标签对应的模块
              const modules: string[] = [];
              for (const [tagName, moduleNames] of Object.entries(tagToModuleMap)) {
                if (
                  tag.name.toLowerCase().includes(tagName.toLowerCase()) ||
                  tagName.toLowerCase().includes(tag.name.toLowerCase())
                ) {
                  moduleNames.forEach((name) => modules.push(name));
                }
              }

              if (staffLevel === 'L2') {
                modules.forEach((moduleName) => {
                  if (l2Options[moduleName]) {
                    l2Options[moduleName].push({
                      label: `${staff.name} ${staff.id} (${staffLevel} - ${tag.name})`,
                      value: `${staff.pinyin} ${staff.id}`,
                      tags: staff.tags.map((t) => t.name),
                      level: staffLevel, // 添加级别标识
                    });
                  }
                });
              }
            });
          });

          // 去重L1人员列表
          const uniqueL1Staff: Array<{
            label: string;
            value: string;
            tags?: string[];
            level?: string;
          }> = [];
          const seenL1 = new Set();

          l1StaffList.forEach((staff) => {
            if (!seenL1.has(staff.value)) {
              seenL1.add(staff.value);
              uniqueL1Staff.push(staff);
            }
          });

          // 按名称排序
          uniqueL1Staff.sort((a, b) => a.label.localeCompare(b.label));
          allL1Staff.value = uniqueL1Staff;

          // 对每个模块的人员列表进行去重和排序
          moduleOptions.value.forEach((item: any) => {
            if (item && typeof item === 'object' && 'value' in item && l2Options[item.value]) {
              const uniqueStaff: Array<{
                label: string;
                value: string;
                tags?: string[];
                level?: string;
              }> = [];
              const seen = new Set();

              l2Options[item.value].forEach((staff) => {
                if (!seen.has(staff.value)) {
                  seen.add(staff.value);
                  uniqueStaff.push(staff);
                }
              });

              // 按级别排序：L2在前，L1在后
              uniqueStaff.sort((a, b) => {
                if (a.level === b.level) {
                  return a.label.localeCompare(b.label);
                }
                return a.level === 'L2' ? -1 : 1;
              });

              l2Options[item.value] = uniqueStaff;
            }
          });
        }

        return Promise.resolve({ l2Options, allL1Staff: allL1Staff.value });
      }
      return Promise.resolve({});
    } catch (error) {
      console.error('初始化L1/L2人员失败:', error);
      return Promise.reject(error);
    }
  };

  let towishList = async () => {
    let params = {
      dtsOrder: problemInfo.value.orderId,
      description: problemInfo.value.description?.split('】').pop() || '',
      faultType: model.value.delimit.module,
    };
    let res = await addWishOrder(params);
    message.success(res);
    fetchData();
  };

  // 添加表单校验规则
  const rules = {
    delimit: {
      faultTreeStatus: {
        required: true,
        message: '请选择知识匹配结果',
        trigger: ['blur', 'change'],
      },
      current: {
        required: true,
        message: '请输入当前处理人',
        trigger: ['blur', 'input'],
      },
      module: {
        required: true,
        message: '请选择模块',
        trigger: ['blur', 'change'],
      },
      attribution: {
        required: true,
        message: '请选择问题归属',
        trigger: ['blur', 'change'],
      },
      duplicateProblemID: {
        required: (rule: any, value: any) => {
          return model.value.delimit.isDuplicated;
        },
        message: '请输入重复问题单号',
        trigger: ['blur', 'input'],
      },
      reason: {
        required: true,
        message: '请输入原因分析',
        trigger: ['blur', 'input'],
      },
      knowId: {
        required: (rule: any, value: any) => {
          return shouldShowKnowIdField.value;
        },
        message: '请输入知识ID',
        trigger: ['blur', 'input'],
      },
    },
    plan: {
      nextMethod: {
        required: true,
        message: '请选择下一步处理方式',
        trigger: ['blur', 'change'],
      },
      next: {
        required: true,
        message: '请输入下一步处理人',
        trigger: ['blur', 'input'],
      },
      time: {
        required: true,
        message: '请选择计划锁定时间',
        trigger: ['blur', 'change'],
        validator: (rule, val) => {
          if (!val) {
            return new Error('请选择计划锁定时间');
          }
          return true;
        },
      },
      attribution: {
        required: true,
        message: '请选择问题归属',
        trigger: ['blur', 'change'],
      },
      solution: {
        required: true,
        message: '请输入解决方案',
        trigger: ['blur', 'input'],
      },
    },
    // 新增审核表单校验规则
    review: {
      reviewResult: {
        required: true,
        message: '请输入审核意见',
        trigger: ['blur', 'input'],
      },
      nextHandler: {
        required: true,
        message: '请选择下一步处理人',
        trigger: ['blur', 'change'],
      },
    },
    close: {
      next: {
        required: true,
        message: '请输入下一步处理人',
        trigger: ['blur', 'input'],
      },
      method: {
        required: true,
        message: '请输入处理方式',
        trigger: ['blur', 'input'],
      },
      closeType: {
        required: true,
        message: '请选择关闭类型',
        trigger: ['blur', 'change'],
      },
      reportOrRecommendation: {
        required: true,
        message: '请输入测试报告/处理建议',
        trigger: ['blur', 'input'],
      },
    },
  };

  // 修改 handleClose 函数，添加随机分配逻辑
  const handleClose = async () => {
    await validate();

    let params = {
      dtsOrder: problemInfo.value.orderId,
      nextHandler: model.value.plan.next,
      resolveTime: model.value.plan.time,
      solution: model.value.plan.solution,
    };
    let res = await lock(params);
    console.log('res:', res);
    message.success('提交审核成功');
    current.value += 1;
    // isClosed.value = true; // 修改这里，提交审核后不是最终关闭，而是进入审核阶段
    // 更新工单状态
    fetchData();
  };

  // 新增审核驳回处理函数
  const handleReject = async () => {
    try {
      await reviewFormRef.value?.validate?.();

      let params = {
        dtsOrder: problemInfo.value.orderId,
        reviewResult: model.value.review.reviewResult,
        reviewer: problemInfo.value.currentHandler,
        nextHandler: model.value.delimit.current, // 驳回到定位责任人
        status: 0,
      };

      await reviewOrder(params);

      message.success('审核驳回成功，已返回给定位责任人');
      current.value = 1; // 回到定界步骤

      // 更新工单状态
      fetchData();
    } catch (error: any) {
      message.error(error?.message || '审核驳回失败');
    }
  };

  // 新增审核通过处理函数
  const handleApprove = async () => {
    console.log('model :>> ', model);
    try {
      await reviewFormRef.value?.validate?.();
      let params = {
        dtsOrder: problemInfo.value.orderId,
        reviewer: problemInfo.value.currentHandler,
        reviewResult: model.value.review.reviewResult,
        nextHandler: model.value.review.nextHandler,
        status: true ? 1 : 0,
      };
      await reviewOrder(params);

      message.success('审核通过成功，已完成工单流转');
      // 因为移除了问题闭环步骤，所以审核通过就是最终步骤
      isClosed.value = true; // 审核通过后直接关闭

      // 更新工单状态
      fetchData();
    } catch (error: any) {
      message.error(error?.message || '审核通过失败');
    }
  };

  // 添加表单 ref
  const delimitFormRef = ref<any>(null);
  const planFormRef = ref<any>(null);
  const reviewFormRef = ref<any>(null);

  const validate = () => {
    if (isClosed.value) {
      return true;
    }
    return (
      delimitFormRef?.value?.validate?.() ||
      planFormRef?.value?.validate?.() ||
      reviewFormRef?.value?.validate?.()
    );
  };
  const handleDelimit = async () => {
    await validate();
    let params = {
      dtsOrder: problemInfo.value.orderId,
      analysis: model.value.delimit.reason,
      delimiter: model.value.delimit.current,
      faultType: model.value.delimit.module,
      faultTreeStatus: model.value.delimit.faultTreeStatus,
      knowId: model.value.delimit.knowId,
      attribution: model.value.delimit.attribution,
      isRepeat: model.value.delimit.isDuplicated ? 1 : 0,
      repeatOrder: model.value.delimit.duplicateProblemID,
    };
    try {
      let res = await delimit(params);
      current.value += 1;
      updateExpendedNames();
      fetchData();
    } catch (error: any) {
      message.error(error?.message || '提交失败');
    }
  };

  const handleSaveDelimit = async () => {
    // await validate();
    let params = {
      dtsOrder: problemInfo.value.orderId,
      analysis: model.value.delimit.reason,
      delimiter: model.value.delimit.current,
      faultType: model.value.delimit.module,
      faultTreeStatus: model.value.delimit.faultTreeStatus,
      knowId: model.value.delimit.knowId,
      attribution: model.value.delimit.attribution,
      isRepeat: model.value.delimit.isDuplicated ? 1 : 0,
      repeatOrder: model.value.delimit.duplicateProblemID,
    };
    try {
      await saveDelimit(params);
      message.success('保存成功');
    } catch (error) {
      message.error('保存失败');
    }
    // message.success(res);
  };
  const prev = () => {
    current.value -= 1;
    updateExpendedNames();
    getDelimitView();
  };
  const toDts = () => {
    window.open(
      `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${problemInfo.value.orderId}`,
      '_blank'
    );
  };
  const toBeta = () => {
    window.open(
      `https://betaclub.huawei.com/beta/products/#/ProblemDetail?quesId=${problemInfo.value.betaOrder}`,
      '_blank'
    );
  };
  const curState = ref('');
  const getDelimitView = async () => {
    try {
      let res = await delimitView(problemInfo.value.orderId);
      model.value.delimit.reason = res.analysis;
      model.value.delimit.module = res.faultType;
      model.value.delimit.attribution = res.attribution;
      model.value.delimit.faultTreeStatus = res.faultTreeStatus;
      model.value.delimit.knowId = res.knowId;
      model.value.delimit.isDuplicated = !!res.isRepeat;
      model.value.delimit.duplicateProblemID = res.repeatOrder;
      model.value.delimit.current = res.delimiter;
    } catch (error: any) {}
  };

  const getLockView = async () => {
    try {
      let res = await lockView(problemInfo.value.orderId);
      model.value.plan.next = res.reviewer;
      model.value.plan.time = res.resolveTime;
      model.value.plan.solution = res.solution;
    } catch (error: any) {}
  };

  let knowledgeList = ref<
    Array<{
      id: string;
      desc: string;
      content: string;
      expanded: boolean;
      url?: string;
    }>
  >([]);
  const fetchDelimitData = async (text) => {
    try {
      const queryParams = {
        dtsTitle: text || '',
      };
      const link = 'faultTree/faultTreeQueryByDtsTitle';
      const data = await getDataList(link, queryParams);
      if (data) {
        knowledgeList.value = data.dataList.slice(0, 10).map((item) => ({
          ...item,
          expanded: false,
        }));
      }
    } catch (error) {
      console.error('Failed to load data:', error);
      throw error;
    } finally {
    }
  };
  const fetchData = async () => {
    try {
      const queryParams = {
        orderId: orderId,
        pageNo: 1,
        pageSize: 99,
      };
      const { records } = await getWorkOrderList(queryParams);
      curState.value = records[0].ewpStatus;
      info.value = records[0];
      console.log('info.value :>> ', info.value);
      if (curState.value === '待定界') {
        current.value = 1;
        await getDelimitView();
        model.value.delimit.transferTo =
          problemInfo.value.currentLevel === 'L2' ? problemInfo.value.preHandler : null;
      } else if (curState.value === '待锁定') {
        current.value = 2;
        await getDelimitView();
        await getLockView();
      } else if (curState.value === '待审核') {
        current.value = 3;
        await getDelimitView();
        await getLockView();
      } else if (
        curState.value === '待归档' ||
        curState.value === '待回归' ||
        curState.value === '已闭环'
      ) {
        isClosed.value = true;
        current.value = 3;
        await getDelimitView();
        await getLockView();
        await getReviewView();
      }
    } catch (error) {
      console.error('Failed to load table data:', error);
    }
  };

  // 添加获取审核信息函数
  const getReviewView = async () => {
    try {
      let res = await reviewView(problemInfo.value.orderId);
      model.value.review.reviewResult = res.reviewResult || '';
      model.value.review.nextHandler = res.nextHandler;
    } catch (error: any) {
      console.error('获取审核信息失败:', error);
    }
  };

  const saveNewIR = async () => {
    try {
      let params = {
        orderId: problemInfo.value.orderId,
        appName: problemInfo.value.appName,
        ewpOwner: problemInfo.value.currentHandler,
        acceptanceOwner: problemInfo.value.acceptanceOwner,
        creator: problemInfo.value.creator,
        severity: problemInfo.value.level,
        irOrderStatus: '未提单',
        top: problemInfo.value.top || false,
      };
      let irList: any = [];
      irList.push(params);
      await batchCreateIR(irList);
      await submitIr({ orderIds: irList.map((row) => row.orderId) });
      message.success('IR单创建成功');
    } catch (error: any) {
      if (error?.response?.status === 400) {
        message.error(error.response.data.message || 'IR单号已存在');
      } else {
        message.error(error?.message || 'IR单创建失败');
      }
    }
  };

  const updateExpendedNames = () => {
    if (current.value - 1 <= getProcessList().length) {
      expendedNames.value = getProcessList()?.[current.value - 1]?.defauleExpendedNames || [];
    }
  };

  const getStepStatus = (index) => {
    if (index < current.value - 1) return 'finish';
    if (index === current.value - 1) return 'process';
    return 'wait';
  };

  function toKnowledgeClick(item: any) {
    window.open(`http://***********/knowledgeDetail/${item}`, '_blank');
  }

  // 处理知识点击
  function handleKnowledgeClick(item: any) {
    window.open(item.url, '_blank');
  }

  // 复制链接
  function copyLink(link: string) {
    navigator.clipboard
      .writeText(link)
      .then(() => {
        message.success('链接已复制到剪贴板');
      })
      .catch(() => {
        message.error('复制失败，请手动复制');
      });
  }

  // 侧边栏折叠状态
  const isPanelCollapsed = ref(false);

  // 切换侧边栏折叠状态
  const togglePanel = () => {
    isPanelCollapsed.value = !isPanelCollapsed.value;
  };

  // 新增辅助函数
  const getSeverityType = (level: string) => {
    const types = {
      严重: 'error',
      一般: 'warning',
      轻微: 'info',
    };
    return types[level] || 'default';
  };

  const getStatusType = (status: string) => {
    const types = {
      处理中: 'info',
      已完成: 'success',
      已关闭: 'default',
    };
    return types[status] || 'default';
  };

  // 添加快捷操作配置
  const quickActions = [
    { key: 'CCTemplate', label: '抄送模板', icon: 'TimeOutline', type: 'warning' },
    { key: 'addIR', label: '登记IR', icon: 'TimeOutline', type: 'info' },
    { key: 'viewLog', label: '查看日志', icon: 'TimeOutline', type: 'success' },
  ];
  // 模拟操作日志数据
  const operationLogs = ref<
    Array<{
      dtsOrder?: string;
      createTime?: string | number;
      creator?: string;
      retentionTime?: number;
      dealerCn?: string;
      processNode?: string;
      operation?: string;
      handlerCn?: string;
      dealStep?: string;
    }>
  >([]);
  // 处理快捷操作点击
  const handleQuickAction = async (action: any) => {
    // 根据不同action.key实现具体功能
    if (action.key === 'addIR') {
      saveNewIR();
    } else if (action.key === 'CCTemplate') {
      if (curState.value === '待定界') {
        message.warning('还未定界，不能生成模板');
      } else {
        generateTemplates();
      }
    } else if (action.key === 'viewLog') {
      const backendData = await getProcessLog(problemInfo.value.orderId);
      // operationLogs.value = convertLogData(backendData);
      operationLogs.value = backendData;
      showLogModal.value = true;
    } else {
      message.info(`功能还在开发中。。。。`);
    }
  };

  let selectedRows = ref<any[]>([]);
  const checkedRowKeys = ref<string[]>([]); // 添加缺失的变量
  const generateTemplates = () => {
    selectedRows.value = [info.value];
    const templates = selectedRows.value.map(
      (row) =>
        `【问题描述】${
          row.description?.split('】').pop() + '\n' || ''
        }【DTS链接】https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${row.orderId || ''}
${
  row.irOrderId
    ? `【IR链接】https://issuereporter.developer.huawei.com/detail/${row.irOrderId
        ?.split('\n')
        ?.shift()}/comment` + '\n'
    : '【IR链接】待补充 ' + '\n'
}【分析结论】${row.conclusion || 'xxxx'}
【修改建议】${row.suggestion || 'xxxx'}
【知识链接】${row.knowledgeUrl || 'xxxx'}
【应用PM】${row.appPM || 'xxxx'}
【问题跟踪平台】https://dtse.cbg.huawei.com/listingProtection/ecologicalTracking
请执行：在【高级筛选】中过滤【当前处理人】为自己的问题，点击【应用PM更新】按钮更新进展
【生态舆情保障运作规范】https://3ms.huawei.com/km/groups/3945040/blogs/details/19122126?l=zh-cn`
    );

    const content = templates.join('\n');
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard.writeText(content).then(() => {
        message.success('模板已生成并复制到剪贴板');
        selectedRows.value = [];
      });
    } else {
      // 创建text area
      let textArea = document.createElement('textarea');
      textArea.value = content;
      // 使text area不在viewport，同时设置不可见
      textArea.style.position = 'absolute';
      textArea.style.opacity = '0';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      return new Promise((res, rej) => {
        // 执行复制命令并移除文本框
        document.execCommand('copy') ? res(null) : rej();
        textArea.remove();
        message.success('模板已生成并复制到剪贴板');
        // 重置选择状态
        checkedRowKeys.value = [];
        selectedRows.value = [];
      });
    }
  };
  // 编辑器实例，必须用 shallowRef
  const editorRef = shallowRef();

  // 修改 valueHtml 的初始化和绑定方式
  const valueHtml = ref('');

  // 添加检测移动设备的变量
  const isMobile = ref(false);

  // 检测设备类型
  const checkDeviceType = () => {
    isMobile.value = window.innerWidth < 768;
  };

  // 在组件挂载和窗口大小变化时检测设备类型
  onMounted(async () => {
    // 直接设置初始值
    valueHtml.value = model.value.delimit.reason;
    await fetchData();
    // 检测设备类型
    checkDeviceType();
    window.addEventListener('resize', checkDeviceType);
    // 初始化所有模块的L1/L2人员
    initAllL2Staff().then(() => {
      // 如果已经有模块信息，立即加载该模块的处理人
      if (model.value.delimit.module) {
        fetchL2StaffByModule(model.value.delimit.module);
      }
    });
    // 确保在获取到问题描述后再调用fetchDelimitData
    setTimeout(() => {
      const description = problemInfo.value.description?.split('】').pop() || '';
      console.log('description:', description);
      fetchDelimitData(description);
    }, 100);
    fetchHandleOptions();
  });

  // 组件销毁时移除事件监听
  onBeforeUnmount(() => {
    window.removeEventListener('resize', checkDeviceType);
    const editor = editorRef.value;
    if (editor == null) return;
    editor.destroy();
  });

  // 添加对 model.value.delimit.reason 的监听
  watch(
    () => model.value.delimit.reason,
    (newValue) => {
      if (newValue && valueHtml.value !== newValue) {
        valueHtml.value = newValue;
      }
    }
  );

  // 添加对 valueHtml 的监听
  watch(valueHtml, (newValue) => {
    if (newValue !== model.value.delimit.reason) {
      model.value.delimit.reason = newValue;
    }
  });

  // 修改编辑器配置
  const editorConfig = {
    placeholder: '请输入内容...',
    autoFocus: false,
    MENU_CONF: {
      uploadImage: {
        server: `/ewp/management/ewpOrder/uploadFile`, // 替换为你的实际上传接口
        fieldName: 'file',
        maxFileSize: 10 * 1024 * 1024, // 限制为最大10M
        maxNumberOfFiles: 10, // 最多可上传10张图片
        allowedFileTypes: ['image/*'],
        headers: {
          Accept: 'application/json',
          env: import.meta.env.MODE === 'development' ? 'dev' : '',
          // 可以添加其他自定义 headers
        },
        // 上传之前触发
        // onBeforeUpload(file: File) {
        //   // file 选中的文件，返回值是 Promise
        //   return new Promise((resolve, reject) => {
        //     // 可以在这里进行文件大小和类型校验
        //     if (file.size > 10 * 1024 * 1024) {
        //       message.error('文件大小不能超过10M');
        //       reject('文件大小不能超过10M');
        //       return;
        //     }
        //     // if (!file.type.includes('image')) {
        //     //   message.error('只能上传图片格式文件');
        //     //   reject('只能上传图片格式文件');
        //     //   return;
        //     // }
        //     resolve(file);
        //   });
        // },
        // 上传进度条
        onProgress(progress: number) {
          // progress 是 0-100 的数字
          console.log('progress', progress);
        },
        // 单个文件上传成功之后
        onSuccess(file: File, res: any) {
          message.success(`${file.name} 上传成功`);
        },
        // 单个文件上传失败
        onFailed(file: File, res: any) {
          message.error(`${file.name} 上传失败`);
        },
        // 上传错误，或者触发 reject 时触发
        onError(file: File, err: any, res: any) {
          message.error(`${file.name} 上传出错`);
        },
        // 自定义插入图片
        customInsert(res: any, insertFn: any) {
          // insertFn 可把图片插入到编辑器
          insertFn(
            'https://dts-szv.clouddragon.huawei.com/v1/nfs/downLoadFile?filePath=' + res.data
          );
        },
      },
    },
  };

  // 工具栏配置
  const toolbarConfig = {
    toolbarKeys: [
      'headerSelect',
      'bold',
      'italic',
      'underline',
      'bulletedList',
      'numberedList',
      '|',
      'undo',
      'redo',
      'fullScreen', // 全屏
      '|',
      {
        key: 'group-image',
        title: '图片',
        menuKeys: ['uploadImage', 'insertImage'],
      },
    ],
  };

  // 修改编辑器创建回调
  const handleCreated = (editor: any) => {
    editorRef.value = editor;
    // 确保在编辑器创建后设置内容
    if (model.value.delimit.reason) {
      editor.setHtml(model.value.delimit.reason);
    }
  };

  // 添加搜索相关的响应式变量
  const searchKeyword = ref('');
  const selectedKeywords = ref<string[]>([]);

  // 处理搜索
  const handleSearch = () => {
    if (!searchKeyword.value.trim()) return;
    fetchDelimitData(searchKeyword.value);
  };

  // 移除关键词
  const removeKeyword = (keyword: string) => {
    selectedKeywords.value = selectedKeywords.value.filter((k) => k !== keyword);
  };

  // 将问题描述分词
  const splitDescription = computed(() => {
    const description = problemInfo.value?.description?.split('】').pop() || '';
    // 使用正则表达式分词，保留中文、英文、数字
    return description
      .split(/([，。！？；：、\s])/g)
      .filter((word) => word.trim() && !/[，。！？；：、\s]/.test(word));
  });

  // 判断词是否已被选中
  const isWordSelected = (word: string) => {
    return selectedKeywords.value.find((item) => item === word);
  };
  const curIndex = ref(null);
  // 切换词的选中状态
  const toggleWord = (word: string, index) => {
    if (isWordSelected(word)) {
      removeKeyword(word);
    } else {
      selectedKeywords.value.push(word);
      searchKeyword.value = word;
      curIndex.value = index;
      fetchDelimitData(searchKeyword.value);
    }
  };

  // 添加模块与知识ID的映射关系
  const moduleToKnowIdMap = {
    应用缺失: 'SFAQ20250515103917440241',
    功能缺失: 'SFAQ20250515102555229689',
    '非问题-单双一致': 'SFAQ20250515171911101001',
    '非问题-规格设计': 'SFAQ20250515172402155295',
    '非问题-小概率': 'SFAQ20250515162753065629',
    '非问题-用户操作不当': 'SFAQ20250515164916484249',
    '非问题-应用下架且无计划': 'S2CFK20250625135820925188',
    新版本已修复: 'SFAQ20250515161601876405',
    无条件复现: 'SFAQ20250515181639115595',
    重复问题单: 'SFAQ20250605205926647872',
  };

  // 监听模块选择变化，自动填写知识ID和处理转单选项
  watch(
    () => model.value.delimit.module,
    (newValue, oldValue) => {
      // 处理知识ID自动填充
      if (newValue && moduleToKnowIdMap[newValue] && curState.value === '待定界') {
        model.value.delimit.knowId = moduleToKnowIdMap[newValue];
      }

      // 重置转单处理人，因为从L1转L2或从L2转L1需要重新选择
      model.value.delimit.transferTo = null;

      // 处理模块相关的L2处理人加载
      if (newValue) {
        // 如果选择了模块，则需要确保有该模块的L2处理人
        if (!l2Options[newValue] || l2Options[newValue].length === 0) {
          // 获取该模块的L2处理人
          fetchL2StaffByModule(newValue);
        } else if (l2Options[newValue] && l2Options[newValue].length > 0) {
          // 筛选出仅L2级别的人员
          const l2StaffOnly = l2Options[newValue].filter((staff) => staff.level === 'L2');
          if (l2StaffOnly.length > 0) {
            l2Options[newValue] = l2StaffOnly; // 只保留L2人员
          } else {
            message.warning(`未找到${newValue}模块的L2处理人，请联系管理员`);
          }
        }
      } else {
        // 如果清空了模块，显示所有L1处理人
        if (allL1Staff.value.length > 0) {
          // 如果是从有值变为无值，显示提示信息
          if (oldValue) {
            message.warning(
              `已清空故障分类，转单对象已切换为L1处理人（共${allL1Staff.value.length}人）`
            );
          } else {
            message.info(`请选择L1处理人，共${allL1Staff.value.length}人可选`);
          }
        } else {
          message.warning('L1处理人列表为空，请稍后再试');
        }
      }
    }
  );

  // 切换知识展开状态
  const toggleKnowledgeExpand = (item) => {
    item.expanded = !item.expanded;
  };

  // 使用marked库渲染Markdown内容
  const renderMarkdown = (content) => {
    if (!content) return '';
    try {
      return marked(content);
    } catch (error) {
      console.error('Markdown渲染错误:', error);
      return content;
    }
  };

  // 引导步骤配置
  const guideSteps = [
    {
      target: '.side-panel',
      title: '问题详情',
      content: '这里展示问题的详细信息，包括问题单号、严重程度等基本信息，另外还有一些快捷操作',
      tooltipPosition: 'right',
    },
    {
      target: '.step-card',
      title: '处理进度',
      content: '显示问题处理的各个阶段，帮助你了解当前进展',
      tooltipPosition: 'bottom',
    },
    {
      target: '.form-card',
      title: '定界信息',
      content: '在这里填写问题定界相关信息，注意定界流程需要和知识结合',
      tooltipPosition: 'right',
    },
    {
      target: '.know-card',
      title: '知识推荐',
      content: '这里结合故障库提供了问题解决方案参考',
      tooltipPosition: 'top',
    },
  ];

  // 引导状态
  const showGuide = ref(false);
  const currentStepIndex = ref(0);
  const highlightPosition = ref({ x: 0, y: 0, width: 0, height: 0 });

  // 计算当前步骤
  const currentGuideStep = computed(() => guideSteps[currentStepIndex.value]);
  const isLastStep = computed(() => currentStepIndex.value === guideSteps.length - 1);

  // 更新高亮区域位置
  const updateHighlightPosition = () => {
    const step = guideSteps[currentStepIndex.value];
    const element = document.querySelector(step.target);
    if (element) {
      const rect = element.getBoundingClientRect();
      highlightPosition.value = {
        x: rect.left,
        y: rect.top,
        width: rect.width,
        height: rect.height,
      };
    }
  };

  // 计算提示框位置
  const getTooltipStyle = () => {
    const step = currentGuideStep.value;
    const position = {
      position: 'absolute' as const,
    };

    switch (step.tooltipPosition) {
      case 'left':
        return {
          ...position,
          right: '100%',
          top: '50%',
          transform: 'translateY(-50%)',
          marginRight: '10px',
        };
      case 'right':
        return {
          ...position,
          left: '100%',
          top: '50%',
          transform: 'translateY(-50%)',
          marginLeft: '10px',
        };
      case 'top':
        return {
          ...position,
          bottom: '100%',
          left: '50%',
          transform: 'translateX(-50%)',
          marginBottom: '10px',
        };
      case 'bottom':
      default:
        return {
          ...position,
          top: '100%',
          left: '50%',
          transform: 'translateX(-50%)',
          marginTop: '10px',
        };
    }
  };

  // 引导控制方法
  const nextGuideStep = () => {
    if (isLastStep.value) {
      finishGuide();
    } else {
      currentStepIndex.value++;
      nextTick(updateHighlightPosition);
    }
  };

  const skipGuide = () => {
    finishGuide();
  };

  const finishGuide = () => {
    showGuide.value = false;
    localStorage.setItem('process_guide_shown', 'true');
  };

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    if (showGuide.value) {
      updateHighlightPosition();
    }
  });

  // 添加折叠面板状态控制
  const expandedNames = ref<string[]>(['delimit-summary']);
  const expandedPlanNames = ref<string[]>([]);

  // 监听当前步骤变化，自动展开/折叠面板
  watch(current, (newValue) => {
    // 在进入锁定计划时，默认展开定界信息汇总
    if (newValue === 2) {
      expandedNames.value = ['delimit-summary'];
    }
    // 在进入审核阶段时，默认展开定界信息汇总
    if (newValue === 3) {
      expandedNames.value = ['delimit-summary'];
      expandedPlanNames.value = [];
    }
  });

  // 添加返回锁定计划的函数
  const returnToLockPlan = () => {
    current.value = 2;
    updateExpendedNames();
  };

  // 智能问答相关
  const showAiAssistant = ref(false);
  const toggleAiAssistant = () => {
    showAiAssistant.value = !showAiAssistant.value;
  };

  // 添加转单处理函数
  const handleTransfer = async () => {
    if (!model.value.delimit.transferTo) {
      message.warning('请选择转单处理人');
      return;
    }

    try {
      // 保存当前的定界信息
      await handleSaveDelimit();

      // 构建转单参数
      let params = {
        orderIds: [problemInfo.value.orderId],
        target: model.value.delimit.transferTo,
      };

      // 使用DTSTurnTo接口进行转单
      await DTSTurnTo(params);

      const level = model.value.delimit.module ? 'L2' : 'L1';
      message.success(`已成功将工单转交给${level}处理人：${model.value.delimit.transferTo}`);

      // 转单成功后返回列表页
      router.push({ name: 'MyTodo' });
    } catch (error: any) {
      message.error(error?.message || '转单失败');
    }
  };

  // 添加时间轴相关的辅助函数
  const getTimelineItemType = (item: any) => {
    const types: Record<string, 'default' | 'info' | 'success' | 'warning' | 'error'> = {
      待定界: 'info',
      待锁定: 'warning',
      待审核: 'warning',
      待归档: 'success',
      已归档: 'success',
    };
    return types[item.dealStep] || 'default';
  };

  const getTimelineTitle = (item: any) => {
    return item.operation || '未知环节';
  };

  const getTimelineTime = (item: any) => {
    return item.createTime ? '处理时间: ' + formatDate(item.createTime) : '';
  };

  const getTimelineContent = (item: any) => {
    return item.operation || '';
  };

  const getTimelineIcon = (item: any) => {
    const icons: Record<string, any> = {
      待定界: HourglassOutline,
      待锁定: TimeOutline,
      待审核: SwapHorizontalOutline,
      待归档: CheckmarkCircleOutline,
      已归档: CheckmarkDoneOutline,
    };
    return icons[item.operation] || TimeOutline;
  };

  const getProcessTagType = (item: any) => {
    const types: Record<string, 'default' | 'info' | 'success' | 'warning' | 'error'> = {
      待定界: 'info',
      待锁定: 'warning',
      待审核: 'warning',
      待归档: 'success',
      已归档: 'success',
    };
    return types[item.dealStep] || 'default';
  };

  // 格式化滞留时间（毫秒转为可读形式）
  const formatretentionTime = (ms: number) => {
    if (!ms) return '0分钟';

    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}天${hours % 24}小时`;
    } else if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`;
    } else if (minutes > 0) {
      return `${minutes}分钟`;
    } else {
      return `${seconds}秒`;
    }
  };
</script>

<style lang="less" scoped>
  .process-container {
    display: flex;
    background: #f5f7fa;
    gap: 16px;
    padding: 16px;
    box-sizing: border-box;
    overflow: hidden;
    height: 100%;
  }

  // 添加定界信息汇总卡片样式
  .summary-card {
    margin-bottom: 16px;

    :deep(.n-card__content) {
      padding: 16px;
    }

    .reason-summary {
      max-height: 300px;
      overflow-y: auto;
      padding: 8px;
      background: #f9f9f9;
      border-radius: 4px;
      font-size: 14px;
      line-height: 1.6;

      :deep(img) {
        max-width: 100%;
        height: auto;
      }

      :deep(p) {
        margin-bottom: 10px;
      }
    }
  }

  // 折叠面板样式
  .delimit-collapse {
    background-color: #fff;
    border-radius: 8px;

    :deep(.n-collapse-item) {
      .n-collapse-item__header {
        font-weight: 500;
        color: #333;
        background-color: #f9fafc;
        border-radius: 8px 8px 0 0;
      }

      .n-collapse-item__content-wrapper {
        .n-collapse-item__content-inner {
          padding: 16px;
        }
      }
    }

    .reason-summary {
      max-height: 300px;
      overflow-y: auto;
      padding: 8px;
      background: #f9f9f9;
      border-radius: 4px;
      font-size: 14px;
      line-height: 1.6;

      :deep(img) {
        max-width: 100%;
        height: auto;
      }

      :deep(p) {
        margin-bottom: 10px;
      }
    }
  }

  // 统一所有卡片的基础样式
  :deep(.n-card) {
    border-radius: 8px;
    background: white;

    .n-card-header {
      padding: 16px 20px;
      border-bottom: 1px solid #f0f0f0;
    }

    .n-card__content {
      padding: 20px;
    }
  }

  // 左侧面板样式
  .side-panel {
    background: transparent;

    .full-height {
      height: 100%;

      .side-card {
        height: 100%;
        box-shadow: none;

        :deep(.n-card__content) {
          padding: 0;

          .n-scrollbar {
            padding: 20px;

            .detail-list {
              .n-descriptions {
                margin: 0;
              }
            }
          }
        }
      }
    }
  }

  // 主内容区域样式
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
    height: 100%;
    overflow: hidden;
    background: transparent;

    // 步骤条卡片
    .step-card {
      margin: 0;

      :deep(.n-card__content) {
        padding: 20px;

        .description-alert {
          margin-bottom: 16px;
        }

        .n-steps {
          padding: 24px 0;
        }
      }
    }

    // 内容区域
    .content-scrollbar {
      flex: 1;
      height: 0;

      .content-card {
        height: 100%;
        margin: 0;

        :deep(.n-card__content) {
          padding: 20px;
        }

        .step-content {
          height: 100%;
          display: flex;
          flex-direction: column;

          // 表单网格布局
          .n-grid {
            gap: 16px;
            margin-bottom: 16px;

            // 左侧表单卡片
            .form-card {
              background: #fafafa;
              margin: 0;

              :deep(.n-card-header) {
                padding: 16px 20px;
              }

              :deep(.n-card__content) {
                padding: 20px;
              }
            }

            // 右侧推荐卡片
            :deep(.n-space) {
              .n-card {
                margin: 0;

                :deep(.n-card__content) {
                  padding: 16px;
                }
              }
            }
          }
        }
      }
    }
  }

  // 操作按钮区域
  .action-buttons {
    padding: 16px 0;
    background: white;
    border-top: 1px solid #f0f0f0;
    margin-top: auto;
  }

  // 表单项样式统一
  :deep(.n-form-item) {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  // 输入控件统一宽度
  :deep(.n-input),
  :deep(.n-select),
  :deep(.n-date-picker) {
    width: 100%;
  }

  // 步骤条样式
  .step-card {
    // background: linear-gradient(to right, #f7f9fe, #ffffff);

    &.closed {
      background: linear-gradient(to right, #f0f7ff, #ffffff);
    }

    :deep(.n-steps) {
      .n-step {
        align-items: center;

        &-indicator {
          width: 32px;
          height: 32px;
        }

        .step-icon {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          background: #e5e8ef;
          color: #666;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-weight: 500;
        }

        &.n-step--finish .step-icon {
          background: #18a058;
          color: #fff;
        }

        &.n-step--process .step-icon {
          background: #2080f0;
          color: #fff;
        }
      }
    }
  }

  :deep(.n-step-splitor) {
    margin-top: 9px !important;
  }
  // 滚动条样式
  :deep(.n-scrollbar-rail) {
    &.n-scrollbar-rail--vertical {
      width: 6px;
      opacity: 0;
      transition: opacity 0.2s;

      &:hover {
        opacity: 1;
      }
    }
  }

  .quick-actions {
    margin-top: 20px;
    padding: 0 4px;

    .action-group {
      width: 100%;
    }

    .quick-divider {
      margin: 16px 0;
    }

    .section-title {
      font-size: 13px;
      color: #666;
      margin-bottom: 8px;
      padding-left: 4px;
    }

    .action-tag {
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    :deep(.n-tag) {
      padding: 6px 12px;

      .n-icon {
        margin-right: 4px;
      }
    }

    :deep(.n-button-group) {
      .n-button {
        padding: 4px 12px;

        .n-icon {
          margin-right: 4px;
        }
      }
    }

    .related-actions {
      padding: 4px;
    }
  }

  // 优化描述列表和快捷操作之间的间距
  .detail-list {
    :deep(.n-descriptions) {
      margin-bottom: 0;
    }
  }

  // 添加富文本编辑器相关样式
  .editor-card {
    :deep(.n-card__content) {
      padding: 0;
    }

    .editor-container {
      border: 1px solid #ccc;
      z-index: 100; // 防止编辑器被其他元素遮挡

      .editor-toolbar {
        background-color: #fff;
      }

      .editor-content {
        background-color: #fff;
        padding: 0 10px;
      }
    }
  }

  :deep(.w-e-text-container) {
    min-height: 300px !important;
    .w-e-image-container {
      img {
        max-width: 100%;
        height: auto;
      }
    }
  }

  :deep(.w-e-toolbar) {
    border-bottom: 1px solid #eee !important;
    .w-e-bar-item-group {
      .w-e-bar-item {
        &:hover {
          background-color: #f1f1f1;
        }
      }
    }
  }

  // 添加搜索区域样式
  .search-area {
    margin-bottom: 16px;

    .description-select {
      margin-bottom: 12px;

      .label {
        font-size: 13px;
        color: #666;
        margin-bottom: 8px;
      }

      .text-wrapper {
        line-height: 2;
        background: #f9f9f9;
        padding: 8px 12px;
        border-radius: 4px;

        .word-btn {
          padding: 2px 6px;
          margin: 0 2px;
          font-size: 14px;
          transition: all 0.2s ease;
          border-radius: 4px;

          &:hover {
            background-color: #e8f3ff;
            color: #2080f0;
          }

          &.selected {
            background-color: #2080f0;
            color: #fff;

            &:hover {
              background-color: #1060c9;
            }
          }
        }
      }
    }

    .keyword-tags {
      margin-top: 8px;

      :deep(.n-tag) {
        margin-right: 8px;
        margin-bottom: 8px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  // 知识内容卡片样式
  .knowledge-content-card {
    margin-top: 10px;
    margin-bottom: 10px;
    width: 100%;

    :deep(.n-card__content) {
      padding: 12px;
    }
  }

  // Markdown内容样式
  .markdown-content {
    width: 100%;
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;

    :deep(h1) {
      font-size: 1.5em;
      margin-top: 0.5em;
      margin-bottom: 0.5em;
    }

    :deep(h2) {
      font-size: 1.3em;
      margin-top: 0.5em;
      margin-bottom: 0.5em;
    }

    :deep(h3) {
      font-size: 1.1em;
      margin-top: 0.5em;
      margin-bottom: 0.5em;
    }

    :deep(p) {
      margin-bottom: 0.8em;
      line-height: 1.6;
    }

    :deep(ul, ol) {
      padding-left: 1.5em;
      margin-bottom: 0.8em;
    }

    :deep(li) {
      margin-bottom: 0.3em;
    }

    :deep(code) {
      background-color: #f5f5f5;
      padding: 0.2em 0.4em;
      border-radius: 3px;
      font-family: monospace;
    }

    :deep(pre) {
      background-color: #f5f5f5;
      padding: 1em;
      border-radius: 5px;
      overflow-x: auto;
      margin-bottom: 1em;
      white-space: pre-wrap;
    }

    :deep(blockquote) {
      border-left: 4px solid #ddd;
      padding-left: 1em;
      margin-left: 0;
      color: #666;
    }

    :deep(table) {
      border-collapse: collapse;
      width: 100%;
      margin-bottom: 1em;
    }

    :deep(th, td) {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }

    :deep(th) {
      background-color: #f5f5f5;
    }

    :deep(img) {
      max-width: 100%;
      height: auto;
    }
  }

  // 知识列表项样式优化
  :deep(.n-list-item) {
    transition: all 0.3s ease;

    &:hover {
      background-color: #f9f9f9;
    }

    .n-thing {
      width: 100%;
    }
  }

  /* 添加转单处理人选择提示样式 */
  .transfer-level-indicator {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    display: inline-block;
  }

  .transfer-level-l1 {
    background-color: #fef0f0;
    color: #f56c6c;
    border-left: 3px solid #f56c6c;
  }

  .transfer-level-l2 {
    background-color: #f0f9eb;
    color: #67c23a;
    border-left: 3px solid #67c23a;
  }

  .guide-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 1000;

    .highlight-area {
      position: absolute;
      background: transparent;
      box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.6);
      border: 2px solid #2080f0;
      border-radius: 4px;
      transition: all 0.3s ease;
    }

    .guide-tooltip {
      background: white;
      padding: 16px;
      border-radius: 8px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      width: 280px;

      .guide-title {
        font-size: 16px;
        font-weight: 500;
        margin: 0 0 8px;
        color: #2080f0;
      }

      .guide-content {
        font-size: 14px;
        line-height: 1.5;
        margin: 0;
        color: #666;
      }
    }
  }

  // 修改卡片样式
  .know-card {
    display: flex;
    flex-direction: column;
    height: 100%;

    :deep(.n-card__content) {
      display: flex;
      flex-direction: column;
      height: calc(100% - 56px); // 减去标题高度
      padding: 12px;
      overflow: hidden;
    }

    .search-area {
      flex-shrink: 0;
      margin-bottom: 12px;
    }

    .n-scrollbar {
      flex-grow: 1;
    }
  }

  // 智能问答按钮样式
  .ai-assistant-btn {
    position: fixed;
    right: 30px;
    bottom: 100px;
    display: flex;
    align-items: center;
    padding: 10px 16px;
    background: #2080f0;
    color: white;
    border-radius: 24px;
    box-shadow: 0 4px 12px rgba(32, 128, 240, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 100;

    &:hover {
      background: #1060c9;
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(32, 128, 240, 0.4);
    }

    .ai-icon {
      margin-right: 8px;
    }

    .ai-text {
      font-size: 14px;
      font-weight: 500;
    }
  }

  // 智能问答侧边栏
  .ai-assistant-drawer {
    position: fixed;
    top: 0;
    right: 0;
    width: 560px;
    height: 100vh;
    background: white;
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;

    &.drawer-open {
      transform: translateX(0);
    }

    .drawer-header {
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-shrink: 0;

      .drawer-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }

    .drawer-content {
      flex-grow: 1;
      height: calc(100vh - 53px);
      overflow: hidden;

      iframe {
        border: none;
        width: 100%;
        height: 100%;
      }
    }

    // 添加侧边关闭区域
    .drawer-close-handle {
      position: absolute;
      left: -20px;
      top: 50%;
      transform: translateY(-50%);
      width: 20px;
      height: 50px;
      background: #2080f0;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      border-radius: 4px 0 0 4px;
      cursor: pointer;
      box-shadow: -2px 0 6px rgba(0, 0, 0, 0.1);

      &:hover {
        background: #1060c9;
      }
    }
  }

  // 遮罩层 - 移除点击事件和改为半透明
  .drawer-mask {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.15);
    z-index: 998; // 确保在侧边栏下方但在其他内容上方
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    pointer-events: none; // 允许与下方内容交互

    &.mask-visible {
      opacity: 1;
      visibility: visible;
    }
  }

  // 添加时间轴样式
  :deep(.n-timeline) {
    padding: 16px;

    .n-timeline-item {
      margin-bottom: 24px;

      .n-timeline-item-content {
        margin-top: 8px;

        .timeline-item-content {
          padding: 12px 16px;
          background: #f9fafc;
          border-radius: 8px;
          margin-top: 8px;

          .timeline-label {
            font-size: 14px;
            color: #666;
            font-weight: 500;
            width: 70px;
            display: inline-block;
            text-align: right;
            margin-right: 8px;
          }
        }
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // 日志弹窗样式
  :deep(.n-modal) {
    .n-card__content {
      max-height: 70vh;
      overflow-y: auto;
    }
  }
</style>
