import service from '@/utils/axios';
import { ewpService } from '@/utils/axios';

const baseUrl = '/oi';

export interface ProblemStatus {
  planLockTimeEnd?: any;
  planLockTimeStart?: any;
  id?: number;
  dtsNum?: string;
  appName?: string;
  status?: string;
  owner?: string;
  guarantor?: string;
  level?: string;
  dtsStatus?: string;
  warnTimeStart?: number;
  warnTimeEnd?: number;
  planTimeStart?: number;
  planTimeEnd?: number;
  descField?: string[];
  module?: string[];
  isOverdue?: Array<boolean | null>;
}
export interface QueryProblemListReq extends ProblemStatus {
  pageNum: number;
  pageSize: number;
}
export const getProblemList = (data: QueryProblemListReq) => {
  return service({
    url: `${baseUrl}/query`,
    method: 'post',
    data,
  });
};

export const addProblem = (data) => {
  return service({
    url: `${baseUrl}/add`,
    method: 'post',
    data,
  });
};

export const updateProblem = (data) => {
  return service({
    url: `${baseUrl}/update`,
    method: 'post',
    data,
  });
};

export const exportProblem = (data: ProblemStatus) => {
  return service({
    url: `${baseUrl}/export`,
    method: 'post',
    data,
    responseType: 'blob',
  });
};
export const deleteProblem = (params) => {
  return service({
    url: `${baseUrl}/delete`,
    method: 'get',
    params,
  });
};

export const exportPublicSentiment = (data) => {
  return ewpService({
    url: `/management/futOrder/exportVocData`,
    method: 'post',
    data,
    responseType: 'blob',
  });
};
