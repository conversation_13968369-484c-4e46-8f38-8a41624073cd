window.onload = async () => {
  const src = chrome.runtime.getURL("./settings.js");
  const { SETTINGS } = await import(src);

  chrome.runtime.sendMessage(
    {
      action: "getCookie",
      domain: ".developer.huawei.com",
    },
    (res) => {
      customDownload(res.csrfToken);
    }
  );

  let customDownload = async (csrfToken) => {
    let t = new Date().getTime();

    let customPost = async (param, src = "") => {
      return fetch(
        src ||
          `https://svc-drcn.developer.huawei.com/codeserver/Common/v1/delegate`,
        {
          method: "post",
          credentials: "include",
          headers: {
            "content-type": "application/json;charset=UTF-8",
            accept: "application/json, text/plain, */*",
            "x-hd-csrf": csrfToken,
            "x-hd-date": new Date()
              .toISOString()
              .replaceAll(/([\d-:T]*)(\.\d*Z)/g, ($1, b, c) => {
                return b.replaceAll(/[:-]/g, "") + "Z";
              }),
            "x-hd-serialno": Math.ceil(
              ((t = (9301 * t + 49297) % 233280), (t / 233280) * 10000000)
            ),
          },
          body: JSON.stringify(param),
        }
      );
    };

    let formatDate = (date) => {
      const Y = date.getFullYear();
      const M = `00${date.getMonth() + 1}`.slice(-2);
      const D = `00${date.getDate()}`.slice(-2);
      return `${Y}-${M}-${D}`;
    };

    // 创建下载任务
    let createExportManagerIssueList = async (userType) => {
      let now = new Date();
      let start = formatDate(new Date(now.getTime() - 24 * 60 * 60 * 1000));
      let end = formatDate(now);
      let isImportFullData = end === "2024-12-24";
      let param = {
        svc: "PartnerIssueService.Developer.exportManagerIssueList",
        reqType: 0,
        reqJson: `{"req":{"query":"{\\"createTime\\":\\"${
          isImportFullData ? "2023-12-01" : start
        } ${SETTINGS.downloadTime}|${end} ${
          SETTINGS.downloadTime
        }\\",\\"isSubmittedByHWTest\\":0}","languageType":"zh_CN","listNO":5501,"selectedUserType":${userType}}}`,
      };
      return (await customPost(param)).json();
    };
    // 查询用户角色
    let queryAvailableUserTypes = async () => {
      let param = {
        svc: "PartnerIssueService.Developer.queryAvailableUserTypes",
        reqType: 0,
        reqJson: '{"req":{"selectedUserType":null}}',
      };
      return (await customPost(param)).json();
    };
    // 查询下载任务列表
    let queryTaskList = async (userType) => {
      let param = {
        svc: "PartnerIssueService.Developer.queryTaskList",
        reqType: 0,
        reqJson: `{"req":{"currentPage":1,"perPageRecords":10,"selectedUserType":${userType}}}`,
      };
      return (await customPost(param)).json();
    };
    // 获取下载地址
    let queryTaskDownloadUrl = async (id, userType) => {
      let param = {
        svc: "PartnerIssueService.Developer.queryTaskDownloadUrl",
        reqType: 0,
        reqJson: `{"req":{"id": "${id}","selectedUserType":${userType}}}`,
      };
      return (await customPost(param)).json();
    };
    // 根据下载链接下载压缩包
    let getBlob = async (downloadUrl) => {
      let res = await fetch(downloadUrl, { method: "get" });
      let blob = await res.blob();
      return blob;
    };
    // 上传
    let downloadTask = async (blob, password, userId) => {
      try {
        const formData = new FormData();
        formData.append("file", blob, "filename.zip");
        formData.append("config", JSON.stringify({ password, userId }));
        fetch("https://dtse.cbg.huawei.com/board/downloadTask/downloadZip", {
          method: "post",
          body: formData,
        });
      } catch (error) {}
    };
    // 获取下载任务ID
    let getTaskId = async (userType) => {
      let task = JSON.parse((await queryTaskList(userType)).resJson)
        .resultList[0];
      if (task.status === 2) {
        return null;
      } else if (task.status !== 1) {
        await new Promise((resolve) => {
          setTimeout(() => {
            resolve(true);
          }, 200);
        });
        return await getTaskId(userType);
      } else {
        return task.id;
      }
    };
    // 获取用户信息
    let getUserRouteInfo = async () => {
      let param = {
        svc: "OpenCommon.DelegateTm.OpenUP_Server4User_getInfo",
        reqType: 0,
        reqJson: '{"req":{"queryRangeFlag":"00011"}}',
      };
      return (await customPost(param)).json();
    };

    let getCanDownLoad = async (userId) => {
      let res = await fetch(
        "https://dtse.cbg.huawei.com/board/downloadTask/select",
        {
          method: "post",
          body: JSON.stringify({ userId: userId }),
        }
      );
      let response = await res.json();
      if (!response?.data?.[0]?.downloadTime) {
        return true;
      }
      let before = new Date(response.data[0].downloadTime).toLocaleDateString();
      let beforeTime = new Date(before).getTime();
      return beforeTime < new Date(new Date().toDateString()).getTime();
    };

    let init = async () => {
      if (!this.customUserId) {
        this.customUserId = JSON.parse(
          (await getUserRouteInfo()).resJson
        ).userID;
      }
      let canDownLoad = await getCanDownLoad(this.customUserId);
      if (!canDownLoad) {
        return;
      }
      if (!this.customUserType) {
        this.customUserType = JSON.parse(
          (await queryAvailableUserTypes()).resJson
        ).resultList;
      }
      let userType = this.customUserType.includes(
        Number(localStorage.getItem("roleInfo"))
      )
        ? localStorage.getItem("roleInfo")
        : this.customUserType[0];
      let exportManagerIssueList = JSON.parse(
        (await createExportManagerIssueList(userType)).resJson
      );
      if (exportManagerIssueList.code !== 0) {
        return;
      }
      let taskId = await getTaskId(userType);
      if (!taskId) {
        return;
      }

      let downloadUrlRes = JSON.parse(
        (await queryTaskDownloadUrl(taskId, userType)).resJson
      );
      let compressPwd = downloadUrlRes.result.compressPwd;
      let downloadUrl = downloadUrlRes.result.downloadUrl;
      let blob = await getBlob(downloadUrl);
      downloadTask(blob, compressPwd, this.customUserId);
    };

    let getStatusList = async (issueList, userType) => {
      let param = {
        svc: "PartnerIssueService.Developer.getManagerIssueList",
        reqType: 0,
        reqJson: `{"req":{"query":"{\\"issueID\\":\\"${issueList.join(
          ","
        )}\\"}","perPageRecords":${
          issueList.length || 1
        },"currentPage":1,"isDelete":0,"languageType":"zh_CN","selectedUserType": ${userType}}}`,
      };
      return (await customPost(param)).json();
    };

    let getIrList = async () => {
      try {
        return (
          await fetch(
            "https://dtse.cbg.huawei.com/board/IRTransport/queryUnresolvedNew",
            {
              method: "post",
              body: null,
            }
          )
        ).json();
      } catch (error) {}
    };

    let utcToLocal = (time) => {
      try {
        const DateList = time.replaceAll(/[\-\:]+/g, " ").split(" ");
        DateList[1] = DateList[1] - 1;
        const date = new Date(Date.UTC(...DateList));
        let Y = date.getFullYear();
        let M = `00${date.getMonth() + 1}`.slice(-2);
        let D = `00${date.getDate()}`.slice(-2);
        let h = `00${date.getHours()}`.slice(-2);
        let m = `00${date.getMinutes()}`.slice(-2);
        let s = `00${date.getSeconds()}`.slice(-2);
        return `${Y}-${M}-${D} ${h}:${m}:${s}`;
      } catch (error) {
        return null;
      }
    };

    let updateStatus = async (statusMap) => {
      try {
        await fetch(
          "https://dtse.cbg.huawei.com/board/IRTransport/updateStatusNew",
          {
            method: "post",
            body: JSON.stringify({ statusMap }),
          }
        );
      } catch (error) {}
    };
    let refreshFieldTeam = async (statusMap) => {
      try {
        await fetch(
          "https://dtse.cbg.huawei.com/board/IRTransport/refreshFieldTeam",
          {
            method: "get",
          }
        );
      } catch (error) {}
    };

    let splitArray = (arr, chunkSize) => {
      let result = [];
      for (let i = 0; i < arr.length; i += chunkSize) {
        result.push(arr.slice(i, i + chunkSize));
      }
      return result;
    };

    let update = async () => {
      try {
        let irListRes = (await getIrList())?.data?.list || [];
        let irList = irListRes.map((item) => {
          return item?.irNumber;
        });
        if (!this.customUserType) {
          try {
            this.customUserType = JSON.parse(
              (await queryAvailableUserTypes()).resJson
            ).resultList;
          } catch (error) {}
        }
        let userType = this.customUserType?.includes(
          Number(localStorage.getItem("roleInfo"))
        )
          ? localStorage.getItem("roleInfo")
          : this.customUserType?.[0];
        if (!irList.length) {
          return;
        }
        irList = Array.from(new Set(irList));
        let irListArray = splitArray(irList, 50).map(async (item) => {
          return (await getStatusList(item, userType)).resJson;
        });
        let res = await Promise.all(irListArray);
        let IRStatusList = [];
        res.forEach((item) => {
          let list = JSON.parse(item)?.resultList || [];
          IRStatusList = IRStatusList.concat(list);
        });
        let statusConfig = {
          0: "草稿",
          1: "待审核",
          3: "处理中",
          4: "已接纳",
          5: "不接纳",
          6: "已解决",
          9: "待验证",
          "-1": "已驳回",
        };
        if (IRStatusList?.length) {
          let statusMap = {};
          IRStatusList.forEach((item) => {

            let time = item.issueLatestClosedTime
              ? utcToLocal(item.issueLatestClosedTime)
              : "";
            let irType =
              item.issueType === 2
                ? "需求"
                : item.issueType === 4
                ? "咨询"
                : "BUG";
            let fileId = item.component?.componentName;
            const keywords = ['c++', 'native', 'napi', 'ndk','编译'];
            for (let keyword of keywords) {
              if (item.issueDesc.toLowerCase().includes(keyword.toLowerCase())) {
                fileId = "Native";
                  break; 
              }
          }
            let appName = item.project?.projectName;
            let maxUserType = item.maxUserType || 0;
            statusMap[item.issueID] = `${statusConfig[item.status]}${
              time
                ? `,${time},${utcToLocal(item.createTime)},${irType},${fileId},${appName},${maxUserType}`
                : `, ,${utcToLocal(item.createTime)},${irType},${fileId},${appName},${maxUserType}`
            }`;
          });
          updateStatus(statusMap);
          refreshFieldTeam()
        }
      } catch (error) {
        console.log(error);
      }
    };

    let refreshToken = () => {
      try {
        let param = {};
        customPost(
          param,
          "https://svc-drcn.developer.huawei.com/codeserver/oauth2/v1/refreshAccessToken"
        );
      } catch (error) {}
    };

    chrome.runtime.onMessage.addListener((request) => {
      if (request === "download") {
        if (
          Date.now() <
          new Date(
            `${new Date().toDateString()} ${SETTINGS.downloadTime}`
          ).getTime()
        ) {
          return;
        }
        init();
      }
      if (request === "refreshToken") {
        console.log('111')
        refreshToken();
        update();
      }
    });
  };
};
