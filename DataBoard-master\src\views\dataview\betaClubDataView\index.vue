<template>
  <div class="fut-container">
    <beta-chart></beta-chart>
    <statistics-table></statistics-table>
  </div>
</template>

<script lang="ts" setup>
import betaChart from './betaClubChart/chart.vue'
import statisticsTable from './statisticsTable/statisticsTable.vue'
</script>

<style scoped>
.fut-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
  width: 100%;
}
</style>
