import service from '@/utils/axios';
import { ewpService } from '@/utils/axios';
const baseUrl = '/shipTransfer';
export const getProblemList = (data) => {
  return service({
    url: `${baseUrl}/query`,
    method: 'post',
    data,
  });
};
export const addProblem = (data) => {
  return service({
    url: `${baseUrl}/add`,
    method: 'post',
    data,
  });
};

export const updateProblem = (data) => {
  return service({
    url: `${baseUrl}/update`,
    method: 'post',
    data,
  });
};

export const uploadExcel = (data) => {
  return service({
    url: `${baseUrl}/upload`,
    method: 'post',
    data,
  });
};
export const downloadTemplate = () => {
  return service({
    url: `${baseUrl}/downloadTemplate`,
    method: 'get',
    responseType: 'blob',
  });
};

export const createOutlookFile = (data) => {
  return service({
    url: `/email/create_outlook_file`,
    method: 'post',
    data,
  });
};

export const downloadExcel = (str) => {
  console.log(str);
  
  return service({
    url: str,
    method: 'get',
    responseType: 'blob'
  });
};

/**
 * 获取应用信息
 */
export async function getAppInfo(data): Promise<any> {
  try {
    const res = await ewpService.post('/management/appInfo/query', {
      pageNo: 1,
      pageSize: 1000,
      appName: data.applicationName || '',
      sortField: 'riskScore',
      sortOrder: 'desc',
      riskScoreGe: 0,
    });
    if (res.records.length > 0) {
      const appInfoList = res.records;
      const appInfo =
        appInfoList.filter(
          (v) => v.appName === data.applicationName && v.company === data.applicationSubject
        )[0] || {};
      return appInfo;
    }
    return {};
  } catch (error) {
    return {};
  }
}

/**
 * 查询四组一队邮箱
 */
export const getAddress = async (data) => {
  return service({
    url: '/shipTransfer/queryAppInfo',
    method: 'post',
    data,
  });
};
