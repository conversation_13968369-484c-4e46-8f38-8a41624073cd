import { defineMock } from '@alova/mock';
import { resultSuccess } from '../_util';
import { CodeDemoDto } from "@/api/system/code";

const result: CodeDemoDto[] = [
  {
    id: 0,
    demoName: 'demo',
    demoDescription: 'demo',
    demoLink: 'https://developer.huawei.com/consumer/cn/',
    demoSubmitter: '大郎',
    lineNumber: 1999,
    currentHandler: '大郎',
    reviewSuggestions: 'demo',
    reviewStatus: 2,
    createTime: 'demo',
    updateTime: 'demo',
    reviewTime: 'demo',
    deleted: 0,
  },
  {
    id: 1,
    demoName: 'demo',
    demoDescription: 'demo',
    demoLink: 'https://developer.huawei.com/consumer/cn/',
    demoSubmitter: '小白',
    lineNumber: 777,
    currentHandler: '小白',
    reviewSuggestions: 'demo',
    reviewStatus: 3,
    createTime: 'demo',
    updateTime: 'demo',
    reviewTime: 'demo',
    deleted: 0,
  },
]

export default defineMock({
  '/api/code/list': () => {
    return resultSuccess({
      data: result,
      pageInfo: {
        total: result.length
      }
    });
  },
});
