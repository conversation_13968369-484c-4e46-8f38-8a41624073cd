<template>
  <div>
    <n-grid :x-gap="24" :cols="2">
      <!-- 左侧树形分类 -->
      <n-grid-item>
        <n-card title="故障分类" :bordered="false">
          <template #header-extra>
            <n-button type="primary" @click="handleAddRootCategory">
              <template #icon>
                <n-icon><PlusOutlined /></n-icon>
              </template>
              新增一级分类
            </n-button>
          </template>

          <x-n-tree
            :data="categoryTreeData"
            :expandedKeys="expandedKeys"
            :selectedKeys="selectedKeys"
            block-line
            selectable
            @update:selected-keys="handleSelect"
            @update:expanded-keys="handleExpand"
          >
            <template #render-label="{ option }">
              <div class="tree-node">
                <div class="node-info">
                  <n-tag size="small" :bordered="false" type="info" class="level-tag">
                    {{ getNodeLevelLabel(option) }}
                  </n-tag>
                  <span class="node-label">{{ option.label }}</span>
                </div>
                <div class="node-actions">
                  <n-button-group size="small">
                    <n-button
                      v-if="option.level < 3"
                      secondary
                      size="tiny"
                      type="primary"
                      @click.stop="handleAddChild(option)"
                    >
                      <template #icon>
                        <n-icon><PlusOutlined /></n-icon>
                      </template>
                      新增
                    </n-button>
                    <n-button secondary size="tiny" type="info" @click.stop="handleEdit(option)">
                      <template #icon>
                        <n-icon><EditOutlined /></n-icon>
                      </template>
                      编辑
                    </n-button>
                    <n-button secondary size="tiny" type="error" @click.stop="handleDelete(option)">
                      <template #icon>
                        <n-icon><DeleteOutlined /></n-icon>
                      </template>
                      删除
                    </n-button>
                  </n-button-group>
                </div>
              </div>
            </template>
          </x-n-tree>
        </n-card>
      </n-grid-item>

      <!-- 右侧详情/编辑 -->
      <n-grid-item>
        <n-card :title="formTitle" :bordered="false" v-if="selectedNode">
          <n-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-placement="left"
            label-width="100"
          >
            <n-form-item label="分类名称" path="name">
              <n-input v-model:value="formData.name" placeholder="请输入分类名称" />
            </n-form-item>

            <n-form-item label="分类描述">
              <n-input v-model:value="formData.description" placeholder="请输入分类描述" />
            </n-form-item>

            <!-- <n-form-item label="排序序号" path="sort">
            <n-input-number v-model:value="formData.sort" placeholder="请输入排序序号" />
          </n-form-item> -->

            <div class="flex justify-end">
              <n-space>
                <n-button @click="resetForm">重置</n-button>
                <n-button type="primary" @click="handleSubmit">保存</n-button>
              </n-space>
            </div>
          </n-form>
        </n-card>
      </n-grid-item>
    </n-grid>

    <!-- 新增分类弹窗 -->
    <n-modal
      v-model:show="showModal"
      title="新增子类"
      preset="dialog"
      :positive-text="'确认'"
      :negative-text="'取消'"
      @positive-click="handleModalConfirm"
      @negative-click="closeModal"
    >
      <n-form
        ref="modalFormRef"
        :model="modalFormData"
        :rules="rules"
        label-placement="left"
        label-width="100"
      >
        <n-form-item label="分类名称" path="name">
          <n-input v-model:value="modalFormData.name" placeholder="请输入分类名称" />
        </n-form-item>

        <n-form-item label="分类描述">
          <n-input v-model:value="modalFormData.description" placeholder="请输入分类描述" />
        </n-form-item>
      </n-form>
    </n-modal>
  </div>
</template>
<script lang="ts">
  export default {
    name: 'MategoryManage',
  };
</script>
<script setup lang="ts">
  import { ref, reactive, computed } from 'vue';
  import { useMessage, useDialog } from 'naive-ui';
  import { PlusOutlined, EditOutlined, DeleteOutlined } from '@vicons/antd';
  import { onMounted } from 'vue-demi';
  import {
    getCategoryTreeApi,
    addCategoryApi,
    updateCategoryApi,
    deleteCategoryApi,
    transformToTreeData,
    transformToApiData,
    type Category,
  } from '@/api/dataview/faultScenes';

  const message = useMessage();
  const dialog = useDialog();

  // 修改类型定义
  interface TreeNode {
    key: string;
    description: string;
    label: string;
    level: number;
    sort: number;
    parentId: string | null;
    children?: TreeNode[];
  }

  // 树形数据
  const categoryTreeData = ref<TreeNode[]>([]);
  const expandedKeys = ref<string[]>([]);
  const selectedKeys = ref<string[]>([]);
  let modalFormRef = ref();
  let formRef = ref();

  // 表单数据
  const selectedNode = ref<TreeNode | null>(null);
  const formTitle = computed(() => (selectedNode.value ? '编辑分类' : '分类详情'));

  const formData = reactive({
    name: '',
    description: '',
    sort: 0,
  });

  // 表单校验规则
  const rules = {
    name: { required: true, message: '请输入分类名称', trigger: 'blur' },
    sort: { type: 'number', message: '请输入有效的排序号' },
  };

  // 弹窗相关
  const showModal = ref(false);
  const modalTitle = ref('');

  // 修改 modalFormData 的类型定义
  interface ModalFormData {
    name: string;
    description: string;
    parentId: string | null;
    level: number;
    sort: number;
  }

  // 修改 modalFormData 的初始化
  const modalFormData = reactive<ModalFormData>({
    name: '',
    description: '',
    parentId: null,
    level: 1,
    sort: 0,
  });
  function handleExpand(keys: string[]) {
    expandedKeys.value = keys;
  }
  // 加载分类树
  async function loadCategoryTree() {
    try {
      const data = await getCategoryTreeApi();
      console.log('data:', data);
      // 转换数据结构
      categoryTreeData.value = transformToTreeData(data);
      // 默认展开一级节点
      expandedKeys.value = data.map((item) => item._id);
    } catch (error) {
      message.error('获取分类数据失败');
    }
  }

  // 选择节点
  function handleSelect(keys: string[]) {
    selectedKeys.value = keys;
    const node = findNodeByKey(categoryTreeData.value, keys[0]);
    console.log('node:', node);
    if (node) {
      selectedNode.value = node;
      // 更新表单数据
      formData.name = node.label;
      formData.description = node.description;
      formData.sort = node.sort;
    }
  }

  // 新增根分类
  function handleAddRootCategory() {
    modalTitle.value = '新增一级分类';
    modalFormData.parentId = null;
    modalFormData.level = 1;
    showModal.value = true;
  }

  // 新增子分类
  function handleAddChild(node: TreeNode) {
    if (node.level >= 3) {
      message.warning('最多只能创建三级分类');
      return;
    }
    modalTitle.value = `新增${getNodeLevelLabel(node)}分类`;
    modalFormData.parentId = node.key;
    modalFormData.level = node.level + 1;
    showModal.value = true;
  }

  // 编辑分类
  function handleEdit(node: TreeNode) {
    selectedNode.value = node;
    formData.name = node.label;
    formData.description = node.description;
    formData.sort = node.sort;
  }

  // 删除分类
  async function handleDelete(node: TreeNode) {
    const hasChildren = node.children && node.children.length > 0;
    dialog.warning({
      title: '确认删除',
      content: hasChildren
        ? '该分类下包含子分类，删除后所有子分类将一并删除，是否继续？'
        : '确定要删除该分类吗？删除后将无法恢复！',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          await deleteCategoryApi(node.key);
          message.success('删除成功');
          selectedNode.value = null;
          selectedKeys.value = [];
          await loadCategoryTree();
        } catch (error) {
          message.error('删除失败');
        }
      },
    });
  }
  async function resetForm() {}
  // 提交表单
  async function handleSubmit() {
    try {
      await formRef.value?.validate();
      if (selectedNode.value) {
        await updateCategoryApi(selectedNode.value.key, {
          ...formData,
        });
        message.success('保存成功');
        await loadCategoryTree();
      }
    } catch (error) {
      message.error('保存失败');
    }
  }

  // 确认弹窗
  async function handleModalConfirm() {
    try {
      await modalFormRef.value?.validate();
      await addCategoryApi({
        name: modalFormData.name,
        description: modalFormData.description,
        parentId: modalFormData.parentId,
        level: modalFormData.level,
        sort: modalFormData.sort,
      });
      message.success('新增成功');
      closeModal();
      await loadCategoryTree();

      // 展开父节点
      if (modalFormData.parentId) {
        expandedKeys.value = Array.from(new Set([...expandedKeys.value, modalFormData.parentId]));
      }
    } catch (error) {
      message.error('新增失败');
    }
  }

  // 获取节点层级
  function getNodeLevel(node: TreeNode): string {
    const levelMap = ['一', '二', '三'];
    return levelMap[node.level - 1] || String(node.level);
  }

  // 获取节点层级标签
  function getNodeLevelLabel(node: TreeNode): string {
    return `${getNodeLevel(node)}级`;
  }

  // 查找节点
  function findNodeByKey(nodes: TreeNode[], key: string): TreeNode | null {
    for (const node of nodes) {
      if (node.key === key) {
        return node;
      }
      if (node.children) {
        const found = findNodeByKey(node.children, key);
        if (found) return found;
      }
    }
    return null;
  }

  // 关闭弹窗
  function closeModal() {
    showModal.value = false;
    Object.assign(modalFormData, {
      name: '',
      description: '',
      parentId: null,
      level: 1,
      sort: 0,
    });
  }

  onMounted(() => {
    loadCategoryTree();
  });
</script>

<style scoped>
  .tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 8px;
    width: 100%;
    min-height: 32px;
    border-radius: 4px;
    transition: background-color 0.3s;
  }

  .node-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
  }

  .node-actions {
    opacity: 0;
    transition: opacity 0.3s;
  }

  .tree-node:hover .node-actions {
    opacity: 1;
  }

  .tree-node:hover {
    /* background-color: rgba(0, 0, 0, 0.04); */
  }

  .level-tag {
    font-size: 12px;
  }

  .node-label {
    font-size: 14px;
  }

  .tree-node .n-space {
    /* display: none; */
  }

  .n-card {
    height: calc(100vh - 180px);
    overflow: auto;
  }

  .n-tree {
    margin: 8px 0;
  }

  .operation-buttons {
    display: flex;
    gap: 8px;
  }

  .form-footer {
    margin-top: 24px;
    text-align: right;
  }

  .n-button-group {
    display: flex;
    gap: 2px;
  }

  .n-button-group .n-button {
    padding: 2px 8px;
    min-width: 24px;
    height: 24px;
  }
</style>
