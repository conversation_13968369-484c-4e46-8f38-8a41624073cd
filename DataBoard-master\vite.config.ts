import type { UserConfig, ConfigEnv } from 'vite';
import { loadEnv } from 'vite';
import { resolve } from 'path';
import { wrapperEnv } from './build/utils';
import { createVitePlugins } from './build/vite/plugin';
import { OUTPUT_DIR } from './build/constant';
import { createProxy } from './build/vite/proxy';
import pkg from './package.json';
import { format } from 'date-fns';
const { dependencies, devDependencies, name, version } = pkg;

const __APP_INFO__ = {
  pkg: { dependencies, devDependencies, name, version },
  lastBuildTime: format(new Date(), 'yyyy-MM-dd HH:mm:ss'),
};

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir);
}

export default ({ command, mode }: ConfigEnv): UserConfig => {
  const root = process.cwd();
  const env = loadEnv(mode, root);
  const viteEnv = wrapperEnv(env);
  const { VITE_PUBLIC_PATH, VITE_PORT, VITE_PROXY } = viteEnv;
  const isBuild = command === 'build';
  return {
    base: VITE_PUBLIC_PATH,
    esbuild: {},
    resolve: {
      alias: [
        {
          find: /\/#\//,
          replacement: pathResolve('types') + '/',
        },
        {
          find: '@',
          replacement: pathResolve('src') + '/',
        },
      ],
      dedupe: ['vue'],
    },
    plugins: createVitePlugins(viteEnv, isBuild),
    define: {
      __APP_ENV__: JSON.stringify(env.APP_ENV),
      __APP_INFO__: JSON.stringify(__APP_INFO__),
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false,
    },
    server: {
      host: true,
      port: VITE_PORT,
      proxy: {
        '/board': {
          // target:'http://*************:8082',//吴天宇
          // target: 'http://***********:8082',//林泽浩
          target: 'http://***********', // 目标服务器
          // target: 'http://************:8082', //杨昊琪
          // target: 'http://*************:8080', // 刘晓龙
          changeOrigin: true, // 推荐开启
          // rewrite: (path) => path.replace(/^\/ewp/, ''), // 可选，重写路径
        },
        '/ewp': {
          // target: 'http://*************:8881', // 王志伟
          // target: 'http://***********:8084', // 目标服务器
          target: 'http://************:8881', // yinxianda
          // target: 'http://**************:8881', // jinshengri
          // target: 'http://************:8881', // huangxingping
          // target: 'http://*************:8881', // 李博文
          // target: 'http://************:8881', //石燚磊
          // target: 'http://************:8881',//潘舒华
          // target: 'http://localhost:8881',//自己
          changeOrigin: true, // 推荐开启
          // rewrite: (path) => path.replace(/^\/ewp/, ''), // 可选，重写路径
        },
        '/admin': {
          target: 'http://***********:6060', // 目标服务器
          // target: 'http://**************:6060', // shenli
          changeOrigin: true, // 推荐开启
          // rewrite: (path) => path.replace(/^\/rest/, ''), // 可选，重写路径
        },
        // 知识平台
        '/knowledge-admin': {
          target: 'http://***********:80', // 目标服务器
          changeOrigin: true, // 推荐开启
        },
      },
    },
    optimizeDeps: {
      include: [],
      exclude: ['vue-demi'],
    },
    build: {
      target: 'es2015',
      cssTarget: 'chrome80',
      outDir: OUTPUT_DIR,
      reportCompressedSize: false,
      chunkSizeWarningLimit: 2000,
    },
  };
};
