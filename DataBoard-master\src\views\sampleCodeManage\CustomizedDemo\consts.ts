import { CodeWishReviewStatusEnum } from "@/enums/CodeWishReviewStatusEnum";
import { FormItemRule, FormRules, SelectOption } from "naive-ui";

/** 不展示当前处理人的状态 */
export const NO_CURRENT_HANDLER_REVIEW_STATUS: CodeWishReviewStatusEnum[] = [
  CodeWishReviewStatusEnum.TO_BE_ACCEPTED,
  CodeWishReviewStatusEnum.REJECTED,
  // CodeWishReviewStatusEnum.PASSED,
];
export const CODE_WISH_LIST_FORM_RULES: FormRules = {
  demoName: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入样例代码demo名称',
    }
  ],
  demoDescription: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入样例代码心愿描述',
    }
  ],
  source: [
    {
      required: true,
      trigger: ['blur', 'select'],
      message: '请选择样例代码来源',
    }
  ],
  field: [
    {
      required: true,
      trigger: ['blur', 'select'],
      message: '请选择领域',
    }
  ],
  demoLink: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入样例代码链接',
    }
  ],
  demoAuthor: [
  ],
  currentHandler: [
    {
      required: true,
      trigger: ['blur', 'input'],
      validator(rule: FormItemRule, value: string) {
        if (!value) {
          return new Error('请输入处理人');
        }
        return true;
      },
    }
  ],
  expectedTime: [
    {
      required: true,
      trigger: ['blur', 'input'],
      validator(rule: FormItemRule, value: string) {
        if (!value) {
          return new Error('请输入期望交付时间');
        }
        return true;
      },
    }
  ],
  reviewTime: [
    {
      required: true,
      trigger: ['blur', 'input'],
      validator(rule: FormItemRule, value: string) {
        if (!value) {
          return new Error('请输入评审时间');
        }
        return true;
      },
    }
  ],
  promisedTime: [
    {
      required: true,
      trigger: ['blur', 'input'],
      validator(rule: FormItemRule, value: string) {
        if (!value) {
          return new Error('请输入承诺交付时间');
        }
        return true;
      },
    }
  ],
  requirementReviewSuggestions: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入评审意见',
    }
  ],
  reviewSuggestions: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入评审意见',
    }
  ],
  reviewStatus: [
    {
      required: true,
      trigger: ['blur', 'input'],
      validator(rule: FormItemRule, value: number) {
        if (!value) {
          return new Error('请选择评审状态');
        }
        return true;
      },
    }
  ],
  reviewers: [
    {
      required: true,
      trigger: ['blur', 'input'],
      validator(rule: FormItemRule, value: string[]) {
        if (!value.length) {
          return new Error('请选择评审人');
        }
        return true;
      },
    }
  ],
  deleteReason: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入删除原因',
    }
  ],
  linkDemoId: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入样例代码ID',
    }
  ],
  score: [
    {
      required: true,
      trigger: ['blur', 'input'],
      validator(rule: FormItemRule, value: string) {
        const num = Number(value);
        if (!value) {
          return new Error('请输入分数');
        } else if (!(/^\d{1,3}$/.test(value) && num >= 1 && num <= 100)) {
          return new Error('请输入1-100之间的正整数');
        }
        return true;
      },
    }
  ],
  outGoingPerson: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请选择代码外发责任人',
    }
  ],
  externalLink: [
    {
      required: true,
      trigger: ['blur', 'input'],
      message: '请输入托管链接',
    }
  ],
};

export const SOURCE_LIST = [
  {
    label: '【IR】',
    value: '【IR】',
  },
  {
    label: '【论坛】',
    value: '【论坛】',
  },
  {
    label: '【知识沉淀】',
    value: '【知识沉淀】',
  },
  {
    label: '【gitee】',
    value: '【gitee】',
  },
  {
    label: '【官网示例代码】',
    value: '【官网示例代码】',
  },
];
export const FIRED_LIST_MAP = {
  'ArkUI': ['葛颖森/g00934689', '张伟隆/z00886422', '李克武/l00935680'],
  '应用框架': ['刘中宇/l30045753', '唐繁/t00533404', '石桂娟/s00935627'],
  'Web': ['王小云/w30043405', '王校宏/w00660686', '马久晟/m30021254'],
  '应用服务': ['邵宝/s30029748', '张高驰/z00605025', '梁潇/l30039370'],
  'Native': ['施博洋/s00573969', '曹健/c00564722', '钟亦友/z00806601'],
  '系统': ['廉成洋/l00446994', '杨昊棋/y30026430', '刘吉岭/l30033353'],
  '媒体/图形': ['徐志超/x00503403', '朱维俊/z30047898', '谭俊/t00453994'],
  'Flutter': ['郭浩然/g00823974', '陈鑫科/c30049167', '王志文/w30049765'],
  '三方开闭源库': ['房智睿/f00928641', '左江江/z00611962', '周俊星/z00588433'],
  'ReactNative': ['周超/z00568055', '	魏素芳/w30047907', '徐瀚/x30027354'],
  'AI': ['刘中宇/l30045753', '喻慧宇/y30047897', '卢艳弟/l00828132'],
  'DFX': ['邵宝/s30029748', '	黄幸平/h30043518', '戴行健/d00624191'],
  '元服务': ['王小云/w30043405', '王宁/w00425655', '汪中科/w00456280'],
  'IDE': ['邵宝/s30029748', '黄幸平/h30043518', '汪中科/w00456280'],
  '三方框架其他': ['徐志超/x00503403', '魏素芳/w30047907', '周俊星/z00588433'],
  '华为应用': ['施博洋/s00573969', '王宁/w00425655', '周俊星/z00588433'],
  'Wearable': ['Bilal Basboz/b00931491','Cagatay Kizildag/c00262224','Dogan Burak Ziyanak/d00741683','Bunyamin Eymen Alagoz/bwx1232948','Fatih Muti/fwx1322646','Hasan Kaya/h00650466','Mucahid Kincir/mwx1273679','Sinan Yilmaz/s00935431','Taha Enes Kon/t00932960','Zulfu Balkan/z00930805'],
  'Atomic Service': ['Bilal Basboz/b00931491','Cagatay Kizildag/c00262224','Dogan Burak Ziyanak/d00741683','Bunyamin Eymen Alagoz/bwx1232948','Fatih Muti/fwx1322646','Hasan Kaya/h00650466','Mucahid Kincir/mwx1273679','Sinan Yilmaz/s00935431','Taha Enes Kon/t00932960','Zulfu Balkan/z00930805'],
}

export const SOURCE_LIST_SAMPLE_OUTGOING = [
  {
    label: '【内部规划】',
    value: '【内部规划】',
  },
  {
    label: '【开发者声音】',
    value: '【开发者声音】',
  },
];

export enum CodeSampleDrawerMode {
  ADD,
  EDIT,
  SHOW_ONLY,
}
// 定制demo状态对应的名称
export const REQUIREMENT_REVIEW_STATUS_MAP: Record<CodeWishReviewStatusEnum, string> = {
  [CodeWishReviewStatusEnum.UNDER_DEVELOPMENT]: '开发中',
  [CodeWishReviewStatusEnum.TO_BE_SUBMITTED]: '待提交',
  [CodeWishReviewStatusEnum.TO_BE_ACCEPTED]: '待接纳',
  [CodeWishReviewStatusEnum.TO_BE_REVIEWED]: '待评审',
  [CodeWishReviewStatusEnum.REJECTED]: '不接纳',
  // [CodeWishReviewStatusEnum.PASSED]:'已通过',
  [CodeWishReviewStatusEnum.TO_BE_CLAIMED]: '待认领',
  [CodeWishReviewStatusEnum.ABANDONED]: '已废弃',
  [CodeWishReviewStatusEnum.TO_BE_SENT_OUT]: '待外发',
  [CodeWishReviewStatusEnum.TO_BE_HOSTED]: '待托管',
  [CodeWishReviewStatusEnum.HOSTED]: '已托管',
}
export const TAG_OPTIONS: SelectOption[] = [];
// 全部评审状态
export const ALL_REVIEW_STATUS: CodeWishReviewStatusEnum[] = [
  CodeWishReviewStatusEnum.UNDER_DEVELOPMENT,
  CodeWishReviewStatusEnum.TO_BE_SUBMITTED,
  CodeWishReviewStatusEnum.TO_BE_ACCEPTED,
  CodeWishReviewStatusEnum.TO_BE_REVIEWED,
  CodeWishReviewStatusEnum.REJECTED,
  // CodeWishReviewStatusEnum.PASSED,
  CodeWishReviewStatusEnum.TO_BE_CLAIMED,
  CodeWishReviewStatusEnum.ABANDONED,
  CodeWishReviewStatusEnum.TO_BE_SENT_OUT,
  CodeWishReviewStatusEnum.TO_BE_HOSTED,
  CodeWishReviewStatusEnum.HOSTED,
];
// 当前可选的评审状态和对应的操作名称
export const NEXT_REVIEW_STATUS_MAP: Record<CodeWishReviewStatusEnum, SelectOption[]> = {
  [CodeWishReviewStatusEnum.UNDER_DEVELOPMENT]: [
    {
      value: CodeWishReviewStatusEnum.TO_BE_REVIEWED,
      label: '待验收',
    }
  ],
  [CodeWishReviewStatusEnum.TO_BE_SUBMITTED]: [
    {
      value: CodeWishReviewStatusEnum.TO_BE_ACCEPTED,
      label: '待接纳'
    }
  ],
  [CodeWishReviewStatusEnum.TO_BE_ACCEPTED]: [
    {
      value: CodeWishReviewStatusEnum.TO_BE_SUBMITTED,
      label: '驳回',
    },
    {
      value: CodeWishReviewStatusEnum.UNDER_DEVELOPMENT,
      label: '接纳'
    },
    {
      value: CodeWishReviewStatusEnum.REJECTED,
      label: '不接纳'
    },
  ],
  [CodeWishReviewStatusEnum.TO_BE_REVIEWED]: [
    {
      value: CodeWishReviewStatusEnum.PASSED,
      label: '通过'
    },
    {
      value: CodeWishReviewStatusEnum.UNDER_DEVELOPMENT,
      label: '不通过'
    },
    {
      value: CodeWishReviewStatusEnum.ABANDONED,
      label: '废弃'
    },
  ],
  [CodeWishReviewStatusEnum.REJECTED]: [],
  // [CodeWishReviewStatusEnum.PASSED]: [],
  [CodeWishReviewStatusEnum.TO_BE_CLAIMED]: [
    {
      value: CodeWishReviewStatusEnum.UNDER_DEVELOPMENT,
      label: '开发中'
    },
  ],
  [CodeWishReviewStatusEnum.ABANDONED]: [],
  [CodeWishReviewStatusEnum.TO_BE_SENT_OUT]: [],
  [CodeWishReviewStatusEnum.TO_BE_HOSTED]: [],
  [CodeWishReviewStatusEnum.HOSTED]: [],
};
export const DELIVER_TIPS: string[] = [
  '确认代码仓地址指向代码仓具体代码的位置',
  '确认Readme已添加动态图',
  '确认Readme动态图已加机框',
  '确认Readme描述的功能运行正常',
  '确认无信息安全风险',
]
