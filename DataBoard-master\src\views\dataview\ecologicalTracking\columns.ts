import { h, ref } from 'vue';
import {DataTableColumns, NPopconfirm, NTooltip, useMessage} from 'naive-ui';
import { NTag, NButton, NSpace, NPopover, NEllipsis } from 'naive-ui';
import { useEventBus } from '@/hooks/useEventBus';
import { BasicColumn } from '@/components/Table';
import { ListData } from '@/views/dataview/futAPPList/columns';
import { useUserStore } from '@/store/modules/user';
import {ewpService as service} from "@/utils/axios";

export type EcologicalRecord = {
  id: string;
  registrationDate: number | null;
  ewpOwner: string;
  defeatAttribution: string;
  representativeOffice: string;
  appName: string;
  defeatDescription: string;
  defeatLevel: string;
  source: string;
  opinionVolume: number;
  knowledgeId: string;
  dtsOrderId: string;
  orderStatus: string;
  sourceClosed: string;
  irOrderId: string;
  orderProgress: string;
  currentHandler: string;
  domainLeader: string;
  affectedProduct: string[];
  affectedProducts: string[];
  solutionPlanTime: number | null;
  dataSource: string;
  device: string | null;
  keyDefeat: string;
};

const statusColorMap = {
  // OPEN: 'error',
  // TRACKING: 'warning',
  // CLOSED: 'success',
  '待定界': 'error',
  '待锁定': 'warning',
  '待回归': 'info',
  '已闭环': 'success',
};

const levelColorMap = {
  '极高': 'error',
  '高': 'error',
  '中': 'warning',
  '低': 'info',
};

export const roles = ref([]);

async function getUserName() {
  try {
    const userInfo = await useUserStore().getInfo();
    roles.value = userInfo.roles.includes('5_admin');
    console.log(roles.value, 'roles');
    return roles;
  } catch (error) {
    console.error('Error:', error);
  }
}

const showButton = await getUserName()
console.log(showButton.value,'show')

const eventBus = useEventBus();
const message = useMessage();

export const columns: DataTableColumns<EcologicalRecord> = (loadData) => [
    {
      type: 'selection',
      disabled: (row) => row.orderStstus === '已闭环',
      multiple: true,
    },
    {
      title: '登记日期',
      key: 'registrationDate',
      width: 100,
      render: (row) => {
        if (!row.registrationDate) return '—';
        return new Date(row.registrationDate).toLocaleDateString('zh-CN');
      },
      sorter: true,
      resizable: true,
    },
    {
      title: 'EWP责任人',
      key: 'ewpOwner',
      width: 120,
      sorter: true,
      resizable: true,
    },
    {
      title: '问题归属',
      key: 'defeatAttribution',
      width: 100,
      resizable: true,
    },
    {
      title: '代表处',
      key: 'representativeOffice',
      width: 100,
      resizable: true,
    },
    {
      title: '应用名称',
      key: 'appName',
      width: 120,
      resizable: true,
    },
    {
      title: '问题描述',
      key: 'defeatDescription',
      width: 180,
      resizable: true,
      ellipsis: {
        tooltip: true,
        lineClamp: 2,
      },
      // render: (row) => {
      //   return h(
      //     NEllipsis,
      //     {
      //       style: 'max-width: 180px;',
      //       tooltip: true,
      //       lineClamp: 2,
      //     },
      //     { default: () => row.defeatDescription }
      //   );
      // },
    },
    {
      title: '问题级别',
      key: 'defeatLevel',
      width: 100,
      resizable: true,
      render: (row) => {
        return h(
          NTag,
          {
            type: (levelColorMap[row.defeatLevel] as any) || 'default',
          },
          { default: () => row.defeatLevel }
        );
      },
      sorter: 'default',
      filterOptions: [
        { label: '极高', value: '极高' },
        { label: '高', value: '高' },
        { label: '中', value: '中' },
        { label: '低', value: '低' },
      ],
      filter: (value, row) => row.defeatLevel === value,
    },
    {
      title: '来源',
      key: 'source',
      width: 140,
      resizable: true,
    },
    {
      title: '来源登记人',
      key: 'sourceOwner',
      width: 120,
      resizable: true,
      render: (row) => {
        if (!row.sourceOwner) return '—';
        return row.sourceOwner;
      }
    },
    {
      title: '声量',
      key: 'opinionVolume',
      width: 70,
      render: (row) => row.opinionVolume || 1,
      sorter: true,
      resizable: true,
    },
    {
      title: '知识ID',
      key: 'knowledgeId',
      width: 120,
      resizable: true,
    },
    {
      title: 'DTS单',
      key: 'dtsOrderId',
      width: 140,
      resizable: true,
      render: (row) => {
          if (!row.dtsOrderId) return '—';
          return h(
            'div',
            {
              style: {
                position: 'relative',
                display: 'inline-block',
              },
            },
            [
              // 问题单号标签
              h(
                NTag,
                {
                  type: !row.irOrderId ? 'warning' : 'info',
                  bordered: false,
                  onClick: async () => {
                    const orderId  = row.dtsOrderId;
                    window.open(
                      `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${orderId}`,
                      '_blank'
                    );
                  },
                },
                {
                  default: () => row.dtsOrderId,
                }
              ),
            ]
          );
        }
    },
    {
      title: 'DTS单状态',
      key: 'orderStatus',
      width: 120,
      resizable: true,
      render: (row) => {
        if (!row.orderStatus) return '—';
        return h(
          NTag,
          {
            type: (statusColorMap[row.orderStatus] as any) || 'default',
          },
          { default: () => row.orderStatus }
        );
      },
      sorter: 'default',
      filterOptions: [
        { label: '待定界', value: '待定界' },
        { label: '待锁定', value: '待锁定' },
        { label: '待回归', value: '待回归' },
        { label: '已闭环', value: '已闭环' },
        { label: '空', value: '-' },
      ],
      filter: (value, row) => row.orderStatus === value,
    },
    {
      title: 'IssueRreport单',
      key: 'irOrderId',
      width: 130,
      resizable: true,
      render: (row) => {
        if (!row.irOrderId) return '—';
        return  row.irOrderId
        // h(
        //   'a',
        //   {
        //     href: `https://issue.example.com/${row.irOrderId}`,
        //     target: '_blank',
        //   },
        // );
      },
    },
    {
      title: '当前处理人',
      key: 'currentHandler',
      width: 140,
      resizable: true,
    },
    {
      title: '领域主管',
      key: 'domainLeader',
      width: 140,
      resizable: true,
    },
    {
      title: '影响品类/产品',
      key: 'affectedProduct',
      width: 150,
      resizable: true,
      render: (row) => {
        if (!row.affectedProduct || !row.affectedProduct.length) return '—';

        return h(
          NSpace,
          { wrap: true },
          {
            default: () =>
              row.affectedProduct.split(',').map((product) => {
                const isPuraX = product === 'ALL';
                return h(
                  NTag,
                  {
                    type: isPuraX ? 'default' : 'success',
                    round: true,
                    size: 'small',
                  },
                  { default: () => product }
                );
              }),
          }
        );
      },
      filterOptions: [
        { label: 'Pura X', value: 'Pura X' },
        { label: 'nova 14', value: 'nova 14' },
        // { label: 'Mate X6', value: 'Mate X6' },
        // { label: 'Mate X5', value: 'Mate X5' },
        { label: 'ALL', value: 'ALL' },
      ],
      filter: (value, row) => {
        if (!row.affectedProduct) return false;
        return row.affectedProduct.includes(value as string);
      },
    },
    {
      title: '数据来源',
      key: 'dataSource',
      width: 120,
      resizable: true,
      render: (row) => {
        return row.dataSource ? h(
          NTag,
          {
            type: row.dataSource === '手动登记' ? 'default' : 'success',
          },
          { default: () => row.dataSource }
        ) : '——';
      },
      // filterOptions: [
      //   { label: '手动登记', value: 'manual' },
      //   { label: 'Pura X自动识别', value: 'auto' },
      // ],
      // filter: (value, row) => row.dataSource === value,
    },
    {
      title: '是否是关键问题',
      key: 'keyDefeat',
      width: 120,
      resizable: true,
      sorter: true,
    },
    {
      title: '来源是否闭环',
      key: 'sourceClosed',
      width: 110,
      resizable: true,
      fixed: 'right',
    },
    {
      title: '计划闭环时间',
      key: 'solutionPlanTime',
      width: 120,
      resizable: true,
      fixed: 'right',
      render: (row) => {
        if (!row.solutionPlanTime) return '—';
        return new Date(row.solutionPlanTime).toLocaleDateString('zh-CN');
      },
      sorter: true,
    },
/*    {
      title: '进展',
      key: 'orderProgress',
      width: 200,
      resizable: true,
      fixed: 'right',
      ellipsis: {
        tooltip: true,
        lineClamp: 2,
      },
      // render: (row) => {
      //   return h(
      //     NPopover,
      //     {
      //       trigger: 'hover',
      //       placement: 'top',
      //       style: { maxWidth: '400px', whiteSpace: 'pre-wrap' },
      //     },
      //     {
      //       trigger: () =>
      //         h(
      //           NEllipsis,
      //           {
      //             style: 'max-width: 180px;',
      //             lineClamp: 2,
      //           },
      //           { default: () => row.orderProgress || '—' }
      //         ),
      //       default: () => row.orderProgress || '—',
      //     }
      //   );
      // },
    },*/
  {
    title: () =>
      h(
        'div',
        { style: { display: 'flex', alignItems: 'center', gap: '6px', justifyContent: 'center', } },
        [
          h(
            NTooltip,
            { trigger: 'hover' },
            {
              trigger: () =>
                h('div', { style: { display: 'flex', alignItems: 'center' } }, [
                  '最新进展',
                  h(
                    'span',
                    { style: { marginLeft: '4px', cursor: 'pointer', color: '#1890ff' } },
                    '?'
                  ),
                ]),
              default: () => '双击编辑最新进展',
            }
          ),
        ]
      ),
    key: 'orderProgress',
    resizable: true,
    fixed: 'right',
    width: 200,
    ellipsis: {
      tooltip: true,
      lineClamp: 2,
    },
    render: (row) => h(
      'div',
      {
        style: {alignItems: 'center', gap: '6px', justifyContent: 'center'}
      },
      [
        row.orderProgress && row.orderProgress.length>0?
          h(
            NTooltip,
            { trigger: 'hover',maxWidth:'600px' },
            {
              trigger: () =>
                h('div', {
                  style: {  alignItems: 'center',whiteSpace:'nowrap',overflow: 'hidden',textOverflow:'ellipsis',width:'200px'},
                  onDblclick: ()=>{
                    eventBus.emit('showModel',row);
                  }
                }, [
                  row.orderProgress
                ]),
              default: () => row.orderProgress,
            }
          ): h('div',{style:{width:'200px',height:'50px'}, onDblclick: ()=>{
              eventBus.emit('showModel',row);
            }},()=>{})
      ]
    ),
  },
    {
      title: '操作',
      key: 'actions',
      width: 140,
      fixed: 'right',
      render: (row, index) => {
        return h(
          NSpace,
          { justify: 'start', space: 10 },
          {
            default: () => [
              showButton.value && h(
                NButton,
                {
                  size: 'small',
                  type: 'primary',
                  ghost: true,
                  width: 120,
                  onClick: () => {
                    eventBus.emit('openEditModal', { row, mode: 'ewp' });
                  },
                },
                { default: () => 'EWP责任人编辑' }
              ),
              h(
                NButton,
                {
                  size: 'small',
                  type: 'info',
                  ghost: true,
                  width: 120,
                  onClick: () => {
                    eventBus.emit('openEditModal', { row, mode: 'pm' });
                  },
                },
                { default: () => '应用PM编辑' }
              ),
              showButton.value && h(
                NPopconfirm,
                {
                  onPositiveClick: async () => {
                    await service.delete(`/management/special-item/order/${row.id}`);
                    // message.success('删除成功');
                    loadData();
                  },
                },
                {
                  trigger: () =>
                    h(
                      NButton,
                      {
                        size: 'small',
                        style: {
                          marginRight: '6px',
                        },
                        secondary: true,
                        type: 'error', // 添加红色样式
                      },
                      { default: () => '删除' }
                    ),
                  default: () => '确定要删除吗？',
                }
              ),
            ],
          }
        );
      },
    },
];
