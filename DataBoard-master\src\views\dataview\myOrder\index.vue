<template>
  <div class="app-container">
    <div class="filter-container">
      <n-card>
        <n-tabs type="line" animated @update:value="handleTabChange">
          <n-tab-pane name="order" tab="我的待办" />
          <n-tab-pane v-if="!isOnlyViewRole" name="severity" tab="严重致命单" />
          <n-tab-pane v-if="!isOnlyViewRole" name="vip" tab="VIP单" />
          <n-tab-pane v-if="!isOnlyViewRole" name="check" tab="问题审核" />
        </n-tabs>
        <n-form
          :model="searchForm"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          @submit.prevent="handleSearch"
        >
          <n-grid :cols="24" :x-gap="24">
            <n-grid-item v-for="item in visibleSearchFormItems" :key="item.field" :span="6">
              <n-form-item :label="item.label" :path="item.field">
                <template v-if="item.field === 'knowId'">
                  <n-input-group>
                    <n-input
                      v-model:value="searchForm.knowId"
                      :placeholder="'请输入知识id'"
                      clearable
                      @keyup.enter="handleSearch"
                    />
                    <n-select
                      v-model:value="searchForm.hasKnowId"
                      :options="[
                        { label: '有知识id', value: 'notempty' },
                        { label: '无知识id', value: 'empty' },
                      ]"
                      style="width: 100px"
                      @update:value="handleKnowIdFilterChange"
                      clearable
                    />
                  </n-input-group>
                </template>
                <template v-else-if="item.field === 'deviceType'">
                  <n-select
                    v-model:value="searchForm['deviceTypeList']"
                    filterable
                    placeholder="搜索机型"
                    :options="deviceOptions"
                    :loading="deviceLoading"
                    clearable
                    multiple
                    remote
                    @search="handleSearchDevice"
                    @focus="handleSearchDevice('')"
                  />
                </template>
                <template v-else-if="item.field === 'weightValueRange'">
                  <n-input-group>
                    <n-input-number
                      v-model:value="searchForm.weightValueMin"
                      placeholder="最小值"
                      clearable
                      :min="0.3"
                      :max="3"
                      :step="0.1"
                      :precision="1"
                      style="width: 120px"
                    />
                    <n-input-group-label>-</n-input-group-label>
                    <n-input-number
                      v-model:value="searchForm.weightValueMax"
                      placeholder="最大值"
                      clearable
                      :min="0.3"
                      :max="3"
                      :step="0.1"
                      :precision="1"
                      style="width: 120px"
                    />
                  </n-input-group>
                </template>
                <n-input
                  clearable
                  v-else-if="item.component === 'Input'"
                  v-model:value="searchForm[item.field]"
                  @keyup.enter="handleSearch"
                />
                <n-select
                  clearable
                  v-else-if="item.component === 'Select'"
                  v-model:value="searchForm[item.field]"
                  :options="item.componentProps.options"
                  :filterable="item.componentProps.filterable"
                  :multiple="item.componentProps.multiple"
                />
                <n-date-picker
                  v-else-if="item.component === 'DateRangePicker'"
                  v-model:value="searchForm[item.field]"
                  type="daterange"
                  clearable
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>
          <div class="form-actions">
            <n-space>
              <n-button @click="resetForm">重置</n-button>
              <n-button type="primary" attr-type="submit">查询</n-button>
              <n-button @click="toggleExpandForm">
                {{ isExpanded ? '收起' : '展开' }}
                <template #icon>
                  <n-icon>
                    <chevron-down v-if="!isExpanded" />
                    <chevron-up v-else />
                  </n-icon>
                </template>
              </n-button>
            </n-space>
          </div>
        </n-form>
      </n-card>
    </div>

    <n-card style="margin-top: 8px">
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          margin-top: -9px;
        "
      >
        <n-space align="center" v-if="!isOnlyViewRole">
          <n-upload accept=".xlsx, .xls" :custom-request="handleFileUpload" :show-file-list="false">
            <!-- <n-button secondary :loading="isImporting">
              {{ isImporting ? '导入中...' : '导入 Excel' }}
            </n-button> -->
          </n-upload>
          <!-- <n-upload
            accept=".xlsx, .xls"
            :custom-request="handleIRFileUpload"
            :show-file-list="false"
          >
            <n-button secondary :loading="isIRImporting">
              {{ isIRImporting ? '导入中...' : '导入IR单Excel' }}
            </n-button>
          </n-upload> -->
          <n-button type="error" secondary :loading="exportLoading" @click="handleExport">
            导出Excel
          </n-button>
          <n-button type="success" :disabled="selectedRows.length === 0" @click="generateTemplates">
            生成通报模板 ({{ selectedRows.length }})
          </n-button>
          <n-popconfirm @positive-click="exportToCSV">
            <template #trigger>
              <n-button type="info" :disabled="getValidRowsToCsv.length === 0">
                生成表格模版 ({{ getValidRowsToCsv.length }})
              </n-button>
            </template>
            将会自动选取DTS单状态为CCB方案审核的问题单
          </n-popconfirm>
          <n-popconfirm @positive-click="handleBatchCreateIR">
            <template #trigger>
              <n-button type="warning" :disabled="getValidRows.length === 0">
                一键提单 ({{ getValidRows.length }})
              </n-button>
            </template>
            将会自动过滤掉已提IR和状态为非CCB方案审核的问题单
          </n-popconfirm>
          <!-- <n-button type="success" @click="disOrder" :loading="distributeLoading">
            一键分单
          </n-button> -->
          <!--          <n-popconfirm @positive-click="handleDTSTurnTo">
            <template #trigger>
              <n-button type="warning"> 批量转单({{ getTurnDTSNum.length }})</n-button>
            </template>
            <n-select
              v-model:value="searchForm.personNo"
              placeholder="请选择要转单的人"
              :options="dtsNameAccountOptions"
              style="width: 200px"
              clearable
            />
          </n-popconfirm>-->
          <!-- <n-button
            type="error"
            :disabled="selectedRows.length === 0"
            @click="disCCB"
            :loading="CCBLoading"
          >
            一键CCB ({{ selectedRows.length }})</n-button
          > -->
        </n-space>
        <div v-else></div>
        <n-space>
          <n-space v-if="!isOnlyViewRole">
            <n-select
              v-model:value="searchParam"
              @update:value="selectSearchParam"
              style="width: 200px"
              :options="selectParamOptions"
              :render-label="saveParamSelectRender"
              placeholder="选择保存的筛选条件"
            />
            <n-button @click="openSaveParam">保存筛选条件</n-button>
          </n-space>
          <n-switch v-model:value="isCleanMode" :rail-style="railStyle" style="padding-top: 6px">
            <template #checked> 清爽模式</template>
            <template #unchecked> 完整模式</template>
          </n-switch>
          <n-switch
            v-model:value="onlyMine"
            @update:value="handleOnlyMineChange"
            style="padding-top: 6px"
            v-if="!isOnlyViewRole"
          >
            <template #checked> 只看我</template>
            <template #unchecked> 全部</template>
          </n-switch>
          <n-switch
            v-model:value="isCheckState"
            :rail-style="railStyle"
            v-if="tabCheckState && !isOnlyViewRole"
            style="padding-top: 6px"
          >
            <template #checked> 问题审核 </template>
            <template #unchecked> 详细审核 </template>
          </n-switch>
          <n-icon size="18" class="setting-icon" @click="openColumnDrawer">
            <settings />
          </n-icon>
        </n-space>
      </div>

      <n-data-table
        remote
        :bordered="false"
        :single-line="false"
        striped
        @update:sorter="handleSorterChange"
        @update:filters="handleFiltersChange"
        :columns="columns"
        :data="tableData"
        :pagination="paginationReactive"
        :loading="loading"
        :scroll-x="tableWidth"
        :row-key="(row) => row.id"
        :checked-row-keys="checkedRowKeys"
        @update:checked-row-keys="handleCheckedRowKeysChange"
        :max-height="435"
      />
    </n-card>

    <n-modal v-model:show="showEditModal" preset="card" title="编辑工单" style="width: 600px">
      <n-form
        :model="editingWorkOrder"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="问题单号" path="orderId">
          <n-input v-model:value="editingWorkOrder.orderId" disabled />
        </n-form-item>
        <n-form-item label="应用名称" path="appName">
          <n-input v-model:value="editingWorkOrder.appName" />
        </n-form-item>
        <n-form-item label="问题单描述" path="description">
          <n-input v-model:value="editingWorkOrder.description" type="textarea" />
        </n-form-item>
        <n-form-item label="应用责任人" path="ewpOwner">
          <n-select
            v-model:value="editingWorkOrder.ewpOwner"
            placeholder="请选择应用责任人"
            :options="employeesOptions"
            clearable
            disabled
          />
        </n-form-item>
        <n-form-item label="严重程度" path="severity">
          <n-select
            disabled
            v-model:value="editingWorkOrder.severity"
            :options="
              searchFormItems.find((item) => item.field === 'severity')?.componentProps.options
            "
          />
        </n-form-item>
        <n-form-item label="DTS单状态" path="status">
          <n-select
            disabled
            v-model:value="editingWorkOrder.status"
            :options="
              searchFormItems.find((item) => item.field === 'status')?.componentProps.options
            "
          />
        </n-form-item>
        <n-form-item label="当前处理人" path="currentHandler">
          <n-input v-model:value="editingWorkOrder.currentHandler" disabled />
        </n-form-item>
        <n-form-item label="关联IR单号" path="irOrderId">
          <n-input v-model:value="editingWorkOrder.irOrderId" />
        </n-form-item>
        <n-form-item label="IR单状态" path="irOrderStatus">
          <n-select
            v-model:value="editingWorkOrder.irOrderStatus"
            :options="
              searchFormItems.find((item) => item.field === 'irOrderStatus')?.componentProps.options
            "
          />
        </n-form-item>
        <!-- <n-form-item label="异常状态" path="errorStatus">
          <n-select
            v-model:value="editingWorkOrder.errorStatus"
            :options="
              searchFormItems.find((item) => item.field === 'errorStatus')?.componentProps.options
            "
          />
        </n-form-item>
        <n-form-item label="异常状态细分" path="errorStatusSubdivision">
          <n-select
            v-model:value="editingWorkOrder.errorStatusSubdivision"
            :options="
              searchFormItems.find((item) => item.field === 'errorStatusSubdivision')
                ?.componentProps.options
            "
          />
        </n-form-item> -->
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showEditModal = false">取消</n-button>
          <n-button type="primary" @click="saveEdit">保存</n-button>
        </n-space>
      </template>
    </n-modal>

    <n-modal
      v-model:show="showEditDelimit"
      preset="card"
      title="快捷定界"
      style="width: 800px"
      :on-close="clearForm"
    >
      <n-form
        :model="selectedDelimit"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        :rules="rulesSence"
      >
        <n-card class="description-card" size="small">
          <template #header>
            <n-space align="center">
              <n-icon>
                <document-text />
              </n-icon>
              <span>问题描述</span>
            </n-space>
          </template>
          <div class="description-content">
            {{ delimitTitle?.split('】').pop() }}
          </div>
        </n-card>

        <n-form-item label="匹配结果" path="senceIsHit" class="match-result">
          <n-radio-group v-model:value="selectedDelimit.senceIsHit">
            <n-space>
              <n-radio v-for="option in delimitOptions" :key="option.value" :value="option.value">
                {{ option.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>

        <n-form-item
          path="selectedSence"
          class="scene-tree"
          v-if="selectedDelimit.senceIsHit === '3' || selectedDelimit.senceIsHit === '4'"
        >
          <n-card
            size="small"
            :title="selectedDelimit.senceIsHit === '4' ? '模块选择' : '场景选择'"
            class="scene-card"
          >
            <n-scrollbar style="max-height: 400px">
              <n-space vertical size="small">
                <!-- 故障场景命中时显示 -->
                <template v-if="selectedDelimit.senceIsHit === '3'">
                  <n-radio-group v-model:value="ordioValue" class="scene-radio">
                    <div class="scene-content" style="width: 100%; display: block">
                      <n-radio
                        v-for="(scene, index) in DelimitOneData"
                        :key="index"
                        :value="scene.title"
                        @click="handleScene(scene)"
                      >
                        <div class="scene-category">
                          <div style="margin-bottom: 4px">
                            <n-tag :bordered="false" type="success" size="small">
                              场景 {{ index + 1 }}
                            </n-tag>
                            <span
                              style="margin-left: 10px; font-weight: 700; font-size: 15px"
                              class="category-title"
                            >
                              {{ scene.title }}
                            </span>
                          </div>
                          <div>
                            {{ scene.desc }}
                          </div>
                        </div>
                        <n-divider
                          v-if="index !== DelimitOneData.length - 1"
                          style="margin: 8px 0"
                        />
                      </n-radio>
                    </div>
                  </n-radio-group>
                </template>

                <!-- 未命中时显示模块选择 -->
                <template v-if="selectedDelimit.senceIsHit === '4'">
                  <div class="module-cards">
                    <div
                      v-for="option in moduleOptions"
                      :key="option.value"
                      class="module-card"
                      :class="{ active: moduleValue === option.value }"
                      @click="moduleValue = option.value"
                    >
                      <div class="icon-wrapper" :style="{ backgroundColor: option.color + '15' }">
                        <n-icon :color="option.color" size="24">
                          <component :is="option.icon" />
                        </n-icon>
                      </div>
                      <div class="card-content">
                        <div class="card-title">{{ option.label }}</div>
                        <div class="card-desc">{{ option.desc }}</div>
                      </div>
                      <div class="check-icon" v-if="moduleValue === option.value">
                        <n-icon color="#36ad6a">
                          <checkmark-circle-outline />
                        </n-icon>
                      </div>
                    </div>
                  </div>
                </template>
              </n-space>
            </n-scrollbar>
          </n-card>
        </n-form-item>
      </n-form>

      <template #footer>
        <n-space justify="end">
          <n-button @click="handleCancel">取消</n-button>
          <n-button type="primary" @click="submitDelimit" :disabled="!canSubmitDelimit"
            >下一步
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <n-modal
      v-model:show="showHitDelimit"
      preset="card"
      :title="showWindowTitle"
      style="width: 800px"
      :on-close="clearForm"
    >
      <n-form
        :model="DelimitDataValue"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <!-- 问题描述卡片 -->
        <n-card class="description-card" size="small">
          <template #header>
            <n-space align="center">
              <n-icon>
                <document-text />
              </n-icon>
              <span>问题描述</span>
            </n-space>
          </template>
          <div class="description-content">
            {{ delimitTitle?.split('】').pop() }}
          </div>
        </n-card>

        <!-- 模块和原因分析 -->
        <n-space vertical size="large" style="margin-top: 16px">
          <n-form-item label="模块：" class="module-section">
            <n-tag :bordered="false" type="success" size="medium" round class="module-tag">
              <template #icon>
                <n-icon>
                  <cube-outline />
                </n-icon>
              </template>
              {{ DelimitDataValue.module }}
            </n-tag>
          </n-form-item>

          <n-form-item>
            <template #label>
              <n-space>
                <span>原因分析：</span>
              </n-space>
            </template>
            <div
              v-html="DelimitDataValue.result"
              contenteditable
              class="analysis-content"
              @input="updateContent"
            >
            </div>
          </n-form-item>
        </n-space>
      </n-form>

      <template #footer>
        <n-space justify="end">
          <n-button @click="handleBackToDelimit">
            <template #icon>
              <n-icon>
                <arrow-back />
              </n-icon>
            </template>
            上一步
          </n-button>
          <n-button
            type="primary"
            @click="submitDelimitData(DelimitDataValue.result, DelimitDataValue.module)"
          >
            <template #icon>
              <n-icon>
                <checkmark-circle />
              </n-icon>
            </template>
            一键CCB
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <n-modal
      v-model:show="showNoHitDelimit"
      preset="card"
      title="场景缺失"
      style="width: 800px"
      :on-close="clearForm"
    >
      <n-form label-placement="left" label-width="auto" require-mark-placement="right-hanging">
        <n-form-item label="选择模块：" class="bold-font">
          <div class="module-cards">
            <div
              v-for="option in moduleOptions"
              :key="option.value"
              class="module-card"
              :class="{ active: moduleValue === option.value }"
              @click="moduleValue = option.value"
            >
              <div class="icon-wrapper" :style="{ backgroundColor: option.color + '15' }">
                <n-icon :color="option.color" size="24">
                  <component :is="option.icon" />
                </n-icon>
              </div>
              <div class="card-content">
                <div class="card-title">{{ option.label }}</div>
                <div class="card-desc">{{ option.desc }}</div>
              </div>
              <div class="check-icon" v-if="moduleValue === option.value">
                <n-icon color="#36ad6a">
                  <checkmark-circle-outline />
                </n-icon>
              </div>
            </div>
          </div>
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="handleBackToDelimit">
            <template #icon>
              <n-icon>
                <arrow-back />
              </n-icon>
            </template>
            上一步
          </n-button>
          <n-button
            type="primary"
            @click="submitDelimitData(null, moduleValue)"
            :disabled="!moduleValue"
          >
            <template #icon>
              <n-icon>
                <Bulb />
              </n-icon>
            </template>
            场景反馈
          </n-button>
        </n-space>
      </template>
    </n-modal>

    <n-drawer v-model:show="showColumnDrawer" :width="300" placement="right">
      <n-drawer-content title="表格列设置" closable>
        <div class="column-settings">
          <div class="column-settings-header">
            <n-space justify="space-between" align="center">
              <n-checkbox v-model:checked="allSelect" @click="allSelectClick"> 全选</n-checkbox>
              <n-button size="small" @click="resetColumnSettings">重置</n-button>
            </n-space>
          </div>
          <n-divider />
          <div class="column-list">
            <n-checkbox-group v-model:value="showColumnListRef">
              <div v-for="item in allColumnList" :key="item" class="column-item">
                <n-checkbox :value="item" :label="item" v-if="item !== '操作'" />
              </div>
            </n-checkbox-group>
          </div>
          <div class="drawer-footer">
            <n-space justify="end">
              <n-button @click="showColumnDrawer = false">取消</n-button>
              <n-button type="primary" @click="saveTableSetting">保存</n-button>
            </n-space>
          </div>
        </div>
      </n-drawer-content>
    </n-drawer>

    <n-modal v-model:show="showAddParam">
      <n-card
        style="width: 600px"
        title="保存筛选条件名称"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-input v-model:value="paramSaveName" />
        <template #footer>
          <n-space justify="end">
            <n-button @click="showAddParam = false">取消</n-button>
            <n-button type="primary" @click="saveSearchParam">保存</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>

    <n-modal
      v-model:show="showComment"
      style="width: 600px"
      preset="card"
      :title="'审核意见'"
      :mask-closable="false"
    >
      <n-form
        :model="editingComment"
        label-placement="left"
        label-width="160"
        v-if="editingComment"
        :rule-props="{ immediate: true }"
        ref="formRef"
      >
        <n-form-item>
          <n-input
            v-model:value="editingComment.reviewResult"
            type="textarea"
            :rows="3"
            :default-value="'pls'"
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showComment = false">取消</n-button>
          <n-button type="primary" @click="saveCheckEditedRecord">确认</n-button>
        </n-space>
      </template>
    </n-modal>

    <n-modal
      v-model:show="showDelimit"
      :style="{ width: '1200px', height: '800px' }"
      preset="card"
      :title="'定界详情'"
      :mask-closable="false"
    >
      <n-descriptions bordered :column="2" size="small">
        <n-descriptions-item label="故障分类">
          <n-tag type="info">{{ model.module }}</n-tag>
        </n-descriptions-item>
        <n-descriptions-item label="问题归属">
          <n-tag type="info">{{ model.attribution }}</n-tag>
        </n-descriptions-item>
        <n-descriptions-item label="知识ID">
          <n-tag type="info" @click="turnToKnowLedge(model.knowId)">
            {{ model.knowId || '无' }}</n-tag
          >
        </n-descriptions-item>
        <n-descriptions-item label="是否重复问题">
          <n-tag :type="model.isDuplicated ? 'warning' : 'success'">
            {{ model.isDuplicated ? '是' : '否' }}
          </n-tag>
        </n-descriptions-item>
        <n-descriptions-item v-if="model.isDuplicated" label="重复问题单号">
          {{ model.duplicateProblemID }}
        </n-descriptions-item>
      </n-descriptions>

      <n-divider>原因分析</n-divider>
      <div v-html="model.reason" class="reason-analysis"></div>
    </n-modal>

    <ProblemProgressModal
      v-model:show="showProblemModal"
      :order-id="currentProblem?.orderId || ''"
      :current-problem="currentProblem"
    />
  </div>
</template>

<script lang="ts">
  export default {
    name: 'MyTodo',
  };
</script>

<script lang="ts" setup>
  import { ref, reactive, onMounted, computed, h, watch, onActivated } from 'vue';
  import {
    NCard,
    NForm,
    NFormItem,
    NInput,
    NSelect,
    NButton,
    NSpace,
    NGrid,
    NGridItem,
    NIcon,
    NUpload,
    useMessage,
    NModal,
    NSwitch,
    NScrollbar,
    NEllipsis,
    NTag,
    NDivider,
    NInputGroup,
    NInputNumber,
    NInputGroupLabel,
    NTabs,
    NTabPane,
    SelectOption,
    SelectGroupOption,
    NDrawer,
    NDrawerContent,
    NCheckbox,
    NCheckboxGroup,
  } from 'naive-ui';
  import {
    ChevronDown,
    ChevronUp,
    Settings,
    DocumentText,
    Bulb,
    ArrowBack,
    CubeOutline,
    CheckmarkCircle,
    CheckmarkCircleOutline,
    PersonOutline,
  } from '@vicons/ionicons5';
  import { employeesOptions } from '../appState/index.vue';
  import {
    createColumns,
    ListData,
    getSearchFormItems,
    allColumnList,
    columnKeyMap,
    baseShowColumns,
    dtsNameAccountOptions,
  } from './columns';
  import {
    getWorkOrderList,
    importExcel,
    submitIr,
    updateWorkOrder,
    distributeOrder,
    distributeOrderCcb,
  } from '@/api/dataview/appState';
  import { filterObjectValues, formatDateTime } from '@/utils';
  import { useRoute, useRouter } from 'vue-router';
  import { cloneDeep } from 'lodash-es';
  import { useUserStore } from '@/store/modules/user';
  import { batchCreateIR, DTSTurnTo } from '@/api/dataview/irManagement';
  import * as XLSX from 'xlsx-js-style';
  import { useDialog, TreeOption, FormItemRule } from 'naive-ui';
  import { ewpService as service } from '@/utils/axios';
  import {
    ColorPalette, // UX体验
    BugSharp, // 功能故障
    Shield, // 稳定性
    SpeedometerSharp, // 性能功耗
    LockClosed,
  } from '@vicons/ionicons5';
  import ProblemProgressModal from './components/ProblemProgressModal.vue';
  import { queryByNameOrType } from '@/api/dataview/phoneConfig';
  import { Delete } from '@vicons/carbon';
  import quickCheckTable from '../dtsCheck/index.vue';
  import { analysisTemplates } from '@/views/dataview/myOrder/components/processModel';

  // 添加权限控制变量
  const isOnlyViewRole = ref(false);

  let ordioValue = ref();
  const userStore = useUserStore();
  const userInfo: any = userStore.getUserInfo || {};
  const route = useRoute();
  const appName = route.query.appName as string;
  const fromPage = route.query.fromPage as string;
  const searchForm: any = reactive({
    orderId: '',
    personNo: '',
    appName: '',
    knowId: '',
    top: '',
    represent: '',
    ewpOwner: '',
    severity: [],
    ewpStatus: [],
    appLevel: [],
    status: [], // Change this to an array
    currentHandler: !isOnlyViewRole.value ? extractNumbers(userInfo.account) : '',
    commonProblemMark: null,
    irOrderId: '',
    irOrderStatus: null,
    createTime: null,
    firstImplementModifyTime: null,
    //solvePlanDate: '',
    solvePlanStart: '',
    solvePlanEnd: '',
    errorStatus: '',
    errorStatusSubdivision: '',
    deviceTypeList: [],
    weightValueMin: null,
    weightValueMax: null,
    vip: [],
  });
  const sortState = ref({
    sortField: 'riskScore',
    sortOrder: 'desc',
  });
  const tableData = ref<any[]>([]);
  const loading = ref(false);
  const distributeLoading = ref(false);
  const CCBLoading = ref(false);
  const isCheckState = ref(true);
  const showComment = ref(false);
  const showDelimit = ref(false);
  const checkState = ref(false);
  const tabCheckState = ref(false);

  const editingComment = ref({
    reviewResult: 'pls',
    orderId: '',
    currentHandler: '',
    lastImplementModifier: '',
    nextHandler: '',
  });

  const model = ref({
    isDuplicated: false,
    knowId: '',
    reason: analysisTemplates['常规模板'],
    module: null as string | null,
    attribution: '三方应用',
    duplicateProblemID: null as string | null,
  });
  //设备类型搜索多选
  const deviceOptions = ref([]);
  const deviceLoading = ref(false);
  const handleSearchDevice = async (query: string) => {
    const params = {
      pageNo: 1,
      pageSize: 20,
      nameOrType: query,
    };
    deviceLoading.value = true;
    const res = await queryByNameOrType(params);
    deviceOptions.value = res.map((item) => {
      return {
        label: item,
        value: item,
      };
    });
    deviceLoading.value = false;
  };

  const paginationReactive = reactive({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    showSizePicker: true,
    pageSizes: [10, 20, 50, 100],
    prefix({ itemCount }) {
      return `Total：${itemCount}`;
    },
    onChange: (page: number) => {
      paginationReactive.page = page;
      fetchData();
    },
    onUpdatePageSize: (pageSize: number) => {
      paginationReactive.pageSize = pageSize;
      paginationReactive.page = 1;
      fetchData();
    },
  });

  const isExpanded = ref(false);
  const visibleSearchFormItems = computed(() => {
    return isExpanded.value ? searchFormItems.value : searchFormItems.value.slice(0, 4);
  });

  const handleSearch = () => {
    console.log('Search form data:', searchForm);
    paginationReactive.page = 1;
    fetchData();
  };

  const resetParam = () => {
    Object.keys(searchForm).forEach((key) => {
      searchForm[key] = '';
    });
    // 根据onlyMine状态和用户角色设置currentHandler
    if (onlyMine.value && !isOnlyViewRole.value) {
      searchForm.currentHandler = extractNumbers(userInfo.account);
    } else {
      searchForm.currentHandler = '';
    }
    searchForm.vip = [];
    if (activeTab.value === 'severity') {
      searchForm.ewpStatus = ['待定界', '待锁定'];
      searchForm.severity = ['严重', '致命'];
      searchForm.top = 'TOP2000';
    } else if (activeTab.value === 'vip') {
      searchForm.ewpStatus = ['待定界', '待锁定'];
      searchForm.vip = ['vip', 'svip'];
    } else if (activeTab.value === 'check') {
      searchForm.ewpStatus = ['待审核'];
    }
    searchForm.createTime = null;
    searchForm.firstImplementModifyTime = null;
    searchForm.weightValueMin = null;
    searchForm.weightValueMax = null;
    sortState.value.sortField = 'riskScore';
    sortState.value.sortOrder = 'desc';
  };

  const resetForm = () => {
    resetParam();
    fetchData();
    // 重置选中状态
    checkedRowKeys.value = [];
    selectedRows.value = [];
  };

  const toggleExpandForm = () => {
    isExpanded.value = !isExpanded.value;
  };
  const formatDate = (date: number | Date | null): string => {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };
  const fetchData = async () => {
    loading.value = true;
    let isNull = false;
    if (searchForm.appName === ' ') {
      isNull = true;
      searchForm.appName = 'empty';
    }
    try {
      const queryParams: any = {
        ...(isNull ? searchForm : filterObjectValues(searchForm)),
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
        sortField: sortState.value.sortField,
        sortOrder: sortState.value.sortOrder,
        knowId:
          searchForm.hasKnowId === 'notempty'
            ? 'notempty'
            : searchForm.hasKnowId === 'empty'
            ? 'empty'
            : searchForm.knowId,
      };
      if (searchForm.createTime) {
        const [start, end] = searchForm.createTime;
        queryParams.beginCreateTime = formatDate(start);
        queryParams.endCreateTime = formatDate(end);
      }
      if (searchForm.firstImplementModifyTime) {
        const [start, end] = searchForm.firstImplementModifyTime;
        queryParams.beginFirstImplementModifyTime = formatDate(start);
        queryParams.endFirstImplementModifyTime = formatDate(end);
      }

      // 处理权重值范围
      if (searchForm.weightValueMin === null) {
        queryParams.weightValueMin = searchForm.weightValueMax;
      }
      if (searchForm.weightValueMax === null) {
        queryParams.weightValueMax = searchForm.weightValueMin;
      }

      const { total, records } = await getWorkOrderList(queryParams);
      tableData.value = records;
      paginationReactive.itemCount = total;
      delete queryParams.weightValueMin;
      delete queryParams.weightValueMax;
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loading.value = false;
      // 更新选中行的数据
      selectedRows.value = selectedRows.value.map((selectedRow) => {
        const updatedRow = tableData.value.find((row) => row.id === selectedRow.id);
        return updatedRow ? cloneDeep(updatedRow) : selectedRow;
      });
    }
  };

  const message = useMessage();

  const handleFileUpload = async ({ file }) => {
    isImporting.value = true;
    try {
      const response = await importExcel(file.file);
      message.success('Excel 文件导入成功');
      console.log('Import successful:', response);
      fetchData(); // 重新加载数据
    } catch (error) {
      console.error('Failed to import Excel file:', error);
      message.error('Excel 文件导入失败');
    } finally {
      isImporting.value = false;
    }
  };

  // 导入IR单数据
  const handleIRFileUpload = async ({ file }) => {
    isIRImporting.value = true;
    try {
      const formData = new FormData();
      formData.append('file', file.file);

      const response = await service.post('/management/ir/importData', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      message.success('应用数据导入成功');
      fetchData(); // Refresh the table data
    } catch (error) {
      console.error('Failed to import Excel file:', error);
      message.error('Excel 文件导入失败');
    } finally {
      isIRImporting.value = false;
    }
  };

  const disOrder = async () => {
    try {
      distributeLoading.value = true;
      await distributeOrder();
    } catch (error) {
      console.error('Error distributeOrder:', error);
      message.error('分发失败');
    } finally {
      distributeLoading.value = false;
    }
  };
  const disCCB = async () => {
    if (selectedRows.value.length === 0) {
      message.warning('请先选择要生成模板的行');
      return;
    }
    let isCompliance = selectedRows.value.every(
      (item) => item.currentHandler.split(' ').pop() === userInfo.account.substr(1)
    );
    if (!isCompliance) {
      message.warning('勾选行存在不属于你的应用');
      return;
    }
    let res: any = await distributeOrderCcb({
      ownerId: userInfo.account,
      workOrderList: selectedRows.value,
    });
    if (res.length > 0) {
      let successList = res.filter((item) => item.status === 'success');
      let failedList = res.filter((item) => item.status === 'failed');
      if (successList.length > 0) {
        message.success(
          `单号为${successList.map((item) => item.orderId).join(',')}成功走到CCB状态`
        );
      }
      if (failedList.length > 0) {
        message.error(`单号为${failedList.map((item) => item.orderId).join(',')}走到CCB失败`);
      }
    }
  };

  /**
   * 发起数据请求
   * @param link 请求链接
   * @param params 请求参数
   */
  async function getDataList(link: string, params: object): Promise<any> {
    try {
      const response = await service.post(`/management/${link}`, params);
      return response;
    } catch (error) {
      console.error('Error fetching work order list:', error);
      throw error;
    }
  }

  const selectedDelimit = reactive({
    senceIsHit: '',
    selectedSence: '',
  });

  // 添加一个监听器来处理匹配结果变化
  watch(
    () => selectedDelimit.senceIsHit,
    (newValue) => {
      // 清空场景选择
      selectedDelimit.selectedSence = '';
      // 清空相关参数
      delimitParams.id = '';
      delimitParams.secondClassification = '';
      delimitParams.sceneEntity = null;
      delimitQueryParam.id = '';
      delimitQueryParam.score = null;
      delimitQueryParam.sortNum = null;
    }
  );

  const rulesSence = {
    senceIsHit: {
      required: true,
      validator(rule: FormItemRule, value: string) {
        if (!value) {
          return new Error('请选择匹配结果');
        }
        return true;
      },
      trigger: ['input', 'blur'],
    },
    selectedSence: {
      required: true,
      validator(rule: FormItemRule, value: string) {
        if (selectedDelimit.senceIsHit === '3' && ordioValue) {
          return true;
        }
      },
      trigger: ['input', 'blur'],
    },
  };

  const delimitOptions = [
    { value: '2', label: '上架意愿' },
    { value: '1', label: '功能完善' },
    { value: '3', label: '故障场景命中' },
    { value: '4', label: '故障场景缺失' },
    { value: '5', label: '非问题' },
  ];
  const moduleOptions = [
    {
      value: '4796-UX体验',
      label: 'UX体验',
      icon: ColorPalette,
      color: '#36ad6a',
      desc: '用户体验相关问题',
    },
    {
      value: '4796-功能故障',
      label: '功能故障',
      icon: BugSharp,
      color: '#f0a020',
      desc: '功能异常或缺陷',
    },
    {
      value: '4796-稳定性',
      label: '稳定性',
      icon: Shield,
      color: '#2080f0',
      desc: '系统稳定性问题',
    },
    {
      value: '4796-性能功耗',
      label: '性能功耗',
      icon: SpeedometerSharp,
      color: '#e88080',
      desc: '性能或资源消耗',
    },
  ];
  const moduleValue = ref(null);
  const showEditDelimit = ref(false);
  const showHitDelimit = ref(false);
  const showNoHitDelimit = ref(false);
  const editQuickDelimit = ref<Partial<ListData>>({});
  let delimitTitle: string | undefined = '';
  const defaultExpandedKeys = ref(['1', '2']);
  const DelimitOneData = ref<any[]>([]);
  const DelimitData = ref<any[]>([]);
  const DelimitDataValue = reactive({
    module: '',
    result: '',
  });
  const delimitParams = reactive({
    matchResult: '',
    id: '',
    secondClassification: '',
    sceneEntity: null,
  });
  const delimitQueryParam = reactive({
    matchResult: '',
    id: '',
    score: null,
    sortNum: null,
    module: '',
    result: '',
    workOrderPo: ref<any>(),
  });

  const fetchDelimitData = async (dtsTitle) => {
    try {
      const queryParams = {
        dtsTitle: dtsTitle,
      };
      const link = 'faultTree/faultTreeQueryByDtsTitle';
      const data = await getDataList(link, queryParams);
      if (data && data.totalCount > 10) {
        DelimitOneData.value = data.dataList.splice(0, 10);
      } else {
        DelimitOneData.value = data.dataList;
      }
      selectedDelimit.senceIsHit = data.dtsType ? data.dtsType : '3';
    } catch (error) {
      console.error('Failed to load data:', error);
      throw error;
    } finally {
    }
  };

  // 快捷定界
  const handleDelimit = (row: ListData) => {
    editQuickDelimit.value = { ...row };
    delimitTitle = editQuickDelimit.value.description;
    delimitQueryParam.workOrderPo = editQuickDelimit.value;
    fetchDelimitData(editQuickDelimit.value.description);
    showEditDelimit.value = true;
  };

  const handleScene = (scene: any) => {
    delimitParams.id = scene.id;
    delimitParams.sceneEntity = scene;
    delimitParams.secondClassification = scene.secondClassification;
    delimitQueryParam.id = scene.id;
    delimitQueryParam.score = scene.score;
    delimitQueryParam.sortNum = scene.sortNum;
    window.open(scene.url, '_blank');
  };

  // 快捷定界 - 下一步
  const submitDelimit = async () => {
    delimitParams.dtsType = selectedDelimit.senceIsHit;

    try {
      if (selectedDelimit.senceIsHit === '4') {
        delimitParams.secondClassification = moduleValue;
      }
      const queryParams = delimitParams;
      const link = 'faultTree/prepareCcb';
      const data = await getDataList(link, queryParams);
      DelimitData.value = data;
      DelimitDataValue.module = DelimitData.value.module;
      DelimitDataValue.result = DelimitData.value.result;
      showEditDelimit.value = false;
      showHitDelimit.value = true;
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  };

  //快捷定界 - 一键CCB
  const submitDelimitData = async (result: string, module: string) => {
    delimitQueryParam.dtsType = selectedDelimit.senceIsHit;
    delimitQueryParam.result = ChangeDelimitDataValue.value || result;
    delimitQueryParam.module = module;
    try {
      const queryParams = delimitQueryParam;
      const link = 'faultTree/faultMatchToCcb';
      const data = await getDataList(link, queryParams);
      // todo
      message.success(data.faultMatchDataFeedbackResponse?.msg || '成功');
      showHitDelimit.value = false;
      showNoHitDelimit.value = false;
    } catch (error) {
      console.error('Failed to load data:', error);
      throw error;
    } finally {
    }
  };

  const clearForm = () => {
    selectedDelimit.senceIsHit = '3';
    delimitParams.id = '';
    delimitParams.secondClassification = '';
    delimitParams.sceneEntity = null;
    delimitQueryParam.id = '';
    delimitQueryParam.score = null;
    delimitQueryParam.sortNum = null;
    selectedDelimit.selectedSence = '';
  };

  const showEditModal = ref(false);
  const editingWorkOrder = ref<Partial<ListData>>({});

  const handleEdit = (row: ListData) => {
    editingWorkOrder.value = { ...row };
    showEditModal.value = true;
  };

  const saveEdit = async () => {
    try {
      await updateWorkOrder(editingWorkOrder.value);
      message.success('工单更新成功');
      showEditModal.value = false;
      fetchData();
    } catch (error) {
      console.error('Failed to update work order:', error);
      message.error('工单更新失败');
    }
  };
  const handleAppNameClick = (appName: string) => {
    // 更新搜索表单中的 appName 字段
    searchForm.appName = appName;
    // 触发搜索
    handleSearch();
  };
  const isCleanMode = ref(true);

  const railStyle = ({ focused, checked }) => {
    const style: { background?: string } = {};
    if (checked) {
      style.background = '#18a058';
    }
    return style;
  };

  const ChangeDelimitDataValue = ref('');

  const updateContent = (event) => {
    ChangeDelimitDataValue.value = event.target.innerHTML;
  };

  const columns = computed(() =>
    createColumns(
      true,
      handleEdit,
      handleProblem,
      problemProgress,
      openCheckEditModal,
      openDelimitDetailModal,
      fetchData,
      message,
      isCleanMode.value,
      isCheckState.value,
      tabCheckState.value,
      isOnlyViewRole.value
    )
  );

  const tableWidth = computed(() => {
    let width = 0;
    columns.value.forEach((item) => {
      if (item.show && item.width) {
        width = width + item.width + 25;
      }
    });
    return width + 20;
  });
  const showProblemModal = ref(false);
  const currentProblem = ref<ListData | null>(null);
  const problemProgress = (row: ListData) => {
    currentProblem.value = row;
    console.log('currentProblem.value:', currentProblem.value);
    showProblemModal.value = true;
  };

  const turnToKnowLedge = async (knowId) => {
    console.log(knowId, 'data');
    window.open(`http://7.190.7.136/knowledgeDetail/${knowId}`, '_blank');
  };

  // 打开审核详情弹窗
  const openCheckEditModal = (row: ListData, state: boolean) => {
    editingComment.value = {
      ...row,
      reviewResult: 'pls',
      nextHandler: state ? 'lichengfang 30052277' : row.lastImplementModifier,
    };
    checkState.value = state;
    showComment.value = true;
  };

  // 审核详情保存
  const saveCheckEditedRecord = async () => {
    try {
      await service.post('/management/ewpOrder/review/process', {
        dtsOrder: editingComment.value.orderId,
        reviewer: editingComment.value.currentHandler,
        nextHandler: editingComment.value.nextHandler,
        reviewResult: editingComment.value.reviewResult,
        status: checkState.value ? 1 : 0,
      });

      showComment.value = false;
      checkState.value
        ? message.success('审核通过成功，已完成工单流转')
        : message.error('审核驳回成功，已返回给定位责任人');
      fetchData();
    } catch (error) {
      console.error('Failed to save record:', error);
    }
  };

  // 打开定界详情弹窗
  const openDelimitDetailModal = async (row: ListData) => {
    console.log(row, 'row');
    const data = await service.get(`/management/ewpOrder/delimitView?dtsOrder=${row.orderId}`);
    console.log(data, 'delimitData');
    model.value = {
      ...data,
      reason: data.analysis,
      module: data.faultType,
      isDuplicated: !!data.isRepeat,
      duplicateProblemID: data.repeatOrder,
    };
    showDelimit.value = true;
  };

  const router = useRouter();
  const handleProblem = (row: ListData) => {
    console.log('row:', row);
    const encodedInfo = encodeURIComponent(JSON.stringify(row));
    router.push({
      name: 'Process',
      query: {
        orderId: row.orderId,
      },
    });
  };
  const handleSorterChange = (sorter) => {
    if (sorter) {
      const { columnKey, order } = sorter;
      sortState.value.sortField = columnKey;
      sortState.value.sortOrder = order === 'ascend' ? 'asc' : 'desc';
    } else {
      sortState.value.sortField = 'riskScore';
      sortState.value.sortOrder = 'desc';
    }
    paginationReactive.page = 1;
    fetchData();
  };

  onMounted(() => {
    checkUserRole();
    // 从URL参数获取应用名称
    const appNameFromUrl = route.query.appName as string;
    if (appNameFromUrl) {
      searchForm.appName = appNameFromUrl;
    }
    searchForm.commonProblemMark = fromPage || null;
    searchForm.appName = appName || '';
    searchForm.commonProblemMark = fromPage || null;
    fetchData();
    handleSearchDevice('');
    let columnsSetting = {};
    const storageSetting = window.localStorage.getItem('__4796_admin_dts_columns_setting');
    if (storageSetting) {
      columnsSetting = JSON.parse(storageSetting || '{}');
    } else {
      allColumnList.forEach((item) => {
        if (item === '操作') return;
        const key = columnKeyMap[item];
        if (!baseShowColumns.includes(key)) {
          columnsSetting[key] = { show: 'false' };
        } else {
          columnsSetting[key] = { show: 'true' };
        }
      });
      window.localStorage.setItem(
        '__4796_admin_dts_columns_setting',
        JSON.stringify(columnsSetting)
      );
    }
    showColumnListRef.value = allColumnList.filter((item) => {
      if (item === '操作') return false;
      const key = columnKeyMap[item];
      return !columnsSetting[key] || columnsSetting[key].show !== 'false';
    });

    refreshOptions();
  });

  const checkUserRole = () => {
    const userRoles = userInfo.roles || [];
    console.log('userRoles:', userRoles);

    isOnlyViewRole.value =
      userRoles.includes('5_only_view_public_opinion') &&
      !userRoles.includes('5_admin') &&
      !userRoles.includes('super_admin');

    if (isOnlyViewRole.value) {
      activeTab.value = 'order';
      resetParam();
      return;
    }

    const isAdmin = userRoles.includes('5_admin') || userRoles.includes('super_admin');
    const hasViewRole = userRoles.includes('5_only_view_public_opinion');

    if (isAdmin) {
      if (hasViewRole) {
        onlyMine.value = false;
        searchForm.currentHandler = '';
      } else {
        onlyMine.value = true;
        searchForm.currentHandler = extractNumbers(userInfo.account);
      }
    } else {
      isOnlyViewRole.value = true;
      activeTab.value = 'order';
      resetParam();
      return;
    }
  };

  const isImporting = ref(false);
  const isIRImporting = ref(false);
  const selectedRows = ref<any[]>([]);
  const checkedRowKeys = ref<(string | number)[]>([]);

  const generateTemplates = () => {
    if (selectedRows.value.length === 0) {
      message.warning('请先选择要生成模板的行');
      return;
    }
    const templates = selectedRows.value.map(
      (row) =>
        `【问题描述】${
          row.description.split('】').pop() + '\n'
        }【DTS链接】https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${row.orderId}
${
  row.irOrderId
    ? `【IR链接】https://issuereporter.developer.huawei.com/detail/${row.irOrderId
        .split('\n')
        .shift()}/comment` + '\n'
    : '【IR链接】待补充 ' + '\n'
}【分析结论】${row.conclusion || 'xxxx'}
【修改建议】${row.suggestion || 'xxxx'}
【知识链接】${row.knowledgeUrl || 'xxxx'}
【应用PM】${row.appPM || 'xxxx'}
【问题跟踪平台】https://dtse.cbg.huawei.com/listingProtection/ecologicalTracking
请执行：在【高级筛选】中过滤【当前处理人】为自己的问题，点击【应用PM更新】按钮更新进展
【生态舆情保障运作规范】https://3ms.huawei.com/km/groups/3945040/blogs/details/19122126?l=zh-cn`
    );

    const content = templates.join('\n');
    if (navigator.clipboard && window.isSecureContext) {
      navigator.clipboard.writeText(content).then(() => {
        message.success('模板已生成并复制到剪贴板');
        // 重置选择状态
        checkedRowKeys.value = [];
        selectedRows.value = [];
      });
    } else {
      // 创建text area
      let textArea = document.createElement('textarea');
      textArea.value = content;
      // 使text area不在viewport，同时设置不可见
      textArea.style.position = 'absolute';
      textArea.style.opacity = '0';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      return new Promise((res, rej) => {
        // 执行复制命令并移除文本框
        document.execCommand('copy') ? res(null) : rej();
        textArea.remove();
        message.success('模板已生成并复制到剪贴板');
        // 重置选择状态
        checkedRowKeys.value = [];
        selectedRows.value = [];
      });
    }
  };
  const handleCheckedRowKeysChange = (keys: (string | number)[]) => {
    const newSelectedRows = keys
      .map((key) => {
        const existingRow = selectedRows.value.find((row) => row.id === key);
        if (existingRow) {
          return existingRow;
        }
        return tableData.value.find((row) => row.id === key);
      })
      .filter(Boolean) as ListData[];

    selectedRows.value = newSelectedRows;
    checkedRowKeys.value = keys;
  };
  // 计算有效IR的行数
  const getValidRows = computed(() => {
    return selectedRows.value.filter((row) => {
      return !row.irOrderId && ['CCB方案审核', '开发人员实施修改'].includes(row.status);
    });
  });
  // 计算有效和无效的行数
  const getValidRowsToCsv = computed(() => {
    return selectedRows.value;
  });
  // 批量创建IR单
  const handleBatchCreateIR = async () => {
    try {
      if (getValidRows.value.length === 0) {
        message.warning('没有可以创建IR单的记录');
        return;
      }

      const irList = getValidRows.value.map((row) => ({
        orderId: row.orderId,
        appName: row.appName,
        ewpOwner: row.ewpOwner,
        severity: row.severity,
        creator: row.creator,
        appLevel: row.appLevel,
        top: row.top,
        irOrderStatus: '未提单',
      }));

      await batchCreateIR(irList);
      await submitIr({ orderIds: irList.map((row) => row.orderId) });
      message.success(`成功创建 ${getValidRows.value.length} 条IR单`);
      checkedRowKeys.value = [];
      selectedRows.value = [];
      await fetchData();
    } catch (error: any) {
      message.error(error?.message + JSON.stringify(error.existingOrderIds) || '创建IR单失败', {
        duration: 10000,
        closable: true,
      });
    }
  };

  // 计算转单勾选的行数
  const getTurnDTSNum = computed(() => {
    return selectedRows.value;
  });
  // 批量创建IR单
  const handleDTSTurnTo = async () => {
    try {
      if (getTurnDTSNum.value.length === 0) {
        message.warning('没有勾选需要转的单子');
        return;
      }
      if (searchForm.personNo.length === 0) {
        message.warning('请勾选被转单的责任人');
        return;
      }

      const dtsList = {
        orderIds: getTurnDTSNum.value.map((row) => row.orderId),
        target: searchForm.personNo,
      };

      await DTSTurnTo(dtsList);
      message.success(`转单成功`);
      checkedRowKeys.value = [];
      selectedRows.value = [];
      await fetchData();
    } catch (error: any) {
      message.error('转单失败', {
        duration: 10000,
        closable: true,
      });
    }
  };

  const exportToCSV = () => {
    if (selectedRows.value.length === 0) {
      message.warning('请先选要导的');
      return;
    }

    // 准备数据
    const headers = ['应用名称', '问题单描述', '严重程度', 'DTS单链接', '分析结论', '修改建议'];
    const data = getValidRowsToCsv.value.map((row) => [
      row.appName,
      row.description.split('】').pop(),
      row.severity,
      `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${row.orderId}`,
      row.conclusion,
      row.suggestion,
    ]);

    // 创建工作簿和工作表
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.aoa_to_sheet([headers, ...data]);

    // 设置列宽
    const colWidths = [15, 40, 15, 40, 20];
    ws['!cols'] = colWidths.map((width) => ({ width }));

    // 设置表头样式（黄色背景）
    const headerStyle = {
      fill: { fgColor: { rgb: 'FFFF00' } },
      font: { bold: true },
      alignment: { horizontal: 'center' },
    };

    // 设置解决计划列样式（红色）
    const planStyle = {
      font: { color: { rgb: 'FF0000' } },
    };

    // 应用样式
    for (let i = 0; i < headers.length; i++) {
      const cellRef = XLSX.utils.encode_cell({ r: 0, c: i });
      ws[cellRef].s = headerStyle;
    }

    // 为解决计划列应用红色样式
    for (let i = 1; i <= data.length; i++) {
      const cellRef = XLSX.utils.encode_cell({ r: i, c: 4 }); // 第5列（索引4）是解决计划列
      if (!ws[cellRef]) ws[cellRef] = { v: '', t: 's' };
      ws[cellRef].s = planStyle;
    }

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, 'DTS问题单列表');

    // 导出文件
    const appName = selectedRows.value[0]?.appName || '未知应用';
    XLSX.writeFile(wb, `DTS问题单列表-${appName}.xlsx`);

    message.success('Excel文件导出成功');
    // 重置选择状态
    checkedRowKeys.value = [];
    selectedRows.value = [];
  };

  const dialog = useDialog();

  // 处理问题进展
  const handleRemark = (row: ListData) => {
    const tempRemark = ref(row.progress || '');

    dialog.create({
      title: '问题进展',
      content: () =>
        h('div', [
          h('textarea', {
            rows: 4,
            type: 'error', // 添加红样式
            value: tempRemark.value,
            style: {
              width: '100%',
              padding: '8px',
              marginTop: '8px',
              border: '1px solid #ccc',
              borderRadius: '4px',
            },
            onInput: (e: Event) => {
              tempRemark.value = (e.target as HTMLTextAreaElement).value;
            },
          }),
        ]),
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        try {
          await updateWorkOrder({
            ...row,
            progress: tempRemark.value,
          });
          message?.success('更新成功');
          fetchData();
        } catch (error) {
          message?.error('更新失败');
        }
      },
    });
  };

  const exportLoading = ref(false);
  const handleExport = () => {
    return new Promise((resolve, reject) => {
      const req = new XMLHttpRequest();
      exportLoading.value = true;
      // let isNull = false;
      // if (searchForm.appName === ' ') {
      //   isNull = true;
      //   searchForm.appName = 'empty';
      // }
      const queryParams: any = {
        ...filterObjectValues(searchForm),
        sortField: sortState.value.sortField,
        sortOrder: sortState.value.sortOrder,
        pageNo: 1,
        pageSize: paginationReactive.itemCount || 999999,
        simpleDesc: isCleanMode.value,
      };
      if (searchForm.createTime) {
        const [start, end] = searchForm.createTime;
        queryParams.beginCreateTime = formatDate(start);
        queryParams.endCreateTime = formatDate(end);
      }
      if (searchForm.firstImplementModifyTime) {
        const [start, end] = searchForm.firstImplementModifyTime;
        queryParams.beginFirstImplementModifyTime = formatDate(start);
        queryParams.endFirstImplementModifyTime = formatDate(end);
      }

      // 处理权重值范围
      if (searchForm.weightValueMin === null) {
        queryParams.weightValueMin = searchForm.weightValueMax;
      }
      if (searchForm.weightValueMax === null) {
        queryParams.weightValueMax = searchForm.weightValueMin;
      }
      req.open('POST', `http://${window.location.host}/ewp/management/workOrder/exportData`, true);
      req.responseType = 'blob';
      req.setRequestHeader('Content-Type', 'application/json');
      req.onload = function () {
        const data = req.response;
        console.log('data:', data);
        if (data.size === 0) {
          exportLoading.value = false;
          message.warning('有数据可导出');
          reject(new Error('No data to export'));
          return;
        }
        const blob = new Blob([data]);
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = 'DTS问题导出.xlsx';
        a.href = blobUrl;
        a.click();
        exportLoading.value = false;
        resolve(true);
      };
      req.send(JSON.stringify(queryParams));
    });
  };

  const searchFormItems = computed(() => getSearchFormItems());

  const showTableSeetingModal = ref(false);
  const editingTableSetting = ref([]);
  const showColumnListRef = ref<string[]>([]);

  //全选按钮
  const allSelect = computed(() => {
    for (let i = 0; i < allColumnList.length; i++) {
      const item = allColumnList[i];
      if (item !== '操作' && !showColumnListRef.value.includes(item)) {
        return false;
      }
    }
    return true;
  });

  const allSelectClick = () => {
    if (allSelect.value) {
      showColumnListRef.value = [];
    } else {
      showColumnListRef.value = allColumnList;
    }
  };

  const onTableSettingClick = () => {
    openColumnDrawer();
  };

  const saveTableSetting = () => {
    const tableSetting = {};
    allColumnList.forEach((item) => {
      // 跳过操作列的设置保存
      if (item === '操作') return;
      const key = columnKeyMap[item];
      // 如果列不在选中列表中，则设置为不显示
      if (!showColumnListRef.value.includes(item)) {
        tableSetting[key] = { show: 'false' };
      } else {
        tableSetting[key] = { show: 'true' };
      }
    });

    window.localStorage.setItem('__4796_admin_dts_columns_setting', JSON.stringify(tableSetting));
    message.success('保存成功');
    // window.location.reload();
    fetchData();
    reLoadTable();
    showColumnDrawer.value = false;
  };

  const handleFiltersChange = (filters: any) => {
    // 合并筛选条件到搜索表单
    Object.assign(searchForm, filters);
    // 重置到第一页
    paginationReactive.page = 1;
    // 重新获取数据
    fetchData();
  };

  // 添加返回上一步的处理函数
  const handleBackToDelimit = () => {
    showHitDelimit.value = false;
    showNoHitDelimit.value = false;
    showEditDelimit.value = true;
    ChangeDelimitDataValue.value = '';
  };

  // 修改计算属性，增加更严格的判断
  const canSubmitDelimit = computed(() => {
    if (
      selectedDelimit.senceIsHit === '1' ||
      selectedDelimit.senceIsHit === '2' ||
      selectedDelimit.senceIsHit === '5'
    ) {
      return selectedDelimit.senceIsHit;
    }
    if (selectedDelimit.senceIsHit === '4') {
      return !!moduleValue.value;
    }
    return selectedDelimit.senceIsHit === '3' && ordioValue.value;
  });

  const showWindowTitle = computed(() => {
    if (selectedDelimit.senceIsHit === '1') {
      return '功能完善';
    } else if (selectedDelimit.senceIsHit === '2') {
      return '上架意愿';
    } else if (selectedDelimit.senceIsHit === '3') {
      return '故障场景命中';
    } else if (selectedDelimit.senceIsHit === '4') {
      return '故障场景缺失';
    } else {
      return '非问题';
    }
  });
  const activeTab = ref('定界锁定');

  // 修改取消按钮的处理函数
  const handleCancel = () => {
    // 重置所有状态
    selectedDelimit.senceIsHit = '3';
    selectedDelimit.selectedSence = '';
    delimitParams.id = '';
    delimitParams.secondClassification = '';
    delimitParams.sceneEntity = null;
    delimitQueryParam.id = '';
    delimitQueryParam.score = null;
    delimitQueryParam.sortNum = null;

    // 清空场景数据
    DelimitOneData.value = [];
    DelimitData.value = [];
    DelimitDataValue.module = '';
    DelimitDataValue.result = '';
    ChangeDelimitDataValue.value = '';

    // 关闭弹窗
    showEditDelimit.value = false;
    moduleValue.value = null;
  };

  const handleTabChange = (value: string) => {
    // 如果是只读角色，强制保持在"我的待办"标签
    if (isOnlyViewRole.value) {
      activeTab.value = 'order';
      return;
    }

    activeTab.value = value;
    resetForm();
    if (value === 'severity') {
      searchForm.ewpStatus = ['待定界', '待锁定'];
      searchForm.severity = ['严重', '致命'];
      tabCheckState.value = false;
    } else if (value === 'vip') {
      searchForm.ewpStatus = ['待定界', '待锁定'];
      searchForm.severity = [];
      searchForm.vip = ['vip', 'svip'];
      tabCheckState.value = false;
    } else if (value === 'check') {
      searchForm.ewpStatus = ['待审核'];
      tabCheckState.value = true;
    } else if (value === 'order') {
      tabCheckState.value = false;
    }
    handleSearch();
  };

  // 根据用户角色设置默认值，如果是管理员则默认显示全部
  const onlyMine = ref(!userInfo.roles?.includes('5_admin'));

  const handleOnlyMineChange = (value: boolean) => {
    if (value) {
      searchForm.currentHandler = extractNumbers(userInfo.account);
    } else {
      searchForm.currentHandler = '';
    }
    handleSearch();
  };

  function extractNumbers(str) {
    // 使用正则表达式匹配所有数字部分
    const numbers = str.match(/\d+/g);
    return numbers ? numbers.join('') : ''; // 将匹配到的数字拼接成字符串
  }

  const resLoad = () => {
    const reLoadTable = () => {
      isCleanMode.value = !isCleanMode.value;
      isCleanMode.value = !isCleanMode.value;
    };
    return { reLoadTable };
  };

  const { reLoadTable } = resLoad();

  //保存查询参数模块
  const searchParamSaveModule = () => {
    const localStorageKey = 'searchParams';
    const searchParam = ref(null);
    const selectParamOptions = ref([]);
    const showAddParam = ref(false);
    const paramSaveName = ref('');
    const label = ref('');

    const selectSearchParam = (value, option) => {
      label.value = option.label;
      const obj = JSON.parse(option.realValue);
      Object.keys(obj).forEach((key) => {
        searchForm[key] = obj[key];
      });
      paginationReactive.page = 1;
      fetchData();
    };

    const openSaveParam = () => {
      showAddParam.value = true;
    };

    const saveSearchParam = () => {
      if (paramSaveName.value && paramSaveName.value.length > 0) {
        const key = localStorage.getItem(localStorageKey);
        if (key) {
          const keyObj = JSON.parse(key);
          if (keyObj.hasOwnProperty(paramSaveName.value)) {
            message.error('保存名称重复，请输入其他名称');
          } else {
            keyObj[paramSaveName.value] = JSON.stringify(searchForm);
            localStorage.setItem(localStorageKey, JSON.stringify(keyObj));
            refreshOptions();
            showAddParam.value = false;
          }
        } else {
          const keyObj = {};
          keyObj[paramSaveName.value] = JSON.stringify(searchForm);
          localStorage.setItem(localStorageKey, JSON.stringify(keyObj));
          refreshOptions();
          showAddParam.value = false;
        }
      } else {
        message.error('保存名字为空，请输入保存名称');
      }
    };

    const refreshOptions = () => {
      const params = localStorage.getItem(localStorageKey);
      selectParamOptions.value = [];
      if (params) {
        const obj = JSON.parse(params);
        Object.keys(obj).forEach((key) => {
          selectParamOptions.value.push({ label: key, value: key, realValue: obj[key] });
        });
      }
    };

    const saveParamSelectRender = (option: Array<SelectOption | SelectGroupOption>) => {
      return h(
        'div',
        {
          style: {
            width: '150px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
          },
        },
        [
          h(
            'span',
            {
              style: {
                width: '130px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              },
              title: option.label,
            },
            option.label
          ),
          h(
            NIcon,
            {
              size: 16,
              color: '#1890ff',
              onClick: (event) => {
                event.stopPropagation();
                //删除option
                const params = localStorage.getItem(localStorageKey);
                const obj = JSON.parse(params);
                delete obj[option.label];
                localStorage.setItem(localStorageKey, JSON.stringify(obj));
                searchParam.value = null;
                refreshOptions();
              },
            },
            { default: () => h(Delete) }
          ),
        ]
      );
    };

    refreshOptions();

    onActivated(() => {
      fetchData();
    });

    return {
      searchParam,
      selectParamOptions,
      refreshOptions,
      paramSaveName,
      openSaveParam,
      saveSearchParam,
      selectSearchParam,
      showAddParam,
      saveParamSelectRender,
    };
  };

  const {
    searchParam,
    selectParamOptions,
    showAddParam,
    refreshOptions,
    paramSaveName,
    openSaveParam,
    saveSearchParam,
    selectSearchParam,
    saveParamSelectRender,
  } = searchParamSaveModule();

  // 列设置抽屉
  const showColumnDrawer = ref(false);

  // 打开列设置抽屉
  const openColumnDrawer = () => {
    // 从localStorage获取最新设置
    const columnsSetting = JSON.parse(
      window.localStorage.getItem('__4796_admin_dts_columns_setting') || '{}'
    );

    // 更新显示的列，确保操作列始终存在
    showColumnListRef.value = allColumnList.filter((item) => {
      // 操作列始终显示
      if (item === '操作') return true;
      const key = columnKeyMap[item];
      return !columnsSetting[key] || columnsSetting[key].show !== 'false';
    });

    showColumnDrawer.value = true;
  };

  // 重置列设置
  const resetColumnSettings = () => {
    showColumnListRef.value = allColumnList.filter((item) => {
      // 操作列始终显示
      if (item === '操作') return false;
      const key = columnKeyMap[item];
      return baseShowColumns.includes(key);
    });
  };
</script>

<style scoped lang="less">
  .filter-container {
    margin-bottom: 14px;
  }

  /* 修改表单项样式 */
  :deep(.n-form-item) {
    display: flex;
    margin-right: 0;
    margin-bottom: 18px;
  }

  :deep(.n-form-item-label) {
    width: 90px !important;
    text-align: right;
  }

  :deep(.n-form-item-blank) {
    flex: 1;
  }

  /* 统一输入框和选择框的宽度和对齐方式 */
  :deep(.n-input),
  :deep(.n-select) {
    /* width: 300px !important; */
  }

  /* 确保输入内容左对齐 */
  :deep(.n-input__input-el),
  :deep(.n-select-option__content) {
    text-align: left !important;
  }

  /* 确保选择框的内容左对齐 */
  :deep(.n-base-selection-label) {
    text-align: left !important;
  }

  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 0px;
  }

  .setting-icon {
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
    color: #666;
  }

  .setting-icon:hover {
    background-color: #f5f5f5;
    color: #2080f0;
    transform: rotate(30deg);
  }

  .bold-font {
    width: 100%;
    font-weight: 700;
  }

  .gradient-text {
    font-size: 14px;
    background: -webkit-linear-gradient(90deg, red 0%, green 50%, blue 100%); /* Chrome, Safari */
    background: linear-gradient(90deg, red 0%, green 50%, blue 100%); /* 标准语法 */
    -webkit-background-clip: text; /* Chrome, Safari */
    background-clip: text;
    -webkit-text-fill-color: transparent; /* Chrome, Safari */
    color: transparent;
  }

  .description-card {
    margin-bottom: 16px;
    border: 1px solid #ebeef5;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .description-content {
    padding: 12px;
    background: #f9f9f9;
    border-radius: 4px;
    line-height: 1.6;
    color: #606266;
    font-size: 14px;
  }

  .match-result {
    margin: 16px 0;
  }

  .scene-card {
    :deep(.n-card__content) {
      padding: 16px;
    }
  }

  .scene-tree {
    .scene-category {
      //display: flex;
      //align-items: center;
      width: 100%;
      gap: 12px;
      padding: 12px 16px;
      margin-bottom: 8px;
      background: #f8f9fb;
      border-radius: 8px;
      border-left: 4px solid #f0a020;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      .category-title {
        font-size: 15px;
        font-weight: 600;
        color: #1a1a1a;
      }
    }

    .scene-category:hover {
      cursor: pointer;
      background-color: rgba(0, 0, 0, 0.05);
    }

    .scene-option {
      position: relative;
      transition: all 0.3s ease;
      margin: 2px 0;

      &:hover {
        transform: translateX(4px);
      }
    }

    .scene-radio {
      width: 100%;
      padding: 8px 16px;
      border-radius: 6px;
      transition: all 0.3s;
      border: 1px solid transparent;
      margin: 2px 0;

      &:hover {
        background-color: #f9fafc;
        border-color: #ebeef5;
      }

      &.n-radio--checked {
        background-color: #ecf5ff;
        border-color: #a0cfff;
      }

      .n-radio {
        display: flex;
        align-items: center !important;
      }

      .scene-content {
        // display: flex;
        // align-items: center;
        // gap: 12px;
        // font-size: 14px;
        // color: #606266;
      }
    }
  }

  :deep(.n-radio-group) {
    width: 100%;
  }

  :deep(.n-scrollbar) {
    .n-scrollbar-rail {
      right: 2px;
    }
  }

  :deep(.n-divider) {
    margin: 8px 0;
  }

  .analysis-content {
    width: 100%;
    padding: 12px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    min-height: 120px;
    background: #fff;
    line-height: 1.6;
    color: #303133;
    font-size: 14px;
    transition: border-color 0.2s;
  }

  .analysis-content:hover,
  .analysis-content:focus {
    border-color: #409eff;
    outline: none;
  }

  .n-form-item.bold-font {
    .n-space {
      flex-wrap: wrap;
      gap: 12px;

      .n-button {
        transition: all 0.3s ease;

        &:not(:disabled):hover {
          transform: translateY(-2px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &.n-button--primary {
          font-weight: 500;
        }
      }
    }
  }

  .module-cards {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 2fr));
    gap: 24px;
    padding: 8px;
  }

  .module-card {
    position: relative;
    display: flex;
    align-items: center;
    padding: 16px;
    background: #fff;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &.active {
      border-color: #36ad6a;
      background: #f8fff9;
    }

    .icon-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48px;
      height: 48px;
      border-radius: 12px;
      margin-right: 16px;
    }

    .card-content {
      flex: 1;

      .card-title {
        font-size: 16px;
        font-weight: 500;
        color: #1a1a1a;
        margin-bottom: 4px;
      }

      .card-desc {
        font-size: 13px;
        color: #909399;
      }
    }

    .check-icon {
      position: absolute;
      top: 12px;
      right: 12px;
    }
  }

  .reason-analysis {
    max-height: 400px;
    overflow-y: auto;
    padding: 8px;
    background: #f9f9f9;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.6;

    :deep(img) {
      max-width: 100%;
      height: auto;
    }

    :deep(p) {
      margin-bottom: 10px;
    }
  }

  .column-settings {
    height: 100%;
    display: flex;
    flex-direction: column;

    .column-settings-header {
      padding: 0 0 8px 0;
    }

    .column-list {
      flex: 1;
      overflow-y: auto;
      padding: 0 8px;

      .column-item {
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
      }
    }

    .drawer-footer {
      padding: 16px 0 0 0;
      margin-top: auto;
    }
  }
</style>
