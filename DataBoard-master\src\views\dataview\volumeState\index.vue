<template>
  <div class="container-box">
    <!-- 表单查询部分 -->
    <div class="search-box">
      <div class="form-container">
        <n-form :size="size" label-placement="left" ref="formRef" inline :model="form">
          <n-form-item label="应用名称">
            <n-input
              clearable
              v-model:value.trim="form.appName"
              @keyup.enter="handleSearch"
              placeholder="请输入应用名称"
            />
          </n-form-item>
          <n-form-item label="问题分类">
            <n-input
              clearable
              v-model:value.trim="form.type"
              @keyup.enter="handleSearch"
              placeholder="请输入问题分类"
            />
          </n-form-item>
          <n-form-item label="问题单号">
            <n-input
              clearable
              v-model:value.trim="form.dtsOrder"
              @keyup.enter="handleSearch"
              placeholder="请输入问题单号"
            />
          </n-form-item>
          <n-form-item label="问题描述">
            <n-input
              clearable
              v-model:value.trim="form.description"
              @keyup.enter="handleSearch"
              placeholder="请选择问题描述"
            />
          </n-form-item>
        </n-form>
      </div>
      <n-space justify="center">
        <n-button @click="handleReset">重置</n-button>
        <n-button type="primary" @click="handleSearch">查询</n-button>
      </n-space>
    </div>
    <!-- 表格部分 -->
    <div class="table-container">
      <n-space vertical :size="12">
        <n-space>
          <n-upload accept=".xlsx, .xls" :custom-request="handleFileUpload" :show-file-list="false">
            <n-button secondary :loading="importing">
              {{ importing ? '正在导入...' : '导入应用' }}
            </n-button>
          </n-upload>
        </n-space>
        <n-data-table
          remote
          ref="tableRef"
          :bordered="false"
          :single-line="false"
          striped
          :loading="loadingRef"
          :columns="columns"
          :data="tableData"
          :pagination="paginationReactive"
          @update:sorter="handleSorterChange"
          @update:page="handlePageChange"
          :scroll-x="3000"
        />
      </n-space>
    </div>
    <!-- 模态框部分 -->
    <n-drawer v-model:show="dialogVisible" :width="502">
      <n-drawer-content :title="isAdd ? '新增' : '编辑'" closable>
        <n-form :model="actionForm" :rules="rules" label-width="auto" label-placement="left">
          <n-form-item label="问题识别" path="identify">
            <n-input v-model:value.trim="actionForm.identify" />
          </n-form-item>
          <n-form-item label="问题单号" path="dtsOrder">
            <n-input v-model:value.trim="actionForm.dtsOrder" placeholder="请输入问题单号" />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space>
            <n-button @click="cancel">取消</n-button>
            <n-button type="primary" @click="sureAdd">确认</n-button>
          </n-space>
        </template>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script lang="ts" setup>
  import { filterObjectValues } from '@/utils';
  import { ewpService as service } from '@/utils/axios';
  import {
    DataTableInst,
    FormRules,
    NButton,
    NTag,
    useMessage,
    NUpload,
    NPopconfirm,
    useDialog,
  } from 'naive-ui';
  import { ref, reactive, h, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { BasicColumn } from '@/components/Table';
  import { ListData } from '../myOrder/columns';
  import { UserInfoType, useUserStore } from '@/store/modules/user';

  const tableRef = ref<DataTableInst>();
  const formRef = ref(null);
  const dialogVisible = ref(false);
  const isAdd = ref(true);
  const size = ref('medium');
  const message = useMessage();
  const dialog = useDialog();
  const userStore = useUserStore();
  const paginationReactive = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    prefix({ itemCount }) {
      return `Total：${itemCount}`;
    },
    onChange: (page: number) => {
      paginationReactive.page = page;
      fetchData();
    },
    onUpdatePageSize: (pageSize: number) => {
      paginationReactive.pageSize = pageSize;
      paginationReactive.page = 1;
      fetchData();
    },
  });

  const rules: FormRules = {
    appName: [
      {
        required: true,
        message: '请输入应用名称',
      },
    ],
    ewpOwner: [
      {
        required: false,
        message: '请输入应用责任人',
      },
    ],
    dtseOwner: [
      {
        required: true,
        message: '请输入DTSE',
      },
    ],
    appType: [
      {
        required: true,
        message: '请选择应用类型',
      },
    ],
    appLevel: [
      {
        required: true,
        message: '请选择应用标签',
      },
    ],
  };
  const userInfo: UserInfoType = userStore.getUserInfo || {};
  const loadingRef = ref(true);
  const form = ref({
    appName: '',
    ewpOwner: '',
    label: '',
    appType: null,
    appLevel: null,
  });
  const actionForm = ref({
    identify: '',
    dtsOrder: '',
  });
  const appTypeOptions = ref([]);

  const appLevelOptions = ref([]);

  const actionsArr: any = [];
  actionsArr.push(
    {
      title: '编辑',
      key: 'edit',
    }
    // {
    //   title: '删除',
    //   key: 'delete',
    // }
  );

  const createColumns = () => [
    {
      title: '应用名',
      key: 'appName',
      render: (row) => {
        return h(
          NTag,
          {
            bordered: false,
            style: {
              cursor: 'pointer',
            },
            onClick: () => handleAppNameClick(row.appName),
          },
          {
            default: () => row.appName,
          }
        );
      },
    },
    {
      title: '问题分类',
      key: 'type',
    },
    {
      title: '问题描述',
      key: 'description',
      resizable: true,
      width: 400,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '问题识别',
      key: 'identify',
      width: 100,
    },
    {
      title: '问题单号',
      key: 'dtsOrder',
      width: 170,
      render: (row) => {
        if (!row.dtsOrder) {
          return '';
        }
        return h(
          NTag,
          {
            type: 'info',
            bordered: false,
            onClick: async () => {
              const { dtsOrder } = row;
              // 根据dtsOrder跳转到对应的详情页面
              window.open(
                `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${dtsOrder}`,
                '_blank'
              );
            },
          },
          {
            default: () => row.dtsOrder,
          }
        );
      },
    },
    {
      title: '声量汇总',
      key: 'volumeSummary',
    },
  ];

  const actionsColumn = () => [
    {
      title: '操作',
      key: 'actions',
      width: 100,
      fixed: 'right',
      render(row) {
        return h('div', {}, [
          ...actionsArr.map((item) => {
            if (item.key === 'delete') {
              return h(
                NPopconfirm,
                {
                  onPositiveClick: async () => {
                    await service.post(`/management/appInfo/delete/${row.id}`);
                    message.success('删除成功');
                    fetchData();
                  },
                },
                {
                  trigger: () =>
                    h(
                      NButton,
                      {
                        size: 'small',
                        style: {
                          marginRight: '6px',
                        },
                        secondary: true,
                        type: 'error', // 添加红色样式
                      },
                      { default: () => '删除' }
                    ),
                  default: () => '确定要删除吗？',
                }
              );
            } else {
              return h(
                NButton,
                {
                  size: 'small',
                  style: {
                    marginRight: '6px',
                  },
                  secondary: true,
                  onClick: () => {
                    actionForm.value = { ...row };
                    if (item.key === 'edit') {
                      isAdd.value = false;
                      dialogVisible.value = true;
                    }
                  },
                },
                {
                  default: () => item.title,
                }
              );
            }
          }),
        ]);
      },
    },
  ];

  const columns = ref<any[]>(createColumns());

  const tableData = ref<ListData[]>([]);

  const sortState = ref({
    sortField: 'riskScore',
    sortOrder: 'desc',
  });

  // Add this new ref for import status
  const importing = ref(false);

  // 在组件挂载时获取数据
  onMounted(() => {
    fetchData();
    getAppType();
    getEwpOwner();
  });

  const fetchData = async () => {
    loadingRef.value = true;
    const tableHead = await service.get('/management/experienceFeedback/getTableHead');
    console.log(`tableHead`, tableHead);
    if (tableHead) {
      columns.value = [
        ...createColumns(),
        ...tableHead.map((i) => ({ title: i, key: i })),
        ...actionsColumn(),
      ];
    }
    const response = await service.post('/management/experienceFeedback/query', {
      pageNo: paginationReactive.page,
      pageSize: paginationReactive.pageSize,
      ...filterObjectValues(form.value),
    });
    if (response) {
      console.log(`response`, response);
      tableData.value = response.records;
      paginationReactive.itemCount = response.total;
      loadingRef.value = false;
    }
  };
  const router = useRouter();

  const handleAppNameClick = (name: string) => {
    router.push({
      name: 'MyTodo',
      query: { appName: name },
    });
  };
  const getAppType = async () => {
    const response = await service.get('/management/appInfo/appType');
    if (response) {
      appTypeOptions.value = response.map((item) => {
        return { label: item, value: item };
      });
    }
  };

  const getEwpOwner = async () => {
    const response = await service.get('/management/appInfo/appLevel');
    if (response) {
      appLevelOptions.value = response.map((item) => {
        return { label: item, value: item };
      });
    }
  };

  const handleSearch = () => {
    paginationReactive.page = 1;
    fetchData();
    // 这里可以编写查询逻辑，更新 tableData
  };
  const handleReset = () => {
    // 重置表单数据为初始状态
    // formRef.value.resetFields();
    // 重置表单数据为初始状态
    form.value.appName = '';
    form.value.type = '';
    form.value.dtsOrder = '';
    form.value.description = '';
    sortState.value.sortField = 'riskScore';
    sortState.value.sortOrder = 'desc';
    fetchData();
  };

  const downloadCsv = () => {
    // 导出 CSV 逻辑
    const csvData = tableData.value.map((item: any) => {
      return {
        应用名称: item.appName,
        应用负责人: item.ewpOwner,
        DTSE: item.dtseOwner,
        应用垂类: item.appType,
        标签: item.appLevel, // 假设标签是数组，需要转换为字符串
      };
    });
    // 创建 CSV 内容
    const csvContent = [
      ['应用名称', '负责人', '应用垂类', '标签'], // 表头
      ...csvData.map((e) => Object.values(e)), // 数据行
    ]
      .map((e) => e.join(',')) // 将每行数据转换为字符串
      .join('\r\n'); // 每行之间用换行符分隔

    // 添加 UTF-8 BOM
    const BOM = '\uFEFF';
    const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'data.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const add = () => {
    dialogVisible.value = true;
    isAdd.value = true;
    actionForm.value = {
      identify: '',
      dtsOrder: '',
    };
  };

  const sureAdd = async () => {
    let response = await service.post('/management/experienceFeedback/save', {
      ...actionForm.value,
    });
    console.log('response :>> ', response);
    message.success(isAdd.value ? '新增成功' : '修改成功');
    dialogVisible.value = false;
    fetchData();
  };
  const cancel = () => (dialogVisible.value = false);

  const handleSorterChange = (sorter) => {
    if (sorter) {
      const { columnKey, order } = sorter;
      sortState.value.sortField = columnKey;
      sortState.value.sortOrder = order === 'ascend' ? 'asc' : 'desc';
    } else {
      sortState.value.sortField = 'riskScore';
      sortState.value.sortOrder = 'desc';
    }
    paginationReactive.page = 1; // 重置到第一页
    fetchData();
  };

  const handlePageChange = (page: number) => {
    paginationReactive.page = page;
    fetchData();
  };

  const getStatusColor = (status: number) => {
    if (status >= 3) {
      return 'error';
    } else if (status > 1) {
      return 'warning';
    } else {
      return 'success';
    }
  };

  // Update the handleFileUpload function
  const handleFileUpload = async ({ file }) => {
    importing.value = true;
    try {
      const formData = new FormData();
      formData.append('file', file.file);

      const response = await service.post('/management/experienceFeedback/importData', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      message.success('应用数据导入成功');
      fetchData(); // Refresh the table data
    } catch (error) {
      console.error('Failed to import application data:', error);
      message.error('应用数据导入失败');
    } finally {
      importing.value = false;
    }
  };

  const handleClipboardSearch = async () => {
    try {
      const clipboardText = await navigator.clipboard.readText();

      if (clipboardText) {
        console.log('clipboardContent:', clipboardText);
        dialog.warning({
          title: '确认搜索',
          content: `是否要搜索剪贴板中的内容：${clipboardText}`,
          positiveText: '确认',
          negativeText: '取消',
          onPositiveClick: () => {
            form.value.appName = clipboardText.trim();
            handleSearch();
          },
        });
      } else {
        message.warning('剪贴板为空');
      }
    } catch (err) {
      console.error('Failed to read clipboard:', err);
      message.error('无法读取剪贴板内容');
    }
  };
</script>
<style scoped>
  .container-box {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .button-box {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .search-box,
  .table-container {
    background-color: white;
    padding: 20px;
    /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); */
    border-radius: 8px;
  }
  .form-container {
    display: flex;
    flex: 1;
  }
  :deep(.n-form.n-form--inline .n-form-item) {
    width: 100%;
  }
  .table-container {
    height: 100%;
  }
</style>
