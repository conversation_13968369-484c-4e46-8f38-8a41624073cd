<template>
  <div>
    <n-card>
      <n-form
        :model="searchForm"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
        @submit.prevent="handleSearch"
      >
        <n-grid :cols="24" :x-gap="24">
          <n-grid-item v-for="item in visibleSearchFormItems" :key="item.field" :span="6">
            <n-form-item :label="item.label" :path="item.field">
              <n-input
                v-if="item.component === 'Input'"
                v-model:value="searchForm[item.field]"
                clearable
                @keyup.enter="handleSearch"
              />
              <n-select
                v-else-if="item.component === 'Select'"
                v-model:value="searchForm[item.field]"
                :options="item.componentProps.options"
                :filterable="item.componentProps.filterable"
                clearable
              />
              <n-date-picker
                v-else-if="item.component === 'DateRangePicker'"
                v-model:value="searchForm[item.field]"
                type="daterange"
                clearable
              />
            </n-form-item>
          </n-grid-item>
        </n-grid>
        <div class="form-actions">
          <n-space>
            <n-button @click="resetForm">重置</n-button>
            <n-button type="primary" attr-type="submit">查询</n-button>
            <n-button @click="toggleExpandForm">
              {{ isExpanded ? '收起' : '展开' }}
              <template #icon>
                <n-icon>
                  <chevron-down v-if="!isExpanded" />
                  <chevron-up v-else />
                </n-icon>
              </template>
            </n-button>
          </n-space>
        </div>
      </n-form>
    </n-card>

    <n-card style="margin-top: 24px">
      <n-space align="center" style="margin-bottom: 16px">
        <n-button type="primary" @click="showAddModal = true">新增</n-button>
        <n-button type="success" @click="clickExportExcel">导出</n-button>
        <n-button type="warning" @click="showChartModal = true">未提单统计</n-button>
      </n-space>

      <n-data-table
        remote
        :bordered="false"
        :single-line="false"
        striped
        :columns="columns"
        :data="tableData"
        :pagination="paginationReactive"
        :loading="loading"
        :scroll-x="1800"
        :max-height="tableHeight"
      />
    </n-card>

    <n-modal v-model:show="showEditModal" preset="card" title="编辑IR单" style="width: 600px">
      <n-form
        :model="editingIR"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="问题单号" path="orderId">
          <n-input v-model:value="editingIR.orderId" disabled />
        </n-form-item>
        <n-form-item label="应用名称" path="appName">
          <n-input v-model:value="editingIR.appName" />
        </n-form-item>
        <n-form-item label="EWP责任人" path="ewpOwner">
          <n-input v-model:value="editingIR.ewpOwner" />
        </n-form-item>
        <n-form-item label="验收责任人" path="acceptanceOwner">
          <n-input v-model:value="editingIR.acceptanceOwner" />
        </n-form-item>
        <n-form-item label="DTS提单人" path="creator">
          <n-input v-model:value="editingIR.creator" />
        </n-form-item>
        <n-form-item label="IR单状态" path="irOrderStatus">
          <n-select
            v-model:value="editingIR.irOrderStatus"
            :options="
              searchFormItems.find((item) => item.field === 'irOrderStatus')?.componentProps.options
            "
          />
        </n-form-item>
        <n-form-item label="严重程度" path="severity">
          <n-select
            v-model:value="editingIR.severity"
            :options="
              searchFormItems.find((item) => item.field === 'severity')?.componentProps.options
            "
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showEditModal = false">取消</n-button>
          <n-button type="primary" @click="saveEdit">保存</n-button>
        </n-space>
      </template>
    </n-modal>

    <n-modal v-model:show="showAddModal" preset="card" title="新增IR单" style="width: 600px">
      <n-form
        ref="formRef"
        :model="newIR"
        :rules="rules"
        label-placement="left"
        label-width="auto"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="问题单号" path="orderId">
          <n-input v-model:value="newIR.orderId" />
        </n-form-item>
        <n-form-item label="应用名称" path="appName">
          <n-input v-model:value="newIR.appName" />
        </n-form-item>
        <n-form-item label="EWP责任人" path="ewpOwner">
          <n-input v-model:value="newIR.ewpOwner" />
        </n-form-item>
        <n-form-item label="验收责任人" path="acceptanceOwner">
          <n-input v-model:value="newIR.acceptanceOwner" />
        </n-form-item>
        <n-form-item label="DTS提单人" path="creator">
          <n-input v-model:value="newIR.creator" />
        </n-form-item>
        <n-form-item label="严重程度" path="severity">
          <n-select
            v-model:value="newIR.severity"
            :options="
              searchFormItems.find((item) => item.field === 'severity')?.componentProps.options
            "
          />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showAddModal = false">取消</n-button>
          <n-button type="primary" @click="saveNewIR">保存</n-button>
        </n-space>
      </template>
    </n-modal>

    <n-modal v-model:show="showChartModal" preset="card" style="width: 1000px">
      <n-form>
        <n-form-item label="统计日期范围">
          <n-date-picker
            v-model:value="chartDateRange"
            type="daterange"
            clearable
            :default-value="getDefaultDateRange()"
          />
        </n-form-item>
      </n-form>
      <div ref="chartRef" style="height: 400px; margin-bottom: 20px"></div>

      <n-data-table
        :columns="statColumns"
        :data="statTableData"
        :pagination="false"
        :bordered="false"
        striped
      />

      <template #footer>
        <n-space justify="end">
          <n-button @click="showChartModal = false">关闭</n-button>
        </n-space>
      </template>
    </n-modal>

    <!-- 提IR弹窗 -->
    <n-modal
      v-model:show="showModal"
      title="提IR"
      preset="dialog"
      positive-text="创建"
      :negative-text="'取消'"
      @positive-click="handleSubmitIR"
      @negative-click="handleCloseSubmit"
      @close="handleCloseSubmit"
      style="width: 800px; height: 90vh; overflow-y: auto"
    >
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="项目名称" path="projectName">
          <n-input v-model:value="formData.projectName" type="text" placeholder="请输入项目名称" />
        </n-form-item>
        <n-form-item label="标题" path="IRtitle">
          <n-input
            v-model:value="formData.title"
            type="textarea"
            :autosize="{
              minRows: 2,
            }"
            placeholder="请输入标题"
          />
        </n-form-item>
        <n-form-item label="描述" path="desp">
          <n-input
            v-model:value="formData.description"
            type="textarea"
            :autosize="{
              minRows: 11,
            }"
            placeholder="请输入描述"
          />
        </n-form-item>
        <n-form-item label="" path="">
          <div style="width: 100%; display: flex; justify-content: flex-end">
            <n-button size="small" style="margin-right: 12px" @click="saveTemplate"
              >保存描述为模板</n-button
            >
            <n-button size="small" type="primary" @click="openTemplateModal">模板管理</n-button>
          </div>
        </n-form-item>
        <n-form-item label="附件" path="rule">
          <n-upload
            action="#"
            :custom-request="customRequest"
            :multiple="true"
            :default-file-list="fileList"
            accept=".png,.jpg,.jpeg,.gif,.bmp,.mp4,.mov,.wmv,.avi"
            directory-dnd
          >
            <n-upload-dragger>
              <n-text style="font-size: 16px">
                点击添加附件或拖拽文件到此处上传（支持PNG, JPG, JPEG, GIF, BMP, MP4, MOV, WMV,
                AVI类型，单个文件大小不得超过30M)
              </n-text>
            </n-upload-dragger>
          </n-upload>
        </n-form-item>
      </n-form>
    </n-modal>
    <!-- 模板管理弹窗 -->
    <n-modal v-model:show="showTemplateModal" title="模板管理" preset="dialog" style="width: 500px">
      <n-card style="overflow-y: auto">
        <!-- 模板列表 -->
        <n-list bordered>
          <n-list-item
            v-for="(template, index) in templates"
            :key="template.id"
            :class="{ 'selected-item': selectedIndex === index }"
            @click="selectTemplate(index)"
          >
            <template #suffix>
              <n-space style="width: 200px; justify-content: end">
                <n-button size="small" @click="editTemplate(index)">修改模板名称</n-button>
                <n-button size="small" type="error" @click="deleteTemplate(index)">删除</n-button>
              </n-space>
            </template>
            <div class="template-title">
              {{ template.templateTitle }}
            </div>
          </n-list-item>
        </n-list>

        <n-space v-show="selectedIndex !== -1" vertical style="margin-top: 20px">
          <n-input
            v-model:value="currentTemplate.templateTitle"
            placeholder="输入模板名称（默认自动生成）"
          />
        </n-space>

        <!-- 操作按钮 -->
        <n-space justify="end" style="margin-top: 20px">
          <n-button v-show="selectedIndex !== -1" @click="saveTemplateTitle">保存模板名称</n-button>
          <n-button @click="applySelectedTemplate">应用当前模板</n-button>
        </n-space>
      </n-card>
    </n-modal>
  </div>
</template>
<script lang="ts">
  export default {
    name: 'IrManagement',
  };
</script>
<script lang="ts" setup>
  import { ref, reactive, onMounted, computed, watch, onUnmounted } from 'vue';
  import {
    NCard,
    NForm,
    NFormItem,
    NInput,
    NSelect,
    NButton,
    NSpace,
    NGrid,
    NGridItem,
    NIcon,
    NDataTable,
    useMessage,
    NModal,
    NDatePicker,
    FormRules,
    UploadCustomRequestOptions,
  } from 'naive-ui';
  import { ChevronDown, ChevronUp } from '@vicons/ionicons5';
  import { createColumns, searchFormItems, ListData, TemplateItem } from './columns';
  import {
    getIRList,
    createIR,
    updateIR,
    deleteIR,
    batchCreateIR,
    submitIR,
    getUnsubmittedStats,
    exportExcel,
  } from '@/api/dataview/irManagement';
  import type { FormInst, UploadFileInfo } from 'naive-ui';
  import * as echarts from 'echarts';
  import { filterObjectValues } from '@/utils';
  import { ewpService as service } from '@/utils/axios';
  import axios from 'axios';

  const tableHeight = ref(450);
  const message = useMessage();
  const fileList = ref([] as UploadFileInfo[]);
  const showModal = ref(false);
  const defaultDesp = `【应用信息】
应用版本：1.x.x
【设备信息】
Mate60 Pro：xxx
【预置条件】

【测试步骤】

【预期结果】

【实际结果】`;
  const formData = reactive({
    dtsOrder: '',
    projectName: '',
    title: '',
    description: defaultDesp,
    files: [],
  });
  const showTemplateModal = ref(false);
  const selectedIndex = ref(-1);
  const templates = ref<TemplateItem[]>([]);
  const currentTemplate = reactive<TemplateItem>({
    id: null,
    templateTitle: '',
    content: '',
    originalContent: '',
  });
  const rules: FormRules = {
    orderId: [
      {
        required: true,
        message: '请输入问题单号',
      },
    ],
    appName: [
      {
        required: true,
        message: '请输入应用名称',
      },
    ],
    ewpOwner: [
      {
        required: true,
        message: '请输入EWP责任人',
      },
    ],
    acceptanceOwner: [
      {
        required: true,
        message: '请输入验收责任人',
      },
    ],
    creator: [
      {
        required: true,
        message: '请输入DTS提单人',
      },
    ],
  };
  const searchForm = reactive({
    orderId: '',
    appName: '',
    ewpOwner: '',
    acceptanceOwner: '',
    creator: '',
    irOrderStatus: null,
    irRegisTimeRange: null,
    irOverTimeRange: null,
  });
  // 计算属性：内容简写
  const shortContent = computed(() => (content) => {
    return content.length > 20 ? content.substring(0, 20) + '...' : content;
  });
  //上传
  const customRequest = async ({
    file,
    data,
    headers,
    withCredentials,
    action,
    onFinish,
    onError,
    onProgress,
  }: UploadCustomRequestOptions) => {
    try {
      formData.files.push(file.file);
      onFinish();
    } catch (err) {
      console.log('err');
      fileList.value = [];
      onError();
    }
  };
  const tableData = ref<ListData[]>([]);
  const loading = ref(false);
  const paginationReactive = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 50],
    itemCount: 0,
    prefix({ itemCount }) {
      return `Total：${itemCount}`;
    },
    onChange: (page: number) => {
      paginationReactive.page = page;
      fetchData();
    },
    onUpdatePageSize: (pageSize: number) => {
      paginationReactive.pageSize = pageSize;
      paginationReactive.page = 1;
      fetchData();
    },
  });

  const isExpanded = ref(false);
  const visibleSearchFormItems = computed(() => {
    return isExpanded.value ? searchFormItems : searchFormItems.slice(0, 4);
  });

  const handleSubmitIR = async () => {
    try {
      const params = new FormData();
      Object.keys(formData).forEach((key) => {
        if (Array.isArray(formData[key])) {
          for (let i = 0; i < formData[key].length; i++) {
            params.append(key, formData[key][i]);
          }
        } else {
          params.append(key, formData[key]);
        }
      });
      console.log(`formData`, formData.files);
      let res = await submitIR(params);
      message.success('创建成功');
      console.log(`res`, res);
    } catch (err) {
      message.error(err?.msg);
      console.log('err', err);
      fileList.value = [];
    } finally {
      handleCloseSubmit();
    }
  };

  const handleCloseSubmit = () => {
    formData.files = [];
    formData.dtsOrder = '';
    formData.description = defaultDesp;
    showModal.value = false;
  };

  const handleSearch = () => {
    paginationReactive.page = 1;
    fetchData();
  };

  const resetForm = () => {
    Object.keys(searchForm).forEach((key) => {
      searchForm[key] = '';
    });
    searchForm.irOverTimeRange = null;
    searchForm.irRegisTimeRange = null;
    fetchData();
  };

  const toggleExpandForm = () => {
    isExpanded.value = !isExpanded.value;
    if (isExpanded.value) {
      tableHeight.value = 400;
    } else {
      tableHeight.value = 450;
    }
  };

  // 首先定义查询参数的接口
  interface QueryParams {
    pageNo: number;
    pageSize: number;
    orderId: string;
    appName: string;
    ewpOwner: string;
    creator: string;
    acceptanceOwner: string;
    irOrderStatus: string | null;
    startRegisTime?: string;
    endRegisTime?: string;
    [key: string]: any;
  }

  // 修改 ListData 和 API 响应类型定义
  interface ApiResponse<T> {
    records: T[];
    total: number;
  }

  interface IRData extends ListData {
    irOrderStatus: string;
    _id: string;
  }

  const fetchData = async () => {
    loading.value = true;
    try {
      const params: any = {
        ...filterObjectValues(searchForm),
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
      };

      if (searchForm.irRegisTimeRange && Array.isArray(searchForm.irRegisTimeRange)) {
        const [start, end] = searchForm.irRegisTimeRange as [number, number];
        params.startRegisTime = formatDate(start);
        params.endRegisTime = formatDate(end);
        delete params.irRegisTimeRange;
      }

      if (searchForm.irOverTimeRange && Array.isArray(searchForm.irOverTimeRange)) {
        const [start, end] = searchForm.irOverTimeRange as [number, number];
        params.startTime = formatDate(start);
        params.endTime = formatDate(end);
        delete params.irOverTimeRange;
      }
      const res = await getIRList(params);
      tableData.value = res.records;
      paginationReactive.itemCount = res.total;
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      loading.value = false;
    }
  };

  // 修改 formatDate 函数
  const formatDate = (date: number | Date | null): string => {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const showEditModal = ref(false);
  const editingIR = ref<Partial<ListData>>({} as Partial<ListData>);

  const handleEdit = (row: ListData) => {
    editingIR.value = { ...row };
    showEditModal.value = true;
  };

  const saveEdit = async () => {
    try {
      if (!editingIR.value._id) {
        message.error('缺少ID信息');
        return;
      }
      await updateIR(editingIR.value._id, editingIR.value);
      message.success('IR单更新成功');
      showEditModal.value = false;
      fetchData();
    } catch (error: any) {
      console.error('Failed to update IR:', error);
      message.error('IR单更新失败');
    }
  };

  const showAddModal = ref(false);
  const newIR = ref<IRData>({} as IRData);

  const formRef = ref<FormInst | null>(null);

  const saveNewIR = async () => {
    try {
      newIR.value.irOrderStatus = '未提单';
      await formRef.value?.validate();
      await createIR(newIR.value);
      message.success('IR单创建成功');
      showAddModal.value = false;
      newIR.value = {} as IRData;
      fetchData();
    } catch (error: any) {
      if (error?.response?.status === 400) {
        message.error(error.response.data.message || 'IR单号已存在');
      } else {
        message.error(error?.message || 'IR单创建失败');
      }
    }
  };

  const handleDelete = async (row: ListData) => {
    try {
      await deleteIR(row._id);
      message.success('IR单删除成功');
      fetchData();
    } catch (error) {
      console.error('Failed to delete IR:', error);
      message.error('IR单删除失败');
    }
  };

  const buildIRtitle = (str) => {
    // 匹配【开头，允许括号内测存在空格，支持中文或Beta测试（不区分大小写）
    const regex = /【\s*(内测|beta测试)\s*】/i;
    const match = regex.exec(str);
    if (match) {
      return str.substring(match.index);
    }
    return str;
  };

  const openDialog = async (row: ListData) => {
    try {
      const { records } = await service.post(`/management/workOrder/query`, {
        orderId: row.orderId,
        severity: [],
        appLevel: [],
        status: [],
        pageNo: 1,
        pageSize: 10,
        sortField: 'riskScore',
        sortOrder: 'desc',
      });
      formData.projectName = row.appName + 'HarmonyOS应用开发项目';
      formData.title = buildIRtitle(records?.[0]?.description);
      formData.dtsOrder = row.orderId;
      formData.files = [];
      showModal.value = true;
    } catch (error) {
      console.error('Failed to fetch ticket data:', error);
    } finally {
    }
  };

  const columns = createColumns(true, handleEdit, undefined, handleDelete, openDialog);

  const showChartModal = ref(false);
  const chartRef = ref<HTMLElement | null>(null);
  let chart: echarts.ECharts | null = null;

  // 首先修改接口类型定义
  interface DailyStat {
    date: string;
    count: number;
  }

  interface StatData {
    dailyStats: DailyStat[];
    totalCount: number;
    acceptanceOwner: string;
  }

  const chartDateRange = ref<[number, number] | null>(null);

  const getDefaultDateRange = (): [number, number] => {
    const end = new Date();
    const start = new Date();
    start.setHours(0, 0, 0, 0);
    end.setHours(23, 59, 59, 999);
    return [start.getTime(), end.getTime()];
  };

  const fetchChartData = async () => {
    try {
      const params: any = {};
      if (chartDateRange.value) {
        const [start, end] = chartDateRange.value;
        params.startDate = formatDate(start);
        params.endDate = formatDate(end);
      }
      const res = await getUnsubmittedStats(params);
      console.log('res:', res);
      if (res.data) {
        renderChart(res.data);
        const totalCount = res.data.reduce((sum, item) => sum + item.totalCount, 0);
        const tableData = [
          ...res.data,
          {
            acceptanceOwner: '总计',
            totalCount: totalCount,
            dailyStats: [],
          },
        ];
        statTableData.value = tableData;
      } else {
        message.error('获取统计数据失败');
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
      message.error('获取统计数据失败');
    }
  };

  async function clickExportExcel() {
    try {
      const response = await axios.get('/admin/irmange/exportdata', {
        responseType: 'blob', // 确保设置为 blob
      });
      // 获取文件名
      const contentDisposition = response.headers['content-disposition'];

      // 创建 Blob 对象
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });

      // 创建下载链接
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.setAttribute('download', 'IR导出.xlsx'); // 设置文件名
      document.body.appendChild(link);
      link.click();
      // 清理
      setTimeout(() => {
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
      }, 100);
    } catch (error) {
      console.error('下载文件失败:', error);
      alert('下载文件失败，请稍后再试。');
    }
  }
  const renderChart = (data: StatData[]) => {
    if (!chartRef.value) return;

    chart = echarts.init(chartRef.value);

    const option = {
      title: {
        text: '验收责任人未提单统计',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: (params: any) => {
          const dataIndex = params[0].dataIndex;
          const statData = data[dataIndex];
          let html = `<div>${statData.acceptanceOwner}</div>`;
          html += `<div>总数量：${statData.totalCount}</div>`;
          html += '<div>每日统计：</div>';
          statData.dailyStats.forEach((stat) => {
            html += `<div>${stat.date}: ${stat.count}个</div>`;
          });
          return html;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: data.map((item) => item.acceptanceOwner),
        axisLabel: {
          rotate: 45,
        },
      },
      yAxis: {
        type: 'value',
        name: '未提单总数量',
      },
      series: [
        {
          type: 'bar',
          data: data.map((item) => item.totalCount),
          label: {
            show: true,
            position: 'top',
          },
        },
      ],
    };

    chart.setOption(option);
  };

  watch(
    () => showChartModal.value,
    (newVal) => {
      if (newVal) {
        // 当模态框打开时，如果没有选择日期范围，则设置默认值
        if (!chartDateRange.value) {
          chartDateRange.value = getDefaultDateRange();
        }
        fetchChartData();
      }
    }
  );

  watch(chartDateRange, () => {
    if (showChartModal.value) {
      fetchChartData();
    }
  });

  onUnmounted(() => {
    if (chart) {
      chart.dispose();
      chart = null;
    }
  });

  onMounted(() => {
    const saved = localStorage.getItem('templates');
    if (saved) {
      templates.value = JSON.parse(saved);
    }
    fetchData();
  });

  // 打开模板弹窗
  const openTemplateModal = () => {
    showTemplateModal.value = true;
    selectedIndex.value = -1;
    Object.assign(currentTemplate, {
      id: null,
      templateTitle: '',
      content: '',
    });
  };

  // 保存模板到本地存储
  const saveToLocalStorage = () => {
    localStorage.setItem('templates', JSON.stringify(templates.value));
  };

  // 选择模板
  const selectTemplate = (index) => {
    selectedIndex.value = index;
    Object.assign(currentTemplate, templates.value[index]);
  };

  // 保存模板
  const saveTemplate = () => {
    if (!formData.description.trim()) {
      message.warning('模板内容不能为空');
      return;
    }
    // 添加新模板
    templates.value.push({
      id: Date.now(),
      templateTitle: `模板_${Date.now()}`,
      content: formData.description,
      originalContent: '',
    });
    saveToLocalStorage();
    message.success('模板保存成功');
  };

  // 编辑模板名称
  const editTemplate = (index) => {
    selectTemplate(index);
  };

  const saveTemplateTitle = () => {
    const template = templates.value[selectedIndex.value];
    Object.assign(template, {
      templateTitle: currentTemplate.templateTitle,
    });
    saveToLocalStorage();
    message.success('保存成功');
  };

  // 删除模板
  const deleteTemplate = (index) => {
    templates.value.splice(index, 1);
    saveToLocalStorage();
    selectedIndex.value = -1;
    message.success('模板删除成功');
  };

  // 应用选中模板
  const applySelectedTemplate = () => {
    if (selectedIndex.value !== -1) {
      formData.description = templates.value[selectedIndex.value].content;
      showTemplateModal.value = false;
    } else {
      message.warning('请先选择模板');
    }
  };

  window.addEventListener('resize', () => {
    chart?.resize();
  });

  const statColumns = [
    {
      title: '验收责任人',
      key: 'acceptanceOwner',
    },
    {
      title: '未提单总数',
      key: 'totalCount',
    },
    {
      title: '每日统计',
      key: 'dailyStats',
      render(row) {
        if (row.acceptanceOwner === '总计') return '';
        return row.dailyStats.map((stat: DailyStat) => `${stat.date}: ${stat.count}个`).join(', ');
      },
    },
  ];

  const statTableData = ref<StatData[]>([]);
</script>

<style scoped>
  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 12px;
  }
  .selected-item {
    background-color: #f3f4f6;
    border-left: 3px solid #2d8cf0;
    transition: all 0.2s ease;
  }

  .template-title {
    display: flex;
    align-items: center;
    font-weight: 500;
  }
</style>
