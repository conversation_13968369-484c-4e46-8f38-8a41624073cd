import { format } from 'date-fns';

const DATE_TIME_FORMAT = 'yyyy-MM-dd HH:mm';
const DATE_TIME_SECOND_FORMAT = 'yyyy-MM-dd HH:mm:ss';
const DATE_FORMAT = 'yyyy-MM-dd ';

export function formatToDateTime(date: Date | number, formatStr = DATE_TIME_FORMAT): string {
  return format(date, formatStr);
}

export function formatToDateSecondTime(date: Date | number, formatStr = DATE_TIME_SECOND_FORMAT): string {
  return format(date, formatStr);
}

export function formatToDate(date: Date | number, formatStr = DATE_FORMAT): string {
  return format(date, formatStr);
}

export function formatToDateEnd(date: Date | number): string {
  return format(new Date(formatToDate(date,DATE_FORMAT)+" 23:59:59"), DATE_TIME_SECOND_FORMAT);
}

export function formatToDateStart(date: Date | number): string {
  return format(new Date(formatToDate(date)+" 00:00:00"), DATE_TIME_SECOND_FORMAT);
}
