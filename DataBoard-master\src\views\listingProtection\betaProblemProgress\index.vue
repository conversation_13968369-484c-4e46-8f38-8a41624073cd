<template>
<div class="table-header">
    <div class="title">应用归属代表处 Beta 问题进展</div>
    <n-data-table :row-class-name="rowClassName" :columns="columns" :data="data" :pagination="pagination" :single-line="false" table-layout="fixed" />
</div>
</template>

<script lang="ts">
import {
    defineComponent,
    h
} from 'vue';
import {
    NButton,
    NTag,
    useMessage,
    NGradientText,
    NTooltip
} from 'naive-ui';

function renderTooltip(trigger, content) {
    return h(NTooltip, null, {
        trigger: () => trigger,
        default: () => content
    })
}

function createColumns({
    sendMail
}) {
    return [{
            title: '归属',
            key: 'belong',
            width: 180,
        },
        {
            title: '领域责任人',
            key: 'personResponsible',
            width: 180,

        },
        {
            title: '提示',
            key: 'prompt',
            children: [{
                    title: '待锁定',
                    key: 'pending'
                },
                {
                    title: '总数',
                    key: 'total'
                }
            ]
        },
        {
            title: '一般',
            key: 'normal',
            children: [{
                    title: '待锁定',
                    key: 'pending'
                },
                {
                    title: '总数',
                    key: 'total'
                }
            ]
        },
        {
            title: '严重',
            key: 'serious',
            children: [{
                    title: '待锁定',
                    key: 'pending'
                },
                {
                    title: '总数',
                    key: 'total'
                }
            ]
        },
        {
            title: '致命',
            key: 'fatal',
            children: [{
                    title: '待锁定',
                    key: 'pending'
                },
                {
                    title: '总数',
                    key: 'total'
                }
            ]
        },
        {
            title: '合计',
            key: 'total',
            children: [{
                    title: '待锁定',
                    key: 'pending'
                },
                {
                    title: '总数',
                    key: 'total'
                }
            ]
        },
        {
            title: '问题锁定&闭环率',
            key: 'closeRate',
            width: 180,
            className: 'closeRate'
        },

    ]
}

function createData() {
    let arrConcat: any = []
    Array.from({
        length: 20
    }).map((_, i) => {
        arrConcat.push({
            key: i + 6,
            belong: '--',
            personResponsible: '--',
            pending: 0,
            total: 0,
            closeRate: 0
        })
    })

    return [{
            key: 0,
            belong: '辽宁',
            personResponsible: '姚季',
            pending: 2,
            total: 28,
            closeRate: '90.07%',
        },
        {
            key: 1,
            belong: '长尾',
            personResponsible: '李长路',
            pending: 6,
            total: 86,
            closeRate: '85.57%',
        },
        {
            key: 2,
            belong: '青海',
            personResponsible: '蒋骁尧',
            pending: 0,
            total: 3,
            closeRate: '70.00%',
        },
        {
            key: 3,
            belong: '江西',
            personResponsible: '王宁',
            pending: 7,
            total: 35,
            closeRate: '68.29%',
        },
        {
            key: 4,
            belong: '--',
            personResponsible: '--',
            pending: 0,
            total: 0,
            closeRate: '0',
        },
        {
            key: 5,
            belong: '--',
            personResponsible: '--',
            pending: 0,
            total: 0,
            closeRate: '0',
        },
    ].concat(arrConcat)
}
export default defineComponent({
    name: 'Application',
    setup() {
        const message = useMessage();
        return {
            data: createData(),
            columns: createColumns({
                sendMail(rowData) {
                    message.info(`send mail to ${rowData.name}`);
                },
            }),
            rowClassName(row: any) {
                if (parseFloat(row.closeRate) >= parseFloat('60%')) {
                    return 'too-fine'
                }
                return ''
            },
            pagination: {
                pageSize: 10,
            },
        };
    },
});
</script>

<style lang="less" scoped>
.table-header {
    margin-top: 20px;
}

.table-header .title {
    font-size: 18px;
    font-weight: bold;
}

:deep(table thead tr th) {
    background-color: #bdd7ee !important;
    font-size: 16px;
    text-align: center !important;
    font-weight: bold !important;
}

/* 设置单元格内容水平和垂直居中 */
:deep(table tbody tr td) {
    text-align: center !important;
}

:deep(.too-fine .closeRate) {
    background-color: rgba(0, 128, 0, 0.75) !important;
}
</style>
