<template>
  <div class="batch-process-container">
    <n-card class="main-card">
      <!-- 顶部统计和操作区 -->
      <div class="action-bar">
        <n-space align="center">
          <n-statistic label="待处理总数" :value="selectedRows.length" />
          <n-divider vertical />
          <n-statistic label="已处理" :value="processedCount" />
          <n-divider vertical />
          <n-date-picker
            v-model:value="processDate"
            type="date"
            clearable
            :is-date-disabled="disablePastDates"
            placeholder="请选择计划解决日期"
          />
        </n-space>
        <n-space>
          <n-button
            type="primary"
            @click="handleBatchApprove"
            :loading="processing"
            :disabled="!processDate"
          >
            全部通过
          </n-button>
        </n-space>
      </div>

      <!-- 卡片网格 -->
      <n-grid :x-gap="12" :y-gap="12" :cols="4" responsive="screen">
        <n-grid-item v-for="item in selectedRows" :key="item.orderId">
          <n-card :class="{ 'card-processed': item.processed }" embedded>
            <!-- 卡片头部 -->
            <template #header>
              <div class="card-header">
                <div class="order-info">
                  <n-tag :type="getSeverityType(item.severity)" size="small">
                    {{ item.severity }}
                  </n-tag>
                  <span class="order-id">{{ item.orderId }}</span>
                </div>
                <n-space>
                  <n-button size="small" type="primary" ghost @click="openDTS(item.orderId)">
                    查看DTS
                  </n-button>
                  <n-button
                    size="small"
                    type="success"
                    :disabled="item.processed || !processDate"
                    @click="handleSingleApprove(item)"
                  >
                    通过
                  </n-button>
                </n-space>
              </div>
            </template>

            <!-- 卡片内容 -->
            <div class="card-content">
              <!-- <div class="info-row">
                <span class="label">应用名称:</span>
                <span class="value">{{ item.appName }}</span>
              </div> -->
              <div class="info-row">
                <span class="label">问题描述:</span>
                <div class="description">
                  <n-ellipsis :line-clamp="2" trigger="click">
                    {{ item.description.split('】').pop() }}
                  </n-ellipsis>
                </div>
              </div>
              <div class="info-row">
                <span class="label">当前状态:</span>
                <span class="value">{{ item.status }}</span>
              </div>
              <div class="info-row">
                <span class="label">风险指数:</span>
                <n-tag :type="getRiskType(item.riskScore)" size="small">
                  {{ item.riskScore }}
                </n-tag>
              </div>
              <div class="info-row" v-if="item.processed">
                <span class="label">处理结果:</span>
                <n-tag :type="item.processResult === 'approved' ? 'success' : 'error'">
                  {{ item.processResult === 'approved' ? '已通过' : '已驳回' }}
                </n-tag>
              </div>
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { useMessage } from 'naive-ui';
  import { batchApprove, singleApprove } from '@/api/dataview/batchProcess';

  interface WorkOrder {
    id: string;
    orderId: string;
    appName: string;
    description: string;
    severity: string;
    currentHandler: string;
    status: string;
    riskScore: number;
    processed?: boolean;
    processResult?: 'approved' | 'rejected';
  }

  const router = useRouter();
  const message = useMessage();

  const selectedRows = ref<WorkOrder[]>([]);
  const processing = ref(false);
  const processDate = ref<number | null>(null);

  // 已处理数量
  const processedCount = computed(() => {
    return selectedRows.value.filter((item) => item.processed).length;
  });

  // 获取严重程度对应的类型
  const getSeverityType = (severity: string) => {
    const map = {
      致命: 'error',
      严重: 'error',
      一般: 'warning',
      提示: 'info',
    };
    return map[severity] || 'default';
  };

  // 打开DTS链接
  const openDTS = (orderId: string) => {
    window.open(`https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${orderId}`, '_blank');
  };

  // 单个通过
  const handleSingleApprove = async (item: WorkOrder) => {
    if (!processDate.value) {
      message.warning('请选择处理日期');
      return;
    }

    try {
      processing.value = true;
      await singleApprove({
        orderId: item.orderId,
        processDate: processDate.value,
      });
      item.processed = true;
      item.processResult = 'approved';
      message.success('通过成功');
    } catch (error) {
      message.error('通过失败');
    } finally {
      processing.value = false;
    }
  };

  // 批量通过
  const handleBatchApprove = async () => {
    if (!processDate.value) {
      message.warning('请选择处理日期');
      return;
    }

    const unprocessedOrders = selectedRows.value.filter((item) => !item.processed);
    if (unprocessedOrders.length === 0) {
      message.warning('没有待处理的工单');
      return;
    }

    try {
      processing.value = true;
      await batchApprove({
        orderIds: unprocessedOrders.map((item) => item.orderId),
        processDate: processDate.value,
      });

      unprocessedOrders.forEach((item) => {
        item.processed = true;
        item.processResult = 'approved';
      });

      message.success('批量通过成功');
    } catch (error) {
      message.error('批量通过失败');
    } finally {
      processing.value = false;
    }
  };

  // 禁用过去的日期
  const disablePastDates = (timestamp: number) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return timestamp < today.getTime();
  };

  const getRiskType = (score: number) => {
    if (score >= 4) return 'error';
    if (score >= 3) return 'warning';
    if (score >= 2) return 'info';
    return 'success';
  };

  // 检查应用名是否一致
  const checkAppNameConsistency = (rows: WorkOrder[]): boolean => {
    if (rows.length <= 1) return true;
    const firstAppName = rows[0].appName;
    return rows.every((row) => row.appName === firstAppName);
  };

  onMounted(() => {
    const rowsData = localStorage.getItem('batchProcessRows');
    console.log('rowsData:', rowsData);

    if (!rowsData) {
      message.error('没有需要处理的数据');
      router.back();
      return;
    }

    try {
      const parsedRows = JSON.parse(rowsData);

      // 检查应用名是否一致
      if (!checkAppNameConsistency(parsedRows)) {
        message.error('只能批量处理相同应用的工单');
        router.back();
        return;
      }

      selectedRows.value = parsedRows;
      localStorage.removeItem('batchProcessRows');
    } catch (error) {
      message.error('数据格式错误');
      router.back();
    }
  });
</script>

<style scoped>
  .batch-process-container {
    padding: 16px;
    background: #f5f7f9;
    min-height: 100vh;
  }

  .main-card {
    min-height: calc(100vh - 32px);
  }

  .action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px 24px;
    background: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .order-info {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .order-id {
    font-weight: 500;
    color: #333;
    font-size: 14px;
  }

  .card-content {
    padding: 16px;
    height: 140px;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .info-row {
    display: flex;
    align-items: flex-start;
    font-size: 14px;
    line-height: 1.6;
  }

  .info-row .label {
    width: 80px;
    color: #666;
    flex-shrink: 0;
  }

  .info-row .description {
    flex: 1;
    color: #333;
  }

  .info-row .value {
    flex: 1;
    color: #333;
  }

  .card-processed {
    opacity: 0.85;
    background: #fafafa;
  }

  :deep(.n-card) {
    transition: all 0.3s ease;
    height: 100%;
  }

  :deep(.n-card:hover) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  :deep(.n-button) {
    padding: 0 12px;
    height: 28px;
  }

  :deep(.n-button--primary-type) {
    font-weight: 500;
  }

  :deep(.n-tag) {
    font-size: 12px;
    padding: 0 8px;
    line-height: 20px;
  }

  :deep(.n-date-picker) {
    width: 200px;
  }

  :deep(.n-grid) {
    margin: 0 -12px;
  }

  :deep(.n-grid-item) {
    padding: 12px;
  }

  :deep(.n-ellipsis) {
    line-height: 1.6;
  }

  :deep(.n-card-header) {
    padding: 0 !important;
  }

  :deep(.n-card__content) {
    /* padding: 0 !important; */
  }
</style>
