<script setup lang="ts">
import { useRoute } from 'vue-router';
import { getLoginInfo } from './index';
import { ref } from 'vue';
import { useUserStore } from '@/store/modules/user';
const userInfo = useUserStore().getUserInfo; // 用户号account  用户名userName
const route = useRoute();
const knowledgeBaseUrl = 'https://dtse.cbg.huawei.com:8767';
const urlMap = {
  knowledgeSearch: '/welcome',
  knowledgeTodos: '/mytodos',
  knowledgeCreation: '/harmonyknowledge',
  knowledgeWishMenus: '/wishmenus',
  knowledgeDashboard: '/datadashboard',
  knowledgeWishContributionList: '/contributionlist',
  knowledgeMap: '/harmonyknowledgeMap',
  knowledgeDataDashboard: '/datadashboard',
  myApplication: '/myapply',
  knowledgeSetting: '/harmonyknowledgeSetting',
};
const iframeUrl = ref('');
const getIframeUrl = async () => {
  const pathName = location.pathname.split('/').pop() ?? 'knowledgeSearch';
  if (route.params.id && location.pathname.includes('knowledgeSearch')) {
    iframeUrl.value = `${knowledgeBaseUrl}/knowledgeDetail/${route.params.id}`;
  } else {
    iframeUrl.value = knowledgeBaseUrl + urlMap[pathName];
  }
  const { data, code } = await getLoginInfo({
    userNo: userInfo.account,
    userName: userInfo.userName,
  });
  if (code === '200') {
    iframeUrl.value += `?sessionId=${data.sessionId}`;
  }
};
getIframeUrl();
</script>

<template>
  <div>
    <iframe :src="iframeUrl" name="iframe" id="iframe" class="iframe-container"> </iframe>
  </div>
</template>

<style scoped lang="less">
.iframe-container {
  width: 100%;
  height: calc(100vh - 110px);
}
</style>
