import { ewpService as service } from '@/utils/axios';

// 批量通过
export function batchApprove(data: { orderIds: string[] }) {
  return service.post('/workorder/batch-approve', data);
}

// 批量驳回
export function batchReject(data: { orderIds: string[]; reason: string; suggestion: string }) {
  return service.post('/workorder/batch-reject', data);
}

// 单个通过
export function singleApprove(orderId: string) {
  return service.post(`/workorder/${orderId}/approve`);
}

// 单个驳回
export function singleReject(data: { orderId: string; reason: string; suggestion: string }) {
  return service.post(`/workorder/${orderId}/reject`, data);
}
