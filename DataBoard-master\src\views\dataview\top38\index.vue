<script setup lang="ts">
  import { ref, reactive, onMounted, h, computed } from 'vue';
  import {
    NCard,
    NSpace,
    NForm,
    NFormItem,
    NInput,
    NSelect,
    NButton,
    NDataTable,
    NPagination,
    NIcon,
    NPopconfirm,
    NInputGroup,
    NGrid,
    NGridItem,
    NCollapse,
    NCollapseItem,
    NTabs,
    NTabPane,
    useMessage,
    NDropdown,
    NSwitch,
    NCheckbox,
    NDrawer,
    NDrawerContent,
    NDivider,
    NTag,
  } from 'naive-ui';
  import {
    SearchOutlined,
    ReloadOutlined,
    PlusOutlined,
    DownOutlined,
    UpOutlined,
    SettingOutlined,
    EditOutlined,
    SaveOutlined,
  } from '@vicons/antd';
  import { getTop38List, deleteTop38 } from '@/api/dataview/top38';
  import {
    allColumns,
    mainSearchFields,
    expandedSearchFields,
    statusOptions,
    priorityOptions,
    columnVisible,
    getVisibleColumns,
    renderEditableCell,
    renderEditableSelect,
    getPlaceholder,
    renderDtsTicketCell,
  } from './columns';
  import { SearchParams, TableState, Top38Item } from './types';
  import type { DataTableColumns } from 'naive-ui';
  import { ArrowForward } from '@vicons/ionicons5';
  import { formatToDateEnd, formatToDateStart } from '@/utils/dateUtil';

  const message = useMessage();

  // 搜索表单状态
  const searchParams = reactive<SearchParams>({
    application: '',
    issueCategory: '',
    priority: '',
    status: '',
    dtsTicket: '',
    issueSource: '',
    mainResponsibleParty: '',
    // responsiblePerson: '',
    proposer: '',
    product: '',
    // keywords: '',
    // module: '',
    // sentimentLevel: '',
    topVoiceOpinion: '',
    goLiveTime: null,
    planProgress: '',
    suggestedPriority: '',
    suggestedResolutionTime: null,
    // reasonHighPriorityUnmet: '',
    referenceVolume: [0, 9999],
    // workOrder: '',
    // workOrderLab: '',
    // vocOther: '',
    // demandMatch: '',
    // week17: '',
    // week18: '',
    // totalCumulative: '',
    // proportion: '',
    // entrySource: '',
    // issueDescription: '',
    // standardDescription: '',
    // assistanceLevel: '',
    pageNo: 1,
    pageSize: 10,
  });

  // 表格状态
  const tableState = reactive<TableState>({
    loading: false,
    data: [],
    pagination: {
      pageNo: 1,
      pageSize: 10,
      itemCount: 0,
      pageCount: 0,
    },
  });

  // 是否展开更多搜索选项
  const isExpanded = ref(false);

  // 列设置抽屉
  const showColumnDrawer = ref(false);

  // 弹窗状态
  const showModal = ref(false);
  const isEditMode = ref(false);
  const currentEditData = ref<Top38Item | undefined>(undefined);

  // 切换展开/收起搜索选项
  const toggleExpand = () => {
    isExpanded.value = !isExpanded.value;
  };

  // 重置搜索表单
  const resetSearchForm = () => {
    Object.keys(searchParams).forEach((key) => {
      if (key !== 'page' && key !== 'pageSize') {
        searchParams[key] = '';
      }
    });
    searchByParams();
  };

  const searchByParams = () => {
    searchParams.pageNo = 1;
    searchData();
  };

  // 搜索数据
  const searchData = async () => {
    tableState.loading = true;

    // 使用mock数据展示
    const useMockData = false;

    if (useMockData) {
      // Mock数据
      setTimeout(() => {
        const mockData = [
          {
            id: 1,
            application: 'App Store',
            issueSource: '用户反馈',
            issueCategory: '功能缺失',
            priority: '高',
            dtsTicket: 'DTS2023001',
            topVoiceOpinion: '无法下载应用',
            mainResponsibleParty: '应用商店团队',
            responsiblePerson: '张三',
            goLiveTime: '2023-12-30',
            planProgress: '已完成80%，预计下周上线',
            status: '处理中',
            proposer: '李四',
            product: '应用市场',
            suggestedPriority: '高',
            suggestedResolutionTime: '2023-12-25',
            reasonHighPriorityUnmet: '影响用户体验',
            referenceVolume: '2000+',
            workOrder: 'WO20231201',
            workOrderLab: 'Lab A',
            vocOther: '用户投诉较多',
            demandMatch: '高',
            week17: '15',
            week18: '12',
            totalCumulative: '27',
            proportion: '15%',
            keywords: '下载,应用,失败',
            entrySource: '客服',
            issueDescription: '用户反馈无法下载应用，点击下载按钮无反应',
            module: '下载模块',
            standardDescription: '应用下载功能异常',
            assistanceLevel: '中',
            sentimentLevel: '负面',
          },
          {
            id: 2,
            application: '相册',
            issueSource: '内部测试',
            issueCategory: 'Bug',
            priority: '中',
            dtsTicket: 'DTS2023002',
            topVoiceOpinion: '照片无法同步',
            mainResponsibleParty: '相册团队',
            responsiblePerson: '王五',
            goLiveTime: '2023-11-15',
            planProgress: '已修复，等待发布',
            status: '已解决',
            proposer: '赵六',
            product: '相册应用',
            suggestedPriority: '中',
            suggestedResolutionTime: '2023-11-10',
            reasonHighPriorityUnmet: '',
            referenceVolume: '500+',
            workOrder: 'WO20231101',
            workOrderLab: 'Lab B',
            vocOther: '',
            demandMatch: '中',
            week17: '8',
            week18: '5',
            totalCumulative: '13',
            proportion: '7%',
            keywords: '相册,同步,云端',
            entrySource: '测试',
            issueDescription: '相册照片无法同步到云端，提示网络错误',
            module: '同步模块',
            standardDescription: '相册云同步功能异常',
            assistanceLevel: '低',
            sentimentLevel: '中性',
          },
          {
            id: 3,
            application: '浏览器',
            issueSource: '用户反馈',
            issueCategory: '性能问题',
            priority: '极高',
            dtsTicket: 'DTS2023003',
            topVoiceOpinion: '浏览器崩溃',
            mainResponsibleParty: '浏览器团队',
            responsiblePerson: '钱七',
            goLiveTime: '2023-12-10',
            planProgress: '正在修复中，预计本周完成',
            status: '处理中',
            proposer: '孙八',
            product: '浏览器',
            suggestedPriority: '极高',
            suggestedResolutionTime: '2023-12-05',
            reasonHighPriorityUnmet: '严重影响用户使用',
            referenceVolume: '5000+',
            workOrder: 'WO20231202',
            workOrderLab: 'Lab C',
            vocOther: '社交媒体大量投诉',
            demandMatch: '高',
            week17: '30',
            week18: '25',
            totalCumulative: '55',
            proportion: '30%',
            keywords: '浏览器,崩溃,卡死',
            entrySource: '社交媒体',
            issueDescription: '浏览器打开多个标签页后容易崩溃',
            module: '内核模块',
            standardDescription: '浏览器多标签页稳定性问题',
            assistanceLevel: '高',
            sentimentLevel: '极负面',
          },
          {
            id: 4,
            application: '日历',
            issueSource: '内部测试',
            issueCategory: 'UI问题',
            priority: '低',
            dtsTicket: 'DTS2023004',
            topVoiceOpinion: '界面显示异常',
            mainResponsibleParty: '日历团队',
            responsiblePerson: '周九',
            goLiveTime: '2024-01-15',
            planProgress: '已纳入下一版本计划',
            status: '待解决',
            proposer: '吴十',
            product: '日历应用',
            suggestedPriority: '低',
            suggestedResolutionTime: '2024-01-10',
            reasonHighPriorityUnmet: '',
            referenceVolume: '100+',
            workOrder: 'WO20231203',
            workOrderLab: 'Lab D',
            vocOther: '',
            demandMatch: '低',
            week17: '3',
            week18: '2',
            totalCumulative: '5',
            proportion: '3%',
            keywords: '日历,UI,显示',
            entrySource: '测试',
            issueDescription: '日历在某些分辨率下UI显示错位',
            module: 'UI模块',
            standardDescription: '日历UI适配问题',
            assistanceLevel: '低',
            sentimentLevel: '中性',
          },
          {
            id: 5,
            application: '邮件',
            issueSource: '用户反馈',
            issueCategory: '功能缺失',
            priority: '高',
            dtsTicket: 'DTS2023005',
            topVoiceOpinion: '无法添加附件',
            mainResponsibleParty: '邮件团队',
            responsiblePerson: '郑十一',
            goLiveTime: '2023-12-20',
            planProgress: '开发中，已完成50%',
            status: '处理中',
            proposer: '王十二',
            product: '邮件应用',
            suggestedPriority: '高',
            suggestedResolutionTime: '2023-12-15',
            reasonHighPriorityUnmet: '影响核心功能',
            referenceVolume: '1500+',
            workOrder: 'WO20231204',
            workOrderLab: 'Lab E',
            vocOther: '用户投诉',
            demandMatch: '高',
            week17: '18',
            week18: '15',
            totalCumulative: '33',
            proportion: '18%',
            keywords: '邮件,附件,上传',
            entrySource: '客服',
            issueDescription: '邮件应用无法添加大于10MB的附件',
            module: '附件模块',
            standardDescription: '邮件附件大小限制问题',
            assistanceLevel: '中',
            sentimentLevel: '负面',
          },
        ];

        tableState.data = [...mockData, ...mockData, ...mockData];
        tableState.pagination = {
          pageNo: 1,
          pageSize: 8,
          itemCount: mockData.length,
          pageCount: Math.ceil(mockData.length / 10),
        };
        tableState.loading = false;
      }, 500);
      return;
    }

    // 处理日期，范围值
    const param = JSON.stringify(handleSearchParam(searchParams));
    try {
      const res = await getTop38List(param);
      const responseData = res as any; // 临时使用any类型来处理响应

      if (responseData && responseData.records) {
        // 如果API返回格式为 { data: { list: [], total: number } }
        tableState.data = responseData.records || [];
        tableState.pagination = {
          pageNo: searchParams.pageNo || 1,
          pageSize: searchParams.pageSize || 8,
          itemCount: responseData.total || 0,
          pageCount: Math.ceil((responseData.total || 0) / (searchParams.pageSize || 8)),
        };
      }
      tableState.loading = false;
    } catch (e) {
      message.error('获取数据失败：' + e);
      tableState.loading = false;
    }
  };

  const handleSearchParam = (param) => {
    const result = Object.assign({}, param);
    if (param.suggestedResolutionTime) {
      result.beginSuggestedResolutionTime = formatToDateStart(param.suggestedResolutionTime[0]);
      result.endSuggestedResolutionTime = formatToDateEnd(param.suggestedResolutionTime[1]);
    }
    if (param.goLiveTime) {
      result.beginGoLiveTime = formatToDateStart(param.goLiveTime[0]);
      result.endGoLiveTime = formatToDateEnd(param.goLiveTime[1]);
    }
    if (param.referenceVolume) {
      // result.beginReferenceVolume = param.referenceVolume[0];
      // result.endReferenceVolume = param.referenceVolume[1];
      result.beginReferenceVolume = '';
      result.endReferenceVolume = '';
    }
    delete result['referenceVolume'];
    delete result['goLiveTime'];
    delete result['suggestedResolutionTime'];
    return result;
  };

  // 页码变化
  const handlePageChange = (page: number) => {
    searchParams.pageNo = page;
    searchData();
  };

  // 每页条数变化
  const handlePageSizeChange = (pageSize: number) => {
    searchParams.pageSize = pageSize;
    searchParams.pageNo = 1;
    searchData();
  };

  // 删除记录
  const handleDelete = (id: string | number) => {
    deleteTop38(id)
      .then(() => {
        message.success('删除成功');
        searchData();
      })
      .catch((err) => {
        message.error('删除失败：' + err.message);
      });
  };

  // 添加新记录
  const handleAdd = () => {
    isEditMode.value = false;
    currentEditData.value = undefined;
    showModal.value = true;
  };

  // 编辑记录
  const handleEdit = (row: Top38Item) => {
    isEditMode.value = true;
    currentEditData.value = { ...row };
    showModal.value = true;
  };

  // 保存单元格编辑
  const handleCellSave = (rowId: string | number, cellKey: string, value: any) => {
    // 查找要编辑的行
    const rowIndex = tableState.data.findIndex((item) => item.id === rowId);
    if (rowIndex === -1) return;

    // 更新本地数据
    tableState.data[rowIndex][cellKey] = value;

    // 这里可以添加API调用来更新后端数据
    // updateTop38Cell(rowId, cellKey, value)
    //   .then(() => {
    //     message.success('更新成功');
    //   })
    //   .catch((err) => {
    //     message.error('更新失败：' + err.message);
    //     // 恢复原值
    //     searchData();
    //   });

    // 临时显示成功消息
    message.success('单元格更新成功');
  };

  // 创建可编辑列
  const createEditableColumns = () => {
    const visibleColumns = getVisibleColumns().map((col: any) => {
      const key = String(col.key);

      if (key === 'priority') {
        return {
          ...col,
          render: renderEditableSelect(
            key,
            priorityOptions.filter((opt) => opt.value),
            handleCellSave
          ),
        };
      } else if (key === 'status') {
        return {
          ...col,
          render: renderEditableSelect(
            key,
            statusOptions.filter((opt) => opt.value),
            handleCellSave
          ),
        };
      } else if (key == 'dtsTicket') {
        return {
          ...col,
          render: renderDtsTicketCell(key, handleCellSave),
        };
      } else if (key !== 'id' && typeof col.key === 'string') {
        return {
          ...col,
          render: renderEditableCell(key, handleCellSave),
        };
      }

      return col;
    });

    return [...visibleColumns] as DataTableColumns<Top38Item>;
  };

  // 操作列
  const actionColumn = {
    title: '操作',
    key: 'actions',
    fixed: 'right' as const,
    width: 150,
    render: (row: Top38Item) => {
      return h(
        NSpace,
        { align: 'center', justify: 'center' },
        {
          default: () => [
            h(
              NButton,
              {
                size: 'small',
                type: 'primary',
                onClick: () => handleEdit(row),
              },
              { default: () => '编辑' }
            ),
            h(
              NPopconfirm,
              {
                onPositiveClick: () => handleDelete(row.id!),
              },
              {
                default: () => '确认删除？',
                trigger: () =>
                  h(
                    NButton,
                    {
                      size: 'small',
                      type: 'error',
                    },
                    { default: () => '删除' }
                  ),
              }
            ),
          ],
        }
      );
    },
  };

  const uploadURL = `${window.location.origin}/ewp/management/futOrder/importUpdateData`;

  // 上传
  const isUploading = ref(false);

  // 上传前钩子函数
  const onBeforeUpload = () => {
    isUploading.value = true;
  };

  // 上传完成钩子函数
  const onFinishUpload = ({ file, event }) => {
    try {
      if (file.status === 'finished') {
        const response = JSON.parse(event.target.response);
        if (response.code === 200) {
          message.success('上传成功');
          searchByParams();
        } else {
          message.error('上传失败');
        }
      } else {
        message.error('上传失败');
      }
      isUploading.value = false;
    } catch (e) {
      message.error('上传失败');
      isUploading.value = false;
    }
  };

  // 动态获取表格列
  const tableColumns = computed(() => {
    return createEditableColumns();
  });

  // 打开列设置抽屉
  const openColumnDrawer = () => {
    showColumnDrawer.value = true;
  };

  // 重置列设置
  const resetColumnSettings = () => {
    Object.keys(columnVisible.value).forEach((key) => {
      columnVisible.value[key] = [
        'id',
        'application',
        'issueSource',
        'issueCategory',
        'priority',
        'dtsTicket',
        'status',
      ].includes(key);
    });
  };

  // 获取可设置的列
  const settableColumns = computed(() => {
    return allColumns.filter((col: any) => typeof col.key === 'string');
  });

  // 初始化
  onMounted(() => {
    searchData();
  });
</script>

<template>
  <div class="top38-container">
    <!-- 搜索表单 -->
    <NCard class="search-card" :bordered="false">
      <NForm :model="searchParams" label-placement="left">
        <!-- 主要搜索字段（始终显示） -->
        <div class="main-search-fields">
          <NGrid :cols="16" :x-gap="12" :y-gap="2">
            <NGridItem :span="4" v-for="item in mainSearchFields" :key="item.field">
              <NFormItem :label="item.label" :path="item.field" label-width="80px">
                <n-input
                  clearable
                  :placeholder="getPlaceholder(item)"
                  v-if="item.component === 'Input'"
                  v-model:value="searchParams[item.field]"
                  @keyup.enter="searchData"
                />
                <n-select
                  v-else-if="item.component === 'Select'"
                  :placeholder="getPlaceholder(item)"
                  clearable
                  v-model:value="searchParams[item.field]"
                  :options="item.componentProps?.options"
                  :multiple="item.componentProps?.multiple"
                  :filterable="item.componentProps?.filterable"
                />
                <n-date-picker
                  clearable
                  v-else-if="item.component === 'Date'"
                  v-model:value="searchParams[item.field]"
                  type="daterange"
                />
              </NFormItem>
            </NGridItem>
          </NGrid>
        </div>

        <!-- 展开的搜索字段 -->
        <div v-if="isExpanded" class="expanded-search-row">
          <NGrid :cols="16" :x-gap="12" :y-gap="2">
            <NGridItem :span="4" v-for="item in expandedSearchFields" :key="item.field">
              <NFormItem :label="item.label" :path="item.field" label-width="80px">
                <n-input
                  :placeholder="getPlaceholder(item)"
                  clearable
                  v-if="item.component === 'Input'"
                  v-model:value="searchParams[item.field]"
                  @keyup.enter="searchByParams"
                />
                <n-select
                  v-else-if="item.component === 'Select'"
                  :placeholder="getPlaceholder(item)"
                  clearable
                  v-model:value="searchParams[item.field]"
                  :options="item.componentProps?.options"
                  :multiple="item.componentProps?.multiple"
                  :filterable="item.componentProps?.filterable"
                />
                <n-date-picker
                  clearable
                  v-else-if="item.component === 'Date'"
                  v-model:value="searchParams[item.field]"
                  type="daterange"
                />
                <template v-else-if="item.component === 'numberRange'">
                  <n-input
                    style="width: 45%"
                    clearable
                    placeholder="输入最大数值"
                    v-model:value="searchParams[item.field][0]"
                  />
                  <NIcon style="width: 10%; padding-left: 2.5%" color="rgba(194, 194, 194, 1)">
                    <ArrowForward />
                  </NIcon>
                  <!--                    <span style="width: 10%;padding-left: 2.5%">-&gt;</span>-->
                  <n-input
                    style="width: 45%"
                    clearable
                    placeholder="输入最大数值"
                    v-model:value="searchParams[item.field][1]"
                  />
                </template>
              </NFormItem>
            </NGridItem>
          </NGrid>
        </div>

        <!-- 按钮行 -->
        <div class="button-row">
          <NSpace>
            <NButton type="primary" @click="searchByParams" secondary>
              <template #icon>
                <NIcon><SearchOutlined /></NIcon>
              </template>
              搜索
            </NButton>
            <NButton @click="resetSearchForm" secondary>
              <template #icon>
                <NIcon><ReloadOutlined /></NIcon>
              </template>
              重置
            </NButton>
            <NButton type="error" @click="toggleExpand" class="expand-btn" secondary>
              <template #icon>
                <NIcon>
                  <component :is="isExpanded ? UpOutlined : DownOutlined" />
                </NIcon>
              </template>
              {{ isExpanded ? '收起' : '高级筛选' }}
            </NButton>
          </NSpace>
        </div>
      </NForm>
    </NCard>

    <!-- 数据表格 -->
    <NCard class="table-card" :bordered="false">
      <div class="table-header">
        <div class="table-title">
          <NSpace align="center">
            <span>数据列表</span>
            <NTag type="success" size="small">共 {{ tableState.pagination.itemCount }} 条数据</NTag>
            <NTag type="info" size="small">单击单元格可直接编辑</NTag>
          </NSpace>
        </div>
        <NSpace>
          <!--          <n-upload accept=".xlsx, .xls" :show-file-list="false">
            <n-button type="success" secondary>
              {{ '导入数据' }}
            </n-button>
          </n-upload>-->
          <n-upload
            :show-file-list="false"
            :action="uploadURL"
            :on-before-upload="onBeforeUpload"
            :on-finish="onFinishUpload"
            class="upload"
          >
            <n-button :loading="isUploading">
              <slot>导入数据</slot>
            </n-button>
          </n-upload>
          <NButton type="default" @click="openColumnDrawer" secondary>
            <template #icon>
              <NIcon><SettingOutlined /></NIcon>
            </template>
            列设置
          </NButton>
        </NSpace>
      </div>

      <NDataTable
        :loading="tableState.loading"
        :columns="tableColumns"
        :bordered="false"
        :single-line="false"
        :data="tableState.data"
        :pagination="false"
        :scroll-x="2500"
        striped
        :row-class-name="() => 'editable-row'"
        resizable
        :max-height="410"
      />

      <!-- 分页 -->
      <div class="pagination-container">
        <NPagination
          v-model:page="tableState.pagination.pageNo"
          v-model:page-size="tableState.pagination.pageSize"
          :item-count="tableState.pagination.itemCount"
          :page-slot="5"
          :page-sizes="[10, 20, 30, 50]"
          show-size-picker
          show-quick-jumper
          @update:page="handlePageChange"
          @update:page-size="handlePageSizeChange"
        />
      </div>
    </NCard>

    <!-- 列设置抽屉 -->
    <NDrawer v-model:show="showColumnDrawer" :width="300" placement="right">
      <NDrawerContent title="表格列设置" closable>
        <div class="column-settings">
          <div class="column-settings-header">
            <NButton size="small" @click="resetColumnSettings">重置</NButton>
          </div>
          <NDivider />
          <div class="column-list">
            <div v-for="col in settableColumns" :key="String((col as any).key)" class="column-item">
              <NCheckbox
                v-if="typeof (col as any).key === 'string'"
                v-model:checked="columnVisible[(col as any).key as string]"
              >
                {{ (col as any).title || '' }}
              </NCheckbox>
            </div>
          </div>
        </div>
      </NDrawerContent>
    </NDrawer>
  </div>
</template>

<style lang="less" scoped>
  .top38-container {
    padding: 16px;

    .search-card {
      margin-bottom: 16px;

      .main-search-fields {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
      }

      .expanded-search-row {
        margin-top: 8px;
        margin-bottom: 16px;
      }

      .button-row {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 5px;
        padding-top: 16px;
        border-top: 1px dashed #e8e8e8;
      }
    }

    .table-card {
      .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .table-title {
          font-size: 16px;
          font-weight: 500;
        }
      }

      .data-table {
        // 固定表格高度
        max-height: calc(100vh - 300px);
      }

      .pagination-container {
        margin-top: 16px;
        display: flex;
        justify-content: flex-end;
      }
    }

    .column-settings {
      .column-settings-header {
        display: flex;
        justify-content: flex-end;
      }

      .column-list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;

        .column-item {
          padding: 4px 0;
        }
      }
    }
  }
</style>
