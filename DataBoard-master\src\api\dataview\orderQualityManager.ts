import service from '@/utils/axios';

export const orderAdd = (data) => {
  return service({
    url: `/nonConformStandardOrder/add`,
    method: 'post',
    data,
  });
};

export const orderUpdate = (data) => {
  return service({
    url: `/nonConformStandardOrder/update`,
    method: 'post',
    data,
  });
};
export const orderDelete = (data) => {
  return service({
    url: `/nonConformStandardOrder/delete`,
    method: 'get',
    params: data,
  });
};
export const orderBatchDelete = (data) => {
  return service({
    url: `/nonConformStandardOrder/batchDelete`,
    method: 'post',
    headers: {
      'content-type': 'multipart/form-data; charset=UTF-8',
    },
    data,
  });
};

export const orderQuery = (data) => {
  return service({
    url: `/nonConformStandardOrder/queryPage`,
    method: 'get',
    params: data,
  });
};
export const orderImport = (data) => {
  return service({
    url: `/nonConformStandardOrder/import`,
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};
export const orderDownloadTemplate = () => {
  return service({
    url: `/nonConformStandardOrder/downloadTemplate`,
    method: 'get',
    responseType: 'blob',
  });
};

export const orderPassRateAdd = (data) => {
  return service({
    url: `/samplingQualificationRate/add`,
    method: 'post',
    data,
  });
};

export const orderPassRateUpdate = (data) => {
  return service({
    url: `/samplingQualificationRate/update`,
    method: 'post',
    data,
  });
};
export const orderPassRateDelete = (data) => {
  return service({
    url: `/samplingQualificationRate/delete`,
    method: 'post',
    data,
  });
};

export const orderPassRateQuery = (data) => {
  return service({
    url: `/samplingQualificationRate/query`,
    method: 'post',
    data,
  });
};
export const orderPassRateImport = (data) => {
  return service({
    url: `/samplingQualificationRate/importExcel`,
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};
export const orderPassRateDownloadTemplate = () => {
  return service({
    url: `/samplingQualificationRate/exportExcel`,
    method: 'post',
    responseType: 'blob',
  });
};
