<template>
  <div class="console">
    <div class="n-layout-page-header" style="margin-bottom: 10px">
      <n-card :bordered="false" title="工作台">
        <n-grid cols="2 s:1 m:1 l:2 xl:2 2xl:2" responsive="screen">
          <n-gi>
            <div class="flex items-center">
              <div>
                <n-avatar circle :size="64" :src="schoolboy" />
              </div>
              <div>
                <p class="px-4 text-xl">早安，{{ userInfo.userName }}，开始您一天的工作吧！</p>
                <p class="px-4 text-gray-400">{{ currentTime }}</p>
              </div>
            </div>
          </n-gi>
          <n-gi>
            <div class="flex justify-end w-full">
              <div class="flex flex-col justify-center flex-1 text-right">
                <span class="text-secondary">DTS单总数</span>
                <span class="text-2xl">{{ dtsDetail.total || 0 }}</span>
              </div>
              <div class="flex flex-col justify-center flex-1 text-right">
                <span class="text-secondary">待定界数</span>
                <span class="text-2xl">{{ dtsDetail.solutionImplementation || 0 }}</span>
              </div>
              <div class="flex flex-col justify-center flex-1 text-right">
                <span class="text-secondary">待锁定数</span>
                <span class="text-2xl">{{ dtsDetail.solutionReview || 0 }}</span>
              </div>
              <div class="flex flex-col justify-center flex-1 text-right">
                <span class="text-secondary">严重/致命数</span>
                <span class="text-2xl">{{ dtsDetail.majorAndCriticalNum || 0 }}</span>
              </div>
            </div>
          </n-gi>
        </n-grid>
      </n-card>
    </div>
    <!--数据卡片-->
    <n-grid cols="1 s:2 m:3 l:4 xl:4 2xl:4" responsive="screen" :x-gap="12" :y-gap="8">
      <n-grid-item>
        <NCard
          title="今日新增"
          :segmented="{ content: true, footer: true }"
          size="small"
          :bordered="false"
        >
          <template #header-extra>
            <n-tag type="success">日</n-tag>
          </template>
          <div class="flex justify-between px-1 py-1">
            <n-skeleton v-if="loading" :width="100" size="medium" />
            <CountTo v-else :startVal="1" :endVal="specialDetail.newDts" class="text-3xl" />
          </div>
          <div class="flex justify-between px-1 py-1 hidden">
            <div class="text-sn">
              <n-skeleton v-if="loading" :width="100" size="medium" />
              <template v-else>
                日同比
                <CountTo :startVal="1" suffix="%" :endVal="visits.rise" />
                <n-icon size="12" color="#00ff6f">
                  <CaretUpOutlined />
                </n-icon>
              </template>
            </div>
            <div class="text-sn">
              <n-skeleton v-if="loading" :width="100" size="medium" />
              <template v-else>
                周同比
                <CountTo :startVal="1" suffix="%" :endVal="visits.decline" />
                <n-icon size="12" color="#ffde66">
                  <CaretDownOutlined />
                </n-icon>
              </template>
            </div>
          </div>
          <template #footer>
            <div class="flex justify-between">
              <n-skeleton v-if="loading" text :repeat="2" />
              <template v-else>
                <div class="text-sn"> 总问题单量： </div>
                <div class="text-sn">
                  <CountTo :startVal="1" :endVal="visits.amount || 0" />
                </div>
              </template>
            </div>
          </template>
        </NCard>
      </n-grid-item>
      <n-grid-item>
        <NCard
          title="超长时间未闭环清理"
          :segmented="{ content: true, footer: true }"
          size="small"
          :bordered="false"
        >
          <template #header-extra>
            <n-tag type="info">季度</n-tag>
          </template>
          <div class="flex justify-between px-1 py-1">
            <n-skeleton v-if="loading" :width="100" size="medium" />
            <CountTo v-else :startVal="1" :endVal="specialDetail.overdue" class="text-3xl" />
          </div>
          <div class="flex justify-between px-2 py-2 hidden">
            <div class="flex-1 text-sn">
              <n-progress
                type="line"
                :percentage="saleroom.degree"
                :indicator-placement="'inside'"
                processing
              />
            </div>
          </div>
          <template #footer>
            <div class="flex justify-between">
              <n-skeleton v-if="loading" :width="100" size="medium" />
              <template v-else>
                <div class="text-sn"> 未闭环占比： </div>
                <div class="text-sn">
                  {{ degree }} %
                  <!-- <CountTo :startVal="1" :endVal="degree" /> -->
                </div>
              </template>
            </div>
          </template>
        </NCard>
      </n-grid-item>
      <n-grid-item>
        <NCard
          title="总闭环数"
          :segmented="{ content: true, footer: true }"
          size="small"
          :bordered="false"
        >
          <template #header-extra>
            <n-tag type="warning">总数</n-tag>
          </template>
          <div class="flex justify-between px-1 py-1">
            <n-skeleton v-if="loading" :width="100" size="medium" />
            <CountTo v-else :startVal="1" :endVal="dtsDetail.closed || 0" class="text-3xl" />
          </div>
          <div class="flex justify-between px-1 py-1 hidden">
            <div class="text-sn">
              <n-skeleton v-if="loading" :width="100" size="medium" />
              <template v-else>
                xxxx
                <CountTo :startVal="1" suffix="%" :endVal="orderLarge.rise" />
                <n-icon size="12" color="#00ff6f">
                  <CaretUpOutlined />
                </n-icon>
              </template>
            </div>
            <div class="text-sn hidden">
              <n-skeleton v-if="loading" :width="100" size="medium" />
              <template v-else>
                xxxx
                <CountTo :startVal="1" suffix="%" :endVal="orderLarge.rise" />
                <n-icon size="12" color="#ffde66">
                  <CaretDownOutlined />
                </n-icon>
              </template>
            </div>
          </div>
          <template #footer>
            <div class="flex justify-between">
              <n-skeleton v-if="loading" :width="100" size="medium" />
              <template v-else>
                <div class="text-sn"> 总闭环占比： </div>
                <div class="text-sn">
                  {{ closeDegree }} %
                  <!-- <CountTo :startVal="1" suffix="%" :endVal="orderLarge.amount" /> -->
                </div>
              </template>
            </div>
          </template>
        </NCard>
      </n-grid-item>
      <n-grid-item>
        <NCard
          title="在我名下"
          :segmented="{ content: true, footer: true }"
          size="small"
          :bordered="false"
        >
          <template #header-extra>
            <n-tag type="error">总数</n-tag>
          </template>
          <div class="flex justify-between px-1 py-1">
            <n-skeleton v-if="loading" :width="100" size="medium" />
            <CountTo v-else :startVal="1" :endVal="myOrder || 0" class="text-3xl" />
          </div>
          <div class="flex justify-between px-1 py-1 hidden">
            <div class="text-sn">
              <n-skeleton v-if="loading" :width="100" size="medium" />
              <template v-else>
                xxxx
                <CountTo :startVal="1" suffix="%" :endVal="volume.rise" />
                <n-icon size="12" color="#00ff6f">
                  <CaretUpOutlined />
                </n-icon>
              </template>
            </div>
            <div class="text-sn">
              <n-skeleton v-if="loading" :width="100" size="medium" />
              <template v-else>
                xxxx
                <CountTo :startVal="1" suffix="%" :endVal="volume.decline" />
                <n-icon size="12" color="#ffde66">
                  <CaretDownOutlined />
                </n-icon>
              </template>
            </div>
          </div>
          <template #footer>
            <div class="flex justify-between">
              <n-skeleton v-if="loading" :width="100" size="medium" />
              <template v-else>
                <div class="text-sn"> 在我名下占比： </div>
                <div class="text-sn">
                  {{ ((myOrder / visits.amount) * 100).toFixed(0) }} %
                  <!-- <CountTo prefix="￥" :startVal="1" :endVal="" /> -->
                </div>
              </template>
            </div>
          </template>
        </NCard>
      </n-grid-item>
    </n-grid>

    <!--导航卡片-->
    <div class="mt-4">
      <n-grid cols="1 s:2 m:3 l:8 xl:8 2xl:8" responsive="screen" :x-gap="16" :y-gap="8">
        <n-grid-item v-for="(item, index) in iconList" :key="index">
          <NCard content-style="padding-top: 0;" size="small" :bordered="false">
            <template #footer>
              <n-skeleton v-if="loading" size="medium" />
              <div class="cursor-pointer" v-else>
                <p class="flex justify-center">
                  <span>
                    <n-icon :size="item.size" class="flex-1" :color="item.color">
                      <component :is="item.icon" v-on="item.eventObject || {}" />
                    </n-icon>
                  </span>
                </p>
                <p class="flex justify-center"
                  ><span>{{ item.title }}</span></p
                >
              </div>
            </template>
          </NCard>
        </n-grid-item>
      </n-grid>
    </div>

    <!--访问量 | 流量趋势-->
    <VisiTab :key-dts-list="keyDtsList" @refresh="fetchConsoleData" />
  </div>
</template>
<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { getConsoleInfo } from '@/api/dashboard/console';
  import VisiTab from './components/VisiTab.vue';
  import { CountTo } from '@/components/CountTo/index';
  import {
    CaretUpOutlined,
    CaretDownOutlined,
    AccountBookOutlined,
    TagsOutlined,
    SettingOutlined,
    SolutionOutlined,
    TrademarkOutlined,
    ScheduleOutlined,
    AreaChartOutlined,
    HeatMapOutlined,
  } from '@vicons/antd';
  import { UserInfoType, useUserStore } from '@/store/modules/user';
  import { useRouter } from 'vue-router';

  interface InVisits {
    dayVisits: number;
    rise: number;
    decline: number;
    amount: number;
  }

  interface InSaleroom {
    weekSaleroom: number;
    amount: number;
    degree: number;
  }

  interface InOrderLarge {
    weekLarge: number;
    rise: number;
    decline: number;
    amount: number;
  }

  interface InVolume {
    weekLarge: number;
    rise: number;
    decline: number;
    amount: number;
  }

  interface DtsDetail {
    total: number;
    toBeAnalyzed: number;
    toBeLocked: number;
    majorAndCriticalNum: number;
    closed?: number;
  }

  interface SpecialDetail {
    newDts: number;
    overdue: number;
  }

  interface ConsoleResponse {
    dtsDetail: DtsDetail;
    specialDetail: SpecialDetail;
    keyDtsList: Array<{
      id: number;
      orderId: string;
      appName: string | null;
      description: string;
      status: string;
      severity: string;
      // ... 其他字段
    }>;
  }

  const userStore = useUserStore();
  const loading = ref(true);
  const degree = ref(0);
  const closeDegree = ref(0);
  const myOrder = ref(0);
  const visits = ref({} as InVisits);
  const saleroom = ref({} as InSaleroom);
  const orderLarge = ref({} as InOrderLarge);
  const volume = ref({} as InVolume);
  const currentTime = ref('');
  const userInfo: UserInfoType = userStore.getUserInfo || {};
  const router = useRouter();
  // 图标列表
  const iconList = [
    {
      icon: TrademarkOutlined,
      size: '32',
      title: 'IR管理',
      color: '#69c0ff',
      eventObject: {
        click: () => router.push('/irmanagement'),
      },
    },
    {
      icon: ScheduleOutlined,
      size: '32',
      title: 'BETA单管理',
      color: '#ff9c6e',
      eventObject: {
        click: () => router.push('/opinionmonitoring/betaState'),
      },
    },
    {
      icon: AccountBookOutlined,
      size: '32',
      title: 'FUT单管理',
      color: '#b37feb',
      eventObject: {
        click: () => router.push('/opinionmonitoring/futQuestions'),
      },
    },
    {
      icon: HeatMapOutlined,
      size: '32',
      title: '应用状态',
      color: '#5cdbd3',
      eventObject: {
        click: () => router.push('/dataview/appstate'),
      },
    },
    {
      icon: TagsOutlined,
      size: '32',
      title: '每日工单定界情况',
      color: '#ff85c0',
      eventObject: {
        click: () => router.push('/dataview/dtsResolveState'),
      },
    },
    {
      icon: SettingOutlined,
      size: '32',
      title: 'FUT数据统计',
      color: '#ffc069',
      eventObject: {
        click: () => router.push('/dataview/futDateView'),
      },
    },
  ];

  const dtsDetail = ref<DtsDetail>({
    total: 0,
    toBeAnalyzed: 0,
    toBeLocked: 0,
    majorAndCriticalNum: 0,
  });

  const specialDetail = ref<SpecialDetail>({
    newDts: 0,
    overdue: 0,
  });

  const keyDtsList = ref([]);

  const fetchConsoleData = async () => {
    try {
      loading.value = true;
      const data = await getConsoleInfo({ username: userInfo.account });
      const response = data; // 获取响应数据

      // 更新数据
      dtsDetail.value = response.dtsDetail;
      specialDetail.value = response.specialDetail;

      // 模拟一些统计数据
      visits.value = {
        dayVisits: specialDetail.value.newDts,
        rise: 35,
        decline: 20,
        amount: dtsDetail.value.total,
      };

      // 计算百分比
      degree.value = Number(((specialDetail.value.overdue / visits.value.amount) * 100).toFixed());
      closeDegree.value = Number(((dtsDetail.value.closed / visits.value.amount) * 100).toFixed());
      myOrder.value = Number(
        dtsDetail.value.solutionImplementation + dtsDetail.value.solutionReview
      );

      // 更新其他数据...
      saleroom.value = {
        weekSaleroom: specialDetail.value.overdue,
        amount: visits.value.amount,
        degree: degree.value,
      };

      orderLarge.value = {
        weekLarge: dtsDetail.value.toBeLocked,
        rise: 15,
        decline: 10,
        amount: 80,
      };

      volume.value = {
        weekLarge: dtsDetail.value.majorAndCriticalNum,
        rise: 25,
        decline: 12,
        amount: 95000,
      };

      keyDtsList.value = response.keyDtsList;
    } catch (error) {
      console.error('Failed to fetch console data:', error);
    } finally {
      loading.value = false;
    }
  };

  onMounted(() => {
    fetchConsoleData();

    // 时间更新逻辑
    const updateTime = () => {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      currentTime.value = `当前时间：${year}年${month}月${day}日 ${hours}时${minutes}分`;
    };

    updateTime();
    setInterval(updateTime, 60000);
  });
</script>

<style lang="less" scoped></style>
