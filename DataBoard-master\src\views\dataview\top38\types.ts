export interface Top38Item {
  id?: number | string;
  application?: string;
  issueSource?: string;
  issueCategory?: string;
  priority?: string;
  dtsTicket?: string;
  topVoiceOpinion?: string;
  mainResponsibleParty?: string;
  responsiblePerson?: string;
  goLiveTime?: string;
  planProgress?: string;
  status?: string;
  proposer?: string;
  product?: string;
  suggestedPriority?: string;
  suggestedResolutionTime?: string;
  reasonHighPriorityUnmet?: string;
  referenceVolume?: string;
  workOrder?: string;
  workOrderLab?: string;
  vocOther?: string;
  demandMatch?: string;
  week17?: string;
  week18?: string;
  totalCumulative?: string;
  proportion?: string;
  keywords?: string;
  entrySource?: string;
  issueDescription?: string;
  module?: string;
  standardDescription?: string;
  assistanceLevel?: string;
  sentimentLevel?: string;
}

export interface SearchParams {
  application?: string | null;
  issueCategory?: string | null;
  priority?: string | null;
  status?: string | null;
  dtsTicket?: string | null;
  issueSource?: string | null;
  topVoiceOpinion?: string | null;
  mainResponsibleParty?: string | null;
  responsiblePerson?: string | null;
  goLiveTime?: string | null;
  planProgress?: string | null;
  proposer?: string | null;
  product?: string | null;
  suggestedPriority?: string | null;
  suggestedResolutionTime?: string | null;
  reasonHighPriorityUnmet?: string | null;
  referenceVolume?: number[] | null;
  workOrder?: string | null;
  workOrderLab?: string | null;
  vocOther?: string | null;
  demandMatch?: string | null;
  week17?: string | null;
  week18?: string | null;
  totalCumulative?: string | null;
  proportion?: string | null;
  keywords?: string | null;
  entrySource?: string | null;
  issueDescription?: string | null;
  module?: string | null;
  standardDescription?: string | null;
  assistanceLevel?: string | null;
  sentimentLevel?: string | null;
  pageNo?: number;
  pageSize?: number;
}

export interface Pagination {
  pageNo: number;
  pageSize: number;
  itemCount?: number;
  pageCount?: number;
}

export interface TableState {
  loading: boolean;
  data: Top38Item[];
  pagination: Pagination;
}
