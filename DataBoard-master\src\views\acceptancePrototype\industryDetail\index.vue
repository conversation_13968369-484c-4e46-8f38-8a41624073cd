<template>
  <div>
    <n-card class="table-card">
      <template #header>
        <div class="chart-title">{{ industryNameVal || applicationName }}</div>
      </template>
      <n-space vertical>
        <div style="display: flex">
          <n-button
            secondary
            type="primary"
            size="small"
            class="export-button"
            @click="onExportClick"
            :loading="exportLoading"
            style="margin-right: 20px"
          >
            <template #icon>
              <n-icon>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                  <path fill="currentColor" d="M13 10h5l-6-6-6 6h5v8h2v-8M4 19v-7h2v7h12v-7h2v7H4Z" />
                </svg>
              </n-icon>
            </template>
            导出
          </n-button>
          <n-button
            type="primary"
            size="small"
            v-if="!isEdit"
            @click="startEdit"
            :disabled="!isTestAdmin"
          >
            编辑
          </n-button>
          <n-button
            type="primary"
            size="small"
            style="margin-right: 12px"
            v-if="isEdit"
            @click="confirmEdit"
          >
            保存
          </n-button>
          <n-button size="small" v-if="isEdit" @click="cancleEdit">取消</n-button>
        </div>
        <n-data-table
          :bordered="false"
          :single-line="false"
          :columns="featuresColumns"
          :data="tableData"
          :scroll-x="1200"
          :max-height="tableHeight"
          virtual-scroll
          row-class-name="custom-table"
          remote
          style="margin-top: 12px"
        />
      </n-space>
    </n-card>
  </div>
</template>


<script lang="ts" setup>
  import { ref, onMounted, h } from 'vue';
  import { getIndustryList, exportData, updateIndustryList } from './index';
  import { useDialog, useMessage, NInput, NFlex, NButton } from 'naive-ui';
  import { cloneDeep } from 'lodash-es';
  import { useRoute } from 'vue-router';
  import { log } from 'console';
  import { useUserStore } from '@/store/modules/user';
  import { UserRoleEnum } from '@/enums/UserRoleEnum';
  // 用户角色
  const userStore = useUserStore();
  const roles = userStore.getUserInfo.roles;
  const isTestAdmin = ref(false);
  isTestAdmin.value = roles.includes(UserRoleEnum.TEST_ADMIN);
  const route = useRoute();
  const industryNameVal = ref(route.query.industryName as string);
  const applicationName = ref(route.query.applicationName as string);
  const loadingRef = ref(true);
  const loading = ref(false);
  const exportLoading = ref(false);
  const dialog = useDialog();
  const message = useMessage();
  const tableData = ref([]);
  const tableHeight = ref(600);
  // 获取屏幕分辨率宽度
  var screenWidth = screen.width;
  if (screenWidth > 1920) {
    tableHeight.value = 900;
  }
  const isEdit = ref(false);
  /**
   * 开始编辑
   */
  const startEdit = () => {
    isEdit.value = true;
  };

  /**
   * 保存编辑内容
   */
  const confirmEdit = async () => {
    message.loading('内容保存中', {
      duration: 200000,
    });
    try {
      const functionList = tableData.value.map((v) => {
        return {
          module1: v.module1,
          module2: v.module2,
          modele3: v.modele3,
          description: v.description,
          type: v.type,
          remark: v.remark,
        };
      });
      const data = { industryName: industryNameVal.value, functionList };
      let res = await updateIndustryList(data);
      if (res.status === '200') {
        message.success('编辑成功');
        isEdit.value = false;
        fetchData();
        message.destroyAll();
      } else {
        message.warning('编辑失败！');
      }
    } catch (e) {
      loading.value = false;
    }
  };

  /**
   * 取消编辑
   */
  const cancleEdit = () => {
    isEdit.value = false;
    // 取消编辑后重新请求数据
    fetchData();
  };
  const onExportClick = async () => {
    exportLoading.value = true;
    const industryName = industryNameVal.value
    try {
      await exportData({industryName})
    } catch (e) {
      console.log(e);
      message.warning('导出失败！');
    } finally {
      exportLoading.value = false;
    }
  };
  onMounted(() => {
    fetchData();
  });
  const featuresColumns = [
    {
      title: '一级模块',
      key: 'module1',
      width: 100,
      minWidth: 60,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
      render(row, index) {
        if (!isEdit.value) {
          return row.module1;
        }
        return h(NInput, {
          value: row.module1,
          type: 'text',
          size: 'small',
          onUpdateValue(v) {
            tableData.value[index]['module1'] = v;
          },
        });
      },
    },
    {
      title: '二级模块',
      key: 'module2',
      width: 120,
      minWidth: 60,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
      render(row, index) {
        if (!isEdit.value) {
          return row.module2;
        }
        return h(NInput, {
          value: row.module2,
          type: 'text',
          size: 'small',
          onUpdateValue(v) {
            tableData.value[index]['module2'] = v;
          },
        });
      },
    },
    {
      title: '三级模块',
      key: 'module3',
      width: 120,
      minWidth: 60,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
      render(row, index) {
        if (!isEdit.value) {
          return row.module3;
        }
        return h(NInput, {
          value: row.module3,
          type: 'text',
          size: 'small',
          onUpdateValue(v) {
            tableData.value[index]['module3'] = v;
          },
        });
      },
    },
    {
      title: '补充描述',
      key: 'description',
      width: 200,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
      render(row, index) {
        if (!isEdit.value) {
          return row.description;
        }
        return h(NInput, {
          value: row.description,
          type: 'text',
          size: 'small',
          onUpdateValue(v) {
            tableData.value[index]['description'] = v;
          },
        });
      },
    },
    {
      title: '类型',
      key: 'type',
      width: 100,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
      render(row, index) {
        if (!isEdit.value) {
          return row.type;
        }
        return h(NInput, {
          value: row.type,
          type: 'text',
          size: 'small',
          onUpdateValue(v) {
            tableData.value[index]['type'] = v;
          },
        });
      },
    },
    {
      title: '备注',
      key: 'remark',
      width: 120,
      minWidth: 60,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
      render(row, index) {
        if (!isEdit.value) {
          return row.remark;
        }
        return h(NInput, {
          value: row.remark,
          type: 'text',
          size: 'small',
          onUpdateValue(v) {
            tableData.value[index]['remark'] = v;
          },
        });
      },
    },
    {
      title: '操作',
      key: 'operation',
      width: 100,
      fixed: 'right',
      render(row, index) {
        return h(NFlex, { wrap: false }, () => [
          h(
            NButton,
            {
              text: true,
              strong: true,
              type: 'info',
              size: 'tiny',
              disabled: !isEdit.value,
              onClick: () => {
                tableData.value.splice(index + 1, 0, {
                  module1: '',
                  module2: '',
                  modele3: '',
                  description: '',
                  type: '',
                  remark: '',
                });
              },
            },
            { default: () => '新增' }
          ),
          h(
            NButton,
            {
              text: true,
              strong: true,
              type: 'error',
              size: 'tiny',
              disabled: !isEdit.value,
              onClick: () => {
                tableData.value.splice(index, 1);
              },
            },
            { default: () => '删除' }
          ),
        ]);
      },
    },
  ];

  const fetchData = async () => {
    loadingRef.value = true;
    let params = {
      pageNum: 1,
      pageSize: 1000,
      industryName: industryNameVal.value || '',
      appName: applicationName.value || '',
    };
    let res = await getIndustryList(params, applicationName.value);
    if (res.status == '200') {
      tableData.value = res.data?.data || [];
    } else {
      tableData.value = [];
    }
  };
</script>
<style scoped>
:deep(.custom-table td) {
  padding-top: 4px !important;
  padding-bottom: 4px !important;
}
:deep(.add-new-line) {
  display: none !important;
  cursor: pointer;
}
:deep(tr:hover .add-new-line) {
  display: inline-block !important;
}
:deep(tr:hover .add-new-line.disabled) {
  cursor: not-allowed;
}
</style>