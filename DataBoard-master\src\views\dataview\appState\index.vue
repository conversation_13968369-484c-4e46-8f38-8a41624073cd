<template>
  <div class="container-box">
    <!-- 表单查询部分 -->
    <div class="search-box">
      <div class="form-container">
        <n-form :size="size" label-placement="left" ref="formRef" :model="form">
          <n-grid :cols="24" :x-gap="24">
            <n-grid-item v-for="item in visibleSearchFormItems" :key="item.field" :span="6">
              <n-form-item :label="item.label" :path="item.field">
                <n-input
                  clearable
                  v-if="item.component === 'Input'"
                  v-model:value.trim="form[item.field]"
                  @keyup.enter="handleSearch"
                />
                <n-select
                  clearable
                  v-else-if="item.component === 'Select'"
                  v-model:value="form[item.field]"
                  :options="item.options"
                  :multiple="item.isMultiple"
                  :filterable="item.filterable"
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>
        </n-form>
      </div>
      <n-space justify="center">
        <n-button @click="handleReset">重置</n-button>
        <n-button type="primary" @click="handleSearch">查询</n-button>
      </n-space>
    </div>

    <!-- 表格部分 -->
    <div class="table-container">
      <n-space vertical :size="12">
        <n-space>
          <n-button type="primary" secondary @click="add"> 新建</n-button>
          <n-upload accept=".xlsx, .xls" :custom-request="handleFileUpload" :show-file-list="false">
            <n-button secondary :loading="importing">
              {{ importing ? '正在导入...' : '导入应用' }}
            </n-button>
          </n-upload>
        </n-space>
        <n-data-table
          remote
          ref="tableRef"
          :bordered="false"
          :single-line="false"
          striped
          :loading="loadingRef"
          :columns="columns"
          :data="tableData"
          :pagination="paginationReactive"
          @update:sorter="handleSorterChange"
          @update:page="handlePageChange"
          :scroll-x="3400"
          max-height="420"
        />
      </n-space>
    </div>

    <!-- 模态框部分 -->
    <n-drawer v-model:show="dialogVisible" :width="502">
      <n-drawer-content :title="isAdd ? '新增' : '编辑'" closable>
        <n-form
          :model="actionForm"
          :rules="dynamicRules"
          :rule-props="{ immediate: true }"
          label-width="auto"
          label-placement="left"
        >
          <n-form-item label="应用名称" path="appName">
            <n-input v-model:value="actionForm.appName" placeholder="请输入应用名称" />
          </n-form-item>
          <n-form-item label="应用责任人" path="ewpOwner">
            <n-select
              v-model:value="actionForm.ewpOwner"
              placeholder="请选择应用责任人"
              :options="employeesOptions"
              filterable
              clearable
            />
          </n-form-item>
          <n-form-item label="DTSE" path="dtseOwner">
            <n-input v-model:value.trim="actionForm.dtseOwner" required placeholder="请输入DTSE" />
          </n-form-item>
          <n-form-item label="DTSELeader" path="dtseLeader">
            <n-input
              v-model:value.trim="actionForm.dtseLeader"
              required
              placeholder="请输入DTSELeader"
            />
          </n-form-item>
          <n-form-item label="解决方案" path="solutionOwner">
            <n-input
              v-model:value.trim="actionForm.solutionOwner"
              required
              placeholder="请输入解决方案"
            />
          </n-form-item>
          <n-form-item label="解决方案Leader" path="solutionLeader">
            <n-input
              v-model:value.trim="actionForm.solutionLeader"
              required
              placeholder="请输入解决方案Leader"
            />
          </n-form-item>
          <n-form-item label="验收责任人" path="acceptanceOwner">
            <n-input
              v-model:value.trim="actionForm.acceptanceOwner"
              required
              placeholder="请输入验收责任人"
            />
          </n-form-item>
          <n-form-item label="验收责任人Leader" path="acceptanceLeader">
            <n-input
              v-model:value.trim="actionForm.acceptanceLeader"
              required
              placeholder="请输入验收责任人Leader"
            />
          </n-form-item>
          <n-form-item label="BD责任人" path="bdOwner">
            <n-input
              v-model:value.trim="actionForm.bdOwner"
              required
              placeholder="请输入BD责任人"
            />
          </n-form-item>
          <n-form-item label="BD责任人Leader" path="bdLeader">
            <n-input
              v-model:value.trim="actionForm.bdLeader"
              required
              placeholder="请输入BD责任人Leader"
            />
          </n-form-item>
          <n-form-item label="应用PM" path="appPM">
            <n-input v-model:value.trim="actionForm.appPM" required placeholder="请输入应用PM" />
          </n-form-item>
          <n-form-item label="应用垂类" path="appType">
            <n-select
              v-model:value="actionForm.appType"
              placeholder="请选择垂类"
              :options="appTypeOptions"
              clearable
            />
          </n-form-item>
          <n-form-item label="标签" path="appLevel">
            <n-select
              v-model:value="actionForm.appLevelList"
              placeholder="请选择标签"
              :options="appLevelOptions"
              @update:value="handleSelectChange"
              clearable
              multiple
            />
          </n-form-item>
          <n-form-item label="应用包名">
            <n-input v-model:value.trim="actionForm.appBundle" placeholder="请输入应用包名" />
          </n-form-item>
          <!--          <n-form-item label="清单来源">-->
          <!--            <n-input v-model:value.trim="actionForm.sourceName" placeholder="请输入应用包名"/>-->
          <!--          </n-form-item>-->
          <!--          <n-form-item label="风险指数">-->
          <!--            <n-input v-model:value.trim="actionForm.riskScore" placeholder="请输入应用包名"/>-->
          <!--          </n-form-item>-->
          <!--          <n-form-item label="首次上架时间">-->
          <!--            <n-date-picker v-model:value="actionForm.firstPublishTime"-->
          <!--                           type="date" clearable/>-->
          <!--          </n-form-item>-->
          <!--          <n-form-item label="更新时间">-->
          <!--            <n-date-picker v-model:value.trim="actionForm.latestPublishTime" clearable/>-->
          <!--          </n-form-item>-->
          <!--          <n-form-item label="声量值">-->
          <!--            <n-input v-model:value.trim="actionForm.opinionNum" placeholder="请输入应用包名"/>-->
          <!--          </n-form-item>-->
          <!--          <n-form-item label="版本">-->
          <!--            <n-input v-model:value.trim="actionForm.version" placeholder="请输入版本"/>-->
          <!--          </n-form-item>-->
          <!--          <n-form-item label="应用id">-->
          <!--            <n-input v-model:value.trim="actionForm.appId" placeholder="请输入应用id"/>-->
          <!--          </n-form-item>-->
          <n-form-item label="应用形态">
            <n-input v-model:value.trim="actionForm.appShape" placeholder="请输入应用形态" />
          </n-form-item>
          <n-form-item label="所属公司">
            <n-input v-model:value.trim="actionForm.company" placeholder="请输入所属公司" />
          </n-form-item>
          <n-form-item label="所属省份">
            <n-input v-model:value.trim="actionForm.represent" placeholder="请输入所属省份" />
          </n-form-item>
          <n-form-item label="测试责任人">
            <n-input v-model:value.trim="actionForm.testOwner" placeholder="请输入测试责任人" />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space>
            <n-button @click="cancel">取消</n-button>
            <n-button type="primary" @click="sureAdd">确认</n-button>
          </n-space>
        </template>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>
<script lang="ts">
  export default {
    name: 'Appstate',
  };
  export const employeesOptions = ref([]);
</script>
<script lang="ts" setup>
  import { filterObjectValues, formatDateTime, getRiskLevel, getStatusColor } from '@/utils';
  import { ewpService as service } from '@/utils/axios';
  import {
    DataTableInst,
    FormRules,
    NButton,
    NTag,
    useMessage,
    NUpload,
    NPopconfirm,
    useDialog,
  } from 'naive-ui';
  import { ref, reactive, h, onMounted, computed } from 'vue';
  import { useRouter } from 'vue-router';
  import { BasicColumn } from '@/components/Table';
  import { ListData } from '../myOrder/columns';
  import { UserInfoType, useUserStore } from '@/store/modules/user';
  import { importUpExcel } from '@/api/dataview/appState';
  import { getStaffList } from '../personManage/staff';
  import { ac } from '@faker-js/faker/dist/airline-CBNP41sR';
  import { DTS_HANDLE_TAG } from '@/views/dataview/personManage/tagContant';
  import { getPersonOptionsOnlyName } from '@/views/dataview/personManage/staffCommonUtils';

  const tableRef = ref<DataTableInst>();
  const formRef = ref(null);
  const dialogVisible = ref(false);
  const isAdd = ref(true);
  const size = ref('medium');
  const isLevel = ref(true);
  const inputRules = ref([]);
  const message = useMessage();
  const dialog = useDialog();
  const userStore = useUserStore();
  const paginationReactive = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    prefix({ itemCount }) {
      return `Total：${itemCount}`;
    },
    onChange: (page: number) => {
      paginationReactive.page = page;
      fetchData();
    },
    onUpdatePageSize: (pageSize: number) => {
      paginationReactive.pageSize = pageSize;
      paginationReactive.page = 1;
      fetchData();
    },
  });

  const userInfo: UserInfoType = userStore.getUserInfo || {};
  const employeesOptions = ref([]);

  const loadingRef = ref(true);
  const form = ref({
    appName: '',
    ewpOwner: null,
    label: '',
    appType: null,
    appLevelList: null,
    top: null,
  });
  const actionForm = ref({
    appName: '',
    appBundle: '',
    ewpOwner: null,
    dtseOwner: '',
    dtseLeader: '',
    solutionOwner: '',
    solutionLeader: '',
    acceptanceOwner: '',
    acceptanceLeader: '',
    bdOwner: '',
    bdLeader: '',
    appPM: '',
    label: '',
    appType: null,
    appLevelList: null,
    // sourceName:'',//清单来源
    // riskScore:'',//风险指数
    // firstPublishTime: null,//首次上架时间
    // latestPublishTime:null,//更新时间
    // opinionNum:'',//声量值
    // version: '',//版本
    // appId: '',//应用id
    appShape: '', //应用形态
    company: '', //所属公司
    testOwner: '', //测试责任人
  });

  const appTypeOptions = ref([]);

  const appLevelOptions = ref([]);

  const sourceNameOptions = [
    {
      label: '代表处',
      value: '代表处',
    },
    {
      label: '互联网',
      value: '互联网',
    },
    {
      label: '行业系统部',
      value: '行业系统部',
    },
  ];

  const visibleSearchFormItems = ref([
    {
      field: 'appName',
      label: '应用名称',
      component: 'Input',
    },
    {
      field: 'ewpOwner',
      label: '应用责任人',
      component: 'Select',
      options: employeesOptions,
      filterable: true,
    },
    {
      field: 'appType',
      label: '应用垂类',
      component: 'Select',
      options: appTypeOptions,
    },
    {
      field: 'appLevelList',
      label: '标签',
      component: 'Select',
      options: appLevelOptions,
      isMultiple: true,
    },
    {
      field: 'top',
      label: 'TOP',
      component: 'Select',
      options: [
        { label: 'TOP2000', value: 'TOP2000' },
        { label: 'TOP38', value: 'TOP38' },
      ],
    },
    {
      field: 'sourceName',
      label: '清单来源',
      component: 'Select',
      options: [
        { label: '代表处', value: '代表处' },
        { label: '互联网', value: '互联网' },
        { label: '行业系统部', value: '行业系统部' },
      ],
      isMultiple: true,
    },
  ]);

  const actionsArr: any = [];
  actionsArr.push(
    {
      title: '编辑',
      key: 'edit',
    },
    {
      title: '删除',
      key: 'delete',
    }
  );
  type ExtendedBasicColumn = BasicColumn & { toWelink?: boolean };

  const createColumns = () => {
    let basicColumns: ExtendedBasicColumn[] = [
      {
        title: '应用名称',
        key: 'appName',
        render: (row) => {
          const isUpdatedRecently =
            row.appPublishInfoPo &&
            new Date().getTime() - new Date(row.appPublishInfoPo.latestPublishTime).getTime() <
              14 * 24 * 60 * 60 * 1000;
          return h(
            NTag,
            {
              bordered: false,
              style: {
                cursor: 'pointer',
              },
            },
            [
              h(
                NTag,
                {
                  bordered: false,
                  onClick: () => handleAppNameClick(row.appName),
                },
                {
                  default: () => row.appName,
                }
              ),
              // top标记
              row.top
                ? h(
                    NTag,
                    {
                      type: 'warning',
                      size: 'tiny',
                      bordered: false,
                      style: {
                        position: 'absolute',
                        right: '-8px',
                        top: '-8px',
                        padding: '0 4px',
                        transform: 'scale(0.8)',
                        fontSize: '12px',
                        lineHeight: '16px',
                        minWidth: '16px',
                        textAlign: 'center',
                        zIndex: 1,
                        color: 'red',
                      },
                    },
                    { default: () => row.top.toUpperCase() }
                  )
                : null,
              isUpdatedRecently
                ? h(
                    'span',
                    {
                      style: {
                        position: 'absolute',
                        left: '-12px',
                        top: '-8px',
                        padding: '0 4px',
                        transform: 'scale(0.8)',
                        fontSize: '14px',
                        lineHeight: '16px',
                        minWidth: '16px',
                        textAlign: 'center',
                        zIndex: 1,
                        color: 'green',
                        fontWeight: 'bold',
                      },
                    },
                    'up'
                  )
                : null, // Conditionally render the "up" label
            ]
          );
        },
      },
      {
        title: '应用责任人',
        key: 'ewpOwner',
      },
      {
        title: '应用包名',
        key: 'appBundle',
      },
      {
        title: '清单来源',
        key: 'sourceName',
      },
      {
        title: '风险指数',
        key: 'riskScore',
        sorter: true,
        render(row) {
          return h(
            NTag,
            {
              style: {
                marginRight: '6px',
              },
              type: getStatusColor(row.riskScore),
              bordered: false,
            },
            {
              default: () => Number(row.riskScore).toFixed(),
            }
          );
        },
      },
      {
        title: '首次上架时间',
        key: 'appPublishInfoPo.firstPublishTime',
        render(row) {
          if (!row.appPublishInfoPo) return;
          return formatDateTime(row.appPublishInfoPo.firstPublishTime);
        },
      },
      {
        title: '更新时间',
        key: 'appPublishInfoPo.latestPublishTime',
        render(row) {
          if (!row.appPublishInfoPo) return;
          return formatDateTime(row.appPublishInfoPo.latestPublishTime);
        },
      },
      {
        title: '声量值',
        key: 'opinionNum',
        sorter: true,
      },
      {
        title: '应用垂类',
        key: 'appType',
      },
      {
        title: '标签',
        key: 'appLevel',
      },
      {
        title: 'DTSE',
        key: 'dtseOwner',
        toWelink: true,
      },
      {
        title: 'DTSELeader',
        key: 'dtseLeader',
        toWelink: true,
      },
      {
        title: '解决方案',
        key: 'solutionOwner',
        toWelink: true,
      },
      {
        title: '解决方案Leader',
        key: 'solutionLeader',
        toWelink: true,
      },
      {
        title: '验收责任人',
        key: 'acceptanceOwner',
        toWelink: true,
      },
      {
        title: '验收Leaderer',
        key: 'acceptanceLeader',
        toWelink: true,
      },
      {
        title: 'BD责任人',
        key: 'bdOwner',
        toWelink: true,
      },
      {
        title: 'BDLeader',
        key: 'bdLeader',
        toWelink: true,
      },
      {
        title: '应用PM',
        key: 'appPM',
        toWelink: true,
      },
      {
        title: '版本',
        key: 'appPublishInfoPo.version',
      },
      {
        title: '应用id',
        key: 'appPublishInfoPo.appId',
      },
      {
        title: '应用形态',
        key: 'appShape',
      },
      {
        title: '所属公司',
        key: 'company',
      },
      {
        title: '所属省份',
        key: 'represent',
      },

      {
        title: '测试责任人',
        key: 'testOwner',
      },
      {
        title: '操作',
        key: 'actions',
        fixed: 'right',
        width: '230',
        render(row) {
          return h('div', {}, [
            ...actionsArr.map((item) => {
              if (item.key === 'delete') {
                return h(
                  NPopconfirm,
                  {
                    onPositiveClick: async () => {
                      await service.post(`/management/appInfo/delete/${row.id}`);
                      message.success('删除成功');
                      fetchData();
                    },
                  },
                  {
                    trigger: () =>
                      h(
                        NButton,
                        {
                          size: 'small',
                          style: {
                            marginRight: '6px',
                          },
                          secondary: true,
                          type: 'error', // 添加红色样式
                        },
                        { default: () => '删除' }
                      ),
                    default: () => '确定要删除吗？',
                  }
                );
              } else {
                return h(
                  NButton,
                  {
                    size: 'small',
                    style: {
                      marginRight: '6px',
                    },
                    secondary: true,
                    onClick: () => {
                      actionForm.value = {
                        ...row,
                        // firstPublishTime:
                        //   row.appPublishInfoPo?.firstPublishTime === undefined ? null : formatDateTime(row.appPublishInfoPo.firstPublishTime),
                        // appId:
                        //   (row.appPublishInfoPo?.appId === undefined) ? '' : row.appPublishInfoPo.appId,
                        // version:
                        //   (row.appPublishInfoPo?.version === undefined) ? '' : row.appPublishInfoPo.version
                      };
                      const appLevelList = [];
                      if (row.appLevel) {
                        const arr = row.appLevel.split(',');
                        appLevelList.push(...arr);
                      }
                      if (row.appPulishInfoPo) {
                        actionForm.value.appBundle = row.appPulishInfoPo.appBundle;
                      }
                      actionForm.value.appLevelList = appLevelList;
                      if (item.key === 'edit') {
                        isAdd.value = false;
                        dialogVisible.value = true;
                      }
                    },
                  },
                  {
                    default: () => item.title,
                  }
                );
              }
            }),
            // 修改"查看详情"按钮
            h(
              NButton,
              {
                size: 'small',
                style: {
                  marginRight: '6px',
                },
                secondary: true,
                type: 'warning', // 添加主要颜色样式
                onClick: () => {
                  router.push({
                    name: 'AppDetail',
                    query: {
                      appName: row.appName,
                      appType: row.appShape,
                      company: row.company,
                    },
                  });
                },
              },
              { default: () => '查看详情' }
            ),
          ]);
        },
      },
    ];
    basicColumns.map((item) => {
      if (item.toWelink === true) {
        item.render = (row) => {
          return h(
            'a',
            {
              href: '#',
              onClick: (e) => {
                e.preventDefault();
                openIMChatClick(row[item.key]);
              },
              style: {
                color: '#1288ff',
                textDecoration: 'none',
                cursor: 'pointer',
              },
            },
            row[item.key]
          );
        };
      }
    });
    return basicColumns;
  };

  const columns = createColumns();

  const tableData = ref<ListData[]>([]);

  const sortState = ref({
    sortField: 'riskScore',
    sortOrder: 'desc',
  });

  const importing = ref(false);
  const isImporting = ref(false);
  const handleUpFileUpload = async ({ file }) => {
    isImporting.value = true;
    try {
      const response = await importUpExcel(file.file);
      message.success('Excel 文件导入成功');
      console.log('Import successful:', response);
      fetchData(); // 重新加载数据
    } catch (error) {
      console.error('Failed to import Excel file:', error);
      message.error('Excel 文件导入失败');
    } finally {
      isImporting.value = false;
    }
  };
  // 在组件挂载时获取数据
  onMounted(async () => {
    initESpace();
    fetchData();
    getAppType();
    getEwpOwner();
    employeesOptions.value = await getPersonOptionsOnlyName(DTS_HANDLE_TAG);
  });

  function initESpace() {
    eSpaceCtrl.init();
    eSpaceCtrl.ready(function (data) {
      console.log('link success');
      //这里做一些加载时做的操作，比如是否让之后的openIMChatClick()可以调用
    });
    eSpaceCtrl.error(() => {
      console.log('link error');
    });
    eSpaceCtrl.on('user-status-change', function (status) {
      // @TODO监听用户的变化
    });
  }

  function openIMChatClick(expId) {
    let expIds = expId.split(',');
    expIds.forEach((id) => {
      eSpaceCtrl.showImDialog(id, function (err) {});
    });
  }

  const fetchData = async () => {
    loadingRef.value = true;
    const response = await service.post('/management/appInfo/query', {
      pageNo: paginationReactive.page,
      pageSize: paginationReactive.pageSize,
      ...filterObjectValues(form.value),
      sortField: sortState.value.sortField,
      sortOrder: sortState.value.sortOrder,
      riskScoreGe: 0,
    });
    if (response) {
      response.records.forEach((item) => {
        if (!item.appBundle && item.appPubishInfoPro && item.appPubishInfoPro.appBundle) {
          item.appBundle = item.appPubishInfoPro.appBundle;
        }
      });
      tableData.value = response.records;
      paginationReactive.itemCount = response.total;
      loadingRef.value = false;
    }
  };
  const router = useRouter();

  const handleSelectChange = (value: string | number) => {
    const isLongTailSelected = value === '长尾';
    isLevel.value = !isLongTailSelected;

    dynamicRules.dtseOwner![0].required = isLevel.value;
    dynamicRules.dtseLeader![0].required = isLevel.value;
    dynamicRules.solutionOwner![0].required = isLevel.value;
    dynamicRules.solutionLeader![0].required = isLevel.value;
    dynamicRules.acceptanceOwner![0].required = isLevel.value;
    dynamicRules.acceptanceLeader![0].required = isLevel.value;
    dynamicRules.bdOwner![0].required = isLevel.value;
    dynamicRules.bdLeader![0].required = isLevel.value;
    dynamicRules.appPM![0].required = isLevel.value;
  };

  const dynamicRules = reactive<FormRules>({
    appName: [{ required: true, message: '请输入应用名称' }],
    ewpOwner: [{ required: true, message: '请输入应用责任人' }],
    dtseOwner: [{ required: true, message: '请输入DTSE', trigger: ['input', 'blur'] }],
    dtseLeader: [{ required: true, message: '请输入DTSELeader', trigger: ['input', 'blur'] }],
    solutionOwner: [{ required: true, message: '请输入解决方案', trigger: ['input', 'blur'] }],
    solutionLeader: [
      { required: true, message: '请输入解决方案Leader', trigger: ['input', 'blur'] },
    ],
    acceptanceOwner: [{ required: true, message: '请输入验收责任人', trigger: ['input', 'blur'] }],
    acceptanceLeader: [
      {
        required: true,
        message: '请输入验收责任人Leader',
        trigger: ['input', 'blur'],
      },
    ],
    bdOwner: [{ required: true, message: '请输入BD责任人', trigger: ['input', 'blur'] }],
    bdLeader: [{ required: true, message: '请输入BDLeader', trigger: ['input', 'blur'] }],
    appPM: [{ required: true, message: '请输入应用PM', trigger: ['input', 'blur'] }],
    appType: [{ required: true, message: '请选择应用垂类' }],
    appLevel: [{ required: true, message: '请选择应用标签' }],
  });

  const handleAppNameClick = (name: string) => {
    router.push({
      name: 'MyTodo',
      query: { appName: name },
    });
  };
  const getAppType = async () => {
    const response = await service.get('/management/appInfo/appType');
    if (response) {
      appTypeOptions.value = response.map((item) => {
        return { label: item, value: item };
      });
    }
  };

  const getEwpOwner = async () => {
    const response = await service.get('/management/appInfo/appLevel');
    if (response) {
      const arr = new Set();
      response.forEach((item) => {
        const itemArr = item.split(',');
        arr.add(...itemArr);
      });
      appLevelOptions.value = Array.from(arr).map((item) => {
        return { label: item, value: item };
      });
      /*appLevelOptions.value = response.map((item) => {
        return { label: item, value: item };
      });*/
    }
  };

  const handleSearch = () => {
    paginationReactive.page = 1;
    fetchData();
    // 这里可以编写查询逻辑，更新 tableData
  };
  const handleReset = () => {
    // 重置表单数据为初始状态
    // formRef.value.resetFields();
    // 重置表单数据为初始状态
    form.value.appName = '';
    form.value.ewpOwner = null;
    form.value.label = '';
    form.value.appType = null;
    form.value.appLevel = null;
    form.value.top = null;
    sortState.value.sortField = 'riskScore';
    sortState.value.sortOrder = 'desc';
    fetchData();
  };

  const downloadCsv = () => {
    // 导出 CSV 逻辑
    const csvData = tableData.value.map((item: any) => {
      return {
        应用名称: item.appName,
        应用负责人: item.ewpOwner,
        DTSE: item.dtseOwner,
        应用垂类: item.appType,
        标签: item.appLevel, // 假设标签是数组，需要转换为字符串
        TOP2000: item.top,
      };
    });
    // 创建 CSV 内容
    const csvContent = [
      ['应用名称', '负责人', '应用垂类', '标签'], // 表头
      ...csvData.map((e) => Object.values(e)), // 数据行
    ]
      .map((e) => e.join(',')) // 将每行数据转换为字符串
      .join('\r\n'); // 每行之间用换行符分隔

    // 添加 UTF-8 BOM
    const BOM = '\uFEFF';
    const blob = new Blob([BOM + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'data.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const add = () => {
    dialogVisible.value = true;
    isAdd.value = true;
    actionForm.value = {
      appName: '',
      appBundle: '',
      ewpOwner: null,
      dtseOwner: '',
      dtseLeader: '',
      solutionOwner: '',
      solutionLeader: '',
      acceptanceOwner: '',
      acceptanceLeader: '',
      bdOwner: '',
      bdLeader: '',
      appPM: '',
      label: '',
      appType: null,
      appLevelList: null,
      // sourceName:'',//清单来源
      // riskScore:'',//风险指数
      // firstPublishTime: null,//首次上架时间
      // latestPublishTime:null,//更新时间
      // opinionNum:'',//声量值
      // version: '',//版本
      // appId: '',//应用id
      appShape: '', //应用形态
      company: '', //所属公司
      represent: '', //所属省份
      testOwner: '', // 测试责任人
    };
  };

  const sureAdd = async () => {
    // if (typeof actionForm.value.firstPublishTime === 'number') {
    //   const date = new Date(actionForm.value.firstPublishTime);
    //
    //   // 获取本地时间的年月日（注意月份需+1，因为 getMonth() 返回 0-11）
    //   const year = date.getFullYear();
    //   const month = String(date.getMonth() + 1).padStart(2, "0"); // 确保两位数（如 4→"04"）
    //   const day = String(date.getDate()).padStart(2, "0");
    //
    //   // 组合成 YYYY-MM-DD 格式的字符串（基于本地时间）
    //   actionForm.value.firstPublishTime = `${year}-${month}-${day}`;
    // }
    let appLevel = '';
    if (actionForm.value.appLevelList.length > 0) {
      appLevel = actionForm.value.appLevelList.join(',');
    }
    let response = await service.post('/management/appInfo/save', {
      ...actionForm.value,
      appLevel,
    });

    message.success(isAdd.value ? '新增成功' : '修改成功');
    dialogVisible.value = false;
    fetchData();
  };
  const cancel = () => (dialogVisible.value = false);

  const handleSorterChange = (sorter) => {
    if (sorter) {
      const { columnKey, order } = sorter;
      sortState.value.sortField = columnKey;
      sortState.value.sortOrder = order === 'ascend' ? 'asc' : 'desc';
    } else {
      sortState.value.sortField = 'riskScore';
      sortState.value.sortOrder = 'desc';
    }
    paginationReactive.page = 1; // 重置到第一页
    fetchData();
  };

  const handlePageChange = (page: number) => {
    paginationReactive.page = page;
    fetchData();
  };
  const handleFileUpload = async ({ file }) => {
    importing.value = true;
    try {
      const formData = new FormData();
      formData.append('file', file.file);

      const response = await service.post('/management/appInfo/importData', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      message.success('应用数据导入成功');
      fetchData(); // Refresh the table data
    } catch (error) {
      console.error('Failed to import application data:', error);
      message.error('应用数据导入失败');
    } finally {
      importing.value = false;
    }
  };

  const handleClipboardSearch = async () => {
    try {
      const clipboardText = await navigator.clipboard.readText();

      if (clipboardText) {
        console.log('clipboardContent:', clipboardText);
        dialog.warning({
          title: '确认搜索',
          content: `是否要搜索剪贴板中的内容：${clipboardText}`,
          positiveText: '确认',
          negativeText: '取消',
          onPositiveClick: () => {
            form.value.appName = clipboardText.trim();
            handleSearch();
          },
        });
      } else {
        message.warning('剪贴板为空');
      }
    } catch (err) {
      console.error('Failed to read clipboard:', err);
      message.error('无法读取剪贴板内容');
    }
  };
</script>
<style scoped>
  .container-box {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .button-box {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .search-box,
  .table-container {
    background-color: white;
    padding: 20px;
    /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); */
    border-radius: 8px;
  }

  .table-container {
    height: 100%;
  }

  /* 新增样式 */
  .n-required ~ .red-star {
    display: none;
  }

  .show-red-star .n-required ~ .red-star {
    display: inline-block;
  }
</style>
