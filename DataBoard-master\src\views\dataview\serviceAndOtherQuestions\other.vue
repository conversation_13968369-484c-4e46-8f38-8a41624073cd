<template>
  <div class="app-container">
    <div class="filter-container">
      <n-card>
        <n-form
          :model="searchForm"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          @submit.prevent="handleSearch"
        >
          <n-grid :cols="24" :x-gap="24">
            <n-grid-item v-for="item in visibleSearchFormItems" :key="item.field" :span="6">
              <n-form-item :label="item.label" :path="item.field">
                <n-input
                  v-if="item.component === 'Input'"
                  v-model:value="searchForm[item.field]"
                  clearable
                  @keyup.enter="handleSearch"
                />
                <n-select
                  v-else-if="item.component === 'Select' && item.componentProps?.options"
                  v-model:value="searchForm[item.field]"
                  :options="item.componentProps.options"
                  clearable
                  :filterable="item.componentProps.filterable"
                />
                <n-date-picker
                  v-else-if="item.component === 'DateRangePicker'"
                  v-model:value="searchForm[item.field]"
                  type="daterange"
                  clearable
                />
              </n-form-item>
            </n-grid-item>
          </n-grid>
          <div class="form-actions">
            <n-space>
              <n-button @click="resetForm" type="default">重置</n-button>
              <n-button type="primary" attr-type="submit">查询</n-button>
              <n-button @click="toggleExpandForm" type="default">
                {{ isExpanded ? '收起' : '展开' }}
                <template #icon>
                  <n-icon>
                    <chevron-down v-if="!isExpanded" />
                    <chevron-up v-else />
                  </n-icon>
                </template>
              </n-button>
            </n-space>
          </div>
        </n-form>
      </n-card>
    </div>

    <n-card style="margin-top: 12px">
      <n-space align="center" style="margin-bottom: 10px; margin-top: -9px">
        <n-space>
          <n-button @click="confirmAddQuestion"> 新增</n-button>
        </n-space>
      </n-space>
      <n-data-table
        remote
        :bordered="false"
        :single-line="false"
        striped
        @update:filters="handleFiltersChange"
        :columns="columns"
        :data="tableData"
        :pagination="paginationReactive"
        :loading="loading"
        :scroll-x="3000"
        :row-key="(row) => row.id"
        :checked-row-keys="checkedRowKeys"
        @update:checked-row-keys="handleCheckedRowKeysChange"
        @update:sorter="handleSorterChange"
        :max-height="477"
      />
    </n-card>

    <n-drawer v-model:show="dialogVisible" :width="502">
      <n-drawer-content :title="isAdd ? '新增' : '编辑'" closable>
        <n-form
          :model="actionForm"
          :rules="dynamicRules"
          :rule-props="{ immediate: true }"
          label-width="auto"
          label-placement="left"
          ref="formRef"
        >
          <n-form-item label="单号" path="orderId">
            <n-input v-model:value="actionForm.orderId" placeholder="请输入单号" />
          </n-form-item>
          <n-form-item label="来源" path="source">
            <n-input v-model:value="actionForm.source" placeholder="请输入来源" />
          </n-form-item>
          <n-form-item label="用户提单时间" path="userSubmissionDate">
            <n-date-picker
              v-model:value="actionForm.userSubmissionDate"
              value-format="yyyy/MM/dd"
              type="date"
              clearable
            />
          </n-form-item>
          <n-form-item label="应用名称" path="appName">
            <n-input
              v-model:value.trim="actionForm.appName"
              required
              placeholder="请输入应用名称"
              @change="selectUsername"
            />
          </n-form-item>
          <n-form-item label="应用版本" path="appVersion">
            <n-input
              v-model:value.trim="actionForm.appVersion"
              required
              placeholder="请输入应用版本"
            />
          </n-form-item>
          <n-form-item label="场景名称" path="sceneName">
            <n-input
              v-model:value.trim="actionForm.sceneName"
              required
              placeholder="请输入场景名称"
            />
          </n-form-item>
          <n-form-item label="问题描述" path="orderDescription">
            <n-input
              v-model:value.trim="actionForm.orderDescription"
              required
              placeholder="请输入问题描述"
            />
          </n-form-item>
          <n-form-item label="关联舆情单号" path="dtsOrderId">
            <n-input
              v-model:value.trim="actionForm.dtsOrderId"
              required
              placeholder="请输入关联舆情单号"
            />
          </n-form-item>
          <n-form-item label="状态" path="orderStatus">
            <n-select
              v-model:value="actionForm.orderStatus"
              placeholder="请选择状态"
              :options="dtsStatusOptions"
              clearable
            />
          </n-form-item>
          <n-form-item label="解决计划" path="solutionPlanDate">
            <n-date-picker
              v-model:value="actionForm.solutionPlanDate"
              value-format="yyyy/MM/dd"
              type="date"
              clearable
            />
          </n-form-item>
          <n-form-item label="问题级别" path="appType">
            <n-select
              v-model:value="actionForm.severity"
              placeholder="请选择问题级别"
              :options="questionLevelOptions"
              clearable
            />
          </n-form-item>
          <n-form-item label="舆情声量" path="opinionVolume">
            <n-input
              v-model:value.trim="actionForm.opinionVolume"
              required
              placeholder="请输入舆情声量"
            />
          </n-form-item>
          <n-form-item label="问题进展" @dblclick="showModel(actionForm)">
            <n-input placeholder="请在表格内双击问题进展填写" type="textarea" disabled clearable />
          </n-form-item>
          <n-form-item label="知识id">
            <n-input v-model:value.trim="actionForm.knowledgeId" placeholder="请输入知识id" />
          </n-form-item>
          <n-form-item label="产品">
            <n-input v-model:value.trim="actionForm.device" placeholder="请输入产品" />
          </n-form-item>
          <n-form-item label="系统版本">
            <n-input v-model:value.trim="actionForm.osVersion" placeholder="请输入系统版本" />
          </n-form-item>
          <n-form-item label="EWP责任人">
            <n-select
              v-model:value="actionForm.ewpOwner"
              placeholder="请选择应用责任人"
              :options="employeesOptions"
              clearable
              filterable
            />
          </n-form-item>
          <n-form-item label="领域责任人">
            <n-input v-model:value.trim="actionForm.domainOwner" placeholder="请输入领域责任人" />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space>
            <n-button @click="cancel">取消</n-button>
            <n-button type="primary" @click="sureAdd">确认</n-button>
          </n-space>
        </template>
      </n-drawer-content>
    </n-drawer>
    <ProblemProgressModal
      v-model:show="showProblemModal"
      :order-id="currentProblem?.orderId || ''"
      :current-problem="currentProblem"
      :after-submit="afterSubmit"
    />
  </div>
</template>

<script lang="ts">
  export default {
    name: 'Dtsstate',
  };
</script>

<script lang="ts" setup>
  import { ref, reactive, onMounted, computed, h } from 'vue';
  import {
    FormRules,
    PaginationProps,
    FormInst,
    NTag,
    NCard,
    NForm,
    NFormItem,
    NInput,
    NSelect,
    NButton,
    NSpace,
    NGrid,
    NGridItem,
    useMessage,
    NDataTable,
    NDatePicker,
    NTooltip,
  } from 'naive-ui';
  import {
    dtsStatusOptions,
    employeesOptions,
    getSearchFormItems,
    questionLevelOptions,
    SearchFormItem,
  } from './columns';
  import { filterObjectValues, formatDateTime } from '@/utils';
  import {
    addQuestion,
    deleteQuestion,
    getQuestions,
    getUsernameFromAppName,
  } from '@/api/dataview/serviceAndOtherQuestions';

  import { ChevronDown, ChevronUp } from '@vicons/ionicons5';
  import dayjs from 'dayjs';
  import ProblemProgressModal from '@/views/dataview/myOrder/components/ProblemProgressModal.vue';

  interface SearchFormType {
    registerDate: [Date, Date] | null;
    complaintId: string;
    issueType: string | null;
    complaintContent: string;
    occurrenceTime: [Date, Date] | null;
    productName: string;
    productVersion: string;
    relatedApp: string;
    appOwner: string;
    isTop1000: boolean | null;
    isNewIssue: boolean | null;
    dtsUrl: string;
    isRegisteredInternal: boolean | null;
    remarks: string;

    [key: string]: any;
  }

  interface TableRowType {
    id: string | number;

    [key: string]: any;
  }

  const isImporting = ref(false);
  const isExporting = ref(false);
  const searchForm = reactive<SearchFormType>({
    registerDate: null,
    complaintId: '',
    issueType: null,
    complaintContent: '',
    occurrenceTime: null,
    productName: '',
    productVersion: '',
    relatedApp: '',
    appOwner: '',
    isTop1000: null,
    isNewIssue: null,
    dtsUrl: '',
    isRegisteredInternal: null,
    remarks: '',
  });

  //新增模块
  const dialogVisible = ref(false);
  const isAdd = ref(true);
  const formRef = ref<FormInst | null>(null);
  const actionForm = ref({
    id: null,
    orderId: '',
    source: '',
    knowledgeId: '',
    userSubmissionDate: null,
    userSubmissionTime: '',
    appName: '',
    appVersion: '',
    sceneName: '',
    orderDescription: '',
    dtsOrderId: '',
    orderStatus: '',
    solutionPlan: '',
    solutionPlanDate: null,
    severity: '',
    opinionVolume: '',
    progress: '',
    device: '',
    osVersion: '',
    ewpOwner: '',
    domainOwner: '',
    feedbackSource: '',
    sourceClosed: '',
  });
  const dynamicRules = reactive<FormRules>({
    appName: [{ required: true, message: '请输入应用名称' }],
    orderDescription: [{ required: true, message: '请输入问题描述' }],
    userSubmissionDate: [
      { required: true, type: 'number', trigger: ['blur', 'change'], message: '请输入提单时间' },
    ],
  });

  const tableData = ref<TableRowType[]>([]);
  const loading = ref(false);
  const message = useMessage();
  const searchFormItems = computed(() => {
    const items = getSearchFormItems();
    const result: SearchFormItem[] = [];
    items.forEach((item) => {
      if (item.label !== '反馈人/响应群名' && item.label !== '来源是否关闭') {
        result.push(item);
      }
    });
    return result;
  });
  const paginationReactive = reactive<PaginationProps>({
    page: 1,
    pageSize: 10,
    itemCount: 0,
    showSizePicker: true,
    pageSizes: [20, 50, 100],
    prefix({ itemCount }) {
      return `Total：${itemCount}`;
    },
    onChange: (page: number) => {
      paginationReactive.page = page;
      fetchData();
    },
    onUpdatePageSize: (pageSize: number) => {
      paginationReactive.pageSize = pageSize;
      paginationReactive.page = 1;
      fetchData();
    },
  });

  const sortState = ref({
    sortField: '',
    sortOrder: '',
  });

  const isExpanded = ref(false);
  const visibleSearchFormItems = computed(() => {
    return isExpanded.value ? searchFormItems.value : searchFormItems.value.slice(0, 4);
  });

  // 表格选择框
  const selectedRows = ref<TableRowType[]>([]);
  const checkedRowKeys = ref<(string | number)[]>([]);
  const handleCheckedRowKeysChange = (keys: (string | number)[]) => {
    const newSelectedRows = keys
      .map((key) => {
        const existingRow = selectedRows.value.find((row) => row.id === key);
        if (existingRow) {
          return existingRow;
        }
        return tableData.value.find((row) => row.id === key);
      })
      .filter(Boolean) as TableRowType[];

    selectedRows.value = newSelectedRows;
    checkedRowKeys.value = keys;
  };

  const handleSorterChange = (sorter) => {
    if (sorter) {
      const { columnKey, order } = sorter;
      sortState.value.sortField = columnKey;
      sortState.value.sortOrder = order === 'ascend' ? 'asc' : 'desc';
    } else {
      sortState.value.sortField = 'riskScore';
      sortState.value.sortOrder = 'desc';
    }
    paginationReactive.page = 1; // 重置到第一页
    fetchData();
  };

  // 关联DTS单模态框
  const showRelateModal = ref(false);

  // 关联DTS单
  const relateDTS = ref('');

  const handleSearch = () => {
    paginationReactive.page = 1;
    fetchData();
  };

  const resetForm = () => {
    Object.keys(searchForm).forEach((key) => {
      searchForm[key as keyof SearchFormType] = key.includes('Date') ? null : '';
    });
    sortState.value.sortField = '';
    sortState.value.sortOrder = '';
    fetchData();
  };

  const toggleExpandForm = () => {
    isExpanded.value = !isExpanded.value;
  };

  // 修改 formatDate 函数
  const formatDate = (date: number | Date | null): string => {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const fetchData = async () => {
    loading.value = true;

    try {
      const queryParams: Record<string, any> = {
        ...filterObjectValues(searchForm),
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
        sortField: sortState.value.sortField,
        sortOrder: sortState.value.sortOrder,
      };

      if (searchForm.registerDate) {
        const [start, end] = searchForm.registerDate;
        queryParams.startTime = formatDate(start);
        queryParams.endTime = formatDate(end);
      }
      queryParams.channel = '其他';
      const { total, records } = await getQuestions(queryParams);
      tableData.value = records;
      paginationReactive.itemCount = total;
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loading.value = false;
    }
  };

  const handleFiltersChange = (filters: any) => {
    // 合并筛选条件到搜索表单
    Object.assign(searchForm, filters);
    // 重置到第一页
    paginationReactive.page = 1;
    // 重新获取数据
    fetchData();
  };

  const columns = computed(() => createColumns());

  const createColumns = () => {
    const columns = [
      {
        title: '来源',
        key: 'source',
        resizable: true,
        width: 120,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '关联舆情单号',
        key: 'dtsOrderId',
        resizable: true,
        width: 200,
        ellipsis: {
          tooltip: true,
        },
        render: (row) => {
          return h(
            'a',
            {
              href: '#',
              onClick: (e) => {
                window.open(
                  `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${row.dtsOrderId}`,
                  '_blank'
                );
              },
              style: {
                color: '#1288ff',
                textDecoration: 'none',
                cursor: 'pointer',
              },
            },
            row.dtsOrderId
          );
        },
      },
      {
        title: '应用名称',
        key: 'appName',
        resizable: true,
        width: 120,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '问题单描述',
        key: 'orderDescription',
        resizable: true,
        width: 200,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: 'EWP责任人',
        key: 'ewpOwner',
        resizable: true,
        width: 120,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '状态',
        key: 'orderStatus',
        resizable: true,
        width: 120,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: () =>
          h(
            'div',
            {
              style: {
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                justifyContent: 'center',
              },
            },
            [
              h(
                NTooltip,
                { trigger: 'hover' },
                {
                  trigger: () =>
                    h('div', { style: { display: 'flex', alignItems: 'center' } }, [
                      '问题最新进展',
                      h(
                        'span',
                        { style: { marginLeft: '4px', cursor: 'pointer', color: '#1890ff' } },
                        '?'
                      ),
                    ]),
                  default: () => '双击编辑最新进展',
                }
              ),
            ]
          ),
        key: 'progress',
        resizable: true,
        width: 200,
        ellipsis: {
          tooltip: true,
        },
        render: (row) =>
          h(
            'div',
            {
              style: { alignItems: 'center', gap: '6px', justifyContent: 'center' },
            },
            [
              row.progress && row.progress.length > 0
                ? h(
                    NTooltip,
                    { trigger: 'hover', maxWidth: '600px' },
                    {
                      trigger: () =>
                        h(
                          'div',
                          {
                            style: {
                              alignItems: 'center',
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              width: 'inherits',
                              height: 'inherits',
                            },
                            onDblclick: () => {
                              showModel(row);
                            },
                          },
                          [row.progress]
                        ),
                      default: () => row.progress,
                    }
                  )
                : h(
                    'div',
                    {
                      style: { width: '200px', height: '50px' },
                      onDblclick: () => {
                        showModel(row);
                      },
                    },
                    () => {}
                  ),
            ]
          ),
      },
      {
        title: '知识id',
        key: 'knowledgeId',
        resizable: true,
        width: 240,
        ellipsis: {
          tooltip: true,
        },
        render: (row) => {
          if (!row.knowledgeId) {
            return null;
          }
          return h(
            NTag,
            {
              type: 'info',
              style: { cursor: 'pointer' },
              bordered: false,
              onClick: async () => {
                const { knowledgeId } = row;
                window.open(`http://***********/knowledgeDetail/${knowledgeId}`, '_blank');
              },
            },
            {
              default: () => row.knowledgeId,
            }
          );
        },
      },
      {
        title: '单号',
        key: 'orderId',
        resizable: true,
        width: 200,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '场景名称',
        key: 'sceneName',
        resizable: true,
        width: 120,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '解决计划',
        key: 'solutionPlan',
        resizable: true,
        width: 120,
        ellipsis: {
          tooltip: true,
        },
        render(row) {
          return formatDateTime(row.solutionPlan);
        },
      },
      {
        title: '问题级别',
        key: 'severity',
        resizable: true,
        width: 120,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '舆情声量',
        key: 'opinionVolume',
        resizable: true,
        width: 200,
        sorter: true,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '用户提单时间',
        key: 'userSubmissionTime',
        resizable: true,
        sorter: true,
        width: 120,
        render(row) {
          return formatDateTime(row.userSubmissionTime);
        },
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '产品',
        key: 'device',
        resizable: true,
        width: 120,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '系统版本',
        key: 'osVersion',
        resizable: true,
        width: 120,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '应用版本',
        key: 'appVersion',
        resizable: true,
        width: 120,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '领域责任人',
        key: 'domainOwner',
        resizable: true,
        width: 120,
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '操作',
        key: 'actions',
        width: 280,
        fixed: 'right',
        render(row) {
          return h(
            'div',
            {
              style: {
                display: 'flex',
                gap: '8px',
              },
            },
            [
              h(
                NButton,
                {
                  strong: true,
                  tertiary: true,
                  size: 'small',
                  onClick: () => handleEdit(row),
                },
                '编辑'
              ),
            ]
          );
        },
      },
    ];
    return columns;
  };

  const cancel = () => (dialogVisible.value = false);

  const sureAdd = async () => {
    // if (typeof actionForm.value.firstPublishTime === 'number') {
    //   const date = new Date(actionForm.value.firstPublishTime);
    //
    //   // 获取本地时间的年月日（注意月份需+1，因为 getMonth() 返回 0-11）
    //   const year = date.getFullYear();
    //   const month = String(date.getMonth() + 1).padStart(2, "0"); // 确保两位数（如 4→"04"）
    //   const day = String(date.getDate()).padStart(2, "0");
    //
    //   // 组合成 YYYY-MM-DD 格式的字符串（基于本地时间）
    //   actionForm.value.firstPublishTime = `${year}-${month}-${day}`;
    // }
    formRef.value?.validate(async (errors) => {
      try {
        if (!errors) {
          if (actionForm.value.userSubmissionDate) {
            actionForm.value.userSubmissionTime = formatDate(actionForm.value.userSubmissionDate);
          } else {
            actionForm.value.userSubmissionTime = '';
          }
          if (actionForm.value.solutionPlanDate) {
            actionForm.value.solutionPlan = formatDate(actionForm.value.solutionPlanDate);
          } else {
            actionForm.value.solutionPlan = '';
          }

          await addQuestion({ ...actionForm.value, channel: '其他' });
          message.success('新增成功');
          dialogVisible.value = false;
          fetchData();
        }
      } catch (error) {
        message.error('新增失败');
      }
    });
  };

  const selectUsername = async () => {
    const username = await getUsernameFromAppName(actionForm.value.appName);
    if (username) {
      actionForm.value.ewpOwner = username;
    }
  };

  const confirmAddQuestion = () => {
    dialogVisible.value = true;
    isAdd.value = true;
    actionForm.value = {
      id: null,
      orderId: '',
      source: '',
      userSubmissionTime: '',
      userSubmissionDate: null,
      appName: '',
      appVersion: '',
      sceneName: '',
      orderDescription: '',
      dtsOrderId: '',
      orderStatus: '',
      solutionPlan: '',
      solutionPlanDate: null,
      severity: '',
      opinionVolume: '',
      progress: '',
      device: '',
      osVersion: '',
      ewpOwner: '',
      domainOwner: '',
      feedbackSource: '',
      sourceClosed: '',
      knowledgeId: '',
    };
  };

  const handleEdit = (row) => {
    actionForm.value = { ...row };
    actionForm.value.userSubmissionDate = actionForm.value.userSubmissionTime
      ? actionForm.value.userSubmissionTime
      : null;
    actionForm.value.solutionPlanDate = actionForm.value.solutionPlan
      ? actionForm.value.solutionPlan
      : null;
    dialogVisible.value = true;
    isAdd.value = false;
  };

  const handleDelete = async (row) => {
    try {
      await deleteQuestion(row.id);
      message.success('删除成功');
      await fetchData();
    } catch (error) {
      message.success('删除失败');
    }
  };

  const progressModel = () => {
    const showProblemModal = ref(false);
    const currentProblem = ref({
      orderId: null,
      appName: '',
      description: '',
    });
    const selectData = ref({});
    const afterSubmit = async (data) => {
      //更新最新进展
      selectData.value.progress =
        dayjs(data.createTime).format('MM/DD') + '：' + data.problemProcess;
      await addQuestion(selectData.value);
    };
    const showModel = (row) => {
      selectData.value = row;
      currentProblem.value.orderId = 'service' + row.id;
      currentProblem.value.appName = row.appName;
      currentProblem.value.description = row.orderDescription;
      showProblemModal.value = true;
    };
    return {
      showProblemModal,
      currentProblem,
      selectData,
      afterSubmit,
      showModel,
    };
  };

  const { showProblemModal, currentProblem, selectData, afterSubmit, showModel } = progressModel();

  onMounted(() => {
    fetchData();
  });
</script>

<style scoped>
  /* 修改表单项样式 */
  :deep(.n-form-item) {
    display: flex;
    margin-right: 0;
    margin-bottom: 18px;
  }

  :deep(.n-form-item-label) {
    width: 90px !important;
    text-align: right;
  }

  :deep(.n-form-item-blank) {
    flex: 1;
  }

  /* 统一输入框和选择框的宽度和对齐方式 */
  :deep(.n-input),
  :deep(.n-select) {
    /* width: 300px !important; */
  }

  /* 确保输入内容左对齐 */
  :deep(.n-input__input-el),
  :deep(.n-select-option__content) {
    text-align: left !important;
  }

  /* 确保选择框的内容左对齐 */
  :deep(.n-base-selection-label) {
    text-align: left !important;
  }

  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 6px;
    padding-bottom: 3px;
  }

  .setting-icon {
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.3s ease;
    color: #666;
  }

  .setting-icon:hover {
    background-color: #f5f5f5;
    color: #2080f0;
    transform: rotate(30deg);
  }

  .bold-font {
    font-weight: 700;
  }

  .gradient-text {
    font-size: 14px;
    background: -webkit-linear-gradient(90deg, red 0%, green 50%, blue 100%); /* Chrome, Safari */
    background: linear-gradient(90deg, red 0%, green 50%, blue 100%); /* 标准语法 */
    -webkit-background-clip: text; /* Chrome, Safari */
    background-clip: text;
    -webkit-text-fill-color: transparent; /* Chrome, Safari */
    color: transparent;
  }

  .statistics-container {
    margin: 24px 0;
  }

  .stat-card {
    background-color: #fff;
    transition: all 0.3s;
    height: 100%;
  }

  .stat-card:hover {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .stat-content {
    text-align: center;
  }

  .stat-label {
    color: #666;
    font-size: 14px;
    margin-bottom: 8px;
  }

  .stat-value {
    color: #2080f0;
    font-size: 24px;
    font-weight: bold;
  }

  .n-form-item {
    margin-bottom: 18px;
  }

  .n-card {
    border-radius: 8px;
  }

  .n-button {
    min-width: 80px;
  }
</style>
