<template>
  <div class="table-container">
    <div class="table-title">TOP共性问题进展</div>
    <n-data-table remote :single-line="false" striped :loading="loadingRef" :columns="columns" :data="tableData" />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { columns, fetchData, fetchTableData } from './statisticsTable';

const loadingRef = ref(true);

const tableData = ref<Record<string, string>[]>([]);

// 在组件挂载时获取数据
onMounted(async () => {
   // tableData.value = await fetchData();
  tableData.value = fetchTableData
  loadingRef.value = false
});
</script>

<style scoped>
.table-container {
  background-color: #ffffff;
}

.table-title {
  font-size: 18px;
  font-weight: bold;
  padding: 10px;
}
</style>
