export class Random {
  public static genUuid(len: number, radix?: number): string {
    const chars = '0123456789abcdefghijklmnopqrstuvwxyz'.split('');
    let uuid: string[] = []
    let r = radix || chars.length;
    if (len) {
      // Compact form
      for (let i = 0; i < len; i++) {
        let index = Math.floor(Math.random()*r)
        uuid.push(chars[index])
        console.log(index)
      }
    }
    return uuid.join('');
  }
}
