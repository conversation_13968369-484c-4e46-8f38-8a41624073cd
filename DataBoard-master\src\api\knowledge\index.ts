import { ewpService as service } from '@/utils/axios';

export async function getWeKnowList(params: any): Promise<any> {
  try {
    const response = await service.post('/management/weKnow/query', params);

    return response;
  } catch (error) {
    console.error('Error fetching work order list:', error);
    throw error;
  }
}

export async function updateWeKnow(data: Partial<any>): Promise<any> {
  try {
    const response = await service.post('/management/weKnow/updateWeKnow', data);
    return response;
  } catch (error) {
    console.error('Error updating work order:', error);
    throw error;
  }
}

export async function distributeAuthor(data: Partial<any>): Promise<any> {
  try {
    const response = await service.post('/management/weKnow/distributeAuthor', data);
    return response;
  } catch (error) {
    console.error('Error updating work order:', error);
    throw error;
  }
}

export async function insertKnowledge(data: Partial<any>): Promise<any> {
  try {
    const response = await service.post('/management/weKnow/insert', data);
    return response;
  } catch (error) {
    console.error('Error updating work order:', error);
    throw error;
  }
}

export async function getKnowledgeStatus(): Promise<any> {
  try {
    const response = await service.get('/management/weKnow/getKnowledgeStatus');
    return response;
  } catch (error) {
    console.error('Error updating work order:', error);
    throw error;
  }
}

export async function getKnowledgeType(): Promise<any> {
  try {
    const response = await service.get('/management/weKnow/getKnowledgeType');
    return response;
  } catch (error) {
    console.error('Error updating work order:', error);
    throw error;
  }
}

export async function queryBasicMsg(id): Promise<any> {
  try {
    const response = await service.get(`/management/weKnow/queryBasicMsg?id=${id}`);
    return response;
  } catch (error) {
    console.error('Error updating work order:', error);
    throw error;
  }
}
