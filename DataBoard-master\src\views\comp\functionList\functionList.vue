<template>
  <div>
    <n-card class="table-card">
      <template #header>
        <div class="chart-title">功能清单</div>
      </template>
      <n-space vertical>
        <n-flex justify="start" align="center">
          <n-button secondary strong type="warning" @click="search()">
            <template #icon>
              <n-icon>
                <Refresh />
              </n-icon>
            </template>
            刷新
          </n-button>
          <n-button
            v-if="isTest"
            secondary
            strong
            type="primary"
            @click="showImportModal = true"
            :disabled="!isTester"
          >
            导入
          </n-button>
          <n-button
            v-if="isTest && !isEdit"
            secondary
            strong
            type="primary"
            @click="startEdit"
            :disabled="!isTester"
          >
            编辑
          </n-button>
          <n-button v-if="isEdit" secondary strong type="primary" @click="confirmEdit">
            保存
          </n-button>
          <n-button v-if="isEdit" @click="cancleEdit">取消</n-button>
          <div v-if="isTest" style="align-content: center"
            >应用功能完备度：{{ functionPercentage }}</div
          >
          <div v-if="isTest" style="margin-left: 16px; align-content: center"
            >应用版本号：{{ appVersion }}</div
          >
        </n-flex>
        <n-data-table
          remote
          scroll-x
          :bordered="false"
          :single-line="false"
          :loading="loading"
          :columns="featuresColumns"
          :data="featureData"
          :max-height="500"
          row-class-name="custom-table"
        />
      </n-space>
    </n-card>
    <n-modal v-model:show="showImportModal">
      <n-card
        style="width: 600px"
        title="导入"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <div style="display: flex; align-items: center; margin-bottom: 12px">
          <n-form-item
            label="应用版本号："
            style="display: flex; align-items: center; margin-bottom: 12px"
          >
            <n-input v-model:value="appVersionVal" />
          </n-form-item>
          <n-button text @click="handleDownload" style="margin-left: 12px; margin-bottom: 12px"
            >点击下载导入模板</n-button
          >
        </div>
        <n-upload
          action="#"
          :custom-request="customRequest"
          :on-before-upload="onBeforeUpload"
          :multiple="true"
          :default-file-list="fileList"
          accept=".xls,.xlsx,"
          :max="1"
          directory-dnd
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <Add />
              </n-icon>
            </div>
            <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
          </n-upload-dragger>
        </n-upload>
      </n-card>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, h, reactive, toRefs } from 'vue';
import {
  query,
  importExcel,
  downloadTemplate,
  checkNew,
  multiUpdate,
} from '@/api/dataview/featuresList';
import { useMessage, useDialog, NDataTable, NInput, NButton, NFlex } from 'naive-ui';
import { UserRoleEnum } from '@/enums/UserRoleEnum';
import { useUserStore } from '@/store/modules/user';
import { Refresh, Add } from '@vicons/ionicons5';
import { cloneDeep } from 'lodash-es';
import { useRoute } from 'vue-router';
import { is } from 'date-fns/locale';
const route = useRoute();
const appName = ref(route.query.appName as string);

const showImportModal = ref(false);
const appType = ref(route.query.appType as string);
const userStore = useUserStore();
const roles = userStore.getUserInfo.roles;
const isTester = roles.includes(UserRoleEnum.TEST_ADMIN) || roles.includes(UserRoleEnum.TEST_USER);
const company = ref(route.query.company as string);
const message = useMessage();
const dialog = useDialog();
const loading = ref(true);
const featureData = ref([]);

const functionPercentage = ref('');
const appVersion = ref('');
const appVersionVal = ref('');
const fileList = ref([]);
const props = defineProps({
  isTest: {
    default: () => false,
  },
});
const { isTest } = toRefs(props);
const isEdit = ref(false);
const search = async () => {
  loading.value = true;
  try {
    let res = await query({
      pageNum: 1,
      pageSize: 1000,
      appName: appName.value,
      appType: appType.value,
      company: company.value,
    });
    if (res.status === '200') {
      featureData.value = res?.data?.data || [];
      functionPercentage.value = res?.data?.functionPercentage
        ? `${res?.data?.functionPercentage}%`
        : '';
      appVersion.value = res?.data?.appVersion ? res?.data?.appVersion : '';
    }
  } catch (e) {}
  loading.value = false;
};
const checkNewListDialog = (newFeaturesData) => {
  return new Promise((resolve) => {
    const dialogColumns = cloneDeep(featuresColumns).filter(
      (item) => !['addLine', 'operation'].includes(item.key)
    );
    dialogColumns[0].fixed = 'left';
    const paginationReactive = reactive({
      page: 1,
      pageSize: 5,
      itemCount: 10,
      showSizePicker: true,
      pageSizes: [5, 10, 20, 50, 100],
      onChange: (page: number) => {
        paginationReactive.page = page;
      },
      onUpdatePageSize: (pageSize: number) => {
        paginationReactive.pageSize = pageSize;
        paginationReactive.page = 1;
      },
    });
    const pagination = ref(paginationReactive);
    dialog.info({
      title: '提示',
      style: { width: '1200px' },
      content: () => {
        return h('div', null, {
          default: () => [
            h('div', { style: { margin: '10px 0' } }, '以下清单中新增功能部分，请确认是否继续导入'),
            h(NDataTable, {
              pagination: pagination.value,
              data: newFeaturesData,
              tableLayout: 'fixed',
              columns: dialogColumns,
              scrollX: '',
              maxHeight: 400,
            }),
          ],
        });
      },
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        resolve(true);
      },
      onNegativeClick: () => {
        resolve(false);
      },
    });
  });
};
const onBeforeUpload = () => {
  if (!appVersionVal.value) {
    message.warning('应用版本号未填！');
    return false;
  } else {
    return true;
  }
};
const customRequest = async ({ file, onError }: any) => {
  const formData = new FormData();
  formData.append('file', file.file as File);
  formData.append('appName', appName.value);
  formData.append('appVersion', appVersionVal.value);
  formData.append('company', company.value);
  formData.append('appType', appType.value);
  //上传接口
  try {
    let newFeaturesRes = await checkNew(formData);
    let newFeaturesData = newFeaturesRes?.data || [];
    let checkRes = true;
    if (newFeaturesData.length) {
      checkRes = await checkNewListDialog(newFeaturesData);
    }
    if (!checkRes) {
      onError();
      return;
    }
    let res = await importExcel(formData);
    if (res.status == '200') {
      showImportModal.value = false;
      message.success('导入成功');
      search();
    } else {
      fileList.value = [];
    }
  } catch (err) {
    fileList.value = [];
    onError();
  }
};
const handleDownload = () => {
  downloadTemplate()
    .then((res) => {
      if (!res) {
        message.error('下载失败！');
      }
    })
    .catch((e) => {
      message.error(`下载失败，原因：${e?.response?.data?.error}`);
    });
};
/**
 * 开始编辑
 */
const startEdit = () => {
  isEdit.value = true;
};

/**
 * 保存编辑内容
 */
const confirmEdit = async () => {
  message.loading('内容保存中', {
    duration: 200000,
  });
  loading.value = true;
  try {
    const functionList = featureData.value.map((v) => {
      return {
        firstLevel: v.firstLevel,
        secondLevel: v.secondLevel,
        thirdLevel: v.thirdLevel,
        source: v.source,
        description: v.description,
        type: v.type,
        gap: v.gap,
        examResult: v.examResult,
        missingFunction: v.missingFunction,
        bug: v.bug,
      };
    });
    const data = {
      appName: appName.value,
      appType: appType.value,
      company: company.value,
      functionList,
    };
    let res = await multiUpdate(data);
    if (res.status === '200') {
      message.success('编辑成功');
      isEdit.value = false;
      search();
      message.destroyAll();
    } else {
      message.warning('编辑失败！');
    }
  } catch (e) {
    loading.value = false;
  }
};

/**
 * 取消编辑
 */
const cancleEdit = () => {
  isEdit.value = false;
  // 取消编辑后重新请求数据
  search();
};

const getFeaturesColumns = [
  {
    title: '一级模块',
    key: 'firstLevel',
    width: 100,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      if (!isEdit.value) {
        return row.firstLevel;
      }
      return h(NInput, {
        value: row.firstLevel,
        type: 'text',
        size: 'small',
        onUpdateValue(v) {
          featureData.value[index]['firstLevel'] = v;
        },
      });
    },
  },
  {
    title: '二级模块',
    key: 'secondLevel',
    width: 100,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      if (!isEdit.value) {
        return row.secondLevel;
      }
      return h(NInput, {
        value: row.secondLevel,
        type: 'text',
        size: 'small',
        onUpdateValue(v) {
          featureData.value[index]['secondLevel'] = v;
        },
      });
    },
  },
  {
    title: '三级模块',
    key: 'thirdLevel',
    width: 200,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      if (!isEdit.value) {
        return row.thirdLevel;
      }
      return h(NInput, {
        value: row.thirdLevel,
        type: 'text',
        size: 'small',
        onUpdateValue(v) {
          featureData.value[index]['thirdLevel'] = v;
        },
      });
    },
  },
  {
    title: '来源',
    key: 'source',
    width: 100,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      if (!isEdit.value) {
        return row.source;
      }
      return h(NInput, {
        value: row.source,
        type: 'text',
        size: 'small',
        onUpdateValue(v) {
          featureData.value[index]['source'] = v;
        },
      });
    },
  },
  {
    title: '说明',
    key: 'description',
    width: 80,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      if (!isEdit.value) {
        return row.description;
      }
      return h(NInput, {
        value: row.description,
        type: 'text',
        size: 'small',
        onUpdateValue(v) {
          featureData.value[index]['description'] = v;
        },
      });
    },
  },
  {
    title: '类型',
    key: 'type',
    width: 80,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      if (!isEdit.value) {
        return row.type;
      }
      return h(NInput, {
        value: row.type,
        type: 'text',
        size: 'small',
        onUpdateValue(v) {
          featureData.value[index]['type'] = v;
        },
      });
    },
  },
  {
    title: 'GAP',
    key: 'gap',
    width: 80,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      if (!isEdit.value) {
        return row.gap;
      }
      return h(NInput, {
        value: row.gap,
        type: 'text',
        size: 'small',
        onUpdateValue(v) {
          featureData.value[index]['gap'] = v;
        },
      });
    },
  },
  {
    title: '验收',
    key: 'examResult',
    width: 80,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      if (!isEdit.value) {
        return row.examResult;
      }
      return h(NInput, {
        value: row.examResult,
        type: 'text',
        size: 'small',
        onUpdateValue(v) {
          featureData.value[index]['examResult'] = v;
        },
      });
    },
  },
  {
    title: '缺失',
    key: 'missingFunction',
    width: 120,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      if (!isEdit.value) {
        return row.missingFunction;
      }
      return h(NInput, {
        value: row.missingFunction,
        type: 'text',
        size: 'small',
        onUpdateValue(v) {
          featureData.value[index]['missingFunction'] = v;
        },
      });
    },
  },
  {
    title: 'BUG',
    key: 'bug',
    width: 120,
    minWidth: 60,
    resizable: true,
    ellipsis: {
      tooltip: {
        contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
      },
    },
    render(row, index) {
      if (!isEdit.value) {
        return row.bug;
      }
      return h(NInput, {
        value: row.bug,
        type: 'text',
        size: 'small',
        onUpdateValue(v) {
          featureData.value[index]['bug'] = v;
        },
      });
    },
  },
  {
    title: '解决计划',
    key: 'solutionPlan',
    width: 120,
    minWidth: 60,
    resizable: true,
  },
  {
    title: '操作',
    key: 'operation',
    width: 100,
    fixed: 'right',
    render(row, index) {
      return h(NFlex, { wrap: false }, () => [
        h(
          NButton,
          {
            text: true,
            strong: true,
            type: 'info',
            size: 'tiny',
            disabled: !isEdit.value,
            onClick: () => {
              featureData.value.splice(index + 1, 0, {
                firstLevel: '',
                secondLevel: '',
                thirdLevel: '',
                source: '',
                description: '',
                type: '',
                gap: '',
                examResult: '',
                missingFunction: '',
                bug: '',
              });
            },
          },
          { default: () => '新增' }
        ),
        h(
          NButton,
          {
            text: true,
            strong: true,
            type: 'error',
            size: 'tiny',
            disabled: !isEdit.value,
            onClick: () => {
              featureData.value.splice(index, 1);
            },
          },
          { default: () => '删除' }
        ),
      ]);
    },
  },
];

const featuresColumns = isTest.value ? getFeaturesColumns.filter(element => element.key!== 'solutionPlan'): getFeaturesColumns.slice(0, -1);
defineExpose({
  search,
});
onMounted(async () => {
  search();
});
</script>

<style scoped>
.chart-title {
  font-size: 18px;
  color: #303133;
  font-weight: bold;
}

@media (max-width: 768px) {
  .charts {
    flex-direction: column;
  }
}
.top-card {
  margin-bottom: 10px;
}

.table-card {
  margin-top: 10px;
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.custom-table td) {
  padding-top: 2px !important;
  padding-bottom: 2px !important;
}
:deep(.add-new-line) {
  display: none !important;
  cursor: pointer;
}
:deep(tr:hover .add-new-line) {
  display: inline-block !important;
}
:deep(tr:hover .add-new-line.disabled) {
  cursor: not-allowed;
}
</style>
