<template>
  <div>
    <div class="layout-page-header">
      <n-space>
        <n-button v-if="isAdmin" secondary strong type="primary" @click="handleAddClick">
          <template #icon>
            <n-icon>
              <Add />
            </n-icon>
          </template>
          添加
        </n-button>
        <n-button
          v-if="isAdmin && roleMgmtModel.batchImport"
          secondary
          strong
          type="primary"
          @click="showImportModal = true"
        >
          <template #icon>
            <n-icon>
              <Add />
            </n-icon>
          </template>
          批量导入
        </n-button>
        <n-button secondary strong type="warning" @click="getUserList()">
          <template #icon>
            <n-icon>
              <Refresh />
            </n-icon>
          </template>
          刷新
        </n-button>
        <n-input-group>
          <n-select
            v-model:value="filters.searchKey"
            :options="ROLE_MGMT_SEARCH_FILTER_OPTIONS"
            style="width: 80px"
          />
          <n-input
            v-model:value="filters.searchValue"
            type="text"
            placeholder="输入姓名或工号"
            style="width: 150px"
            @keyup.enter="queryUserFn"
            clearable
          />
        </n-input-group>
        <n-input-group>
          <n-button>
            部门
          </n-button>
          <n-select 
            v-model:value="filters.searchDepartment"
            :options="departmentOptions" 
            :style="{ width: '300px' }"
            :max-tag-count="1"
            multiple
            filterable
            clearable />
        </n-input-group>
        <n-input-group>
          <n-button>
            组别
          </n-button>
          <n-select 
            v-model:value="filters.teamList"
            :options="TEAM_INFO" 
            :style="{ width: '100px' }"
            :max-tag-count="1"
            multiple
            filterable
            clearable />
        </n-input-group>
        <n-input-group v-show="roleMgmtModel.multipleRoles">
          <n-button>
            角色
          </n-button>
          <n-select 
            v-model:value=filters.roles
            :options="rolesOptions"
            :max-tag-count="2"
            :style="{ width: '320px' }"
            multiple
            filterable
            clearable />
        </n-input-group>
        <n-button type="info" @click="queryUserFn">
            搜索
        </n-button>
        <n-button
          v-if="isWorkSpaceRoleMge && ChangeEmployeePerson.includes(userInfo.account)"
          type="error"
          @click="clickChangeEmployeeButton"
        >
          工号变更
        </n-button>
      </n-space>
    </div>
    <n-data-table
      :columns="columns"
      :data="data"
      :pagination="pagination"
      remote
      style="margin-top: 20px"
    />
    <n-modal v-model:show="showModal" :on-after-leave="initModel">
      <n-card
        style="width: 600px"
        :title="isAdd ? '新增角色' : '编辑角色'"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <template #header-extra> </template>
        <n-form
          ref="formRef"
          :model="model"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="medium"
          :style="{
            maxWidth: '640px',
          }"
        >
          <n-form-item label="姓名" path="userName">
            <n-input v-model:value="model.userName" placeholder="请输入姓名" :disabled="!isAdd" />
          </n-form-item>
          <n-form-item label="工号" path="account">
            <n-input
              v-model:value="model.account"
              placeholder="请输入字母工号"
              :disabled="!isAdd"
            />
          </n-form-item>
          <n-form-item
            v-if="roleMgmtModel.multipleRoles && roleMgmtModel.moduleId !== '-1'"
            label="角色"
            path="roles"
          >
            <n-checkbox-group v-model:value="model.roles">
              <n-space item-style="display: flex;">
                <n-checkbox
                  v-for="(role, index) in roleMgmtModel.roles"
                  :key="index"
                  :value="role.value"
                  :label="role.label"
                />
              </n-space>
            </n-checkbox-group>
          </n-form-item>
          <!-- 工作台-权限管理 -->
          <n-form-item 
            v-if="isWorkSpaceRoleMge"
            label="组别"
            path="team">
            <n-select
              v-model:value="model.team"
              placeholder="组别"
              :options="TEAM_INFO"
            />
          </n-form-item>
          <n-form-item
            v-if="isWorkSpaceRoleMge"
            label="角色"
            path="roles"
          >
            <n-checkbox-group v-model:value="model.roles">
              <n-flex vertical>
                <n-flex v-for="(rolesList, index) in rolesGroup" :key="index" :wrap="false">
                  <n-space item-style="width: 56px">{{ rolesList[0].title }}：</n-space>
                  <n-space>
                    <n-checkbox
                      v-for="(role, i) in rolesList"
                      :key="i"
                      :value="role.value"
                      :label="role.label"
                    />
                  </n-space>
                </n-flex>
              </n-flex>
            </n-checkbox-group>
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" @click="handleSubmit"> 确认 </n-button>
            <n-button secondary strong type="error" @click="showModal = false"> 取消 </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
    <!--角色权限弹窗-->
    <n-modal v-model:show="showAddRolesModal" :on-after-leave="initModel">
      <n-card
        style="width: 600px"
        :title="isAdd ? '添加权限' : '编辑权限'"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form
          ref="formRef"
          :model="model"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="medium"
          :style="{
            maxWidth: '640px',
          }"
        >
          <n-form-item label="姓名/工号" path="account">
            <UserSearch v-if="isAdd" @userSelect="onUserSelect"></UserSearch>
            <div v-else>{{ model.userName }}/{{ model.account }}</div>
          </n-form-item>
          <n-form-item
            v-if="roleMgmtModel.multipleRoles && roleMgmtModel.moduleId !== '-1'"
            label="角色"
            path="roles"
          >
            <n-checkbox-group v-model:value="model.roles">
              <n-space item-style="display: flex;">
                <n-checkbox
                  v-for="(role, index) in roleMgmtModel.roles"
                  :key="index"
                  :value="role.value"
                  :label="role.label"
                />
              </n-space>
            </n-checkbox-group>
          </n-form-item>
          <!-- 工作台-权限管理 -->
          <n-form-item 
            v-if="isWorkSpaceRoleMge"
            label="组别"
            path="team">
            <n-select
              v-model:value="model.team"
              placeholder="组别"
              :options="TEAM_INFO"
            />
          </n-form-item>
          <n-form-item
            v-if="isWorkSpaceRoleMge"
            label="角色"
            path="roles"
          >
            <n-checkbox-group v-model:value="model.roles">
              <n-flex vertical>
                <n-flex v-for="(rolesList, index) in rolesGroup" :key="index" :wrap="false">
                  <n-space item-style="width: 56px">{{ rolesList[0].title }}：</n-space>
                  <n-space>
                    <n-checkbox
                      v-for="(role, i) in rolesList"
                      :key="i"
                      :value="role.value"
                      :label="role.label"
                    />
                  </n-space>
                </n-flex>
              </n-flex>
            </n-checkbox-group>
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" @click="handleRolesChangeSubmit"> 确认 </n-button>
            <n-button secondary strong type="error" @click="showAddRolesModal = false">
              取消
            </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
    <n-modal v-model:show="showImportModal" :on-after-leave="initModel">
      <n-card
        style="width: 600px"
        title="批量导入"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-button text @click="handleDownload" style="margin-bottom: 20px"
          >点击下载导入模板</n-button
        >
        <n-upload
          action="#"
          :custom-request="customRequest"
          :multiple="true"
          accept=".xls,.xlsx,"
          directory-dnd
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <ArchiveIcon />
              </n-icon>
            </div>
            <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
          </n-upload-dragger>
        </n-upload>
      </n-card>
    </n-modal>
    <n-modal v-model:show="showChangeEmployee">
      <n-card
        style="width: 700px"
        title="工号变更"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <span style="font-size: 18px; color: red">请输入不带首字母的工号，如********</span>
        <n-form
          ref="formRef"
          :model="changeEmployeeForm"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="medium"
          label-align="right"
          :rules="rules"
          style="margin-top: 16px"
        >
          <n-grid :cols="24" :x-gap="24">
            <n-form-item-gi :span="12" label="旧工号" path="oldAccount">
              <n-input v-model:value="changeEmployeeForm.oldAccount" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="新工号" path="newAccount">
              <n-input v-model:value="changeEmployeeForm.newAccount" />
            </n-form-item-gi>
          </n-grid>
        </n-form>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" @click="submitChangeEmployee"> 确认 </n-button>
            <n-button secondary strong type="error" @click="showChangeEmployee = false"> 取消 </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { Add, Refresh } from '@vicons/ionicons5';
  import {
    NIcon,
    NButton,
    NTag,
    useDialog,
    UploadCustomRequestOptions,
    FormItemRule,
    FormRules,
    DataTableColumns,
  } from 'naive-ui';
  import { h, ref, reactive, VNode } from 'vue';
  import {
    addUser,
    addUserList,
    importUserListAll,
    editUser,
    deleteUser,
    downloadTemplate,
    queryUser,
    UserDto,
    getDepartmentValues,
    changeEmployeeService,
  } from '@/api/system/usermanage';
  import {
    DEFAULT_ROLE,
    getDefaultFilters,
    getDefaultUserInfo,
    getRoleMgmtModel,
    ROLE_MGMT_SEARCH_FILTER_OPTIONS,
    RoleMgmtFilterStatus,
    SelectOption,
    formatRoles,
    DefaultChangeEmployeeForm,
    ChangeEmployeePerson,
  } from './index';
  import { TEAM_INFO } from './constUserInfo';
  import { ResultEnum } from '@/enums/httpEnum';
  import { useMessage } from 'naive-ui';
  import { useUserStore } from '@/store/modules/user';
  import UserSearch from "@/views/comp/UserSearch.vue";
  import { UserRoleEnum } from "@/enums/UserRoleEnum";
  import { validateAccount } from "@/utils/user";
  import { cloneDeep } from 'lodash-es';

  const pathName = location.pathname.split('/').pop();
  const roleMgmtModel = getRoleMgmtModel(pathName!);
  const roleList = [...roleMgmtModel.roles];
  const rolesGroup = formatRoles(roleList);
  const showModal = ref(false);
  const showAddRolesModal = ref(false);
  const showImportModal = ref(false);
  const isAdd = ref(false);
  const userInfo = useUserStore().getUserInfo;
  const isAdmin = userInfo.roles.includes(roleMgmtModel.roles[0]?.value) || userInfo.isSuperAdmin;
  const filters = ref<RoleMgmtFilterStatus>(
    getDefaultFilters(roleMgmtModel.moduleId === '-1' ? rolesGroup.flat() : roleList)
  );
  const dialog = useDialog();
  const formRef = ref();
  const message = useMessage();
  const departmentOptions = ref<SelectOption[]>()
  const rolesOptions = ref<SelectOption[]>(roleList)
  let data = ref<UserDto[]>([]);
  let model = ref<UserDto>(getDefaultUserInfo(roleMgmtModel));
  let isWorkSpaceRoleMge = ref(roleMgmtModel.multipleRoles && roleMgmtModel.moduleId === '-1')
  let rules: FormRules = {
    userName: [
      {
        required: true,
        trigger: ['blur', 'input'],
        message: '请输入用户名',
      },
    ],
    account: [
      {
        required: true,
        trigger: ['blur', 'input'],
        message: '请输入账号',
      },
      {
        key: 'account',
        validator: validateAccount,
        message: '请输入带姓首字母格式，比如张晓明的工号：z********，英文小写',
        trigger: ['blur', 'input'],
      },
    ],
    password: [
      {
        required: true,
        validator(rule: FormItemRule, value: string) {
          if (!value) {
            return new Error('请输入密码');
          } else if (
            value.length < 6 ||
            value.length > 32 ||
            /[^\x00-\xff]|\s|\r|\n|\r\n/g.test(value)
          ) {
            return new Error('密码应由不短于6位的字母、数字或字符组成');
          }
          return true;
        },
        trigger: ['blur', 'input'],
      },
    ],
    roles: [
      {
        required: roleMgmtModel.moduleId === '-1' ? true :  false, // 只有在工作台-权限管理里，才必选一个角色
        trigger: ['blur', 'input'],
        validator(rule: FormItemRule, value: string) {
          if (!value.length) {
            return new Error('请至少选择一个角色');
          }
          return true;
        },
      },
    ],
    personalSkills: [],
    oldAccount: [
      {
        required: true,
        trigger: ['blur', 'input'],
        validator(rule: FormItemRule, value: string) {
          if (!value) {
            return new Error('请输入旧工号');
          }
          if (!/^00[0-9]{6}$/.test(value)) {
            return new Error('请输入不带首字母的工号，如********');
          }
          return true;
        },
      },
    ],
    newAccount: [
      {
        required: true,
        trigger: ['blur', 'input'],
        validator(rule: FormItemRule, value: string) {
          if (!value) {
            return new Error('请输入新工号');
          }
          if (!/^00[0-9]{6}$/.test(value)) {
            return new Error('请输入不带首字母的工号，如********');
          }
          return true;
        },
      },
    ],
  };
  const columns: DataTableColumns<UserDto> = getColumns();
  const showChangeEmployee = ref(false);
  const changeEmployeeForm = ref(cloneDeep(DefaultChangeEmployeeForm));

  const pagination = reactive({
    page: 1,
    pageSize: 10,
    prefix({ itemCount }) {
      return `总计 ${itemCount}`
    },
    showSizePicker: true,
    pageSizes: [10, 20, 50],
    itemCount: 0,
    onChange: (page: number) => {
      pagination.page = page;
      getUserList();
    },
    onUpdatePageSize: (pageSize: number) => {
      pagination.pageSize = pageSize;
      pagination.page = 1;
      getUserList();
    },
  });

  function getOperations(row: UserDto): VNode[] {
    let result: VNode[] = [];
    if (roleMgmtModel.multipleRoles) {
      const isEdit = userInfo.roles.filter((v) => v.endsWith('_admin'));
      result.push(
        h(
          NButton,
          {
            strong: true,
            tertiary: true,
            type: 'info',
            size: 'medium',
            style: 'margin-right:20px',
            disabled: !isEdit.length,
            onClick: () => {
              isAdd.value = false;
              showAddRolesModal.value = true;
              model.value = { ...row };
            },
          },
          [h('div', '编辑')]
        )
      );
    } else if (roleMgmtModel.moduleId !== '-1') {
      result.push(
        h(
          NButton,
          {
            strong: true,
            tertiary: true,
            type: 'error',
            size: 'medium',
            disabled: !isAdmin || row.roles.includes(UserRoleEnum.SUPER_ADMIN),
            onClick: () => {
              dialog.warning({
                title: '警告',
                content: `你确定移除${row.userName}的${roleMgmtModel.roles[0].label}权限吗？`,
                positiveText: '确定',
                negativeText: '取消',
                onPositiveClick: () => {
                  model.value = { ...row };
                  model.value.roles = model.value.roles.filter((item) => item !== roleMgmtModel.roles[0].value);
                  editUser(model.value)
                    .then((res) => {
                      if (res?.status == ResultEnum.SUCCESS) {
                        message.success('移除权限成功！');
                        showModal.value = false;
                        getUserList();
                      } else {
                        message.error('移除权限失败！');
                      }
                    })
                    .catch((e) => {
                      message.error(`移除权限失败，原因：${e?.response?.data?.error}`);
                    });
                },
                onNegativeClick: () => {},
              });
            },
          },
          [h('div', '移除权限')]
        )
      );
    }
    // 超级管理员才可以删除用户
    if (userInfo.isSuperAdmin) {
      result.push(
        h(
          NButton,
          {
            strong: true,
            tertiary: true,
            type: 'error',
            size: 'medium',
            disabled: row.roles.includes(UserRoleEnum.SUPER_ADMIN),
            onClick: () => {
              dialog.warning({
                title: '警告',
                content: `你确定删除${row.userName}吗？`,
                positiveText: '确定',
                negativeText: '取消',
                onPositiveClick: () => {
                  deleteUserFn(row.account);
                },
                onNegativeClick: () => {},
              });
            },
          },
          [h('div', '删除')]
        )
      );
    }
    return result;
  }

  function getColumns() {
    let columns: DataTableColumns<UserDto> = [];
    columns.push(
      {
        title: '用户名',
        key: 'userName',
        width: '10%',
      },
      {
        title: '账号',
        key: 'account',
        width: '10%',
      },
      {
        title: '部门',
        key: 'department',
        width: '20%',
      },
      {
        title: '邮箱',
        key: 'email',
        width: '20%',
      }
    );
    if (roleMgmtModel.multipleRoles) {
      columns.push(
        {
        title: '角色',
        key: 'roles',
        width: '20%',
        render(row: UserDto) {
          const rolesArr = row.roles?.filter((item) => roleList.some((role) => role.value == item));
          return Array.from(new Set(rolesArr)).map((item) => {
            return h(
              NTag,
              {
                style: {
                  marginRight: '6px',
                  marginBottom: '6px',
                },
                type: 'info',
                bordered: false,
              },
              {
                default: () => roleList.find((role) => role.value == item)?.label,
              }
            );
          });
        },
        }
      );
    }
    columns.push(
      {
        title: '组别',
        key: 'team',
        width: '100px',
      }, {
        title: '操作',
        key: 'action',
        width: '20%',
        render(row) {
          return getOperations(row);
        },
      }
    );
    return columns;
  }
  // 获取全部用户列表
  const getUserList = async () => {
    data.value = [];
    let user: Partial<UserDto> = {};
    if (filters.value.searchValue) {
      user[filters.value.searchKey] = filters.value.searchValue;
    }
    
    if (filters.value.roles?.length) {
      user.roles = filters.value.roles;
    }
    if (filters.value.teamList?.length) {
      user.teamList = filters.value.teamList;
    }
    const response = await queryUser({
      user,
      departmentList: filters.value.searchDepartment || [],
      pageNum: pagination.page,
      pageSize: pagination.pageSize,
    });
    if (response?.data) {
      data.value = response.data.data;
      pagination.itemCount = response.data.pageInfo?.total as number;
    }
  };
  // 查询用户
  const queryUserFn = async () => {
    pagination.page = 1;
    getUserList();
  };
  const onUserSelect = (userInfo: UserDto) => {
    model.value = userInfo;
  }
  const initModel = () => {
    model.value = getDefaultUserInfo(roleMgmtModel);
  };
  //上传
  const customRequest = ({ file }: UploadCustomRequestOptions) => {
    const formData = new FormData();
    formData.append('file', file.file as File);
    //上传接口
    formData.forEach((item) => {
      uploadUserList(item);
    });
  };
  // 删除
  const deleteUserFn = async (account: string) => {
    deleteUser({ account })
      .then((res) => {
        if (res?.data) {
          message.success('删除用户成功！');
          showModal.value = false;
          getUserList();
        } else {
          message.error('删除用户失败！');
        }
      })
      .catch((e) => {
        message.error(`删除用户失败，原因：${e?.response?.data?.error}`);
      });
  };
  // 新增或编辑
  const handleSubmit = async () => {
    formRef.value.validate(async (errors) => {
      if (!errors) {
        const submitData = { ...model.value };
        submitData.userName = submitData.userName.trim(); // 去除姓名前后空格
        submitData.account = submitData.account.trim(); // 去除工号前后空格
        if (isAdd.value) {
          addUser(submitData)
            .then((res) => {
              if (res?.status == ResultEnum.SUCCESS) {
                message.success('添加用户成功！');
                showModal.value = false;
                getUserList();
              } else {
                message.error('添加用户失败！');
              }
            })
            .catch((e) => {
              message.error(`添加用户失败，原因：${e?.response?.data?.error}`);
            });
        } else {
          editUser(submitData)
            .then((res) => {
              if (res?.status == ResultEnum.SUCCESS) {
                message.success('修改用户信息成功！');
                showModal.value = false;
                getUserList();
              } else {
                message.error('修改用户信息失败！');
              }
            })
            .catch((e) => {
              message.error(`修改用户信息失败，原因：${e?.response?.data?.error}`);
            });
        }
      } else {
        console.log(errors);
      }
    });
  };
  // 上传用户清单
  const uploadUserList = async (file) => {
    importUserListAll(file)
      .then((res) => {
        if (res?.status == ResultEnum.SUCCESS) {
          message.success('导入成功！');
          getUserList();
          showImportModal.value = false;
        } else {
          message.error('导入失败！');
        }
      })
      .catch((e) => {
        message.error(`导入失败，原因：${e?.response?.data?.error}`);
      });
  };
  const handleDownload = async () => {
    downloadTemplate()
      .then((res) => {
        if (res) {
        } else {
          message.error('下载失败！');
        }
      })
      .catch((e) => {
        message.error(`下载失败，原因：${e?.response?.data?.error}`);
      });
  };
  const handleAddClick = () => {
    isAdd.value = true;
    roleMgmtModel.moduleId === '-1' ? showModal.value = true : showAddRolesModal.value = true;
  };
  // 修改用户信息
  const handleRolesChangeSubmit = async () => {
    formRef.value.validate(async (errors) => {
      if (!errors) {
        const response = await queryUser({
          user: {
            account: model.value.account,
          },
          pageNum: 1,
          pageSize: pagination.pageSize,
        });
        const [userInfo] = (response?.data?.data as UserDto[]) ?? [null];
        if (userInfo) {
          let roles = roleMgmtModel.multipleRoles
            ? model.value.roles
            : userInfo.roles.concat(roleMgmtModel.roles[0].value);
          editUser({
            account: userInfo.account,
            roles: Array.from(new Set(roles)),
            team: model.value.team,
          })
            .then((res) => {
              if (res?.status == ResultEnum.SUCCESS) {
                message.success('修改信息成功！');
                showModal.value = false;
                getUserList();
              } else {
                message.error('修改信息失败！');
              }
            })
            .catch((e) => {
              message.error(`修改信息失败，原因：${e?.response?.data?.error}`);
            });
          getUserList();
          showAddRolesModal.value = false;
        } else {
          dialog.info({
            title: '提示',
            content: `该用户未注册，点击确定新增用户`,
            positiveText: '确定',
            negativeText: '取消',
            onPositiveClick: () => {
              showAddRolesModal.value = false;
              showModal.value = true;
            },
            onNegativeClick: () => {
              showAddRolesModal.value = false;
            },
          });
        }
      } else {
        console.log(errors);
      }
    });
  };

  // 点击工号变更按钮
  const clickChangeEmployeeButton = () => {
    changeEmployeeForm.value = cloneDeep(DefaultChangeEmployeeForm);
    showChangeEmployee.value = true;
  };

  // 工号变更提交按钮
  const submitChangeEmployee = () => {
    formRef.value.validate(async (errors) => {
      if (!errors) {
        try {
          const data = { ...changeEmployeeForm.value };
          const res = await changeEmployeeService(data);
          if (res.status === '200') {
            message.success('工号变更成功！');
            showChangeEmployee.value = false;
          } else {
            message.error('工号变更失败！');
          }
        } catch (error) {
          message.error(error?.response.data.message);
          Error();
        }
      } else {
        Error();
      }
    });
  };

  // 初始化获取用户列表
  getUserList();

  getDepartmentValues({
    roleList: roleList.map((v) => v.value),
  }).then((res) => {
    departmentOptions.value = res
  })
</script>

<style lang="less" scoped>
  .layout-page-header {
    margin-top: 20px;
  }
</style>
