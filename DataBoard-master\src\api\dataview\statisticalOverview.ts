import service from '@/utils/axios';
const baseUrl = '/output';
export const getOutputList = (data) => {
  return service({
    url: `${baseUrl}/queryNew`,
    method: 'post',
    data,
  });
};
export function updateRemark(params: any) {
  return service({
    url: `${baseUrl}/updateRemark`,
    method: 'post',
    params,
  });
}
export const exportOutputList = (data) => {
  return service({
    url: `${baseUrl}/export`,
    method: 'post',
    responseType: 'blob',
    data
  });
};

// 导入MR检视意见数量
export const mRReviewNumberImport = (data) => {
  return service({
    url: `/output/importMrReview`,
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};

// 导入工程代码开发行数
export const codeDevelopmentNumberImport = (data) => {
  return service({
    url: `/output/importCodeLine`,
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};
// 导入开发者声音
export const importVod = (data) => {
  return service({
    url: `/output/importVod`,
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};

// 导入社区问题数据
export const communityProblemDataImport = (data) => {
  return service({
    url: `/output/importCommunityProblem`,
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};

// 导入驻场开发天数
export const residentDevelopmentDaysImport = (data) => {
  return service({
    url: `/output/importOnSite`,
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};

export const latestMrReview = () => {
  return service({
    url: `/output/latestMrReview`,
    method: 'get'
  });
};

export const latestMrCodeLine = () => {
  return service({
    url: `/output/latestMrCodeLine`,
    method: 'get'
  });
};
// 查询驻场天数
export const queryOnsiteRecord = () => {
  return service({
    url: `/output/queryOnsiteRecord`,
    method: 'post',
  });
};
// 编辑驻场天数
export const updateOnsiteRecord = (data) => {
  return service({
    url: `/output/updateOnsiteRecord`,
    method: 'post',
    data,
  });
};
// 新增驻场天数
export const createOnsiteRecord = (data) => {
  return service({
    url: `/output/createOnsiteRecord`,
    method: 'post',
    data,
  });
};
// 删除驻场天数
export const deleteOnsiteRecord = (data) => {
  return service({
    url: `/output/deleteOnsiteRecord`,
    method: 'post',
    data,
  });
};
// 查询待审批
export const queryToBeApproved = () => {
  return service({
    url: `/output/queryToBeApproved`,
    method: 'post',
  });
};
