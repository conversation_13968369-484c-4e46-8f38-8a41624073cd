<script setup lang="ts">
import { queryUser, UserDto } from "@/api/system/usermanage";
import { getUserLabel } from "@/store/modules/user";
import { ref } from "vue";
import { SelectBaseOption } from "naive-ui/es/select/src/interface";

const defaultUserList: SelectBaseOption<UserDto | null, string>[] = [{
  label: '请输入姓名或工号搜索',
  value: null,
  disabled: true,
}];
const emit = defineEmits(['userSelect']);
const userLabel = defineModel<string>('userLabel');
const userList = ref<SelectBaseOption<UserDto | null, string>[]>(defaultUserList);
const onSelect = (val: UserDto) => {
  if (!val) {
    return;
  }
  emit('userSelect', val);
  userLabel.value = getUserLabel(val);
}
const onClear = () => {
  userLabel.value = '';
}
const onShowStateChange = (show: boolean) => {
  if (!show) {
    userList.value = defaultUserList;
  }
}
const handleSearchUser = async (query: string) => {
  const searchValue = query.trim();
  if (!searchValue) {
    return;
  }
  userList.value = [];
  let params: Partial<UserDto> = {};
  if (/^[a-zA-Z][0-9]{8}$/.test(searchValue)) {
    params.account = searchValue;
  } else {
    params.userName = searchValue;
  }
  const response = await queryUser({
    user: params,
    pageNum: 1,
    pageSize: 10,
  });
  if (response?.data?.data?.length) {
    userList.value = response.data.data.map((item) => {
      return {
        label: getUserLabel(item),
        value: item,
      };
    });
  }
};
</script>

<template>
  <n-select
      :options="userList"
      v-model:value="userLabel"
      placeholder="请输入姓名或工号搜索"
      remote
      clearable
      filterable
      @update:value="onSelect"
      @update:show="onShowStateChange"
      @clear="onClear"
      @search="handleSearchUser"
  />
</template>

<style scoped lang="less">

</style>
