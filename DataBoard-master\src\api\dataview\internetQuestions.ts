import {adminService as server, ewpService as service} from '@/utils/axios';

export async function getInternetQuestions(params: any): Promise<any> {
  try {
    const response = await service.post('/management/internet/query', params);

    return response;
  } catch (error) {
    throw error;
  }
}

export async function importExcel(file: File): Promise<any> {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await service.post('/management/internet/importData', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    console.log('Import response:', response);

    return response;
  } catch (error) {
    console.error('Error importing Excel file:', error);
    throw error;
  }
}

export const exportData = (params:any) => {
  return new Promise((resolve, reject) => {
    const req = new XMLHttpRequest();
    req.open('POST', `http://${window.location.host}/ewp/management/internet/exportData`, true);
    req.responseType = 'blob';
    req.setRequestHeader('Content-Type', 'application/json');
    req.onload = function () {
      const data = req.response;
      const blob = new Blob([data]);
      const blobUrl = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.download = '互联网问题导出.xlsx';
      a.href = blobUrl;
      a.click();
      resolve(true);
    };
    req.send(JSON.stringify(params));
  });
};


