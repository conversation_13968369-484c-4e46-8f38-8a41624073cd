import service from '@/utils/axios';

const baseUrl = '/emergency_contact';
const travelInformation = '/travel_information';
export const getContactList = (data) => {
  return service({
    url: `${baseUrl}/query`,
    method: 'post',
    data,
  });
};
export const getStatistic = () => {
  return service({
    url: `${travelInformation}/statistic`,
    method: 'get',
  });
};
export const addContact = (data) => {
  return service({
    url: `${baseUrl}/add`,
    method: 'post',
    data,
  });
};
export const addApplicationList = (data, isBasicAppData) => {
  return service({
    url: isBasicAppData ? `${baseUrl}/importBasicAppData ` : `${baseUrl}/importData`,
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};
export const updateContact = (data) => {
  return service({
    url: `${baseUrl}/update`,
    method: 'post',
    data,
  });
};
export const deleteContact = (params) => {
  return service({
    url: `${baseUrl}/delete`,
    method: 'DELETE',
    params,
  });
};
export const getTravelInformationCol = () => {
  return service({
    url: `${travelInformation}/column_query`,
    method: 'get',
  });
};
export const addTravelInformation = (data) => {
  return service({
    url: `${travelInformation}/add`,
    method: 'post',
    data,
  });
};
export const getTravelInformation = (data) => {
  return service({
    url: `${travelInformation}/query`,
    method: 'post',
    data,
  });
};
export const updateTravelInformation = (data) => {
  return service({
    url: `${travelInformation}/update`,
    method: 'post',
    data,
  });
};
export const deleteTravelInformation = (params) => {
  return service({
    url: `${travelInformation}/delete`,
    method: 'delete',
    params,
  });
};
export const getTimeDimensionChart = (data, isMouth) => {
  return service({
    url: `${travelInformation}/${isMouth ? 'month_statistic' : 'year_statistic'}`,
    method: 'post',
    data,
  });
};

export const getProvinceDimensionChart = (data, isMouth) => {
  return service({
    url: `${travelInformation}/${isMouth ? 'province_month_statistic' : 'province_year_statistic'}`,
    method: 'post',
    data,
  });
};
export const travelApplicationAdd = (data) => {
  return service({
    url: '/travel_application/add',
    method: 'post',
    data,
  });
};
export const travelApplicationDelete = (data) => {
  return service({
    url: '/travel_application/delete',
    method: 'delete',
    params: data,
  });
};
export const travelApplicationUpdate = (data) => {
  return service({
    url: '/travel_application/update',
    method: 'post',
    data,
  });
};
export const travelApplicationUpdateStatus = (data) => {
  return service({
    url: '/travel_application/updateStatus',
    method: 'post',
    data,
  });
};
export const travelApplicationQuery = (data) => {
  return service({
    url: '/travel_application/query',
    method: 'post',
    params: data,
  });
};
