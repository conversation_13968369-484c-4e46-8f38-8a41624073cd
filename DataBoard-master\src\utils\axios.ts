import axios from 'axios';
import { useUserStore } from '@/store/modules/user';
import { isDevMode } from '@/utils/env';
import dayjs from 'dayjs';
import { storage } from '@/utils/Storage';
import { TABS_ROUTES } from '@/store/mutation-types';
import router from '@/router';

// 创建axios实例
const service = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API || '/board', // api的base_url
  timeout: 180000, // 请求超时时间
  withCredentials: false,
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 可以在这里添加请求头等信息
    // 例如：config.headers['Authorization'] = 'Bearer your-token';
    const userStore = useUserStore();
    if (isDevMode()) {
      const session = storage.get('session_id_board');
      const time = new Date().getTime() - (session?.createTime || 0);
      if (session?.value && time < 7200 * 1000) {
        config.headers['session_id'] = session?.value;
      } else if (!config.url?.endsWith('/user/login')) {
        userStore.logout();
        // 移除标签页
        localStorage.removeItem(TABS_ROUTES);
        router.replace({
          name: 'Login',
        });
      }
      config.headers['env'] = 'dev';
    } else {
      config.headers['env'] = '';
    }
    return config;
  },
  (error) => {
    // 请求错误处理
    Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    // 处理下载接口直接返回的数据
    if (response.status == 200 && response.data instanceof Blob) {
      const blob = new Blob([response.data], {
        type: 'application/vnd.ms-excel',
      });
      // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
      const url = window.URL.createObjectURL(blob); // 3.创建一个临时的url指向blob对象

      // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
      const a = document.createElement('a');
      a.href = url;
      const fileName = response.headers['content-disposition'].split('filename=')[1] || '模板.xlsx';
      a.download = decodeURIComponent(fileName);
      a.click();
      // 5.释放这个临时的对象url
      window.URL.revokeObjectURL(url);
      return true;
    }

    const res = response.data;
    // 如果返回的状态码为200，说明成功，可以直接返回数据
    if (res.status == 200 || res.code == 200) {
      if (response?.headers?.session_id) {
        const session_id = { value: response.headers.session_id, createTime: new Date().getTime() };
        storage.set('session_id_board', session_id);
      }
      return res;
    } else if (res.status === 40001) {
    } else {
      // 其他状态码都当作错误处理
      // 可以在这里对不同的错误码进行不同处理
      return Promise.reject({
        message: res.msg || 'Error',
        status: res.status ?? res.code,
      });
    }
  },
  (error) => {
    // 对���应错误做处理
    if (error.response.data === 'please login') {
      useUserStore().logout();
    }
    console.log('err' + error); // for debug
    return Promise.reject(error);
  }
);

export const adminService = axios.create({
  baseURL: '/admin', // api的base_url
  // timeout: 5000, // 请求超时时间
});

// 为 adminService 添加响应拦截器
adminService.interceptors.response.use(
  (response) => {
    const res = response.data;
    // 如果返回的状态码为200，说明接口请求成功，可以正常拿到数据
    if (res.code === 200) {
      return res.data;
    } else {
      // 其他状态码则判断为错误
      return Promise.reject({
        message: res.msg || 'Error',
        code: res.code,
      });
    }
  },
  (error) => {
    console.error('请求错误', error);
    return Promise.reject({
      message: error.response?.data?.message || '服务器错误',
      existingOrderIds: error.response?.data?.existingOrderIds || '',
      code: error.response?.status || 500,
    });
  }
);
export default service;
// ------------------------EWP---------------------------
// ewp axios实例
export const ewpService = axios.create({
  baseURL: '/ewp', // api的base_url
  // timeout: 5000, // 请求超时时间
});

// 请求拦截器
ewpService.interceptors.request.use(
  (config) => {
    // 可以在这里添加请求头等信息
    config.headers['env'] = import.meta.env.MODE === 'development' ? 'dev' : '';
    // 例如：config.headers['Authorization'] = 'Bearer your-token';
    return config;
  },
  (error) => {
    // 请求错误处理
    Promise.reject(error);
  }
);

// 响应拦截器
ewpService.interceptors.response.use(
  (response) => {
    // 对响应数据做处理，例如只返回data部分
    const res = response.data;
    // 如果返回的状态码为200，说明成功，可以直接返回数据
    // 处理下载接口直接返回的数据
    if (response.status == 200 && response.data instanceof Blob) {
      const blob = new Blob([response.data], {
        type: 'application/vnd.ms-excel',
      });
      // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
      const url = window.URL.createObjectURL(blob); // 3.创建一个临时的url指向blob对象

      // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
      const a = document.createElement('a');
      a.href = url;
      a.download = '模板.xlsx';
      const responseURL = response.request.responseURL;
      if (responseURL.indexOf('/board/download') > -1) {
        const arr = responseURL.split('/');
        a.download = decodeURI(arr[arr.length - 1]);
      }
      a.click();
      // 5.释放这个临时的对象url
      window.URL.revokeObjectURL(url);
      return true;
    }

    if (res.code === 200) {
      // console.log('res.data :>> ', res.data);

      return res.data;
    } else if (res.code === 40001) {
    } else if (response?.status === 200 && response.config.url?.includes('submitIssue')) {
      console.log(`response?.status`, response?.status);
      return res;
    } else {
      // 其他状态码都当作错误处理
      // 可以在这里对不同的错误码进行不同处理
      return Promise.reject({
        message: res.msg || 'Error',
        status: res.code,
      });
    }
  },
  (error) => {
    if (error.response.status == 401) {
      window.location.href = `http://dtse.cbg.huawei.com/ewp/management/oauth/accessCode?redirect=${window.location.href}`;
    }
    console.log(error);
    // 对���应错误做处理
    console.log('err' + error); // for debug
    return Promise.reject(error);
  }
);

// 知识平台 axios实例
export const knowledgeService = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_KNOWLEDGE_API, // api的base_url
  // timeout: 5000, // 请求超时时间
});

// 请求拦截器  用不了
knowledgeService.interceptors.request.use(
  async (config) => {
    // 可以在这里添加请求头等信息
    // 例如：config.headers['Authorization'] = 'Bearer your-token';
    const session = storage.getCookie('session_id_knowledge');
    if (!session) {
      // 获取知识库那边的sessionid
      const userInfo = useUserStore().getUserInfo; // 用户号account  用户名userName
      let knowledgeLoginRes = { data: { sessionId: '' }, code: '' };
      try {
        knowledgeLoginRes = await service({
          url: '/knowledgeBaseManage/login',
          method: 'post',
          data: {
            userNo: userInfo.account,
            userName: userInfo.userName,
          },
        });
      } catch (error) {
        Error();
      }
      if (knowledgeLoginRes.code === '200') {
        // 设置2小时后过期
        storage.setCookie('session_id_knowledge', knowledgeLoginRes.data.sessionId, 2 * 60 * 60);
        config.headers['sessionid'] = knowledgeLoginRes.data.sessionId;
      }
    } else {
      config.headers['sessionid'] = session;
    }
    return config;
  },
  (error) => {
    // 请求错误处理
    Promise.reject(error);
  }
);

// 响应拦截器
knowledgeService.interceptors.response.use(
  (response) => {
    // 对响应数据做处理，例如只返回data部分
    const res = response.data;
    // 如果返回的状态码为200，说明成功，可以直接返回数据
    // 处理下载接口直接返回的数据
    if (response.status == 200 && response.data instanceof Blob) {
      const blob = new Blob([response.data], {
        type: 'application/vnd.ms-excel',
      });
      // 2.获取请求返回的response对象中的blob 设置文件类型，这里以excel为例
      const url = window.URL.createObjectURL(blob); // 3.创建一个临时的url指向blob对象

      // 4.创建url之后可以模拟对此文件对象的一系列操作，例如：预览、下载
      const a = document.createElement('a');
      a.href = url;
      a.download = '模板.xlsx';
      const responseURL = response.request.responseURL;
      if (responseURL.indexOf('/board/download') > -1) {
        const arr = responseURL.split('/');
        a.download = decodeURI(arr[arr.length - 1]);
      }
      a.click();
      // 5.释放这个临时的对象url
      window.URL.revokeObjectURL(url);
      return true;
    }

    if (res.code == 200) {
      // console.log('res.data :>> ', res.data);

      return res.data;
    } else if (res.code === 40001) {
    } else {
      // 其他状态码都当作错误处理
      // 可以在这里对不同的错误码进行不同处理
      return Promise.reject({
        message: res.msg || 'Error',
        status: res.code,
      });
    }
  },
  (error) => {
    // alert('请先登录');
    return Promise.reject(error);
  }
);
