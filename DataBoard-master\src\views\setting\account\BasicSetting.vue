<template>
  <n-descriptions :style="{minHeight: 'calc(100vh - 80px - 4rem)'}" label-placement="top" title="基本信息" size="medium" :column="3">
    <n-descriptions-item label="姓名">
      {{ userInfo?.userName || '-' }}
    </n-descriptions-item>
    <n-descriptions-item label="工号">
      {{ userInfo?.account || '-' }}
    </n-descriptions-item>
    <n-descriptions-item label="角色">
      {{ getRolesLabel(userInfo?.roles) }}
    </n-descriptions-item>
    <n-descriptions-item label="领域">
      {{ userInfo?.modules?.join('、') || '-' }}
    </n-descriptions-item>
    <n-descriptions-item label="技能">
      {{ userInfo?.personalSkills || '-' }}
    </n-descriptions-item>
    <n-descriptions-item label="职责">
      {{ '-' }}
    </n-descriptions-item>
  </n-descriptions>
  <n-loading-bar-provider>
    <content />
  </n-loading-bar-provider>
  <n-modal v-model:show="showModal">
      <n-card
        style="width: 600px"
        title="修改密码"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <template #header-extra> </template>
        <n-form
          ref="formRef"
          :model="model"
          :rules="rules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="medium"
          :style="{
            maxWidth: '640px',
          }"
        >
          <n-form-item  ref="oldPasswordRef" label="旧密码" path="oldPassword">
            <n-input type='password'show-password-on="mousedown" :maxlength="32" v-model:value="model.oldPassword" placeholder="请输入旧密码" />
          </n-form-item>
          <n-form-item  ref="newPasswordRef" label="新密码" path="newPassword">
            <n-input type='password' @blur="onNewPasswordBlur" show-password-on="mousedown" :maxlength="32" v-model:value="model.newPassword" placeholder="请输入新密码" />
          </n-form-item>
          <n-form-item ref="newPasswordCheckRef" label="确认新密码" path="newPasswordCheck">
            <n-input type='password' @blur="onNewPasswordCheckBlur" show-password-on="mousedown" :maxlength="32" v-model:value="model.newPasswordCheck" placeholder="请输入新密码" />
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" :loading="loading" @click="formSubmit"> 确认 </n-button>
            <n-button secondary strong type="error" @click="closeModal"> 取消 </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
<!--  <n-grid cols="2 s:2 m:2 l:3 xl:3 2xl:3" responsive="screen">-->
<!--    <n-grid-item>-->
<!--      <n-form :label-width="80" :model="formValue" :rules="rules" ref="formRef">-->
<!--        <n-form-item label="昵称" path="name">-->
<!--          <n-input v-model:value="formValue.name" placeholder="请输入昵称" />-->
<!--        </n-form-item>-->

<!--        <n-form-item label="邮箱" path="email">-->
<!--          <n-input placeholder="请输入邮箱" v-model:value="formValue.email" />-->
<!--        </n-form-item>-->

<!--        <n-form-item label="联系电话" path="mobile">-->
<!--          <n-input placeholder="请输入联系电话" v-model:value="formValue.mobile" />-->
<!--        </n-form-item>-->

<!--        <n-form-item label="联系地址" path="address">-->
<!--          <n-input v-model:value="formValue.address" type="textarea" placeholder="请输入联系地址" />-->
<!--        </n-form-item>-->

<!--        <div>-->
<!--          <n-space>-->
<!--            <n-button type="primary" @click="formSubmit">更新基本信息</n-button>-->
<!--          </n-space>-->
<!--        </div>-->
<!--      </n-form>-->
<!--    </n-grid-item>-->
<!--  </n-grid>-->
</template>
<script lang="ts" setup>
  import { reactive,onMounted, ref } from 'vue';
  import { FormItemInst, FormItemRule, useMessage } from 'naive-ui';
  import service from '@/utils/axios';
  import { UserVo, useUser } from '@/store/modules/user';
  import { UserRoleEnum, UserRoleCNameEnum } from "@/enums/UserRoleEnum";
import roleManager from '@/views/RoleManagement/rolesMap';
  let showModal = ref(false);
  let loading = ref(false);
  const oldPasswordRef = ref<FormItemInst | null>(null)
  const newPasswordRef = ref<FormItemInst | null>(null)
  const newPasswordCheckRef = ref<FormItemInst | null>(null)
  const reg = /[^\x00-\xff]|\s|\r|\n|\r\n/g
  let model = ref({
    oldPassword: '',
    newPassword: '',
    newPasswordCheck: '',
  });
  let userInfo = ref<UserVo>({
    userName: "",
    account: "10002",
    password: "123456",
    modules: [],
    roles: [],
    personalSkills: null
  })

  function initUserInfo() {
    const userStore = useUser();
    userInfo.value = userStore.getUserInfo
  }

  function getRolesLabel(roles: string[]): string {
    if (!roles?.length) {
      return '';
    }
    
    const rolesNameMap = roleManager.map
    return roles.map(item => rolesNameMap[item]).filter(item => !!item).join('，')
  }

  onMounted(()=>{
    initUserInfo()
  })

  function validator(rule: FormItemRule, value: string, type: 'oldPassword'|'newPassword'|'newPasswordCheck') {
    if (reg.test(value)) {
      return new Error('包含非法字符')
    } if (!value) {
      return new Error('输入不能为空')
    } else if (value.length < 6 && type !== 'oldPassword') {
      return new Error('密码长度不得少于6位')
    } else if (model.value.newPassword !== model.value.newPasswordCheck && ['typenewPassword','newPasswordCheck'].includes(type)) {
      return new Error('两次输入的密码不一致')
    } else 
    return true
  }

  function onNewPasswordBlur(value) {
    if (model.value.newPassword.length >= 6 && model.value.newPasswordCheck.length >= 6 && model.value.newPassword === model.value.newPasswordCheck) {
      newPasswordCheckRef.value.validate({tigger: 'password-input'})
    }
  }

  function onNewPasswordCheckBlur(value) {
    if (model.value.newPassword.length >= 6 && model.value.newPasswordCheck.length >= 6 && model.value.newPassword === model.value.newPasswordCheck) {
      newPasswordRef.value.validate({tigger: 'password-input'})
    }
  }

  const rules = {
    oldPassword: {
      required: true,
      trigger: ['blur'],
      validator(rule: FormItemRule, value: string) {
        return validator(rule, value, 'oldPassword');
      },
    },
    newPassword: {
      required: true,
      trigger: ['password-input','blur'],
      validator(rule: FormItemRule, value: string) {
        return validator(rule, value, 'newPassword');
      },
    },
    newPasswordCheck: {
      required: true,
      trigger: ['password-input', 'blur'],
      validator(rule: FormItemRule, value: string) {
        return validator(rule, value, 'newPasswordCheck');
      },
    },
    name: {
      required: true,
      message: '请输入昵称',
      trigger: 'blur',
    },
    email: {
      required: true,
      message: '请输入邮箱',
      trigger: 'blur',
    },
    mobile: {
      required: true,
      message: '请输入联系电话',
      trigger: 'input',
    },
  };
  const formRef: any = ref(null);
  const message = useMessage();
  const formValue = reactive({
    name: '',
    mobile: '',
    email: '',
    address: '',
  });
  function closeModal() {
    showModal.value = false;
    setTimeout(()=>{
      model.value.oldPassword = '';
      model.value.newPassword = '';
      model.value.newPasswordCheck = '';
    }, 200)
  }

  function formSubmit() {
    if (userInfo.value.account === 'anonymous') {
      alert('浏览账号禁止修改密码！')
      return
    }
    loading.value = true;
    // formRef.value.validate((errors) => {
    //   if (!errors) {
    //     message.success('验证成功');
    //   } else {
    //     message.error('验证失败，请填写完整信息');
    //   }
    // });
    formRef.value.validate(async (errors) => {
      if (!errors) {
        try {
          let res = await service.post('/user/modifyPassword', {
            account: userInfo.value.account,
            oldPassword: model.value.oldPassword,
            password: model.value.newPassword
          })
          message.success(res.data ? '修改成功' : '未知错误');
          res.data && closeModal()
        } catch (err) {
          message.error('出错了，请稍后再试');
        }
      }
    });
  }
</script>
