<template>
  <n-card>
    <template #header>
      <n-space justify="space-between">
        <n-space>
          <n-button type="primary" @click="handleAdd" v-if="isManageRef">
            <template #icon>
              <n-icon>
                <PersonAddOutline />
              </n-icon>
            </template>
            新增
          </n-button>
          <n-button @click="handleRefresh">
            <template #icon>
              <n-icon>
                <Refresh />
              </n-icon>
            </template>
            刷新
          </n-button>
        </n-space>
        <n-space align="center">
          <n-input
            v-model:value="searchText"
            placeholder="请输入分组名/地区"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #suffix>
              <n-icon :component="Search" />
            </template>
          </n-input>
          <n-select
            style="width: 200px"
            placeholder="请选择预约医生"
            :options="dtsHandleEmployeeOptions"
            v-model:value="searchEmployee"
            filterable
            clearable
            @update:value="handleSearch"
          >
          </n-select>
        </n-space>
      </n-space>
    </template>

    <n-data-table
      remote
      :columns="columns"
      :data="tableData"
      :loading="loading"
      :pagination="paginationReactive"
      @update:page="handlePageChange"
      @update:page-size="handlePageSizeChange"
    />

    <!-- 新增/编辑弹窗 -->
    <n-modal v-model:show="showModal" :title="modalTitle" preset="card" :style="{ width: '600px' }">
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        label-width="100"
        require-mark-placement="right-hanging"
      >
        <n-form-item label="名称" path="name">
          <n-input v-model:value="formData.groupName" placeholder="请输入姓名" />
        </n-form-item>
        <n-form-item label="省份" path="represent">
          <n-dynamic-tags v-model:value="formData.represent" />
        </n-form-item>
        <n-form-item label="人员" path="employee">
          <n-select
            filterable
            clearable
            multiple
            :options="dtsHandleEmployeeOptions"
            v-model:value="formData.employeeName" />
        </n-form-item>
      </n-form>
      <template #footer>
        <n-space justify="end">
          <n-button @click="showModal = false">取消</n-button>
          <n-button type="primary" :loading="submitLoading" @click="handleSubmit">确定</n-button>
        </n-space>
      </template>
    </n-modal>

  </n-card>
</template>

<script setup lang="ts">
  import { h, ref, onMounted, computed, reactive } from 'vue';
  import {
    useMessage,
    useDialog,
    type PaginationProps,
    NInput,
    NModal,
    NFormItem,
    NSpace,
    NButton,
    NForm, NDynamicTags, NSelect,
  } from 'naive-ui';
  import {
    Search,
    PersonAddOutline,
    Refresh,
  } from '@vicons/ionicons5';

  import {
    addRep,
    deleteRep,
    getRep,
  } from './representEmployee';

  const rules = {
    groupName: {
      required: true,
      message: '请输入名称',
      trigger: ['blur', 'input'],
    }
  };

  import { useUserStore } from '@/store/modules/user';
  import {
    getPersonNamesFromTag,
    getPersonOptionsOnlyName
  } from '@/views/dataview/personManage/staffCommonUtils';
  import {DTS_HANDLE_TAG, MANAGE_TAG} from '@/views/dataview/personManage/tagContant';

  //人员权限相关
  const userStore = useUserStore();
  const userInfo = userStore.getUserInfo || {};
  const isManageRef = ref(false);
  //判断人员是否为管理员
  const judgeManagePerson = async () => {
    const list = await getPersonNamesFromTag(MANAGE_TAG);
    isManageRef.value = list.includes(userInfo.userName);
  };

  const isEditing = ref(false);

  const message = useMessage();
  const dialog = useDialog();

  // 表格相关
  const loading = ref(false);
  const tableData = ref<any[]>([]);
  const dtsHandleEmployeeOptions = ref([]);
  const paginationReactive = reactive<PaginationProps>({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 50, 100],
    prefix({ itemCount }) {
      return `Total：${itemCount}`;
    },
    onChange: (page: number) => {
      paginationReactive.page = page;
      fetchData();
    },
    onUpdatePageSize: (pageSize: number) => {
      paginationReactive.pageSize = pageSize;
      paginationReactive.page = 1;
      fetchData();
    },
  });

  const formRef = ref(null);

  const formData = ref({
    groupName: '',
    represent:[],
    employeeName: []
  });

  const columns = ref([]);

  const createColumns = (appName: string) => {
    // 表格列定义
    const columnOrigin = [
      {
        title: '名称',
        key: 'groupName',
        width: 200,
        show: true,
      },
      {
        title: '省份',
        key: 'represent',
        width: 600,
        show: true,
        render(row){
          let arr = []
          if(row.represent){
            arr = row.represent.split(',');
          }
          row.representArr = arr;
          return h(NDynamicTags,{
            value:row.representArr,
            disabled:!isManageRef.value,
            onUpdateValue:async (value:string[])=>{
              try {
                const param = {
                  id: row.id,
                  groupName: row.groupName,
                  represent: value.join(','),
                  employeeName: row.employeeName,
                };
                await addRep(param);
                await fetchData();
                message.success("修改成功");
              }catch (err){
                console.log(err);
                message.error("修改失败");
              }
            }
          })
        }
      },
      {
        title: '人员',
        key: 'employeeName',
        show: true,
        render(row){
          let arr = []
          if(row.employeeName){
            arr = row.employeeName.split(',');
          }
          row.employeeNameArr = arr;
          return h(NSelect,{
            value:row.employeeNameArr,
            clearable: true,
            filterable: true,
            disabled:!isManageRef.value,
            options: dtsHandleEmployeeOptions.value,
            multiple: true,
            onUpdateValue:async (value:string[])=>{
              try {
                const param = {
                  id: row.id,
                  groupName: row.groupName,
                  represent: row.represent,
                  employeeName: value.join(','),
                };
                await addRep(param);
                await fetchData();
                message.success("修改成功");
              }catch (err){
                console.log(err);
                message.error("修改失败");
              }
            }
          })
        }
      },
      {
        title: '操作',
        key: 'actions',
        width: 100,
        show: isManageRef.value,
        render(row) {
          return h('div', [
            h(
              'a',
              {
                style: {
                  marginRight: '10px',
                  cursor: 'pointer',
                },
                onClick: () => handleEdit(row),
              },
              '编辑'
            ),
            h(
              'a',
              {
                style: {
                  color: '#d03050',
                  marginRight: '10px',
                  cursor: 'pointer',
                },
                onClick: () => handleDelete(row),
              },
              '删除'
            ),
          ]);
        },
      },
    ];
    columns.value = columnOrigin.filter((item) => item.show);
  };

  const searchText = ref('');
  const searchEmployee = ref('');

  //查询组
  const fetchData = async () => {
    loading.value = true;
    try {
      // TODO: 调用后端API获取数据
      const res = await getRep({
        pageNo: paginationReactive.page,
        pageSize: paginationReactive.pageSize,
        groupNameOrRepresent: searchText.value,
        employeeName: searchEmployee.value?searchEmployee.value:'',
      });
      tableData.value = res.records;
      paginationReactive.itemCount = res.total;
    } catch (error) {
      message.error('获取数据失败');
    } finally {
      loading.value = false;
    }
  };

  const handlePageChange = (page: number) => {
    paginationReactive.page = page;
    fetchData();
  };

  const handlePageSizeChange = (pageSize: number) => {
    paginationReactive.pageSize = pageSize;
    paginationReactive.page = 1;
    fetchData();
  };

  const handleSearch = () => {
    paginationReactive.page = 1;
    fetchData();
  };

  const handleRefresh = () => {
    searchText.value = '';
    fetchData();
  };


  const resetForm = () => {
    formData.value = {
      id: null,
      name: '',
      represent:[]
    };
  };

  const modalTitle = ref('');
  const showModal = ref(false);

  const handleAdd = () => {
    modalTitle.value = '新增分组';
    resetForm();
    isEditing.value = false;
    showModal.value = true;
  };

  const handleEdit = (row) => {
    modalTitle.value = '编辑分组';
    isEditing.value = true;
    formData.value = {
      id: row.id,
      groupName: row.groupName,
      represent: row.representArr,
      employeeName: row.employeeNameArr
    };
    // formData.value = {...row};
    showModal.value = true;
  };

  const handleDelete = async (row) => {
    try {
      await dialog.warning({
        title: '确认删除',
        content: '确定要删除该人员吗？',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: async () => {
          // TODO: 调用后端API删除数据
          await deleteRep(row.id);
          message.success('删除成功');
          fetchData();
        },
      });
    } catch (error) {
      message.error('删除失败');
    }
  };

  const submitLoading = ref(false);

  const handleSubmit = async () => {
    try {
      await formRef.value?.validate();
      submitLoading.value = true;
      const param = {
        id: formData.value.id,
        groupName: formData.value.groupName,
        represent: formData.value.represent.join(','),
        employeeName: formData.value.employeeName.join(',')
      };
      await addRep(param);
      message.success(formData.value.id ? '修改成功' : '新增成功');
      showModal.value = false;
      await fetchData();
    } catch (error) {
      console.error('表单校验失败:', error);
      message.error(formData.value.id ? '修改失败' : '新增失败');
    } finally {
      submitLoading.value = false;
    }
  };

  onMounted(() => {
    judgeManagePerson().then(() => {
      createColumns();
    });
    getPersonOptionsOnlyName(DTS_HANDLE_TAG).then(res =>{
      dtsHandleEmployeeOptions.value = res;
    });
    fetchData();
  });
</script>

<style scoped>
  .n-card {
    margin: 16px;
  }
</style>
