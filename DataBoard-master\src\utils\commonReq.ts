import {ewpService as service} from "@/utils/axios";

export async function getUsernameFromAppName(appName: string): Promise<any> {
  try {
    const params = {
      pageNo: 1,
      pageSize: 10,
      appName: appName,
      sortField: 'riskScore',
      sortOrder: 'desc',
      riskScoreGe: 0,
    };
    const response = await service.post('/management/appInfo/query', params);
    if (response.records.length > 0) {
      return response.records[0].ewpOwner;
    }
    return '';
  } catch (error) {
    console.error(error);
    return '';
  }
}
