<template>
  <div>
    <n-card>
      <n-space>
        <n-button secondary strong type="warning" @click="search()">
          <template #icon>
            <n-icon>
              <Refresh />
            </n-icon>
          </template>
          刷新
        </n-button>
        <n-button secondary strong type="primary" @click="showListAddModal = true"> 导入 </n-button>
      </n-space>
      <n-data-table
        remote
        :columns="columns"
        :data="data"
        :pagination="pagination"
        :loading="loading"
        style="margin-top: 20px"
        scroll-x
      />
    </n-card>
    <n-modal v-model:show="showListAddModal">
      <n-card
        style="width: 600px"
        title="批量导入"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-upload
          action="#"
          :custom-request="customRequest"
          :multiple="true"
          :default-file-list="fileList"
          accept=".xls,.xlsx,"
          :max="1"
          directory-dnd
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <Add />
              </n-icon>
            </div>
            <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
          </n-upload-dragger>
        </n-upload>
      </n-card>
    </n-modal>
  </div>
</template>
<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { Add, Refresh } from '@vicons/ionicons5';
  import { useMessage } from 'naive-ui';
  import { paginationReactive, orderQuery, orderImport } from './index';
  import { debounce } from 'lodash-es';
  import dayjs from 'dayjs';

  const showListAddModal = ref(false);
  const fileList = ref([]);
  const loading = ref(false);
  const message = useMessage();
  const sampleDevidesUsageRegistrationColumn = [
    {
      title: '工单ID',
      key: 'irNumber',
      width: 160,
      fixed: 'left',
    },
    {
      title: '标题',
      key: 'title',
      width: 200,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: 'IR单问题类型',
      key: 'irType',
      width: 120,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: 'IR单状态',
      key: 'irStatus',
      resizable: true,
      width: 120,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '严重级别',
      key: 'level',
      resizable: true,
      width: 120,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '问题分类',
      key: 'category',
      resizable: true,
      width: 120,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '项目归属维度',
      key: 'dimension',
      resizable: true,
      width: 120,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
    {
      title: '创建时间',
      key: 'createTime',
      width: 200,
      resizable: true,
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
      render(row) {
        if (row.createTime) {
          return dayjs(row.createTime).format('YYYY-MM-DD HH:mm:ss');
        }
      },
    },
    {
      title: '项目名称',
      key: 'appName',
      width: 200,
      resizable: true,
      fixed: 'right',
      ellipsis: {
        tooltip: {
          contentStyle: 'max-width: 400px;max-height: 300px;overflow-y: auto',
        },
      },
    },
  ];
  const columns = ref(sampleDevidesUsageRegistrationColumn);
  paginationReactive.onChange = (page: number) => {
    paginationReactive.page = page;
    search();
  };
  paginationReactive.onUpdatePageSize = (pageSize: number) => {
    paginationReactive.pageSize = pageSize;
    paginationReactive.page = 1;
    search();
  };
  const pagination = ref(paginationReactive);
  const data = ref([]);

  // 查询表格数据
  const search = debounce(async () => {
    loading.value = true;
    try {
      let res = await orderQuery({
        pageNumber: pagination.value.page,
        pageSize: pagination.value.pageSize,
      });
      if (res.status === '200') {
        data.value = res?.data?.data || [];
        paginationReactive.itemCount = res?.data?.pageInfo?.total || 0;
        paginationReactive.pageCount =
          Math.ceil(res?.data?.pageInfo?.total / pagination.value.pageSize) || 0;
      }
    } catch (e) {}
    loading.value = false;
  }, 300);

  //上传
  const customRequest = async ({ file, onError }: UploadCustomRequestOptions) => {
    const formData = new FormData();
    formData.append('file', file.file as File);
    //上传接口
    try {
      let res = await orderImport(formData);
      if (res.status == '200') {
        showListAddModal.value = false;
        search();
        message.success('导入成功');
      } else {
        fileList.value = [];
      }
    } catch (err) {
      fileList.value = [];
      onError();
    }
  };
  onMounted(() => {
    search();
  });
</script>
