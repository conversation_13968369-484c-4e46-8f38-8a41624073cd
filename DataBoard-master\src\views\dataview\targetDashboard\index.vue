<template>
  <div class="container-box">
    <!-- 表格部分 -->
    <div class="table-container">
      <n-space justify="space-between" style="margin-bottom: 10px">
        <n-space style="font-size: 17px">全量SLA状态</n-space>
        <n-space>
          <n-date-picker
            clearable
            v-model:value="slaTime"
            type="daterange"
            :is-date-disabled="disablePreviousDate"
            @confirm="onConfirm"
            @clear="clearTime"
          />
          <n-button type="info" @click="handleExportSla" :loading="exportLoading"> 导出 </n-button>
        </n-space>
      </n-space>
      <n-data-table
        :loading="slaLoadingRef"
        :columns="slaColumnsRef"
        :data="form"
        :bordered="false"
        :pagination="false"
        :scroll-x="1000"
        :single-line="false"
        striped
      />
    </div>
    <div class="table-container">
      <n-space justify="space-between" style="margin-bottom: 10px">
        <n-space style="font-size: 17px">问题定界情况全局视图</n-space>
        <n-space>
          <n-date-picker
            clearable
            v-model:value="totalTime"
            type="daterange"
            :is-date-disabled="disablePreviousDate"
            @confirm="onConfirmTotal"
            @clear="clearTimeTotal"
          />
          <n-button type="info" @click="handleExportTotalStatic" :loading="exportTotalLoading" >
            导出
          </n-button>
        </n-space>
      </n-space>
      <n-data-table
        :loading="totalLoadingRef"
        :columns="allColumnsRef"
        :data="totalRef"
        :bordered="false"
        :pagination="false"
        :scroll-x="2100"
        :single-line="false"
        striped
      />
    </div>
    <div class="table-container">
      <n-space justify="space-between" style="margin-bottom: 10px">
        <n-space style="font-size: 17px">每日新增&定界数据</n-space>
        <n-space>
          <n-date-picker
            clearable
            v-model:value="dailyTime"
            type="date"
            :is-date-disabled="disablePreviousDate"
          />
          <n-button type="info"  @click="exportDailyHandleStatic" :loading="exportDailyLoading" >
            导出
          </n-button>
        </n-space>
      </n-space>
      <n-data-table
        :loading="dailyLoadingRef"
        :columns="dailyColumnsRef"
        :data="dailyRef"
        :bordered="false"
        :pagination="false"
        :scroll-x="2500"
        :single-line="false"
        striped
      />
    </div>
    <div class="table-container">
      <n-space justify="space-between" style="margin-bottom: 10px">
        <n-space style="font-size: 17px">问题感知</n-space>
        <n-space>
          <n-date-picker
            clearable
            v-model:value="questionTime"
            type="daterange"
            :is-date-disabled="disablePreviousDate"
            @confirm="onConfirmQuestion"
            @clear="clearTimeQuestion"
          />
          <n-button type="info" @click="handleExportQuestionStatic" :loading="exportQuestionLoading" >
            导出
          </n-button>
        </n-space>
      </n-space>
      <n-data-table
        :loading="questionLoadingRef"
        :columns="questionColumnsRef"
        :data="questionRef"
        :bordered="false"
        :pagination="false"
        :scroll-x="2500"
        :single-line="false"
        striped
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { filterObjectValues } from '@/utils';
  import { ewpService as service } from '@/utils/axios';
  import { NButton, NDataTable, NUpload } from 'naive-ui';
  import { ref, reactive, h, onMounted, watch } from 'vue';
  import {creatColumns} from "@/views/dataview/betaState/columns";

  const formRef = ref(null);

  const loadingRef = ref(true);
  const form = ref([]);

  const slaLoadingRef = ref(false);

  const totalRef = ref([]);

  const dailyRef = ref([]);

  const slaTime = ref([new Date('2025-01-01').getTime(), Date.now()]);

  const totalTime = ref([new Date('2025-01-01').getTime(), Date.now()]);

  const dailyTime = ref(Date.now());

  watch(
    () => dailyTime.value,
    () => {
      fetchDailyData();
    }
  );

  /**
 时间戳转换年月日
 */

  const timestampToDate = (timestamp: number): string => {
    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  };
  const disablePreviousDate = (ts: number) => {
    return ts > Date.now();
  };

  const onConfirm = (v: number | number[] | null) => {
    if (v) {
      slaTime.value = [v[0], v[1]];
    }
    fetchData();
  };

  const onConfirmTotal = (v: number | number[] | null) => {
    if (v) {
      totalTime.value = [v[0], v[1]];
    }
    fetchTotalData();
  };

  const clearTime = () => {
    slaTime.value = null;
    fetchData();
  };

  const clearTimeTotal = () => {
    totalTime.value = null;
    fetchTotalData();
  };

  const slaColumnsRef = ref([
    {
      title: '问题优先级',
      key: 'rowName',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '问题超期数',
      key: 'overDueCount',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '问题定界数',
      key: 'handledCount',
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '问题SLA达成率',
      key: 'slaPercent',
      ellipsis: {
        tooltip: true,
      },
    },
  ]);

  const allColumnsRef = ref([]);

  const totalLoadingRef = ref(false);

  const dailyColumnsRef = ref([]);

  const dailyLoadingRef = ref(false);

  const columnsCreate = name => {
    const baseColumns = [
      {
        title: '全量',
        key: 'total',
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '问题来源',
        key: 'source',
        children: [
          {
            title: 'Beta',
            key: 'beta',
            ellipsis: {
              tooltip: true,
            },
          },
          {
            title: 'FUT',
            key: 'fut',
            ellipsis: {
              tooltip: true,
            },
          },
          {
            title: 'NSS',
            key: 'nss',
            ellipsis: {
              tooltip: true,
            },
          },
          {
            title: 'AG',
            key: 'ag',
            ellipsis: {
              tooltip: true,
            },
          },
          {
            title: '服务',
            key: 'service',
            ellipsis: {
              tooltip: true,
            },
          },
          {
            title: '其他',
            key: 'other',
            ellipsis: {
              tooltip: true,
            },
          },
        ],
      },
      {
        title: '问题类型',
        key: 'type',
        children: [
          {
            title: 'UX体验',
            key: 'ux',
            ellipsis: {
              tooltip: true,
            },
          },
          {
            title: '功能故障',
            key: 'function',
            ellipsis: {
              tooltip: true,
            },
          },
          {
            title: '性能功耗',
            key: 'performance',
            ellipsis: {
              tooltip: true,
            },
          },
          {
            title: '兼容性',
            key: 'compatibility',
            ellipsis: {
              tooltip: true,
            },
          },
          {
            title: '稳定性',
            key: 'stability',
            ellipsis: {
              tooltip: true,
            },
          },
          {
            title: '安全隐私',
            key: 'security',
            ellipsis: {
              tooltip: true,
            },
          },
          {
            title: '非故障类',
            key: 'otherError',
            ellipsis: {
              tooltip: true,
            },
          },
        ],
      },
      {
        title: '优先级',
        key: 'priority',
        children: [
          {
            title: 'P0',
            key: 'p0',
            ellipsis: {
              tooltip: true,
            },
          },
          {
            title: 'P1',
            key: 'p1',
            ellipsis: {
              tooltip: true,
            },
          },
          {
            title: 'P2',
            key: 'p2',
            ellipsis: {
              tooltip: true,
            },
          },
        ],
      },
      {
        title: '机型',
        key: 'model',
        children: [
          {
            title: 'NEXT新机',
            key: 'newPhone',
            ellipsis: {
              tooltip: true,
            },
          },
          {
            title: 'NEXT升级',
            key: 'oldPhone',
            ellipsis: {
              tooltip: true,
            },
          },
          {
            title: '保密机型',
            key: 'securityPhone',
            ellipsis: {
              tooltip: true,
            },
          },
        ],
      },
    ];
    const everyDayColumnsHead = [
      {
        title: '日期',
        key: 'date',
        rowSpan: (rowData, rowIndex) => (rowData.date ? 2 : 1),
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '问题处理进展',
        key: 'process',
        ellipsis: {
          tooltip: true,
        },
      },
    ];
    const allColumnsHead = [
      {
        title: '',
        key: 'rowName',
        width: 150,
        ellipsis: {
          tooltip: true,
        },
      },
    ];
    const questionColumnHead = [
      {
        title: '',
        titleColSpan: 2,
        key: 'topRowName',
        width: 150,
        rowSpan: (rowData, rowIndex) => (rowIndex===0 ? 4 : 1),
        ellipsis: {
          tooltip: true,
        },
      },
      {
        title: '',
        key: 'rowName',
        width: 150,
        ellipsis: {
          tooltip: true,
        },
      },
    ]
    if(name==='all'){
      return [...allColumnsHead, ...baseColumns]
    }else if(name==='daily'){
      return [...everyDayColumnsHead, ...baseColumns];
    }else{
      return [...questionColumnHead,...baseColumns];
    }
  };
  allColumnsRef.value = columnsCreate('all');
  dailyColumnsRef.value = columnsCreate('daily')

  // 在组件挂载时获取数据
  onMounted(() => {
    columnsCreate();
    fetchData();
    fetchTotalData();
    fetchDailyData();
    fetchQuestionData();
  });

  const fetchData = async () => {
    slaLoadingRef.value = true;
    //根据slaTime获取时间
    let queryParams = {};
    if (slaTime.value) {
      const [startTime, endTime] = slaTime.value;
      queryParams = { startTime: timestampToDate(startTime)+" 00:00:00", endTime: timestampToDate(endTime)+" 23:59:59" };
    }
    console.log(`filterObjectValues(queryParams)`, filterObjectValues(queryParams));
    const response = await service.post('/management/overview/slaOverview', {
      ...filterObjectValues(queryParams),
    });
    if (response) {
      form.value = response;
      console.log(`response`, response);
      slaLoadingRef.value = false;
    }
  };

  const exportLoading = ref(false);

  const exportTotalLoading = ref(false);

  const exportDailyLoading = ref(false);

  const handleExportSla = () => {
    return new Promise((resolve, reject) => {
      const req = new XMLHttpRequest();
      exportLoading.value = true;
      let queryParams = {};
      if (slaTime.value) {
        const [startTime, endTime] = slaTime.value;
        queryParams = { startTime: timestampToDate(startTime)+" 00:00:00", endTime: timestampToDate(endTime)+" 23:59:59" };
      }
      req.open(
        'POST',
        `http://${window.location.host}/ewp/management/overview/exportSlaOverview`,
        true
      );
      req.responseType = 'blob';
      req.setRequestHeader('Content-Type', 'application/json');
      req.onload = function () {
        const data = req.response;
        const blob = new Blob([data]);
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = 'SLA全量状态' + timestampToDate(Date.now()) + '.xls';
        a.href = blobUrl;
        a.click();
        exportLoading.value = false;
        resolve(true);
      };
      req.send(JSON.stringify(queryParams));
    });
  };

  const handleExportTotalStatic = () => {
    return new Promise((resolve, reject) => {
      const req = new XMLHttpRequest();
      exportTotalLoading.value = true;
      let queryParams = {};
      if (totalTime.value) {
        const [startTime, endTime] = totalTime.value;
        queryParams = { startTime: timestampToDate(startTime)+" 00:00:00", endTime: timestampToDate(endTime)+" 23:59:59"};
      }
      req.open(
        'POST',
        `http://${window.location.host}/ewp/management/overview/exportTotalHandleStatic`,
        true
      );
      req.responseType = 'blob';
      req.setRequestHeader('Content-Type', 'application/json');
      req.onload = function () {
        const data = req.response;
        const blob = new Blob([data]);
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = '问题定界情况全局视图' + timestampToDate(Date.now()) + '.xls';
        a.href = blobUrl;
        a.click();
        exportTotalLoading.value = false;
        resolve(true);
      };
      req.send(JSON.stringify(queryParams));
    });
  };

  const exportDailyHandleStatic = () => {
    return new Promise((resolve, reject) => {
      const req = new XMLHttpRequest();
      exportDailyLoading.value = true;
      let queryParams = {};
      if (dailyTime.value) {
        const endTime = dailyTime.value;
        queryParams = {
          startTime: timestampToDate(endTime - 13 * 24 * 60 * 60 * 1000)+" 00:00:00",
          endTime: timestampToDate(endTime)+ " 23:59:59",
        };
      }
      req.open(
        'POST',
        `http://${window.location.host}/ewp/management/overview/exportDailyHandleStatic`,
        true
      );
      req.responseType = 'blob';
      req.setRequestHeader('Content-Type', 'application/json');
      req.onload = function () {
        const data = req.response;
        const blob = new Blob([data]);
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = '每日新增&定界数据' + timestampToDate(Date.now()) + '.xls';
        a.href = blobUrl;
        a.click();
        exportDailyLoading.value = false;
        resolve(true);
      };
      req.send(JSON.stringify(queryParams));
    });
  };

  const fetchTotalData = async () => {
    totalLoadingRef.value = true;
    let queryParams = {};
    if (totalTime.value) {
      const [startTime, endTime] = totalTime.value;
      queryParams = { startTime: timestampToDate(startTime)+ " 00:00:00", endTime: timestampToDate(endTime)+" 23:59:59" };
    }
    console.log(`filterObjectValues(DTSQueryParams)`, filterObjectValues(queryParams));
    const response = await service.post('/management/overview/totalHandleStatic', {
      ...filterObjectValues(queryParams),
    });
    if (response) {
      totalRef.value = response;
      console.log(`response`, response);
      totalLoadingRef.value = false;
    }
  };

  const fetchDailyData = async () => {
    dailyLoadingRef.value = true;
    let queryParams = {};
    if (dailyTime.value) {
      const endTime = dailyTime.value;
      queryParams = {
        startTime: timestampToDate(endTime - 7 * 24 * 60 * 60 * 1000)+ " 00:00:00",
        endTime: timestampToDate(endTime)+" 23:59:59",
      };
    }
    console.log(`filterObjectValues(DTSQueryParams)`, filterObjectValues(queryParams));
    const response = await service.post('/management/overview/dailyHandleStatic', {
      ...filterObjectValues(queryParams),
    });
    if (response) {
      const result = [];
      response.forEach((item) => {
        const total = item.total;
        const handled = item.handled;
        total.date = item.day;
        total.process = '日新增';
        handled.process = '日定界';
        result.push(total);
        result.push(handled);
      });
      dailyRef.value = result;
      console.log(`response`, response);
      dailyLoadingRef.value = false;
    }
  };

  const questionModule = () =>{
    const questionTime = ref([new Date('2025-04-01').getTime(), Date.now()]);
    const questionLoadingRef = ref(false);
    const exportQuestionLoading = ref(false);
    const questionRef = ref([]);
    const questionColumnsRef = ref([]);
    questionColumnsRef.value = columnsCreate('question');
    const onConfirmQuestion = (v: number | number[] | null) =>{
      if (v) {
        questionTime.value = [v[0], v[1]];
      }
      fetchQuestionData();
    }

    const clearTimeQuestion = () => {
      questionTime.value = null;
      fetchQuestionData();
    };

    const fetchQuestionData = async () => {
      questionLoadingRef.value = true;
      let queryParams = {};
      if (questionTime.value) {
        const [startTime, endTime] = questionTime.value;
        queryParams = { startTime: timestampToDate(startTime)+ " 00:00:00", endTime: timestampToDate(endTime)+" 23:59:59" };
      }
      console.log(`filterObjectValues(DTSQueryParams)`, filterObjectValues(queryParams));
      const response = await service.post('/management/overview/questionDelimit', {
        ...filterObjectValues(queryParams),
      });
      if (response) {
        questionRef.value = response;
        console.log(`response`, response);
        questionLoadingRef.value = false;
      }
    };

    const handleExportQuestionStatic = () => {
      return new Promise((resolve, reject) => {
        const req = new XMLHttpRequest();
        exportQuestionLoading.value = true;
        let queryParams = {};
        if (questionTime.value) {
          const [startTime, endTime] = questionTime.value;
          queryParams = { startTime: timestampToDate(startTime)+" 00:00:00", endTime: timestampToDate(endTime)+" 23:59:59"};
        }
        req.open(
          'POST',
          `http://${window.location.host}/ewp/management/overview/exportQuestionDelimit`,
          true
        );
        req.responseType = 'blob';
        req.setRequestHeader('Content-Type', 'application/json');
        req.onload = function () {
          const data = req.response;
          const blob = new Blob([data]);
          const blobUrl = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.download = '问题感知' + timestampToDate(Date.now()) + '.xls';
          a.href = blobUrl;
          a.click();
          exportQuestionLoading.value = false;
          resolve(true);
        };
        req.send(JSON.stringify(queryParams));
      });
    };


    return {
      questionTime,
      questionLoadingRef,
      exportQuestionLoading,
      questionRef,
      questionColumnsRef,
      onConfirmQuestion,
      clearTimeQuestion,
      fetchQuestionData,
      handleExportQuestionStatic
    }
  }
  const {
    questionTime,
    questionLoadingRef,
    exportQuestionLoading,
    questionRef,
    questionColumnsRef,
    onConfirmQuestion,
    clearTimeQuestion,
    fetchQuestionData,
    handleExportQuestionStatic
  } = questionModule();
</script>
<style scoped>
  .container-box {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  .text-center {
    h1 {
      color: #666;
      padding: 10px 0;
    }

    word-wrap: break-word;
  }

  .button-box {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .search-box,
  .table-container {
    background-color: white;
    padding: 20px;
    /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); */
    border-radius: 8px;
  }

  .table-container {
    height: 100%;
  }
</style>
