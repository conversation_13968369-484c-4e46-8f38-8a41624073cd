import { h, ref } from 'vue';
import { NButton, NTag, NSelect, NTooltip } from 'naive-ui';
import { formatDateTime } from '@/utils';
import { allIssueType } from '@/api/feedback';

// 修改 createColumns 的返回类型
export const createColumns = (handleAppNameClick?: (row: any) => void): any => {
  const baseColumns = [
    {
      type: 'selection',
    },
    {
      title: '问题编号',
      key: 'code',
      resizable: true,
      render: (row) => {
        return h('div', { style: { position: 'relative' } }, [
          h(
            NTag,
            {
              bordered: false,
              style: {
                cursor: 'pointer',
              },
            },
            {
              default: () => row.code,
            }
          ),
        ]);
      },
      ellipsis: {
        tooltip: true,
      },
      width: 220,
    },
    {
      title: '聚类时间',
      key: 'clusterTime',
      width: 110,
      render(row) {
        return formatDateTime(row.clusterTime);
      },
    },
    {
      title: '应用名称',
      key: 'appName',
      resizable: true,
      width: 120,
      ellipsis: {
        tooltip: true,
      },
      render: (row) => {
        return h(
          NTag,
          {
            type: 'primary',
            bordered: false,
            style: {
              cursor: 'pointer',
            },
            onClick: () => handleAppNameClick?.(row.appName),
          },
          {
            default: () => row.appName,
          }
        );
      },
    },
    {
      title: '问题描述',
      key: 'description',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: 'DTS单',
      key: 'dtsUrl',
      render: (row) => {
        return h(
          NTag,
          {
            type: 'info',
            border: false,
            onClick: async () => {
              window.open(
                `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${row.dtsUrl}`,
                '_blank'
              );
            },
          },
          {
            default: () => row.dtsUrl,
          }
        );
      },
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '分类',
      key: 'kind',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '问题反馈时间',
      key: 'createTime',
      resizable: true,
      width: 110,
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return formatDateTime(row.createTime);
      },
    },

    {
      title: '应用类型',
      key: 'appLevel',
      resizable: true,
      width: 120,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '描述来源',
      key: 'descriptionSource',
      resizable: true,
      width: 120,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '应用行业',
      key: 'appIndustry',
      resizable: true,
      width: 120,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '一级分类',
      key: 'classification',
      resizable: true,
      width: 120,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '问题概要描述',
      key: 'descriptionSummary',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '用户类型',
      key: 'userType',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '机型',
      key: 'model',
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '质量属性',
      key: 'qualityAttribute',
      resizable: true,
      width: 100,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '问题分类',
      key: 'issueType',
      resizable: true,
      width: 100,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '自动关联DTS',
      key: 'autoLink',
      render: (row) => (row.autoLink === 'Y' ? '是' : '否'),
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '自动关联DTS单',
      key: 'autoLinkDtsUrl',
      render: (row) => {
        return h(
          NTag,
          {
            type: 'info',
            border: false,
            onClick: async () => {
              window.open(
                `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${row.dtsUrl}`,
                '_blank'
              );
            },
          },
          {
            default: () => row.dtsUrl,
          }
        );
      },
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
  ];

  return baseColumns;
};
// 创建一个通用的样式对象
const commonStyle = {
  style: {
    width: '300px',
    textAlign: 'left',
  },
  clearable: true,
  placeholder: '请输入',
};

// 为 Select 组件创建特定样式
const selectStyle = {
  ...commonStyle,
  style: {
    ...commonStyle.style,
  },
  filterable: true,
  placeholder: '请选择',
};
const allIssueTypeOptions = ref([]);
const getAllIssueType = async () => {
  try {
    const response = await allIssueType();
    if (response && Array.isArray(response)) {
      allIssueTypeOptions.value = response.map((item) => ({
        label: item,
        value: item,
      }));
    } else {
      allIssueTypeOptions.value = [];
    }
  } catch (error) {
    allIssueTypeOptions.value = [];
  }
};

// 初始化调用
getAllIssueType();
export const getSearchFormItems = () => [
  {
    field: 'appName',
    label: '应用名称',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'clusterTime',
    label: '聚类时间',
    component: 'DateRangePicker',
  },
  {
    field: 'description',
    label: '问题描述',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'dtsUrl',
    label: 'DTS单',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'issueType',
    label: '问题分类',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: allIssueTypeOptions.value,
    },
  },
  {
    field: 'createTime',
    label: '问题反馈时间',
    component: 'DateRangePicker',
  },
];
