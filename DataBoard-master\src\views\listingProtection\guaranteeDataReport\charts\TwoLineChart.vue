<template>
  <div ref="twoLineChartDom" id="twoLineChartDom" class="chart-container"></div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue"
import * as echarts from "echarts"

// let { title, yData, xData } = defineProps(['title', 'xData', 'yData'])

let chart

let twoLineChartDom = ref()

onMounted(() => {
  initChart()
  resizeChart()
})

const resizeChart = () => {
  window.addEventListener('resize', () => {
    if (chart) {
      chart.resize()
    }

  })
}

// 基础配置一下Echarts
function initChart() {
  // chart = echarts.init(document.getElementById("twoLineChartDom"));
  chart = echarts.init(twoLineChartDom.value);
  // 把配置和数据放这里
  chart.setOption({
    title: {
      text: '用户行为数据-PV、UV',
      x: 'center',
      y: 10

    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['2024/10/14', '2024/10/15', '2024/10/16', '2024/10/17', '2024/10/18', '2024/10/19', '2024/10/20', '2024/10/21']
    },
    yAxis: {
      type: 'value'
    },
    legend: {
      data: ['PV', 'UV'],
      x: 'center',
      y: 'bottom'
    },
    series: [
      {
        name: 'PV',
        type: 'line',
        stack: 'Total',
        data: [44, 58, 51, 56, 100, 61, 210, 216]
      },
      {
        name: 'UV',
        type: 'line',
        stack: 'Total',
        data: [220, 182, 191, 234, 290, 330, 310, 320]
      }
    ]
  });
}


</script>

<style scoped>
.fut-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
  width: 100%;
  justify-content: space-between;
}

.chart-container {
  width: 100%;
  height: 100%;
}
</style>
