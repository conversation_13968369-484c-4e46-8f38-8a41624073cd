import { ewpService as service } from '@/utils/axios';

export async function getHiviewFeedback(params: any): Promise<any> {
  try {
    const response = await service.post('/management/hiView/query', params);

    return response;
  } catch (error) {
    throw error;
  }
}

export async function importExcel(file: File): Promise<any> {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await service.post('/management/hiView/importData', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    console.log('Import response:', response);

    return response;
  } catch (error) {
    console.error('Error importing Excel file:', error);
    throw error;
  }
}
