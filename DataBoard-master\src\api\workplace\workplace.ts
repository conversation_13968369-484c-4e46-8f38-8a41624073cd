import service from "@/utils/axios";

export interface WorkplaceDto {
  userNo: string;
  userItemIrData: IrDataDto;
  userItemCustomizedDemoData: DemoDataDto;
}
export interface IrDataDto {
  closedIrNum: number;
  waitHandleIrNum: number;
}
export interface DemoDataDto {
  closedDemoNum: number;
  waitHandleDemoNum: number;
}
interface QueryWorkplaceInfoRsp {
  msg: string;
  status: string;
  data: WorkplaceDto;
}
export function queryWorkplaceInfo(): Promise<QueryWorkplaceInfoRsp> {
  return service.get('/user/query/item_data');
}
