<template>
  <div class="">
    <!-- 查询表单区域 -->
    <n-card class="search-card" title="查询条件">
      <template #header-extra>
        <n-icon size="20" color="#1890ff">
          <SearchOutline />
        </n-icon>
      </template>

      <n-form
        :model="searchForm"
        label-placement="left"
        label-width="100px"
        require-mark-placement="right-hanging"
        @submit.prevent="handleSearch"
        class="search-form"
      >
        <n-grid :cols="24" :x-gap="24" :y-gap="16">
          <!-- 日期选择 -->
          <n-grid-item :span="8">
            <n-form-item label="查询日期" path="createTime" class="form-item">
              <n-date-picker
                v-model:value="searchForm.createTime"
                type="daterange"
                clearable
                placeholder="请选择日期范围"
                class="form-control"
              />
            </n-form-item>
          </n-grid-item>

          <!-- 应用类型 -->
          <n-grid-item :span="8">
            <n-form-item label="应用类型" path="conditionAppType" class="form-item">
              <n-select
                v-model:value="searchForm.conditionAppType"
                :options="appTypeOptions"
                clearable
                placeholder="请选择应用类型"
                class="form-control"
              />
            </n-form-item>
          </n-grid-item>

          <!-- 小组选择 -->
          <n-grid-item :span="8">
            <n-form-item label="所属小组" path="conditionTeam" class="form-item">
              <n-select
                v-model:value="searchForm.conditionTeam"
                :options="teamOptions"
                clearable
                multiple
                placeholder="请选择小组"
                class="form-control"
                max-tag-count="responsive"
              >
                <template #header>
                  <div class="select-all-btn" @click="chooseAllTeam">
                    <n-icon size="14"><CheckmarkCircleOutline /></n-icon>
                    全选
                  </div>
                </template>
              </n-select>
            </n-form-item>
          </n-grid-item>
          <n-grid-item :span="8">
            <n-form-item label="L1/L2" path="conditionTeam" class="form-item">
              <n-select
                v-model:value="searchForm.conditionLevel"
                :options="levelOptions"
                clearable
                placeholder="请选择"
                class="form-control"
                max-tag-count="responsive"
              >
<!--                <template #header>-->
<!--                  <div class="select-all-btn" @click="chooseAllLevel">-->
<!--                    <n-icon size="14"><CheckmarkCircleOutline /></n-icon>-->
<!--                    全选-->
<!--                  </div>-->
<!--                </template>-->
              </n-select>
            </n-form-item>
          </n-grid-item>
        </n-grid>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <n-space justify="center">
            <n-button @click="resetForm" class="action-btn">
              <template #icon>
                <n-icon><RefreshOutline /></n-icon>
              </template>
              重置
            </n-button>
            <n-button type="primary" attr-type="submit" class="action-btn">
              <template #icon>
                <n-icon><SearchOutline /></n-icon>
              </template>
              查询
            </n-button>
          </n-space>
        </div>
      </n-form>
    </n-card>
    <!-- 数据表格区域 -->
    <n-card class="table-card" title="工单解决状态分布">
      <template #header-extra>
        <n-space>
          <n-button type="primary" @click="handleExportDailyGroup" class="export-btn">
            <template #icon>
              <n-icon><DownloadOutline /></n-icon>
            </template>
            导出过去24小时内超期的工单
          </n-button>
          <n-button type="primary" @click="handleExportQuickGroup" class="export-btn">
            <template #icon>
              <n-icon><DownloadOutline /></n-icon>
            </template>
            导出即将超期的工单
          </n-button>
          <n-button type="primary" @click="showWeeklyModal = true" class="export-btn">
            <template #icon>
              <n-icon><DownloadOutline /></n-icon>
            </template>
            导出周报数据
          </n-button>
          <n-button type="primary" @click="handleExportWeeklyGroup" class="export-btn">
            <template #icon>
              <n-icon><DownloadOutline /></n-icon>
            </template>
            导出数据
          </n-button>
        </n-space>
      </template>

      <!-- 数据表格 -->
      <n-data-table
        @update:sorter="handleSorterChange"
        :columns="solveStateColumns"
        :data="tableData"
        :loading="loading"
        :bordered="false"
        :single-line="false"
        :scroll-x="1200"
        :row-class-name="getRowClassName"
        striped
      >
        <template #empty>
          <n-empty description="暂无数据">
            <template #extra>
              <n-button size="small" @click="fetchData">
                <template #icon>
                  <n-icon><RefreshOutline /></n-icon>
                </template>
                刷新
              </n-button>
            </template>
          </n-empty>
        </template>
      </n-data-table>
    </n-card>

    <!-- 导出模态框 -->
    <!-- 下载周报数据 -->
    <n-modal v-model:show="showWeeklyModal" preset="dialog" :showIcon="showIcon">
      <template #header>
        <div class="modal-header">
          <n-icon size="18" color="#1890ff"><DownloadOutline /></n-icon>
          请选择想要导出的小组
        </div>
      </template>
      <div class="modal-content">
        <n-select
          v-model:value="groupValue"
          multiple
          clearable
          :options="groupOptions"
          placeholder="请选择小组"
          class="group-select"
        >
          <template #header>
            <div class="select-all-btn" @click="allGroup">
              <n-icon size="14"><CheckmarkCircleOutline /></n-icon>
              全选
            </div>
          </template>
        </n-select>
      </div>
      <template #action>
        <n-button @click="showWeeklyModal = false">取消</n-button>
        <n-button type="primary" @click="handleExportWeeklyGroup" :loading="exportLoadingGroup">
          确认导出
        </n-button>
      </template>
    </n-modal>

    <!-- 下载历史数据 -->
    <n-modal v-model:show="showHistoryModal" preset="dialog" :showIcon="showIcon">
      <template #header>
        <div class="modal-header">
          <n-icon size="18" color="#1890ff"><DownloadOutline /></n-icon>
          请选择想要导出的小组
        </div>
      </template>
      <div class="modal-content">
        <n-select
          v-model:value="groupValue"
          multiple
          clearable
          :options="groupOptions"
          placeholder="请选择小组"
          class="group-select"
        >
          <template #header>
            <div class="select-all-btn" @click="allGroup">
              <n-icon size="14"><CheckmarkCircleOutline /></n-icon>
              全选
            </div>
          </template>
        </n-select>
      </div>
      <template #action>
        <n-button @click="showHistoryModal = false">取消</n-button>
        <n-button type="primary" @click="handleExportHistoryGroup" :loading="exportLoadingGroup">
          确认导出
        </n-button>
      </template>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue';
  import { ewpService as service } from '@/utils/axios';
  import { NCard, NButton, NSpace, NIcon, NEmpty } from 'naive-ui';
  import {
    SearchOutline,
    RefreshOutline,
    CheckmarkCircleOutline,
    DownloadOutline,
  } from '@vicons/ionicons5';
  import {
    solveStateColumns,
    appTypeOptions,
    teamOptions,
    groupOptions,
    defaultSearchForm,
    defaultSortState,
    API_CONFIG,
    EXPORT_FILE_NAMES,
    ALL_GROUP_VALUES,
    ALL_TEAM_VALUES,
    resetFormFields,
    levelOptions,
    ALL_LEVEL_VALUES,
  } from './bugStatusConfig';

  const showWeeklyModal = ref(false);
  const showHistoryModal = ref(false);
  const showIcon = ref(false);
  const sortState = ref({ ...defaultSortState });
  const groupValue = ref([1]);

  // 导出小组周报全选按钮
  const allGroup = () => {
    groupValue.value = ALL_GROUP_VALUES;
  };
  const chooseAllTeam = () => {
    searchForm.conditionTeam = ALL_TEAM_VALUES;
  };
  const chooseAllLevel = () => {
    searchForm.conditionLevel = ALL_LEVEL_VALUES;
  };

  const exportLoadingGroup = ref(false);

  const searchForm = reactive({ ...defaultSearchForm });

  const tableData = ref([] as any);
  const loading = ref(false);
  const showSummary = ref(true);

  // 分页配置
  const paginationConfig = {
    pageSize: 20,
    showSizePicker: true,
    pageSizes: [10, 20, 50, 100],
    showQuickJumper: true,
    prefix: ({ itemCount }: any) => `共 ${itemCount} 条数据`,
  };

  // 行样式配置
  const getRowClassName = (row: any) => {
    if (row.total > 100) return 'high-workload-row';
    if (row.total > 50) return 'medium-workload-row';
    return 'normal-workload-row';
  };

  // 汇总数据
  const getSummaryData = () => {
    if (!tableData.value || tableData.value.length === 0) return [];

    const summary = tableData.value.reduce(
      (acc: any, row: any) => {
        acc.total += row.total || 0;
        acc.dailyNew += row.dailyNew || 0;
        acc.dailyOverdue += row.dailyOverdue || 0;
        acc.toBeLocated += row.toBeLocated || 0;
        acc.toBeLocked += row.toBeLocked || 0;
        acc.toBeRepaired += row.toBeRepaired || 0;
        acc.toBeArchived += row.toBeArchived || 0;
        acc.toBeReturned += row.toBeReturned || 0;
        acc.closed += row.closed || 0;
        return acc;
      },
      {
        total: 0,
        dailyNew: 0,
        dailyOverdue: 0,
        toBeLocated: 0,
        toBeLocked: 0,
        toBeRepaired: 0,
        toBeArchived: 0,
        toBeReturned: 0,
        closed: 0,
      }
    );

    return [
      {
        name: '合计',
        total: summary.total,
        dailyNew: summary.dailyNew,
        dailyOverdue: summary.dailyOverdue,
        toBeLocated: summary.toBeLocated,
        toBeLocked: summary.toBeLocked,
        toBeRepaired: summary.toBeRepaired,
        toBeArchived: summary.toBeArchived,
        toBeReturned: summary.toBeReturned,
        closed: summary.closed,
        slaRate:
          tableData.value.length > 0
            ? tableData.value.reduce((sum: number, row: any) => sum + (row.slaRate || 0), 0) /
              tableData.value.length
            : 0,
        l1SelfCloseRate:
          tableData.value.length > 0
            ? tableData.value.reduce(
                (sum: number, row: any) => sum + (row.l1SelfCloseRate || 0),
                0
              ) / tableData.value.length
            : 0,
      },
    ];
  };

  const handleSorterChange = (sorter: any) => {
    if (sorter) {
      const { columnKey, order } = sorter;
      sortState.value.sortField = columnKey;
      sortState.value.sortOrder = order === 'ascend' ? 'asc' : 'desc';
    } else {
      sortState.value.sortField = defaultSortState.sortField;
      sortState.value.sortOrder = defaultSortState.sortOrder;
    }
    fetchData();
  };

  async function getDTSSolveStateList(link: string, params: any): Promise<any> {
    try {
      const response = await service.post(`/management/${link}`, params);
      return response;
    } catch (error) {
      console.error('Error fetching work order list:', error);
      throw error;
    }
  }
  const formatDate = (date: number | Date | null): string => {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };
  const fetchData = async () => {
    loading.value = true;
    try {
      const queryParams = {
        condition: searchForm.conditionAppType || '全量',
        groupId: searchForm.conditionTeam || ALL_TEAM_VALUES,
        sortField: sortState.value.sortField,
        sortOrder: sortState.value.sortOrder,
        level: searchForm.conditionLevel,
      };
      if (searchForm.createTime) {
        const [start, end] = searchForm.createTime;
        queryParams.startDate = formatDate(start);
        queryParams.endDate = formatDate(end);
      }
      //L1/L2查询
      // if (searchForm.conditionLevel.length === 2) {
      //   queryParams.level = '全部';
      // } else if (searchForm.conditionLevel.length === 1) {
      //   queryParams.level = searchForm.conditionLevel[0];
      // } else {
      //   queryParams.level = '';
      // }

      const data = await getDTSSolveStateList(API_CONFIG.DTSSolveStateLink, queryParams);

      // 校准返回数据字段名称
      tableData.value = data.map((item: any) => ({
        ...item,
        dailyNew: item.newOrder || 0,
        toBeRepaired: item.toBeChecked || 0,
        toBeArchived: item.toBeAchieved || 0,
        l1SelfCloseRate: item.levelOneClosedRate ? parseFloat(item.levelOneClosedRate) / 100 : 0,
        slaRate: item.slaRate ? parseFloat(item.slaRate) / 100 : 0,
        dailyOverdue: item.overdue || 0,
      }));
    } catch (error) {
      console.error('Failed to load table data:', error);
    } finally {
      loading.value = false;
    }
  };

  const handleSearch = () => {
    fetchData();
  };

  const resetForm = () => {
    resetFormFields(searchForm);
    fetchData();
  };
 // 导出过去24h内超期的工单
 const handleExportDailyGroup = async () => {
    return new Promise((resolve, reject) => {
      const req = new XMLHttpRequest();
      exportLoadingGroup.value = true;
      const queryParams = {
        // condition: searchForm.conditionAppType || '全量',
        // groupId: searchForm.conditionTeam || ALL_TEAM_VALUES,
        // sortField: sortState.value.sortField,
        // sortOrder: sortState.value.sortOrder,
      };
      req.open(
        'POST',
        `http://${window.location.host}${API_CONFIG.exportDailyGroupDataUrl}`,
        true
      );
      req.responseType = 'blob';
      req.setRequestHeader('Content-Type', 'application/json');
      req.setRequestHeader('env', 'dev');
      req.onload = function () {
        const data = req.response;
        if (data.size === 0) {
          exportLoadingGroup.value = false;
          reject(new Error('No data to export'));
          showWeeklyModal.value = false;
          return;
        }
        const blob = new Blob([data]);
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = EXPORT_FILE_NAMES.dailyDataExport;
        a.href = blobUrl;
        a.click();
        exportLoadingGroup.value = false;
        resolve(true);
        showWeeklyModal.value = false;
      };
      req.send(JSON.stringify(queryParams));
    });
  };
 
  // 导出即将超期的工单
  const handleExportQuickGroup = async () => {
    return new Promise((resolve, reject) => {
      const req = new XMLHttpRequest();
      exportLoadingGroup.value = true;
      const queryParams = {
        // condition: searchForm.conditionAppType || '全量',
        // groupId: searchForm.conditionTeam || ALL_TEAM_VALUES,
        // sortField: sortState.value.sortField,
        // sortOrder: sortState.value.sortOrder,
      };
      req.open(
        'POST',
        `http://${window.location.host}${API_CONFIG.exportQuickGroupDataUrl}`,
        true
      );
      req.responseType = 'blob';
      req.setRequestHeader('Content-Type', 'application/json');
      req.setRequestHeader('env', 'dev');
      req.onload = function () {
        const data = req.response;
        if (data.size === 0) {
          exportLoadingGroup.value = false;
          reject(new Error('No data to export'));
          showWeeklyModal.value = false;
          return;
        }
        const blob = new Blob([data]);
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = EXPORT_FILE_NAMES.quickDataExport;
        a.href = blobUrl;
        a.click();
        exportLoadingGroup.value = false;
        resolve(true);
        showWeeklyModal.value = false;
      };
      req.send(JSON.stringify(queryParams));
    });
  };
  // 导出小组周报确认按钮，发出请求
  const handleExportWeeklyGroup = () => {
    return new Promise((resolve, reject) => {
      const req = new XMLHttpRequest();
      exportLoadingGroup.value = true;
      const queryParams = {
        condition: searchForm.conditionAppType || '全量',
        groupId: searchForm.conditionTeam || ALL_TEAM_VALUES,
        sortField: sortState.value.sortField,
        sortOrder: sortState.value.sortOrder,
      };
      if (searchForm.createTime) {
        const [start, end] = searchForm.createTime;
        queryParams.startDate = formatDate(start);
        queryParams.endDate = formatDate(end);
      }
      req.open(
        'POST',
        `http://${window.location.host}${API_CONFIG.exportWeeklyGroupDataUrl}`,
        true
      );
      req.responseType = 'blob';
      req.setRequestHeader('Content-Type', 'application/json');
      req.setRequestHeader('env', 'dev');
      req.onload = function () {
        const data = req.response;
        console.log('data:', data);
        if (data.size === 0) {
          exportLoadingGroup.value = false;
          reject(new Error('No data to export'));
          showWeeklyModal.value = false;
          return;
        }
        const blob = new Blob([data]);
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = EXPORT_FILE_NAMES.weeklyReport;
        a.href = blobUrl;
        a.click();
        exportLoadingGroup.value = false;
        resolve(true);
        showWeeklyModal.value = false;
      };
      req.send(JSON.stringify(queryParams));
    });
  };

  const handleExportHistoryGroup = () => {
    return new Promise((resolve, reject) => {
      const req = new XMLHttpRequest();
      exportLoadingGroup.value = true;
      const queryParams = {
        groupIds: groupValue.value,
      };
      req.open('POST', `http://${window.location.host}${API_CONFIG.exportTotalGroupDataUrl}`, true);
      req.responseType = 'blob';
      req.setRequestHeader('Content-Type', 'application/json');
      req.setRequestHeader('env', 'dev');
      req.onload = function () {
        const data = req.response;
        console.log('data:', data);
        if (data.size === 0) {
          exportLoadingGroup.value = false;
          reject(new Error('No data to export'));
          showHistoryModal.value = false;
          return;
        }
        const blob = new Blob([data]);
        const blobUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.download = EXPORT_FILE_NAMES.historyData;
        a.href = blobUrl;
        a.click();
        exportLoadingGroup.value = false;
        resolve(true);
        showHistoryModal.value = false;
      };
      req.send(JSON.stringify(queryParams));
    });
  };

  onMounted(() => {
    fetchData();
  });
</script>

<style scoped>
  /* 容器样式 */
  .bug-status-container {
    padding: 24px;
    background: #f5f7fa;
    min-height: 100vh;
  }

  /* 搜索卡片样式 */
  .search-card {
    margin-bottom: 24px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaec;
  }

  .search-card :deep(.n-card-header) {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #f0f0f0;
  }

  .search-card :deep(.n-card__content) {
    padding: 24px;
  }

  /* 表单样式 */
  .search-form {
    width: 100%;
  }

  .form-item {
    margin-bottom: 0;
  }

  .form-item :deep(.n-form-item-label) {
    font-weight: 500;
    color: #333;
    min-width: 100px;
    text-align: right;
    padding-right: 12px;
  }

  .form-control {
    width: 100%;
    min-width: 200px;
  }

  .form-control :deep(.n-base-selection) {
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    transition: all 0.3s;
  }

  .form-control :deep(.n-base-selection:hover) {
    border-color: #40a9ff;
  }

  .form-control :deep(.n-base-selection.n-base-selection--focused) {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  /* 操作按钮样式 */
  .form-actions {
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #f0f0f0;
  }

  .action-btn {
    min-width: 100px;
    height: 36px;
    border-radius: 6px;
    font-weight: 500;
  }

  /* 全选按钮样式 */
  .select-all-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    cursor: pointer;
    color: #1890ff;
    font-weight: 500;
    transition: all 0.3s;
  }

  .select-all-btn:hover {
    background-color: #f0f8ff;
    color: #0066cc;
  }

  /* 表格卡片样式 */
  .table-card {
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaec;
  }

  .table-card :deep(.n-card-header) {
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
  }

  .table-card :deep(.n-card__content) {
    padding: 0;
  }

  /* 导出按钮样式 */
  .export-btn {
    height: 36px;
    border-radius: 6px;
    font-weight: 500;
    min-width: 120px;
  }

  /* 汇总按钮样式 */
  .summary-btn {
    height: 36px;
    border-radius: 6px;
    font-weight: 500;
    min-width: 100px;
  }

  /* 数据表格样式 */
  .data-table {
    border-radius: 0 0 12px 12px;
    overflow: hidden;
  }

  .data-table :deep(.n-data-table) {
    background: #fff;
  }

  .data-table :deep(.n-data-table-thead) {
    background: #f2f3f5;
  }

  .data-table :deep(.n-data-table-th) {
    background: transparent;
    font-weight: 600;
    color: #606266;
    border-bottom: 1px solid #ebeef5;
    padding: 16px 12px;
    text-align: center;
    font-size: 13px;
  }

  .data-table :deep(.n-data-table-th:first-child) {
    text-align: center;
  }

  .data-table :deep(.n-data-table-td) {
    padding: 16px 12px;
    border-bottom: 1px solid #f0f0f0;
    text-align: center;
    vertical-align: middle;
  }

  .data-table :deep(.n-data-table-td:first-child) {
    text-align: center;
  }

  .data-table :deep(.n-data-table-tr:hover .n-data-table-td) {
    background-color: #f8f9fa;
  }

  .data-table :deep(.n-data-table-tr:nth-child(even) .n-data-table-td) {
    background-color: #fafbfc;
  }

  .data-table :deep(.n-data-table-sorter) {
    color: #909399;
  }

  .data-table :deep(.n-data-table-sorter--active) {
    color: #1890ff;
  }

  .data-table :deep(.n-data-table-sorter:hover) {
    color: #1890ff;
  }

  /* 行样式 */
  .data-table :deep(.high-workload-row .n-data-table-td) {
    background-color: #fff2f0;
    border-left: 4px solid #ff4d4f;
  }

  .data-table :deep(.medium-workload-row .n-data-table-td) {
    background-color: #fffbe6;
    border-left: 4px solid #faad14;
  }

  .data-table :deep(.normal-workload-row .n-data-table-td) {
    background-color: #f6ffed;
    border-left: 4px solid #52c41a;
  }

  /* 汇总行样式 */
  .data-table :deep(.n-data-table-summary .n-data-table-td) {
    background: #f2f5f9;
    color: #333;
    font-weight: 600;
    border-top: 2px solid #e8eaec;
    font-size: 14px;
  }

  /* 分页样式 */
  .data-table :deep(.n-pagination) {
    margin-top: 24px;
    justify-content: center;
  }

  .data-table :deep(.n-pagination .n-pagination-item) {
    border-radius: 6px;
  }

  .data-table :deep(.n-pagination .n-pagination-item--active) {
    background: #1890ff;
    border-color: #1890ff;
  }

  /* 加载状态样式 */
  .data-table :deep(.n-data-table-loading) {
    background: rgba(255, 255, 255, 0.9);
  }

  /* 空状态样式 */
  .data-table :deep(.n-data-table-empty) {
    padding: 40px 20px;
    color: #999;
    font-size: 14px;
  }

  /* 模态框样式 */
  .modal-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #333;
  }

  .modal-content {
    padding: 16px 0;
  }

  .group-select {
    width: 100%;
  }

  .group-select :deep(.n-base-selection) {
    border-radius: 6px;
    border: 1px solid #d9d9d9;
  }

  .group-select :deep(.n-base-selection:hover) {
    border-color: #40a9ff;
  }

  .group-select :deep(.n-base-selection.n-base-selection--focused) {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  /* 响应式设计 */
  @media (max-width: 1200px) {
    .bug-status-container {
      padding: 16px;
    }

    .search-card :deep(.n-card__content) {
      padding: 16px;
    }

    .table-card :deep(.n-card-header) {
      padding: 16px;
    }
  }

  @media (max-width: 768px) {
    .form-control {
      min-width: 150px;
    }

    .export-btn {
      min-width: 100px;
    }

    .action-btn {
      min-width: 80px;
    }
  }
</style>
