<template>
  <div>
    <n-space vertical>
      <n-card>
        <n-space>
          <n-button secondary strong type="warning" @click="refreshData()">
            <template #icon>
              <n-icon>
                <Refresh />
              </n-icon>
            </template>
            刷新
          </n-button>
          <n-button secondary strong type="primary" @click="showListAddModal = true">
            导入
          </n-button>
          <n-button
            secondary
            strong
            type="primary"
            @click="
              showModal = true;
              isAdd = true;
            "
          >
            <template #icon>
              <n-icon>
                <Add />
              </n-icon>
            </template>
            添加
          </n-button>
        </n-space>
        <n-data-table
          remote
          :columns="columns"
          :data="data"
          :pagination="pagination"
          :loading="loading"
          style="margin-top: 20px"
        />
      </n-card>
    </n-space>

    <n-modal v-model:show="showListAddModal">
      <n-card
        style="width: 600px"
        title="批量导入"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-button text @click="handleDownload" style="margin-bottom: 20px"
          >点击下载导入模板</n-button
        >
        <n-upload
          action="#"
          :custom-request="customRequest"
          :multiple="true"
          :default-file-list="fileList"
          accept=".xls,.xlsx,"
          :max="1"
          directory-dnd
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <Add />
              </n-icon>
            </div>
            <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
          </n-upload-dragger>
        </n-upload>
      </n-card>
    </n-modal>

    <n-modal v-model:show="showModal" :on-after-leave="handleAfterLeave">
      <n-card
        :style="{ width: '1200px' }"
        :title="isAdd ? '新增' : '编辑'"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <template #header-extra> </template>
        <n-form
          ref="formRef"
          :model="model"
          :rules="rules"
          label-placement="left"
          label-width="100"
          require-mark-placement="right-hanging"
          size="medium"
        >
          <n-grid :cols="24" :x-gap="24">
            <n-form-item-gi :span="12" label="应用名" path="appName">
              <n-input :disabled="!isAdd" v-model:value="model.appName" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="使用场景" path="scene">
              <n-input v-model:value="model.scene" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="效率提示" path="upscale">
              <n-input type="textarea" v-model:value="model.upscale" />
            </n-form-item-gi>
            <n-form-item-gi :span="12" label="评分" path="rating">
              <n-input-number
                :style="{ width: '100%' }"
                max="10"
                min="0"
                :precision="1"
                v-model:value="model.rating"
              />
            </n-form-item-gi>
          </n-grid>
        </n-form>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" @click="handleSubmint()"> 确认 </n-button>
            <n-button secondary strong type="error" @click="showModal = false"> 取消 </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>
<script lang="ts" setup>
  import { ref, reactive, h, onMounted } from 'vue';
  import { Add, Refresh, Close, GridOutline } from '@vicons/ionicons5';
  import { NIcon, useMessage, useDialog, NButton, NFlex } from 'naive-ui';
  import { paginationReactive, defaultModel } from './index';
  import {
    dashboardAddOrUpdate,
    dashboardDelete,
    dashboardQuery,
    dashboardImport,
    dashboardDownloadTemplate,
  } from '@/api/dataview/codeDataReportManager';
  import { cloneDeep, debounce } from 'lodash-es';

  const formRef = ref();
  const defaultProgress = ref('');
  const isAdd = ref(true);
  const showModal = ref(false);
  const showListAddModal = ref(false);
  const fileList = ref([]);
  const loading = ref(false);
  const message = useMessage();
  const dialog = useDialog();
  const sampleDevidesUsageRegistrationColumn = [
    {
      title: '应用名',
      key: 'appName',
      minWidth: 100,
      resizable: true,
    },
    {
      title: '使用场景',
      key: 'scene',
      minWidth: 100,
      resizable: true,
    },
    {
      title: '效率提示',
      key: 'upscale',
      minWidth: 200,
      resizable: true,
    },
    {
      title: '评分',
      key: 'rating',
      minWidth: 100,
      resizable: true,
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      minWidth: 50,
      render: (row) => {
        return [
          h(NFlex, { wrap: false }, () => [
            h(
              NButton,
              {
                strong: true,
                tertiary: true,
                type: 'info',
                size: 'medium',
                onClick: () => {
                  let rowVal = cloneDeep(row);
                  rowVal.rating = rowVal.rating ? Number(rowVal.rating) : null;
                  model.value = cloneDeep(rowVal);
                  isAdd.value = false;
                  showModal.value = true;
                },
              },
              {
                default: () => '编辑',
              }
            ),
            h(
              NButton,
              {
                strong: true,
                tertiary: true,
                type: 'error',
                size: 'medium',
                onClick: () => {
                  remove(row);
                },
              },
              {
                default: () => '删除',
              }
            ),
          ]),
        ];
      },
    },
  ];
  const columns = ref(sampleDevidesUsageRegistrationColumn);
  paginationReactive.onChange = (page: number) => {
    paginationReactive.page = page;
    search();
  };
  paginationReactive.onUpdatePageSize = (pageSize: number) => {
    paginationReactive.pageSize = pageSize;
    paginationReactive.page = 1;
    search();
  };
  const pagination = ref(paginationReactive);
  const rules = ref({
    appName: {
      required: true,
      message: '请输入应用名称',
      trigger: 'blur',
    },
    scene: {
      required: true,
      message: '请输入使用场景',
      trigger: 'blur',
    },
    upscale: {
      required: true,
      message: '请输入效率提示',
      trigger: 'blur',
    },
    rating: {
      type: 'number',
      required: true,
      message: '请输入评分',
      trigger: 'blur',
    },
  });
  const data = ref([]);
  let modelReactive = reactive(cloneDeep(defaultModel));
  const model = ref(modelReactive);
  const handleAfterLeave = () => {
    modelReactive = reactive(cloneDeep(defaultModel));
    model.value = modelReactive;
  };

  const remove = (row) => {
    dialog.info({
      title: '提示',
      content: '你确定要删除吗？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: async () => {
        let res = await dashboardDelete({ appName: row.appName });
        if (res.status) {
          message.success('删除成功');
        }
        search();
      },
      onNegativeClick: () => {},
    });
  };

  // 查询表格数据
  const search = debounce(async () => {
    loading.value = true;
    try {
      let res = await dashboardQuery({
        pageNum: pagination.value.page,
        pageSize: pagination.value.pageSize,
      });
      if (res.status === '200') {
        data.value = res?.data?.data || [];
        paginationReactive.itemCount = res?.data?.pageInfo?.total || 0;
        paginationReactive.pageCount =
          Math.ceil(res?.data?.pageInfo?.total / pagination.value.pageSize) || 0;
      }
    } catch (e) {}
    loading.value = false;
  }, 300);

  const refreshData = () => {
    search();
  };

  const handleSubmint = debounce(async () => {
    formRef.value.validate(async (errors) => {
      if (!errors) {
        let param = {
          appName:model.value.appName,
          upscale:model.value.upscale,
          scene:model.value.scene,
          rating:`${model.value.rating || 0}`
        }
        try {
          var res = await dashboardAddOrUpdate(param);
          if (res.status == '200') {
            message.success('提交成功');
            showModal.value = false;
            search();
          }
        } catch (err) {
          message.error(err.message);
        }
      }
    });
  }, 300);
  //上传
  const customRequest = async ({ file, onError }: UploadCustomRequestOptions) => {
    const formData = new FormData();
    formData.append('file', file.file as File);
    //上传接口
    try {
      let res = await dashboardImport(formData);
      if (res.status == '200') {
        showListAddModal.value = false;
        search();
        message.success('导入成功');
      } else {
        fileList.value = [];
      }
    } catch (err) {
      fileList.value = [];
      onError();
    }
  };
  //下载
  const handleDownload = async () => {
    dashboardDownloadTemplate()
      .then((res) => {
        if (!res) {
          message.error('下载失败！');
        }
      })
      .catch((e) => {
        message.error(`下载失败，原因：${e?.response?.data?.error}`);
      });
  };
  onMounted(() => {
    refreshData();
  });
</script>
