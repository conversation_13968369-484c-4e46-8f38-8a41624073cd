<template>
  <div class="console">
    <div class="n-layout-page-header" style="margin-bottom: 10px">
      <n-card :bordered="false" title="工作台">
        <n-grid cols="2 s:1 m:1 l:2 xl:2 2xl:2" responsive="screen">
          <n-gi>
            <div class="flex items-center">
              <div>
                <n-avatar circle :size="64" :src="schoolboy" />
              </div>
              <div>
                <p class="px-4 text-xl">嗨，{{ userInfo.userName }}，开始您一天的工作吧！</p>
                <p class="px-4 text-gray-400">{{ currentTime }}</p>
              </div>
            </div>
          </n-gi>
          <n-gi>
            <div class="flex justify-end w-full">
              <!-- <div class="flex flex-col justify-center flex-1 text-right">
                <span class="text-secondary">已闭环故障知识</span>
                <span class="text-2xl">{{ irData.closedIrNum }}</span>
              </div>
              <div class="flex flex-col justify-center flex-1 text-right">
                <span class="text-secondary">待输出故障知识</span>
                <a v-if="irData.waitHandleIrNum > 0" class="text-2xl" href="/faultKnowledgeBaseConstruction/processOrder">{{ irData.waitHandleIrNum }}</a>
                <span v-else class="text-2xl">{{ irData.waitHandleIrNum }}</span>
              </div> -->
              <!-- <div class="flex flex-col justify-center flex-1 text-right">
                <span class="text-secondary">待输出Demo</span>
                <a v-if="demoData.waitHandleDemoNum > 0" class="text-2xl" href="/sampleCodeManage/customizedDemo/mytodo">{{ demoData.waitHandleDemoNum }}</a>
                <span v-else class="text-2xl">{{ demoData.waitHandleDemoNum }}</span>
              </div>
              <div class="flex flex-col justify-center flex-1 text-right">
                <span class="text-secondary">待评审Demo</span>
                <a v-if="demoData.waitReviewDemoNum > 0" class="text-2xl" href="/sampleCodeManage/customizedDemo/mytodo">{{ demoData.waitReviewDemoNum }}</a>
                <span v-else class="text-2xl">{{ demoData.waitReviewDemoNum }}</span>
              </div>
              <div class="flex flex-col justify-center flex-1 text-right">
                <span class="text-secondary">待外发Demo</span>
                <a v-if="demoData.waitOutGoingDemoNum > 0" class="text-2xl" href="/sampleCodeManage/customizedDemo/mytodo">{{ demoData.waitOutGoingDemoNum }}</a>
                <span v-else class="text-2xl">{{ demoData.waitOutGoingDemoNum }}</span>
              </div>
              <div class="flex flex-col justify-center flex-1 text-right">
                <span class="text-secondary">待托管Demo</span>
                <a v-if="demoData.waitGiteeDemoNum > 0" class="text-2xl" href="/sampleCodeManage/customizedDemo/mytodo">{{ demoData.waitGiteeDemoNum }}</a>
                <span v-else class="text-2xl">{{ demoData.waitGiteeDemoNum }}</span>
              </div> -->
            </div>
          </n-gi>
        </n-grid>
      </n-card>
    </div>

    <!--导航卡片-->
    <div class="mt-4">
      <n-grid cols="1 s:2 m:4 l:8 xl:12 2xl:14" responsive="screen" :x-gap="16" :y-gap="8">
        <n-grid-item v-for="(item, index) in iconList" :key="index" span="1 s:1 m:2 l:2 xl:2 2xl:2">
          <NCard
            @click="item.eventObject.click"
            content-style="padding-top: 0;"
            size="large"
            :bordered="false"
            style="cursor: pointer"
          >
            <template #footer>
              <n-skeleton v-if="loading" size="medium" />
              <div class="cursor-pointer" style="position: relative" v-else>
                <p class="flex justify-center">
                  <span>
                    <n-icon :size="item.size" class="flex-1" :color="item.color">
                      <component :is="item.icon" />
                    </n-icon>
                  </span>
                </p>
                <p class="flex justify-center px-4 text-xl">
                  <span>{{ item.title }}</span>
                </p>
              </div>
            </template>
          </NCard>
        </n-grid-item>
      </n-grid>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import {
  DocumentUnknown,
  Code,
  CodeReference,
  AiResults,
  UserServiceDesk,
  VirtualColumnKey,
  ShoppingCart,
  Result
} from '@vicons/carbon'
import { useUserStore } from '@/store/modules/user';
import { useRouter } from 'vue-router';
import { queryWorkplaceInfo, DemoDataDto, IrDataDto } from "@/api/workplace/workplace";

const userStore = useUserStore();
const loading = ref(true);

const currentTime = ref('');
const userInfo = userStore.getUserInfo;
const router = useRouter();
// 图标列表
const iconList = [
  // {
  //   icon: DocumentUnknown,
  //   size: '32',
  //   title: '故障知识',
  //   color: '#62E884',
  //   eventObject: {
  //     click: () => router.push('/faultKnowledgeBaseConstruction/processOrder'),
  //   },
  // },
  {
    icon: Code,
    size: '36',
    title: 'demo处理',
    color: '#69c0ff',
    eventObject: {
      click: () => router.push('/sampleCodeManage/customizedDemo'),
    },
  },
  {
    icon: AiResults,
    size: '36',
    title: '知识检索',
    color: '#ff85c0',
    eventObject: {
      click: () => router.push('/kowledgeBase/knowledgeSearch'),
    },
  },
  {
    icon: CodeReference,
    size: '36',
    title: '样例代码',
    color: '#ffc069',
    eventObject: {
      click: () => router.push('/sampleCodeManage/SampleCode'),
    },
  },
  {
    icon: UserServiceDesk,
    size: '36',
    title: '应用舆情',
    color: '#ff9c6e',
    eventObject: {
      click: () => router.push('/listingProtection/ProblemHandle'),
    },
  },
  {
    icon: VirtualColumnKey,
    size: '36',
    title: '交付件统计',
    color: '#b37feb',
    eventObject: {
      click: () => router.push('/departmentOKR/statisticalOverview'),
    },
  },
  {
    icon: Result,
    size: '36',
    title: '质量评估',
    color: '#5cdbd3',
    eventObject: {
      click: () => router.push('/acceptancePrototype/applicationAcceptance'),
    },
  },
  // {
  //   icon: VirtualColumnKey,
  //   size: '32',
  //   title: '样机物权转移',
  //   color: '#b37feb',
  //   eventObject: {
  //     click: () => router.push('/acceptancePrototype/urgeSignature'),
  //   },
  // },
  // {
  //   icon: ShoppingCart,
  //   size: '32',
  //   title: '库房管理',
  //   color: '#ffd666',
  //   eventObject: {
  //     click: () => router.push('/acceptancePrototype/SampleDevicesRecord'),
  //   },
  // },
  // {
  //   icon: Result,
  //   size: '32',
  //   title: '借还登记',
  //   color: '#5cdbd3',
  //   eventObject: {
  //     click: () => router.push('/acceptancePrototype/SampleDevidesUsageRegistration'),
  //   },
  // },
];

const irData = ref<IrDataDto>({
  closedIrNum: 0,
  waitHandleIrNum: 0,
});
const demoData = ref<DemoDataDto>({
  closedDemoNum: 0,
  waitHandleDemoNum: 0,
});

const fetchWorkplaceData = async () => {
  try {
    loading.value = true;
    // const { data } = await queryWorkplaceInfo();
    // // 更新数据
    // irData.value = data.userItemIrData;
    // demoData.value = data.userItemCustomizedDemoData;
  } catch (error) {
    console.error('Failed to fetch workplace data:', error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchWorkplaceData();

  // 时间更新逻辑
  const updateTime = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    currentTime.value = `当前时间：${year}年${month}月${day}日 ${hours}时${minutes}分`;
  };

  updateTime();
  setInterval(updateTime, 60000);
});
</script>

<style lang="less" scoped></style>
