let defaultDate = (now = false) => {
  const date = new Date();
  let YYYY = date.getFullYear();
  let MM = `00${date.getMonth() + 1}`.slice(-2);
  let DD = `00${now ? date.getDate() : 1}`.slice(-2);
  return `${YYYY}-${MM}-${DD}`;
};

export const SETTINGS = {
  accounts: `l30035135,y30026430,p00566545,w30047907,w30045387`, //带英文字母工号，以任意非字母非数字的fu分割, excel中复制列内容粘贴替换``中内容即可
  startDate: defaultDate(), // 日期格式-YYYY-MM-DD, 目前未当前月1号-当日
  endDate: defaultDate(true),
};
