<template>
  <div>
    <n-card style="margin-bottom: 12px">
      <n-form label-placement="left" label-width="100px" label-align="right">
        <n-grid x-gap="20" :cols="4" style="margin-bottom: 12px">
          <n-gi>
            <n-form-item label="姓名">
              <n-select
                v-model:value="filters.userNo"
                :options="userList"
                filterable
                multiple
                clearable
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="组别" path="teamList">
              <n-select
                v-model:value="filters.teamList"
                :options="teamList"
                filterable
                multiple
                clearable
              />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="主管" path="leaderList">
              <n-select
                v-model:value="filters.leaderList"
                :options="leaderList"
                filterable
                multiple
                clearable
              />
            </n-form-item>
          </n-gi>
        </n-grid>
      </n-form>
      <n-space justify="center">
        <n-button secondary strong type="default" @click="handleResetFilter">重置 </n-button>
        <n-button secondary strong type="primary" @click="handleUpdateFilter()">查询 </n-button>
      </n-space>
    </n-card>
    <!-- 添加 -->
    <n-modal v-model:show="showModal" :on-after-leave="initModel">
      <div>
        <n-card
          :style="{ width: '600px' }"
          title="编辑紧急联系人"
          :bordered="false"
          size="huge"
          role="dialog"
          aria-modal="true"
        >
          <template #header-extra> </template>
          <n-form
            ref="formRef"
            :model="model"
            :rules="rules"
            label-placement="left"
            label-width="auto"
            require-mark-placement="right-hanging"
            size="medium"
          >
            <n-form
              label-placement="left"
              label-width="100px"
              label-align="right"
              :model="contactModel"
              :rules="rules"
            >
              <n-form-item label="工号" path="account">
                <n-input v-model:value="model.account" disabled />
              </n-form-item>
              <n-form-item label="联系人姓名" path="emergencyContact">
                <n-input v-model:value="model.emergencyContact" clearable />
              </n-form-item>
              <n-form-item label="关系" path="relation">
                <n-select
                  v-model:value="model.relation"
                  :options="relationList"
                  filterable
                  clearable
                />
              </n-form-item>
              <n-form-item label="手机号" path="phone">
                <n-input v-model:value="model.phone" clearable />
              </n-form-item>
            </n-form>
          </n-form>

          <template #footer>
            <n-space>
              <n-button secondary strong type="primary" @click="handleSubmint()"> 确认 </n-button>
              <n-button secondary strong type="error" @click="showModal = false"> 取消 </n-button>
            </n-space>
          </template>
        </n-card>
      </div>
    </n-modal>
    <n-card>
      <div>
        <div class="layout-page-header">
          <n-space>
            <n-button secondary strong type="warning" @click="handleGetContact()">
              <template #icon>
                <n-icon>
                  <Refresh />
                </n-icon>
              </template>
              刷新
            </n-button>
          </n-space>
        </div>
        <n-data-table
          :pagination="pagination"
          :columns="columns"
          :data="tableData"
          style="margin-top: 20px"
          :single-line="false"
          max-height="700px"
          remote
          scroll-x
          @update:sorter="handleSort"
        />
      </div>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
  import { Add, Refresh, Close, GridOutline } from '@vicons/ionicons5';
  import { NButton, useDialog, useMessage } from 'naive-ui';
  import { h, ref, reactive, onBeforeMount, defineProps, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import {
    formatDateTime,
    getDefaultContactFilters,
    districts,
    emergencyContactModelTemplate,
    relationList,
    teamOptions,
  } from '../index';
  import {
    getContactList,
    addContact,
    updateContact,
  } from '@/api/dataview/businessTravelersManage';
  import { useUserStore } from '@/store/modules/user';
  import { getEmployeeList } from '@/api/system/usermanage';
  const contactModel = ref({ ...emergencyContactModelTemplate });
  const teamList = ref(teamOptions);
  const props = defineProps({
    leaderList: {
      default: [],
      required: true,
    },
  });
  watch(
    () => props.leaderList,
    () => {
      leaderList.value = props.leaderList;
    }
  );
  const leaderList = ref(reactive(props.leaderList));
  const userStore = useUserStore();
  const userNo = userStore.getUserInfo.account;
  const userList = ref([]);
  const sortCol = ref(null);
  const hasContact = ref(false);
  const filters = ref(getDefaultContactFilters());
  const message = useMessage();
  const tableData = ref([]);
  const showModal = ref(false);
  const model = ref({ ...emergencyContactModelTemplate });
  const rules = ref({
    emergencyContact: {
      required: true,
    },
    relation: {
      required: true,
    },
    phone: {
      required: true,
    },
    supportApp: {
      required: true,
    },
    travelProvince: {
      required: true,
    },
    travelCity: {
      required: true,
    },
    travelStartDate: {
      required: true,
    },
    travelEndDate: {
      required: true,
    },
  });
  const isAdd = ref(false);
  const formRef = ref();
  const col = [
    {
      title: '姓名',
      key: 'name',
      width: 100,
    },
    {
      title: '工号',
      key: 'account',
      width: 100,
    },
    {
      title: '常驻地',
      key: 'residence',
      width: 80,
    },
    {
      title: '组别',
      key: 'team',
      width: 80,
    },
    {
      title: '主管',
      key: 'leaderName',
      width: 100,
    },
    {
      title: '出差天数（年度累计）',
      key: 'totalTravelDays',
      width: 140,
      sorter: true,
    },
    {
      title: '紧急联系人姓名',
      key: 'emergencyContact',
      width: 70,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '关系',
      key: 'relation',
      width: 70,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '手机号',
      key: 'phone',
      width: 100,
    },

    {
      title: '操作',
      key: 'action',
      width: 100,
      fixed: 'right',
      render(row) {
        return [
          h(
            NButton,
            {
              strong: true,
              tertiary: true,
              type: 'info',
              size: 'medium',
              style: 'margin-right:20px',
              onClick: async () => {
                isAdd.value = false;
                showModal.value = true;
                model.value = JSON.parse(JSON.stringify(row));
              },
            },
            { default: () => [h('div', '编辑')] }
          ),
        ];
      },
    },
  ];
  const columns = ref(col);

  const pagination = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 50],
    itemCount: 0,
    prefix({ itemCount }) {
      return `总条数 ${itemCount}`;
    },
    onChange: (page: number) => {
      pagination.page = page;
      handleGetContact();
    },
    onUpdatePageSize: (pageSize: number) => {
      pagination.pageSize = pageSize;
      pagination.page = 1;
      handleGetContact();
    },
  });

  const handleUpdateFilter = () => {
    pagination.page = 1;
    handleGetContact();
  };
  const initModel = () => {
    model.value = { ...emergencyContactModelTemplate };
  };
  const handleSort = (col) => {
    sortCol.value = col;
    handleGetContact(col);
  };
  //获取人员信息
  const handleGetContact = async () => {
    let params = {
      pageNum: pagination.page,
      pageSize: pagination.pageSize,
      userList: filters.value.userNo || null,
      teamList: filters.value.teamList,
      leaderList: filters.value.leaderList,
    };
    if (sortCol.value?.columnKey === 'totalTravelDays' && sortCol.value?.order) {
      params.travelDaysOrder = sortCol.value.order === 'descend' ? 'desc' : 'asc';
    }
    let res = await getContactList(params);
    if (res.status == '200') {
      tableData.value = res.data.list;
      pagination.itemCount = res.data.pageInfo.total;
    }
  };

  const handleResetFilter = () => {
    pagination.page = 1;
    filters.value = getDefaultContactFilters();
    handleGetContact();
  };
  //提交处理应用
  const handleSubmint = async () => {
    formRef.value.validate(async (errors) => {
      if (!errors) {
        let param = {
          emergencyContact: model.value.emergencyContact,
          relation: model.value.relation,
          phone: model.value.phone,
          account: model.value.account,
          id: model.value.id,
        };
        var res = await updateContact(param);
        if (res.status == '200') {
          message.success('提交成功');
          await handleGetContact();
          showModal.value = false;
        }
      }
    });
  };
  const handlequeryUserList = async () => {
    let { data } = await getEmployeeList();
    userList.value = data.map((item) => {
      return {
        label: item.userName + '/' + item.account,
        value: item.account,
      };
    });
  };
  handlequeryUserList();
  handleGetContact();
</script>

<style lang="less" scoped>
  .layout-page-header {
    margin-top: 20px;
    .n-select {
      min-width: 250px;
    }
  }
  .unfold-icon {
    display: flex;
    align-items: center;
    height: 100%;
    margin-left: -3px;
  }
</style>
