window.onload = async () => {
  try {
    (() => {
      let showFeedback = window.location.href.includes(
        "#/defectManager/defectConfirmDetailNew"
      );
      if (!showFeedback) {
        return;
      }
      let defectId = window.location.href.match(/.*defectId=([^&]*)/)?.[1];
      let url = window.location.href;
      let ticketNo = "";
      let isKnowledgeExist = false;

      let wiseoperAdd = (param) => {
        return fetch("https://dtse.cbg.huawei.com/board/Wiseoper/add", {
          method: "post",
          headers: {
            "Content-Type": "application/json",
          },
          body: param ? JSON.stringify(param) : null,
        }).catch((error) => {
          console.log(error);
        });
      };

      let addFloat = (ticketNo) => {
        let floarContainer = document.createElement("button");
        floarContainer.setAttribute("class", "ant-btn small");
        let floarText = document.createElement("span");
        document.body.appendChild(floarContainer);
        floarContainer.appendChild(floarText);
        floarText.innerText = "feedback";
        floarContainer.style.position = "fixed";
        floarContainer.style.minWidth = "80px";
        floarContainer.style.bottom = "20px";
        floarContainer.style.right = "20px";
        floarContainer.style.zIndex = "10002";
        floarContainer.addEventListener("click", async () => {
          wiseoperAdd({
            defectId,
            ticketNo,
            url,
            isKnowledgeExist,
          }).then(()=>{
            alert('success')
          });
        });
      };
      let options = {
        attributes: true,
        childList: true,
        subtree: true,
        attributeOldValue: true,
      };
      let callback = () => {
        ticketNo = document.querySelector(".ticketNo")?.innerText;
        if (ticketNo) {
          mutationObserver.disconnect();
          addFloat(ticketNo);
        }
      };
      var mutationObserver = new MutationObserver(callback);
      mutationObserver.observe(document.body, options);
    })();
  } catch (error) {}
};
