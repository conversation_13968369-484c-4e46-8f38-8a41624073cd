<template>
  <div class="chart-container">
    <div id="beta-chart"></div>
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, onBeforeUnmount } from 'vue';
  import * as echarts from 'echarts';
  import { getData, getEchartOptions } from './chart';

  let chart: echarts.ECharts | null = null;

  onMounted(async () => {
    try {
      const rowData = await getData();
      console.log('原始数据:', rowData);

      // 数据处理
      Object.keys(rowData).forEach((key) => {
        if (!rowData[key]) return;

        const unhandled =
          (rowData[key]['organizerReviewNum'] || 0) + (rowData[key]['featureOwnerReviewNum'] || 0);
        const finished =
          (rowData[key]['billLadderHandleNum'] || 0) + (rowData[key]['closedNum'] || 0);

        rowData[key]['unhandled'] = unhandled;
        rowData[key]['finished'] = finished;

        console.log(`${key} - 未处理: ${unhandled}, 已完成: ${finished}`);
      });

      const chartOptions = getEchartOptions(rowData);
      chart = echarts.init(document.getElementById('beta-chart'));
      chart.setOption(chartOptions);

      // 添加窗口resize监听
      window.addEventListener('resize', handleResize);
    } catch (error) {
      console.error('图表初始化失败:', error);
    }
  });

  // 处理窗口大小变化
  const handleResize = () => {
    chart?.resize();
  };

  // 组件销毁时清理
  onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize);
    chart?.dispose();
    chart = null;
  });
</script>

<style scoped>
  .chart-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 16px;
  }

  #beta-chart {
    height: 300px;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
</style>
