import { ewpService as service } from '@/utils/axios';
/**
 * 查询抽检工单列表
 * @param params 查询参数
 */
export function getSpotCheckOrders(params: any) {
  return service.post('/management/spot-check/orders', params);
}

/**
 * 新增或编辑抽检工单
 * @param data 工单数据
 */
export function saveSpotCheckOrder(data: any) {
  return service.post('/management/spot-check/order', data);
}

/**
 * 删除抽检工单
 * @param id 工单ID
 */
export function deleteSpotCheckOrder(id: number) {
  return service.delete(`/management/spot-check/order/${id}`);
} 
