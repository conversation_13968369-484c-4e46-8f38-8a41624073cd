import { ref, getCurrentInstance } from 'vue';

type EventPayload = {
  row: any;
  mode: 'ewp' | 'pm';
};

// 创建全局事件存储
const handlers = ref<Record<string, Function[]>>({});

export function useEventBus() {
  // 获取当前组件实例（可选）
  const instance = getCurrentInstance();

  // 订阅事件
  const on = (event: string, callback: Function) => {
    if (!handlers.value[event]) {
      handlers.value[event] = [];
    }
    handlers.value[event].push(callback);

    // 如果在组件中使用，组件卸载时自动清理
    if (instance) {
      const { onUnmounted } = instance.appContext.config.globalProperties;
      if (onUnmounted) {
        onUnmounted(() => {
          off(event, callback);
        });
      }
    }
  };

  // 取消订阅
  const off = (event: string, callback?: Function) => {
    if (!handlers.value[event]) return;

    if (!callback) {
      handlers.value[event] = [];
    } else {
      handlers.value[event] = handlers.value[event].filter((cb) => cb !== callback);
    }
  };

  // 触发事件
  const emit = (event: string, payload?: any) => {
    if (!handlers.value[event]) return;

    handlers.value[event].forEach((callback) => {
      callback(payload);
    });
  };

  return {
    on,
    off,
    emit,
  };
}
