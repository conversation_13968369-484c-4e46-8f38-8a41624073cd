import { h } from 'vue';
import { NButton, NTag } from 'naive-ui';
import { BasicColumn } from '@/components/Table';

export interface ListData {
  id: string;
  orderId: string;
  description: string;
  status: string;
  severity: string;
  dtsOrder: string;
  dtsStatus: string;
  submitter: string;
  submitTime: string;
  clusterTime: string;
  // ... 其他字段可以根据需要添加
}

const severityColorMap = {
  提示: 'info',
  一般: 'default',
  严重: 'error',
  致命: 'error',
};

export const creatColumns = (): BasicColumn<ListData>[] => [
  {
    type: 'selection',
    fixed: 'left'
  },
  {
    title: '问题单号',
    key: 'orderId',
    resizable: true,
    width: 200,
    ellipsis: {
      tooltip: true,
    },
    fixed: 'left',
    render: (row) => {
      return h(
        NTag,
        {
          type: 'info',
          border: false,
          onClick: async () => {
            const { orderId } = row;
            window.open(
              `https://betaclub.huawei.com/beta/products/#/ProblemDetail?quesId=${orderId}`,
              '_blank'
            );
          },
        },
        {
          default: () => row.orderId,
        }
      );
    },
  },
  {
    title: '简要描述',
    key: 'description',
    resizable: true,
    width: 400,
    ellipsis: {
      tooltip: true,
    },
    fixed: 'left',
  },
  {
    title: '聚类时间',
    key: 'clusterTime',
    width: 110,
    render: (rowData) => {
      if (rowData.clusterTime) {
        return String(rowData.clusterTime).split('T')[0].replaceAll('-', '/');
      }
      return '';
    },
  },
  {
    title: '分类',
    key: 'kind',
    resizable: true,
    width: 200,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '问题单状态',
    key: 'status',
    resizable: true,
    width: 200,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '问题严重程度',
    key: 'severity',
    resizable: true,
    width: 200,
    ellipsis: {
      tooltip: true,
    },
    render(record) {
      if (!record.severity) {
        return h('span', 'NA');
      } else {
        return h(
          NTag,
          {
            type: severityColorMap[record.severity] || 'default',
            bordered: false,
          },
          {
            default: () => record.severity,
          }
        );
      }
    },
  },
  {
    title: 'DTS单号',
    key: 'dtsOrder',
    resizable: true,
    width: 200,
    ellipsis: {
      tooltip: true,
    },
    render: (row) => {
      if (row.dtsOrder){
        return h(
          NTag,
          {
            type: 'info',
            border: false,
            onClick: async () => {
              const { dtsOrder } = row;
              window.open(
                `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${dtsOrder}`,
                '_blank'
              );
            },
          },
          {
            default: () => row.dtsOrder,
          }
        );
      }else {
        return '';
      }
    },
  },
  {
    title: 'DTS状态',
    key: 'dtsStatus',
    width: 200,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '提单人',
    key: 'submitter',
    width: 200,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '提单时间',
    key: 'submitTime',
    width: 200,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    render: (row) => {
      let text = '';
      if (row.submitTime.includes('T')) {
        text = row.submitTime.replace(/T/g, ' ');
        text = text.split('.')[0];
      }
      return h('span', text);
    },
  },
  {
    title: '自动关联DTS单',
    key: 'autoLinkDtsUrl',
    resizable: true,
    width: 200,
    ellipsis: {
      tooltip: true,
    },
    render: (row) => {
      if(row.dtsOrder){
        return h(
          NTag,
          {
            type: 'info',
            border: false,
            onClick: async () => {
              const { dtsOrder } = row;
              window.open(
                `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${dtsOrder}`,
                '_blank'
              );
            },
          },
          {
            default: () => row.dtsOrder,
          }
        );
      }else{
        return '';
      }
    },
  },
];

// 定义查询表单项
export const searchFormItems = [
  {
    field: 'orderId',
    label: 'Beta单号',
    component: 'Input',
  },
  {
    field: 'clusterTime',
    label: '聚类时间',
    component: 'Date',
  },
  {
    field: 'status',
    label: 'Beta单状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '特性责任人评审', value: '特性责任人评审' },
        { label: '研发系统跟踪（DTS）', value: '研发系统跟踪（DTS）' },
        { label: '提示', value: '提示' },
        { label: 'NA', value: 'NA' },
      ],
    },
  },
  {
    field: 'severity',
    label: 'Beta严重程度',
    component: 'Select',
    componentProps: {
      options: [
        { label: '一般', value: '一般' },
        { label: '严重', value: '严重' },
        { label: '提示', value: '提示' },
        { label: '致命', value: '致命' },
      ],
    },
  },
  {
    field: 'dtsOrder',
    label: 'DTS单号',
    component: 'Input',
  },
  // {
  //   field: 'dtsStatus',
  //   label: 'DTS单状态',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       { label: '问题提交人填写', value: '问题提交人填写' },
  //       { label: '测试(项目)经理审核', value: '测试(项目)经理审核' },
  //       { label: '开发人员实施修改', value: '开发人员实施修改' },
  //       { label: 'CCB方案审核', value: 'CCB方案审核' },
  //       { label: '审核人员审核修改', value: '审核人员审核修改' },
  //       { label: 'CMO归档', value: 'CMO归档' },
  //       { label: '测试经理组织测试', value: '测试经理组织测试' },
  //       { label: '测试人员回归测试', value: '测试人员回归测试' },
  //       { label: '关闭', value: '关闭' },
  //       { label: '挂起', value: '挂起' },
  //     ],
  //   },
  // },
];
