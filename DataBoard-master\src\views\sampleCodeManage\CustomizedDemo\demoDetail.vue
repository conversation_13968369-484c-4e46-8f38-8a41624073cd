<template>
  <div>
    <!-- 按钮 -->
    <div style="display: flex; justify-content: flex-end; align-items: center">
      <!-- 显示指定外发人按钮：用户是外发管理员且状态为已通过或待外发 -->
      <n-button
        v-if="
          userInfo.roles.includes(UserRoleEnum.SAMPLE_OUTGOING_ADMIN) &&
          [CodeWishReviewStatusEnum.PASSED, CodeWishReviewStatusEnum.TO_BE_SENT_OUT].includes(
            formData.reviewStatus
          )
        "
        type="primary"
        style="margin-right: 10px"
        secondary
        @click.stop="transMethod('handleOut', formData)"
      >
        {{
          formData.reviewStatus === CodeWishReviewStatusEnum.PASSED ? '指定外发人' : '更换外发人'
        }}
      </n-button>
      <!--  显示外发按钮：用户是外发责任人或外发管理人员，且状态为待外发 -->
      <n-button
        v-if="
          (userInfo.label === formData.outGoingPerson ||
            userInfo.roles.includes(UserRoleEnum.SAMPLE_OUTGOING_ADMIN)) &&
          formData.reviewStatus === CodeWishReviewStatusEnum.TO_BE_SENT_OUT
        "
        type="primary"
        style="margin-right: 10px"
        secondary
        @click.stop="transMethod('handleUpdtate', formData)"
      >
        外发
      </n-button>
      <!--  显示托管按钮 -->
      <n-button
        v-if="
          !!formData.custodian &&
          formData.custodian.includes(userInfo.label) &&
          formData.reviewStatus === CodeWishReviewStatusEnum.TO_BE_HOSTED
        "
        type="primary"
        style="margin-right: 10px"
        secondary
        @click.stop="transMethod('handleHosting', formData)"
      >
        托管
      </n-button>
      <!-- 显示编辑和提交评审按钮：用户是提交人且状态为待提交 -->
      <div
        v-if="props.curPermissionMap['isSubmitter'] && props.curPermissionMap['isToBeSubmitted']"
      >
        <n-button
          type="primary"
          style="margin-right: 10px"
          secondary
          @click.stop="transMethod('handleEdit', formData)"
        >
          编辑
        </n-button>
        <n-button
          v-if="props.curPermissionMap['isSubmitter'] && props.curPermissionMap['isToBeSubmitted']"
          type="primary"
          style="margin-right: 10px"
          secondary
          @click.stop="transMethod('handleSubmit', formData)"
        >
          提交
        </n-button>
      </div>
      <!-- 显示认领按钮：demoAuthor不存在且状态为待认领或开发中 -->
      <n-button
        v-if="
          !formData.demoAuthor &&
          [
            CodeWishReviewStatusEnum.TO_BE_CLAIMED,
            CodeWishReviewStatusEnum.UNDER_DEVELOPMENT,
          ].includes(formData.reviewStatus)
        "
        style="margin-right: 10px"
        secondary
        type="info"
        @click.stop="transMethod('handleClaimin', formData)"
      >
        认领
      </n-button>
      <!--  -->
      <n-button
        v-if="
          (props.curPermissionMap['isSubmitter'] && props.curPermissionMap['isToBeSubmitted']) ||
          isAdmin
        "
        style="text-align: end"
        secondary
        type="error"
        @click.stop="transMethod('handleDele', formData)"
      >
        <template #icon>
          <n-icon><DeleteOutlined /></n-icon>
        </template>
        删除
      </n-button>
    </div>
    <n-form :model="formData" label-width="120px" label-placement="left">
      <!-- 详情 -->
      <n-grid :cols="24" :x-gap="24">
        <n-form-item-gi
          v-for="(item, index) in columns"
          :key="index"
          :span="item.key === 'demoDescription' ? 24 : 12"
          :label="`${item.title}：`"
        >
          <span v-if="item.key === 'demoName'"> {{ `${formData.demoName}- ${formData.id}` }}</span>
          <span v-else-if="item.key === 'imgUrlList'">
            {{ formData.imgUrlList?.length ? renderImg(formData) : '--' }}
          </span>
          <span v-else-if="item.key === 'reviewStatus'">
            {{ renderStatus(formData) || '--' }}
          </span>
          <span v-else-if="item.key === 'reviewers'">
            {{ formData.reviewers?.join('，') || '--' }}
          </span>
          <span v-else-if="item.key === 'linkDemo'">
            <div v-if="formData.linkDemoName">
              <a style="cursor: pointer; color: #1890ff;" @click="jumpPage('SampleCode', formData.linkDemoId)">{{formData.linkDemoName}}</a>
              <n-button dashed size="tiny" type="info" @click="transMethod('showLinkDemo', formData)" style="margin-left: 15px;">详情</n-button>
              <br/>
              <span>{{ formData.linkDemoId }}</span>
            </div>
            <span v-else>--</span>
          </span>
          <span style="margin-left: 6px" v-else> {{ formData[item.key] || '--' }}</span>
        </n-form-item-gi>
      </n-grid>

      <!-- 评审记录 -->
      <!--显示需求评审按钮：用户是管理员但不是提交人，且状态为待接纳  -->
      <div style="display: flex; margin-top: 12px; justify-content: space-between">
        <span style="font-size: 18px; color: #303133; font-weight: bold"> 需求评审记录</span>
        <n-button
          v-if="
            !props.curPermissionMap['isSubmitter'] &&
            (isAdmin || (!!formData.reviewer && formData.reviewer === userInfo.label)) &&
            props.curPermissionMap['isToBeAccepted']
          "
          secondary
          type="info"
          @click.stop="handleReviewClick()"
        >
          需求评审
        </n-button>
      </div>
      <n-data-table
        :columns="columnsHistory"
        :data="tableDataHistoryRev"
        style="margin-top: 12px"
        :single-line="false"
        max-height="700px"
      >
      </n-data-table>
      <div style="display: flex; margin-top: 12px; justify-content: space-between">
        <span style="font-size: 18px; color: #303133; font-weight: bold">交付评审记录</span>
        <n-button
          v-if="
            ((!!formData.reviewers?.length && formData.reviewers.includes(userInfo.label)) ||
              isAdmin) &&
            formData.reviewStatus === CodeWishReviewStatusEnum.TO_BE_REVIEWED
          "
          secondary
          type="info"
          @click.stop="transMethod('handleRev', formData, 0)"
        >
          交付评审
        </n-button>
        <!-- 显示交付按钮：用户是交付责任人，且状态为开发中 -->
        <n-button
          v-if="props.curPermissionMap['isDemoAuthor'] && props.curPermissionMap['isToBeDelivered']"
          secondary
          type="info"
          @click.stop="transMethod('handleSampleCode', formData)"
        >
          交付
        </n-button>
      </div>
      <n-data-table
        :columns="columnsHistory"
        :data="tableDataHistoryAcp"
        style="margin-top: 12px"
        :single-line="false"
        max-height="700px"
      >
      </n-data-table>
    </n-form>
  </div>
</template>

<script lang="ts" setup>
import { DeleteOutlined } from '@vicons/antd';

import { CodeWishReviewStatusEnum } from '@/enums/CodeWishReviewStatusEnum';
const props = defineProps<{
  columnsHistory: columnsHistory;
  formData: formData;
  tableDataHistoryRev: tableDataHistoryRev;
  tableDataHistoryAcp: tableDataHistoryAcp;
  curPermissionMap: curPermissionMap;
  isAdmin: isAdmin;
}>();
import { UserRoleEnum } from '@/enums/UserRoleEnum';
import { getUserLabel, useUserStore } from '@/store/modules/user';
const userInfo = useUserStore().getUserInfo;
const emit = defineEmits([
  'handleSubmit',
  'handleEdit',
  'handleSampleCode',
  'handleRev',
  'handleClaimin',
  'handleOut',
  'handleUpdtate',
  'handleHosting',
  'handleDele',
  'handleReviewClick',
  'showLinkDemo',
]);
import { REQUIREMENT_REVIEW_STATUS_MAP } from './consts';
import { DataTableColumns, NCarousel, NEllipsis, NImage, NButton } from 'naive-ui';
import { h, ref, computed, onMounted, nextTick, defineProps } from 'vue';
import { COLUMNS } from '@/views/sampleCodeManage/CustomizedDemo/columns';
import emptyImage from '@/assets/images/emptyImage.png';
import { useRouter } from 'vue-router';

const router = useRouter()

enum ModelEnum {
  REVIEW,
  ACCEPT,
}
type tableDataHistory = any;
const transMethod = (name, formData) => {
  emit(name, formData);
};
const jumpPage = (name: string, linkDemoId: string) => {
  router.push({
    name,
    query: {
      id: linkDemoId,
    },
  });
}
const renderImg = (row) => {
  return h(
    NCarousel,
    {
      mousewheel: true,
      direction: 'vertical',
      dotPlacement: 'right',
      style: 'width: 100%; height: 60px',
    },
    row.imgList.map((item) =>
      h(NImage, {
        src: item.url!,
        fallbackSrc: emptyImage,
        style: 'height: 60px!important',
      })
    )
  );
};
const renderStatus = (row) => {
  return REQUIREMENT_REVIEW_STATUS_MAP[row.reviewStatus!];
};
const handleReviewClick = (formData) => {
  emit('handleReviewClick', props.formData, ModelEnum.ACCEPT);
};

const columns: DataTableColumns<formData> = COLUMNS.map((item: any) => {
  return item;
});
</script>
<style lang="less" scoped>
.app-title {
  font-size: 24px;
  color: #303133;
  margin: 0;
  padding: 10px 0;
  flex-grow: 1;
}
</style>
