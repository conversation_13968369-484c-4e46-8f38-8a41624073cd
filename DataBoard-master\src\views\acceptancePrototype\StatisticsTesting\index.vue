<template>
  <div>
    <n-card style="margin-bottom: 12px">
      <n-form label-placement="left" label-width="100px" label-align="left">
        <div v-if="collapse">
          <n-grid x-gap="20" :cols="4" style="margin-bottom: 12px">
            <n-gi>
              <n-form-item label="最新验收结论">
                <n-select
                  v-model:value="filters.lastTestConclusionList"
                  :options="testResList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="应用层级">
                <n-select
                  v-model:value="filters.label"
                  :options="labelList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="优先级">
                <n-select
                  v-model:value="filters.functionEnhancementPriorityList"
                  :options="functionEnhancementPriorityList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="是否服务公司">
                <n-select
                  v-model:value="filters.isServiceCompanyUndertakes"
                  :options="isServiceList"
                  clearable
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="轮次">
                <n-select
                  v-model:value="filters.roundList"
                  :options="roundsList"
                  filterable
                  clearable
                  multiple
                />
              </n-form-item>
            </n-gi>
            <n-gi>
              <n-form-item label="日期">
                <n-date-picker v-model:value="filters.dateList" type="daterange" clearable />
              </n-form-item>
            </n-gi>
             <n-gi span="20" style="margin-top:20px">
               <n-checkbox id="onlyTop2K" v-model:checked="filters.onlyTop2k"  @update:checked="handleGetTestingRes">只看top2k应用</n-checkbox>
             </n-gi>
          </n-grid>
        </div>
      </n-form>
      <n-space justify="center">
        <n-button secondary strong type="default" @click="handleResetFilter">重置 </n-button>

        <n-button secondary strong type="primary" @click="handleGetTestingRes">查询 </n-button>
        <n-button type="primary" icon-placement="right" @click="collapse = !collapse">
          <template #icon>
            <n-icon size="14" class="unfold-icon" v-if="collapse">
              <UpOutlined />
            </n-icon>
            <n-icon size="14" class="unfold-icon" v-else>
              <DownOutlined />
            </n-icon>
          </template>
          {{ collapse ? '收起' : '展开' }}
        </n-button>
      </n-space>
    </n-card>
    <n-card>
      <n-tabs type="line" animated>
        <n-tab-pane name="体验测试责任人" tab="体验测试责任人">
          <n-data-table
            :columns="columns"
            :data="tableData"
            :single-line="false"
            :row-props="rowProps"
            :loading="loading"
            :max-height="tableHeight"
            :scroll-x="scrollX"
            remote
          />
        </n-tab-pane>
        <n-tab-pane name="所属代表处" tab="所属代表处">
          <n-data-table
            :columns="provinceColumns"
            :data="provinceTableData"
            :single-line="false"
            :row-props="provinceRowProps"
            :loading="loading"
            :max-height="tableHeight"
            :scroll-x="scrollX"
            remote
          />
        </n-tab-pane>
      </n-tabs>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
  import { Add, Refresh, Close, GridOutline } from '@vicons/ionicons5';
  import {
    NIcon,
    NButton,
    NTag,
    useDialog,
    UploadCustomRequestOptions,
    useMessage,
  } from 'naive-ui';
  import { h, ref, reactive, onBeforeMount } from 'vue';
  import { useRouter } from 'vue-router';
  import {
    formatDateTime,
    getDefaultProblemHandleFilters,
    getDatesBetween,
    getLastIndex,
    testResList,
    isServiceList,
    functionEnhancementPriorityList,
    labelList,
  } from './index';

  import { getTestingRes } from '@/api/dataview/applicationAcceptance';
  import { DownOutlined, UpOutlined } from '@vicons/antd';
  import { getColumns } from '@/api/dataview/applicationAcceptance';
  import { debug } from 'console';

  const router = useRouter();
  const collapse = ref(false);
  const filters = ref(getDefaultProblemHandleFilters());
  const roundsList = ref([]);
  const loading = ref(false);
  const message = useMessage();
  const tableHeight = ref(570);
  const tableData = ref([]);
  const scrollX = ref(0);
  const columns = ref([]);
  // 所属代表处视角数据
  const provinceTableData = ref([]);
  const provinceColumns = ref([]);

  const pagination = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 50],
    itemCount: 0,
    prefix({ itemCount }) {
      return `总条数 ${itemCount}`;
    },
    onChange: (page: number) => {
      pagination.page = page;
      handleGetTestingRes();
    },
    onUpdatePageSize: (pageSize: number) => {
      pagination.pageSize = pageSize;
      pagination.page = 1;
      handleGetTestingRes();
    },
  });

  //获取oi单列表
  const handleGetTestingRes = async () => {
    loading.value = true;
    const searchData = { ...filters.value };
    if (filters.value.dateList) {
      const [start, end] = filters.value.dateList;
      searchData.testEndDateStart = formatDateTime(start, 'yyyy-MM-dd');
      searchData.testEndDateEnd = formatDateTime(end, 'yyyy-MM-dd');
    }
    searchData.dateList = undefined;
    let res = await getTestingRes(searchData);
    if (res.status == '200') {
      message.success('获取成功');
      getPersonView(res);
      getProvinceView(res);
      loading.value = false;
    }
  };

  /**
   * 获取体验测试责任人视角列表
   */
  const getPersonView = (res) => {
    let set = new Set();
    let arr = [];
    let currentAll = {};
    res.data.forEach((item, index) => {
      let obj = {
        userName: item.userName,
        province: item.province,
        ...item['dateCountMap'],
      };
      Object.keys(item['dateCountMap']).forEach((key) => {
        set.add(key);
      });
      //计算每天总计
      if (currentAll.userName) {
        if (item.userName === currentAll.userName) {
          Object.keys(item['dateCountMap']).forEach((key) => {
            currentAll[key] = currentAll[key] ? obj[key] + currentAll[key] : obj[key];
          });
        } else {
          arr.push(currentAll);
          currentAll = { ...obj, province: '小计' };
        }
      } else {
        currentAll = { ...obj, province: '小计' };
      }

      arr.push(obj);
      if (index == res.data.length - 1) {
        arr.push(currentAll);
      }
    });
    let allObj = {
      userName: '总计',
    };
    arr.forEach((item, index) => {
      //计算每行总计
      let all = 0;
      Object.keys(item).forEach((key) => {
        if (typeof item[key] === 'number') {
          all += item[key];
        }
      });
      arr[index]['all'] = all;

      //计算总结栏
      Object.keys(item).forEach((key) => {
        if (typeof item[key] === 'number' && item['province'] === '小计') {
          allObj[key] = allObj[key] ? allObj[key] + item[key] : item[key];
        }
      });
    });
    let col = [
      {
        title: '体验测试责任人',
        key: 'userName',
        width: 120,
        fixed: 'left',
      },
      {
        title: '所属代表处',
        key: 'province',
        width: 120,
        fixed: 'left',
      },
    ];
    let syncCol = [...set]
      .map((key) => {
        return {
          title: key,
          key: key,
          width: 120,
        };
      })
      .sort((a, b) => +new Date(a.key) - +new Date(b.key));

    columns.value = [
      ...col,
      ...syncCol,
      {
        title: '总计',
        key: 'all',
        width: 120,
        className: 'allCol',
        fixed: 'right',
      },
    ];
    scrollX.value = columns.value.length * 120;
    arr.reverse();
    let sortArr = [];
    //总计降序
    arr.forEach((item) => {
      let findRes = sortArr.findIndex((array) => array.find((e) => e.userName === item.userName));
      if (findRes >= 0) {
        sortArr[findRes].push(item);
      } else {
        sortArr.push([item]);
      }
    });
    sortArr.sort((a, b) => b[0]['all'] - a[0]['all']);
    Object.keys(sortArr).forEach((key) => {
        sortArr[key].sort((a, b) => b['all'] - a['all']);
    })
    arr = sortArr.flat();
    arr.forEach((item, index) => {
      if (arr.findIndex((e) => e['userName'] === item['userName']) < index) {
        arr[index]['userName'] = '';
      }
    });
    arr.push(allObj);
    tableData.value = arr;
    if (!res.data.length) {
      tableData.value = [];
    }
  };

  /**
   * 获取所属代表处视角单列表
   */
  const getProvinceView = async (res) => {
    const provinceObj = {};
    const columnsSet = new Set();
    res.data.forEach((v) => {
      // 获取时间列
      Object.keys(v['dateCountMap']).forEach((key) => {
        columnsSet.add(key);
      });
      // 根据所属代表处分类
      if (!provinceObj[v.province]) {
        provinceObj[v.province] = [];
      }
      provinceObj[v.province].push(v);
    });
    // 处理时间列并排序
    let syncCol = [...columnsSet]
      .map((key) => {
        return {
          title: key,
          key: key,
          width: 120,
        };
      })
      .sort((a, b) => +new Date(a.key) - +new Date(b.key));
    provinceColumns.value = [
      {
        title: '所属代表处',
        key: 'province',
        width: 120,
        fixed: 'left',
      },
      {
        title: '体验测试责任人',
        key: 'userName',
        width: 120,
        fixed: 'left',
      },
      ...syncCol,
      {
        title: '总计',
        key: 'all',
        width: 120,
        className: 'allCol',
        fixed: 'right',
      },
    ];
    // 获取每所属代表处数据并统计
    const sortArr = [];
    const allObj = { province: '总计', all: 0 };
    Object.keys(provinceObj).forEach((key) => {
      const subtotal = {
        province: key,
        userName: '小计',
        all: 0,
      };
      const tempArr = provinceObj[key].map((v) => {
        const userData = {
          province: '',
          userName: v.userName,
          ...v.dateCountMap,
          all: 0,
        };
        Object.keys(v.dateCountMap).forEach((date) => {
          if (!subtotal[date]) {
            subtotal[date] = 0;
          }
          subtotal[date] += userData[date];
          subtotal.all += userData[date]; // 所属代表处每行的总计
          userData.all += userData[date]; // 人每行的总计
          // 获取总计行数据
          if (!allObj[date]) {
            allObj[date] = 0;
          }
          allObj[date] += userData[date];
          allObj.all += userData[date];
        });
        return userData;
      });
      sortArr.push([subtotal, ...tempArr]);
    });
    // 根据总计排序
    sortArr.sort((a, b) => b[0].all - a[0].all);
     Object.keys(sortArr).forEach((key) => {
        sortArr[key].sort((a, b) => b['all'] - a['all']);
    })
    provinceTableData.value = sortArr.flat().concat([allObj]);
    if (!res.data.length) {
      provinceTableData.value = [];
    }
  };
  const handleGetColumns = async () => {
    let { data } = await getColumns();
    roundsList.value = data['rounds']
      .sort((a, b) => a - b)
      .map((item) => {
        return {
          label: Number(item),
          value: Number(item),
        };
      });
  };
  const rowProps = (row) => {
    return {
      class: row.province === '小计' ? 'rowGreen' : row.userName === '总计' ? 'rowBlue' : '',
    };
  };
  const provinceRowProps = (row) => {
    return {
      class: row.userName === '小计' ? 'rowGreen' : row.province === '总计' ? 'rowBlue' : '',
    };
  };
  const handleResetFilter = () => {
    filters.value = getDefaultProblemHandleFilters();
    handleGetTestingRes();
  };
  // 获取屏幕分辨率宽度
  var screenWidth = screen.width;

  if (screenWidth > 1920) {
    tableHeight.value = 870;
  }
  handleGetTestingRes();
  handleGetColumns();
</script>

<style lang="less" scoped>
  .layout-page-header {
    margin-top: 20px;
    .n-select {
      min-width: 250px;
    }
  }
  .unfold-icon {
    display: flex;
    align-items: center;
    height: 100%;
    margin-left: -3px;
  }
  // :deep(.n-data-table .n-data-table-tr .allCol) {
  //   background-color: rgb(244, 244, 248) !important;
  // }

  // :deep(.n-data-table .n-data-table-thead .n-data-table-th) {
  //   background-color: #fdf0db !important;
  //   color: #f0a020 !important;
  // }
  :deep(.n-data-table .n-data-table-td) {
    background-color: transparent !important;
    padding: 6px !important;
  }
  :deep(.n-data-table .rowBlue .n-data-table-td) {
    background-color: #ddedfd !important;
    color: #3390f1 !important;
  }
  :deep(.n-data-table .rowGreen .n-data-table-td) {
    background-color: #daf0e4 !important;
    color: #18a058 !important;
  }
</style>
