<template>
  <div>
    <n-card style="margin-bottom: 12px">
      <n-form label-placement="left" label-width="60px" label-align="left">
        <n-grid x-gap="21" :cols="3" style="margin-bottom: 12px">
          <n-gi>
            <n-form-item label="工号">
              <n-input v-model:value="filters.userNo" clearable />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="分组">
              <n-select v-model:value="filters.tagList" :options="tagList" multiple clearable />
            </n-form-item>
          </n-gi>
          <n-gi>
            <n-form-item label="时间">
              <n-date-picker v-model:value="filters.time" type="daterange" clearable />
            </n-form-item>
          </n-gi>
        </n-grid>
        <div v-if="collapse">
          <n-space> </n-space>
        </div>
      </n-form>
      <n-space justify="center">
        <n-button secondary strong type="default" @click="handleResetFilter">重置 </n-button>
        <n-button secondary strong type="primary" @click="handleUpdateFilter">查询 </n-button>
      </n-space>
    </n-card>
    <n-card>
      <div>
        <div class="layout-page-header">
          <n-space>
            <n-button secondary strong type="warning" @click="hanldeGetIrList()">
              <template #icon>
                <n-icon>
                  <Refresh />
                </n-icon>
              </template>
              刷新
            </n-button>

            <!-- <n-input
            v-model:value="irNumber"
            placeholder="输入IR单号按回车键筛选"
            style="width: 200px"
            @keypress="handleIrNumber"
          /> -->
            <!-- <n-select
            v-model:value="filterUserListValue"
            multiple
            filterable
            placeholder="选择当前处理人"
            :options="filterUserList"
          >
          </n-select>
          <n-button secondary strong type="warning" @click="handleFilterPeople()"> 确认 </n-button> -->
          </n-space>
        </div>
        <n-data-table
          v-model:checked-row-keys="checkedRowKeys"
          :columns="columns"
          :data="tableData"
          :pagination="pagination"
          :max-height="5000"
          virtual-scroll
          :on-update:checked-row-keys="onUpdateChecked"
          remote
          :on-update:sorter="handleSorter"
          style="margin-top: 20px"
        />
      </div>
    </n-card>
  </div>
</template>

<script lang="ts" setup>
  import { Add, Refresh, Close, GridOutline } from '@vicons/ionicons5';
  import { useMessage } from 'naive-ui';
  import { h, ref, reactive, onBeforeMount } from 'vue';
  import { modelTemplate, tagList, formatDateTime } from './index';

  import { DownOutlined, UpOutlined } from '@vicons/antd';
  import { login, getIrList } from '@/api/dataview/irPlugin';

  const filters = ref({ ...modelTemplate });
  const collapse = ref(false);

  const message = useMessage();

  const tableData = ref([]);

  const rules = ref({
    warnTime: {
      required: true,
    },
    appName: {
      required: true,
    },
    desc: {
      required: true,
    },
    level: {
      required: true,
    },
    status: {
      required: true,
    },
  });
  const col = [
    {
      title: '工号',
      key: 'userNo',
      width: 40,
      resizable: true,
    },
    {
      title: '姓名',
      key: 'userName',
      width: 40,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '组别',
      key: 'tag',
      width: 40,
      resizable: true,
    },
    {
      title: '登录次数',
      key: 'loginCount',
      width: 40,
      resizable: true,
    },
    {
      title: '搜索次数',
      key: 'searchCount',
      width: 40,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '标记次数',
      key: 'markCount',
      width: 40,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '知识修改次数',
      key: 'correctionCount',
      width: 40,
      resizable: true,
    },
    {
      title: '准确率',
      key: 'hitRate',
      width: 40,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '命中率',
      key: 'realHitRate',
      width: 40,
      resizable: true,
      ellipsis: {
        tooltip: true,
      },
    },
  ];
  const columns = ref(col);

  const pagination = reactive({
    page: 1,
    pageSize: 10,
    showSizePicker: true,
    pageSizes: [10, 20, 50],
    itemCount: 0,
    prefix({ itemCount }) {
      return `总条数 ${itemCount}`;
    },
    onChange: (page: number) => {
      pagination.page = page;
      hanldeGetIrList();
    },
    onUpdatePageSize: (pageSize: number) => {
      pagination.pageSize = pageSize;
      pagination.page = 1;
      hanldeGetIrList();
    },
  });
  const initModel = () => {
    filters.value = { ...modelTemplate };
  };

  const handleUpdateFilter = () => {
    pagination.page = 1;
    hanldeGetIrList();
  };
  const handleResetFilter = () => {
    initModel();
    hanldeGetIrList();
  };
  const unfoldToggle = () => {
    collapse.value = !collapse.value;
  };

  const hanldeGetIrList = async () => {
    let { data } = await getIrList({
      page: pagination.page,
      size: pagination.pageSize,
      from: 'ir',
      startTime: filters.value.time
        ? formatDateTime(filters.value.time[0], 'yyyy-MM-dd HH:mm:ss')
        : '',
      endTime: filters.value.time
        ? formatDateTime(filters.value.time[1], 'yyyy-MM-dd HH:mm:ss')
        : '',
      tagList: filters.value.tagList,
      userNo: filters.value.userNo || null
    });
    console.log(data);
    
    message.success('获取成功');
    pagination.itemCount = data.totalCount;
    tableData.value = data.dataList;
  };

  hanldeGetIrList();
</script>

<style lang="less" scoped>
  .layout-page-header {
    margin-top: 20px;
    .n-select {
      min-width: 250px;
    }
  }
  .unfold-icon {
    display: flex;
    align-items: center;
    height: 100%;
    margin-left: -3px;
  }
</style>
