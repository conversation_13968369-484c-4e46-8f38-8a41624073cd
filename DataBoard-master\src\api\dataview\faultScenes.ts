import { adminService as service } from '@/utils/axios';

// Base URL for the API
const BASE_URL = '/fault-tree';

// 创建新的故障树节点
export async function createFaultTree(data: any) {
  const response = await service.post(BASE_URL, data);
  return response;
}

// 更新节点信息
export async function updateFaultTree(data: any) {
  const response = await service.put(`${BASE_URL}/${data.id}`, data);
  return response;
}

// 删除节点
export async function deleteFaultTree(id: string) {
  const response = await service.delete(`${BASE_URL}/${id}`);
  return response;
}

// 获取故障树列表
export async function getFaultTreeList() {
  const response = await service.get(BASE_URL);
  return response;
}

// 获取单个节点详情
export async function getFaultTreeDetail(id: string) {
  const response = await service.get(`${BASE_URL}/${id}`);
  return response;
}

// 搜索故障树节点
export async function searchFaultTreeDetail(data: any) {
  const response = await service.post(`${BASE_URL}/search`, data);
  return response;
}
// 类型定义
export interface Category {
  _id: string;
  name: string;
  description: string;
  parentId: string | null;
  level: number;
  sort: number;
  createdAt: string;
  updatedAt: string;
  children?: Category[];
}

export interface CreateCategoryDto {
  name: string;
  description: string;
  parentId?: string | null;
  level: number;
  sort: number;
}

export interface UpdateCategoryDto {
  name?: string;
  description?: string;
  sort?: number;
}

/**
 * 获取分类树结构
 */
export function getCategoryTreeApi() {
  return service.get<Category[]>('/categories/tree');
}

/**
 * 获取分类列表
 * @param level 可选参数，指定获取特定层级的分类
 */
export function getCategoryListApi(level?: number) {
  return service.get<Category[]>('/categories', {
    params: { level },
  });
}

/**
 * 获取分类详情
 * @param id 分类ID
 */
export function getCategoryByIdApi(id: string) {
  return service.get<Category>(`/categories/${id}`);
}

/**
 * 获取子分类
 * @param id 父分类ID
 */
export function getChildrenCategoriesApi(id: string) {
  return service.get<Category[]>(`/categories/${id}/children`);
}

/**
 * 创建分类
 * @param data 分类数据
 */
export function addCategoryApi(data: CreateCategoryDto) {
  return service.post<Category>('/categories', data);
}

/**
 * 更新分类
 * @param id 分类ID
 * @param data 更新数据
 */
export function updateCategoryApi(id: string, data: UpdateCategoryDto) {
  return service.put<Category>(`/categories/${id}`, data);
}

/**
 * 删除分类
 * @param id 分类ID
 */
export function deleteCategoryApi(id: string) {
  return service.delete<Category>(`/categories/${id}`);
}

/**
 * 将后端数据转换为树形控件所需的数据结构
 */
export function transformToTreeData(categories: Category[]) {
  return categories.map((category) => ({
    key: category._id,
    label: category.name,
    level: category.level,
    sort: category.sort,
    description: category.description,
    parentId: category.parentId,
    children: category.children ? transformToTreeData(category.children) : undefined,
  }));
}

/**
 * 将树形控件数据转换为后端所需的数据结构
 */
export function transformToApiData(treeNode: any): CreateCategoryDto {
  return {
    name: treeNode.label,
    description: treeNode.description,
    parentId: treeNode.parentId,
    level: treeNode.level,
    sort: treeNode.sort || 0,
  };
}

// 添加新的 API 函数
export function markFaultTreeUseful(id: string) {
  return service.post(`/fault-tree/${id}/useful`);
}
