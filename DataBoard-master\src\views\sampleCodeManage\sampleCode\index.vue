<template>
  <div ref="iframeBox" id="iframe-box">
    <iframe
      :src="sampleCodeWebsit"
      frameborder="0"
      name="iframe"
      id="iframe"
      class="iframe-container"
    ></iframe>
  </div>
</template>

<style scoped>
  .iframe-container {
    width: 100%;
    height: calc(100vh - 120px);
  }
</style>
<script setup lang="ts">
import { useRoute } from 'vue-router';
import { ref } from 'vue'
import { SAMPLE_CODE_WEBSITE_URL } from "@/views/sampleCodeManage/consts";
const route = useRoute()

let sampleCodeWebsit = ref(SAMPLE_CODE_WEBSITE_URL)
const id = ref(route.query.id as string);
if (id.value) {
  sampleCodeWebsit = ref(`${SAMPLE_CODE_WEBSITE_URL}?id=${id.value}`)
}
</script>
