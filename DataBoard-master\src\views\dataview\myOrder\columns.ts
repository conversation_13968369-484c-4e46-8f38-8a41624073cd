import { h, ref, computed } from 'vue';
import { NButton, NTag, NSelect, NTooltip, useMessage, NSpace } from 'naive-ui';
import { BasicColumn } from '@/components/Table';
import { useUserStore } from '@/store/modules/user';
import { ewpService as service } from '@/utils/axios';
import { formatDateTime, getRiskLevel, getStatusColor } from '@/utils';
import { updateWorkOrder } from '@/api/dataview/appState';
import { getModules } from '@/api/dataview/myOrder';
import {
  getPersonOptionsNameAccount,
  getPersonOptionsOnlyName,
} from '@/views/dataview/personManage/staffCommonUtils';
import { DTS_HANDLE_TAG } from '@/views/dataview/personManage/tagContant';
const userStore = useUserStore();
const userInfo: any = userStore.getUserInfo || {};
const isOnlyViewRole = ref(false);
const checkUserRole = () => {
  const userRoles = userInfo.roles || [];
  isOnlyViewRole.value =
    userRoles.includes('5_only_view_public_opinion') &&
    !userRoles.includes('5_admin') &&
    !userRoles.includes('super_admin');
};
checkUserRole();
export interface ListData {
  id: string;
  orderId: string;
  appName: string;
  top: string;
  represent: string;
  ewpOwner: string;
  errorStatus: string;
  errorStatusSubdivision: string;
  severity: string;
  status: string;
  currentHandler: string;
  irOrderId: string;
  irOrderStatus: string;
  description: string;
  createTime: string;
  creator: string;
  lastReviewModifyTime: string;
  riskScore: number;
  dtsType: number;
  opinionTotal: number;
  progress: string;
  implementModifyTime: string;
  dtsCloseTime: string;
  lastRegressionTestTime: string;
  commonProblemMark: string;
  opinionLevel?: 'A' | 'B' | 'C' | 'D';
  vip: string;
}

const severityColorMap = {
  一般: 'default',
  严重: 'error', // 将 'warning' 改为 'error'
  提示: 'info',
  致命: 'error',
};

export const isVipClosedOptions = [
  { label: '是', value: 1 },
  { label: '否', value: 0 },
];

export const resOptions = [
  { label: '重大舆情', value: '重大舆情' },
  { label: '风险问题', value: '风险问题' },
  { label: 'Purax问题', value: 'Purax问题' },
  { label: '需走单', value: '需走单' },
  { label: '非ewp问题单', value: '非ewp问题单' },
  { label: '未更新', value: '未更新' },
  { label: '需降级', value: '需降级' },
  { label: '新单待定界', value: '新单待定界' },
  { label: '已更新', value: '已更新' },
  { label: '挂起', value: '挂起' },
];

const dtsAttributionOptions = [
  { label: '三方应用', value: '三方应用' },
  { label: '系统问题', value: '系统问题' },
  { label: '非问题', value: '非问题' },
  { label: '功能', value: '功能' },
  { label: 'UX', value: 'UX' },
  { label: '性能', value: '性能' },
  { label: '稳定性', value: '稳定性' },
  { label: '安全隐私', value: '安全隐私' },
  { label: '关闭', value: '关闭' },
  { label: '挂起', value: '挂起' },
];

const dtsStatusOptions = [
  { label: '问题提交人填写', value: '问题提交人填写' },
  { label: '测试（项目）经理审核', value: '测试（项目）经理审核' },
  { label: '开发人员实施修改', value: '开发人员实施修改' },
  { label: 'CCB方案审核', value: 'CCB方案审核' },
  { label: '审核人员审核修改', value: '审核人员审核修改' },
  { label: 'CMO归档', value: 'CMO归档' },
  { label: '测试经理组织测试', value: '测试经理组织测试' },
  { label: '测试人员回归测试', value: '测试人员回归测试' },
  { label: '关闭', value: '关闭' },
  { label: '挂起', value: '挂起' },
];

const ewpStatusOptions = [
  { label: '待分单', value: '待分单' },
  { label: '待定界', value: '待定界' },
  { label: '待锁定', value: '待锁定' },
  { label: '待审核', value: '待审核' },
  { label: '待归档', value: '待归档' },
  { label: '待回归', value: '待回归' },
  // { label: '已锁定', value: '已锁定' },
  { label: '已闭环', value: '已闭环' },
];

const severityOptions = [
  { label: '提示', value: '提示' },
  { label: '一般', value: '一般' },
  { label: '严重', value: '严重' },
  { label: '致命', value: '致命' },
];

// 添加模块选项常量
export const MODULE_OPTIONS = [
  { value: '4796-UX体验', label: '4796-UX体验' },
  { value: '4796-一多适配', label: '4796-一多适配' },
  { value: '4796-功能故障', label: '4796-功能故障' },
  { value: '4796-功能完善', label: '4796-功能完善' },
  { value: '4796-稳定性', label: '4796-稳定性' },
  { value: '4796-性能功耗', label: '4796-性能功耗' },
  { value: '4796-生态丰富', label: '4796-生态丰富' },
  { value: '4796-其他', label: '4796-其他' },
];

// 添加关闭类型选项常量
export const CLOSE_TYPE_OPTIONS = [
  { value: '问题解决关闭', label: '问题解决关闭' },
  { value: '重复问题关闭', label: '重复问题关闭' },
  { value: '非问题关闭', label: '非问题关闭' },
  { value: 'CCB评审不解决关闭', label: 'CCB评审不解决关闭' },
];

// 添加默认的原因分析模板
export const DEFAULT_ANALYSIS_TEMPLATE = `【定位定界】
原因分析：
修改建议：

1是否新版本已经解决，标记详细版本号：
应用名称：
应用包名：NA
应用版本：
2. 是否为应用自身问题：是
3. 是否需要提IR工单跟踪：是
4. 复现概率：必现
5. 处理策略：推动应用修复，推动给出计划修复时间`;

// 添加问题归属选项常量
export const PROBLEM_ATTRIBUTION_OPTIONS = [
  { value: '系统问题', label: '系统问题' },
  { value: '三方应用', label: '三方应用' },
  { value: '非问题', label: '非问题' },
];

// Add type for selection column
interface SelectionColumn {
  type: 'selection';
  disabled?: (row: any) => boolean;
  multiple?: boolean;
}

export const getColumnKeyMap = () => {
  return columnKeyMap;
};

// 创建一个通用的样式对象
const commonStyle = {
  style: {
    width: '300px',
    textAlign: 'left',
  },
  clearable: true,
  placeholder: '请输入',
};

// 为 Select 组件创建特定样式
const selectStyle = {
  ...commonStyle,
  style: {
    ...commonStyle.style,
  },
  filterable: true,
  placeholder: '请选择',
};

// 在文件顶部添加
interface ExtendedColumn extends BasicColumn<ListData> {
  show?: boolean;
}

interface ExtendedSelectionColumn extends SelectionColumn {
  show?: boolean;
  defaultShow?: boolean;
}
const message = useMessage();
initESpace();
function initESpace() {
  eSpaceCtrl.init();
  eSpaceCtrl.ready(function (data) {
    console.log('link success');
    //这里做一些加载时做的操作，比如是否让之后的openIMChatClick()可以调用
  });
  eSpaceCtrl.error(() => {
    console.log('link error');
  });
  eSpaceCtrl.on('user-status-change', function (status) {
    // @TODO监听用户的变化
  });
}
function openIMChatClick(expId) {
  const expIds = expId.split(',');
  expIds.forEach((id) => {
    eSpaceCtrl.showImDialog(id, function (err) {});
  });
}
export const moduleOptions = ref([]);
const moduleOptionsValues = ref<[string]>([]);
const fecthModules = async () => {
  const res = await getModules();
  moduleOptions.value = res.map((item: string) => {
    moduleOptionsValues.value.push(item);
    return {
      label: item,
      value: item,
    };
  });
  moduleOptions.value.push({
    label: '空',
    value: '-',
  });
};
fecthModules();

export const baseShowColumns = [
  'orderId',
  'appName',
  'module',
  'description',
  'knowId',
  'firstImplementModifyTime',
  'ccbSolutionReviewTime',
  'deviceType',
  'appLevel',
  'weightValue',
  'deviceCategory',
  'ewpOwner',
  'maxLevel',
  'currentLevel',
  'dtseOwner',
  'riskScore',
  'priority',
  'opinionTotal',
  'severity',
  'status',
  'ewpStatus',
  'dtsAttribution',
  'solvePlanDate',
  'dtsCloseTime',
  'createTime',
  'currentHandler',
  'irOrderId',
  'overdueFlag',
  'irOrderStatus',
  'irStatus',
  'commonProblemMark',
  'lastCreateTime',
  'delimitDays',
  'repairTime',
  'lastProblemProcess',
  'conclusion',
];

// 添加只读角色可见的列
export const readOnlyVisibleColumns = [
  'orderId',
  'appName',
  'priority',
  'module',
  'orderSource',
  'represent',
  'description',
  'conclusion',
  'firstImplementModifier',
  'deviceType',
  'ewpOwner',
  'dtseOwner',
  'weightValue',
  'riskScore',
  'opinionTotal',
  'opinionFut',
  'opinionBeta',
  'opinionNss',
  'opinionAg',
  'opinionHiview',
  'severity',
];

export const getBaseColumns = (
  isCleanMode: boolean,
  baseShow: string[],
  openDelimitDetailModal
) => {
  const baseColumn = [
    {
      type: 'selection',
      width: 50,
      disabled: (row: ListData) => row.status === '已关闭',
      multiple: true,
    },
    {
      title: '问题单号',
      key: 'orderId',
      width: 170,
      show: baseShow.includes('orderId'),
      render: (row) => {
        return h('div', { style: { position: 'relative', display: 'inline-block' } }, [
          h(
            NTag,
            {
              type: 'info',
              bordered: false,
              onClick: async () => {
                const { orderId } = row;
                window.open(
                  `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${orderId}`,
                  '_blank'
                );
              },
            },
            { default: () => row.orderId }
          ),
          row.vip
            ? h(
                NTag,
                {
                  type: row.vip === 'svip' ? 'error' : 'warning',
                  size: 'tiny',
                  bordered: false,
                  style: {
                    position: 'absolute',
                    right: '-8px',
                    top: '-8px',
                    padding: '0 4px',
                    transform: 'scale(0.8)',
                    fontSize: '12px',
                    lineHeight: '16px',
                    minWidth: '16px',
                    textAlign: 'center',
                    zIndex: 1,
                    color: 'red',
                  },
                },
                { default: () => row.vip.toUpperCase() }
              )
            : null,
          row.firstReviewModifyTime && row.ewpStatus === '待定界'
            ? h(
                NTag,
                {
                  type: 'error',
                  size: 'tiny',
                  bordered: false,
                  style: {
                    position: 'absolute',
                    left: '-8px',
                    top: '-8px',
                    padding: '0 4px',
                    transform: 'scale(0.8)',
                    fontSize: '12px',
                    lineHeight: '16px',
                    minWidth: '16px',
                    textAlign: 'center',
                    zIndex: 1,
                  },
                },
                { default: () => '打回' }
              )
            : null,
        ]);
      },
    },
    {
      title: '应用名称',
      key: 'appName',
      resizable: true,
      width: 170,
      show: baseShow.includes('appName'),
      render: (row) => {
        return h(
          NTag,
          {
            bordered: false,
            style: {
              cursor: 'pointer',
            },
          },
          [
            // 问题单号标签
            h(
              NTag,
              {
                bordered: false,
              },
              {
                default: () => row.appName,
              }
            ),
            // TOP标记
            row.top
              ? h(
                  NTag,
                  {
                    type: 'warning',
                    size: 'tiny',
                    bordered: false,
                    style: {
                      position: 'absolute',
                      right: '-8px',
                      top: '-8px',
                      padding: '0 4px',
                      transform: 'scale(0.8)',
                      fontSize: '12px',
                      lineHeight: '16px',
                      minWidth: '16px',
                      textAlign: 'center',
                      zIndex: 1,
                      color: 'red',
                    },
                  },
                  { default: () => row.top.toUpperCase() }
                )
              : null,
          ]
        );
      },
    },
    {
      title: '优先级',
      key: 'priority',
      width: 100,
      filter: true,
      render: (row) => {
        // 如果有定界超期，显示定界超期标签
        if (row.priority === 'P0') {
          return h('div', { style: { position: 'relative', display: 'inline-block' } }, [
            h(
              NTag,
              {
                type: 'error' as any,
                bordered: false,
              },
              { default: () => row.priority }
            ),
          ]);
        }

        return h(
          NTag,
          {
            type: 'success' as any,
            bordered: false,
            style: {
              marginRight: '8px',
            },
          },
          { default: () => row.priority }
        );
      },
    },
    {
      title: '定界超期',
      key: 'delimitationOverdueTime',
      width: 150,
      sorter: true,
      render: (row) => {
        if (row.delimitationOverdueTime === '-') {
          return h(
            NTag,
            {
              type: 'success' as any,
              bordered: false,
              style: {
                marginRight: '8px',
              },
            },
            { default: () => '未超期' }
          );
        }

        // 如果有定界超期，显示定界超期标签
        if (row.delimitationOverdueTime) {
          return h('div', { style: { position: 'relative', display: 'inline-block' } }, [
            h(
              NTag,
              {
                type: 'error' as any,
                bordered: false,
              },
              { default: () => row.delimitationOverdueTime }
            ),
            // 如果有即将超期信息，在右上角显示
            row.overdueFlag
              ? h(
                  NTag,
                  {
                    type: 'warning' as any,
                    size: 'tiny',
                    bordered: false,
                    style: {
                      position: 'absolute',
                      right: '-8px',
                      top: '-8px',
                      padding: '0 4px',
                      transform: 'scale(0.8)',
                      fontSize: '12px',
                      lineHeight: '16px',
                      minWidth: '16px',
                      textAlign: 'center',
                      zIndex: 1,
                    },
                  },
                  { default: () => row.overdueFlag }
                )
              : null,
          ]);
        }

        // 如果只有即将超期信息
        if (row.overdueFlag) {
          return h(
            NTag,
            {
              type: 'warning' as any,
              bordered: false,
            },
            { default: () => row.overdueFlag }
          );
        }

        return '未超期';
      },
    },
    {
      title: '模块',
      key: 'module',
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('module'),
      width: 180,
      filterOptions: moduleOptions.value,
      filter(value, row) {
        return ~row.module.indexOf(value);
      },
      resizable: true,
      render(row) {
        return h(NSelect, {
          value: row.module,
          options: moduleOptions.value,
          size: 'small',
          style: { width: '150px' },
          disabled: isOnlyViewRole.value,
          async onUpdateValue(value) {
            console.log('value:', value);
            row.module = value;
            await updateWorkOrder({
              ...row,
              module: value,
            });
            message.success('工单更新成功');
          },
        });
      },
    },
    {
      title: '来源',
      key: 'orderSource',
      ellipsis: {
        tooltip: true,
      },
      filter: true,
      width: 180,
      filterOptions: [
        { label: 'AG', value: 'AG' },
        { label: 'BETA', value: 'BETA' },
        { label: 'FUT', value: 'FUT' },
        { label: 'NSS', value: 'NSS' },
        { label: '互联网', value: '互联网' },
        { label: '服务', value: '服务' },
        { label: '其他', value: '其他' },
        {
          label: '空',
          value: '-',
        },
      ],
      resizable: true,
      render(row) {
        return h(NSelect, {
          value: row.orderSource,
          options: [
            { label: 'AG', value: 'AG' },
            { label: 'BETA', value: 'BETA' },
            { label: 'FUT', value: 'FUT' },
            { label: 'NSS', value: 'NSS' },
            { label: '互联网', value: '互联网' },
            { label: '服务', value: '服务' },
            { label: '其他', value: '其他' },
            {
              label: '空',
              value: '-',
            },
          ],
          size: 'small',
          style: { width: '150px' },
          clearable: true,
          disabled: isOnlyViewRole.value,
          async onUpdateValue(value) {
            console.log('value:', value);
            row.orderSource = value;
            await updateWorkOrder({
              ...row,
              orderSource: value,
            });
            message.success('工单更新成功');
          },
        });
      },
    },
    // {
    //   title: '清单来源',
    //   key: 'sourceName',
    //   width: 100,
    // },
    // {
    //   title: 'TOP',
    //   key: 'top',
    //   width: 100,
    //   render: (row) => {
    //     return row.top
    //       ? h(
    //           NTag,
    //           {
    //             bordered: false,
    //           },
    //           {
    //             default: () => row.top.toUpperCase(),
    //           }
    //         )
    //       : '';
    //   },
    // },
    {
      title: '应用包名',
      key: 'appBundle',
      width: 170,
      show: baseShow.includes('appBundle'),
    },
    {
      title: '代表处',
      key: 'represent',
      width: 170,
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('represent'),
    },
    {
      title: '问题单描述',
      key: 'description',
      resizable: true,
      width: 300,
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('description'),
      render: (row) => {
        const description = isCleanMode
          ? row.description.split('】').pop() || row.description
          : row.description;
        return h('span', {}, description);
      },
    },
    {
      title: '定界结论',
      key: 'conclusion',
      resizable: true,
      width: 250,
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('conclusion'),
      render: (row, index) => {
        return h(
          'span',
          {
            justify: 'start',
            space: 10,
            onClick: async () => openDelimitDetailModal?.(row),
          },
          {
            default: () => row.conclusion,
          }
        );
      },
    },
    {
      title: '定界人',
      key: 'firstImplementModifier',
      width: 130,
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('firstImplementModifier'),
    },
    {
      title: '问题跟踪',
      key: 'issueTrackingList',
      ellipsis: {
        tooltip: true,
      },
      show: true,
      width: 180,
      filterOptions: resOptions,
      resizable: true,
      render(row) {
        return h(NSelect, {
          value: row.issueTrackingList === '' ? null : row.issueTrackingList,
          options: resOptions,
          size: 'small',
          clearable: true,
          multiple: true,
          disabled: isOnlyViewRole.value,
          style: { width: '150px' },
          async onUpdateValue(value) {
            console.log('value:', value);
            if (!value) value = '';
            row.issueTrackingList = value;
            await updateWorkOrder({
              ...row,
              issueTrackingList: value,
            });
            message.success('工单更新成功');
          },
        });
      },
    },
    {
      title: '知识id',
      key: 'knowId',
      width: 230,
      show: baseShow.includes('knowId'),
      render: (row) => {
        if (!row.knowId) {
          return null;
        }
        return h(
          NTag,
          {
            type: 'info',
            style: { cursor: 'pointer' },
            bordered: false,
            onClick: async () => {
              const { knowId } = row;
              window.open(`http://***********/knowledgeDetail/${knowId}`, '_blank');
            },
          },
          {
            default: () => row.knowId,
          }
        );
      },
    },
    {
      title: '设备型号',
      key: 'deviceType',
      width: 180,
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('deviceType'),
      render: (row) => {
        if (!row.deviceType) return;
        const deviceType = isCleanMode
          ? row.deviceType.split('(').shift() || row.deviceType
          : row.deviceType;
        return h('span', {}, deviceType);
      },
    },
    {
      title: '标签',
      key: 'appLevel',
      width: 150,
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('appLevel'),
    },
    {
      title: 'EWP责任人',
      key: 'ewpOwner',
      width: 120,
      show: baseShow.includes('ewpOwner'),
      sorter: true,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: 'DTSE',
      key: 'dtseOwner',
      width: 90,
      show: baseShow.includes('dtseOwner'),
      ellipsis: {
        tooltip: true,
      },
      render: (row) => {
        return h(
          'a',
          {
            href: '#',
            onClick: (e) => {
              e.preventDefault();
              openIMChatClick(row.dtseOwner);
            },
            style: {
              color: '#1288ff',
              textDecoration: 'none',
              cursor: 'pointer',
            },
          },
          row.dtseOwner
        );
      },
    },
    // {
    //   title: '异常状态',
    //   key: 'errorStatus',
    //   ellipsis: {
    //     tooltip: true,
    //   },
    // },
    // {
    //   title: '异常状态细分',
    //   key: 'errorStatusSubdivision',
    //   ellipsis: {
    //     tooltip: true,
    //   },
    //   show: true,
    // },

    {
      title: '机型类别',
      key: 'deviceCategory',
      width: 100,
      show: baseShow.includes('deviceCategory'),
    },
    {
      title: '权重值',
      key: 'weightValue',
      width: 100,
      show: baseShow.includes('weightValue'),
      sorter: true,
    },
    {
      title: '风险指数',
      key: 'riskScore',
      sorter: true,
      show: baseShow.includes('riskScore'),
      width: 100,
      render(row) {
        return h(
          NTag,
          {
            style: {
              marginRight: '6px',
            },
            type: getStatusColor(row.riskScore),
            bordered: false,
          },
          {
            default: () => row.riskScore && row.riskScore.toFixed(),
          }
        );
      },
    },
    {
      title: '声量值',
      key: 'opinionTotal',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('opinionTotal'),
    },
    {
      title: 'FUT声量',
      key: 'opinionFut',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      defaultShow: false,
      show: baseShow.includes('opinionFut'),
    },
    {
      title: 'BetaClub声量',
      key: 'opinionBeta',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      defaultShow: false,
      show: baseShow.includes('opinionBeta'),
    },
    {
      title: 'NSS声量',
      key: 'opinionNss',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      defaultShow: false,
      show: baseShow.includes('opinionNss'),
    },
    {
      title: 'AG声量',
      key: 'opinionAg',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      defaultShow: false,
      show: baseShow.includes('opinionAg'),
    },
    {
      title: '严重程度',
      key: 'severity',
      width: 120,
      show: baseShow.includes('severity'),
      filterOptions: severityOptions,
      filter: true,
      multiple: true,
      render(record) {
        return h(
          NTag,
          {
            type: severityColorMap[record.severity] || 'default',
            bordered: false,
          },
          {
            default: () => record.severity,
          }
        );
      },
    },
    {
      title: 'DTS单状态',
      key: 'status',
      ellipsis: {
        tooltip: true,
      },
      width: 130,
      show: baseShow.includes('status'),
      filterOptions: dtsStatusOptions,
      filter: true,
      multiple: true,
    },
    {
      title: 'EWP状态',
      key: 'ewpStatus',
      ellipsis: {
        tooltip: true,
      },
      width: 100,
      show: baseShow.includes('ewpStatus'),
      filter: true,
      filterOptions: ewpStatusOptions,
    },
    {
      title: '问题归属',
      key: 'dtsAttribution',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('dtsAttribution'),
      filter: true,
      filterOptions: dtsAttributionOptions,
    },
    {
      title: '当前处理人',
      key: 'currentHandler',
      width: 130,
      sorter: true,
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('currentHandler'),
    },
    {
      title: '当前级别',
      key: 'currentLevel',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('currentLevel'),
    },
    {
      title: '最高级别',
      key: 'maxLevel',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('maxLevel'),
    },
    {
      title: 'VIP侧闭环',
      key: 'isVipClosed',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      show: true,
      render(row) {
        if (!row.vip) {
          return '不涉及';
        }
        return h(NSelect, {
          value: row.isVipClosed ? row.isVipClosed : 0,
          options: isVipClosedOptions,
          size: 'small',
          clearable: true,
          disabled: isOnlyViewRole.value,
          style: { width: '75px' },
          async onUpdateValue(value) {
            console.log('value:', value);
            await updateWorkOrder({
              ...row,
              isVipClosed: value,
            });
            row.isVipClosed = value;
            message.success('工单更新成功');
          },
        });
      },
    },
    {
      title: '关联IR单号',
      key: 'irOrderId',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('irOrderId'),
    },
    {
      title: 'IR单状态',
      width: 100,
      key: 'irOrderStatus',
      show: baseShow.includes('irOrderStatus'),
    },
    {
      title: 'IR单解决状态',
      width: 120,
      key: 'irStatus',
      show: baseShow.includes('irStatus'),
    },
    {
      title: '专项标签',
      key: 'commonProblemMark',
      width: 100,
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('commonProblemMark'),
      render(record) {
        if (!record.commonProblemMark) return;
        return h(
          NTag,
          {
            type: 'default',
          },
          {
            default: () => record.commonProblemMark,
          }
        );
      },
    },
    {
      title: '问题发生时间',
      key: 'occurDate',
      show: baseShow.includes('occurDate'),
      width: 110,
      render(row) {
        if (!row.occurDate) return;
        return formatDateTime(row.occurDate);
      },
    },
    {
      title: '问题创建时间',
      key: 'createTime',
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('createTime'),
      width: 110,
      render(row) {
        if (!row.createTime) return;
        return formatDateTime(row.createTime);
      },
    },
    {
      title: '状态改变时间',
      key: 'statusChangeTime',
      width: 110,
      show: baseShow.includes('statusChangeTime'),
      render(row) {
        if (!row.statusChangeTime) return;
        return formatDateTime(row.statusChangeTime);
      },
    },
    {
      title: '定界耗时',
      key: 'delimitDays',
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('delimitDays'),
      width: 110,
    },
    {
      title: '修复耗时',
      key: 'repairTime',
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('repairTime'),
      width: 110,
    },
    {
      title: '感知耗时',
      key: 'senseTime',
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('senseTime'),
      width: 110,
    },
    {
      title: '验证上架耗时',
      key: 'verifyTime',
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('verifyTime'),
      width: 110,
    },
    {
      title: '计划解决时间',
      key: 'solvePlanDate',
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('solvePlanDate'),
      width: 110,
      render(row) {
        if (!row.solvePlanDate) return;
        return row.solvePlanDate.replaceAll('/', '-');
      },
    },

    {
      title: '定界时间',
      key: 'firstImplementModifyTime',
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('firstImplementModifyTime'),
      width: 110,
      render(row) {
        if (!row.firstImplementModifyTime) return;
        return formatDateTime(row.firstImplementModifyTime);
      },
    },
    {
      title: '锁定时间',
      key: 'ccbSolutionReviewTime',
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('ccbSolutionReviewTime'),
      width: 110,
      render(row) {
        if (!row.ccbSolutionReviewTime) return;
        return formatDateTime(row.ccbSolutionReviewTime);
      },
    },
    {
      title: '进展更新时间',
      key: 'lastCreateTime',
      width: 110,
      show: baseShow.includes('lastCreateTime'),
      render(row) {
        return formatDateTime(row.lastCreateTime);
      },
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '最新进展',
      key: 'lastProblemProcess',
      width: 110,
      show: baseShow.includes('lastProblemProcess'),
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: 'DTS关闭时间',
      key: 'dtsCloseTime',
      ellipsis: {
        tooltip: true,
      },
      show: baseShow.includes('dtsCloseTime'),
      width: 110,
      render(row) {
        if (!row.dtsCloseTime) return;
        return formatDateTime(row.dtsCloseTime);
      },
    },
  ];
  const maps = {};
  baseColumn.forEach((column) => {
    if (column.type !== 'selection' && column.title) {
      maps[column.title] = column.key;
    }
  });
  return {
    baseColumn,
    maps,
  };
};

export const columnKeyMap = getBaseColumns(true, []).maps;

export const allColumnList = Object.keys(columnKeyMap);

// 修改 createColumns 的返回类型
export const createColumns = (
  showAction = true,
  handleEdit?: (row: ListData) => void,
  handleProblem?: (row: ListData) => void,
  problemProgress?: (row: ListData) => void,
  openCheckEditModal?: (row: ListData, state: boolean) => void,
  openDelimitDetailModal?: (row: ListData) => void,
  fetchData,
  sendMessage,
  isCleanMode = true,
  isCheckState = true,
  tabCheckState = false,
  isOnlyViewRole = false
): any => {
  const baseColumns: any = getBaseColumns(
    isCleanMode,
    isOnlyViewRole ? readOnlyVisibleColumns : baseShowColumns,
    openDelimitDetailModal
  ).baseColumn;
  if (showAction && !isOnlyViewRole) {
    baseColumns.push({
      title: '操作',
      key: 'actions',
      fixed: 'right',
      width: tabCheckState ? 180 : 250,
      render: (row) => {
        const statusMap = {
          待定界: {
            action: '问题定界',
            type: 'info',
          },
          待输出知识: {
            action: '问题定界',
            type: 'info',
          },
          待锁定: {
            action: '问题锁定',
            type: 'warning',
          },
          待审核: {
            action: '问题审核',
            type: 'success',
          },
          待归档: {
            action: '问题归档',
            type: 'success',
          },
          待回归: {
            action: '查看定界',
            type: 'success',
          },
          已闭环: {
            action: '查看定界',
            type: 'success',
          },
        };

        // 判断是否为新流程，只有新流程才显示对应操作按钮
        const isNewProcess = row.isNewProcess === true;

        return h('div', { style: { display: 'flex', gap: '8px' } }, [
          !tabCheckState &&
            isNewProcess &&
            statusMap[row.ewpStatus] &&
            h(
              NButton,
              {
                size: 'small',
                type: statusMap[row.ewpStatus].type,
                onClick: () => handleProblem?.(row),
              },
              { default: () => statusMap[row.ewpStatus].action }
            ),
          // 不是新流程或没有对应状态，显示"去DTS操作"按钮
          !tabCheckState &&
            !isNewProcess &&
            h(
              NButton,
              {
                size: 'small',
                type: 'error',
                onClick: () => {
                  window.open(
                    `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${row.orderId}`,
                    '_blank'
                  );
                },
              },
              { default: () => '跳转DTS' }
            ),
          !tabCheckState &&
            h(
              NButton,
              {
                size: 'small',
                onClick: () => problemProgress?.(row),
              },
              { default: () => '问题进展' }
            ),
          !tabCheckState &&
            h(
              NButton,
              {
                strong: true,
                tertiary: true,
                size: 'small',
                onClick: () => handleEdit?.(row),
              },
              { default: () => '编辑' }
            ),
          tabCheckState &&
            isCheckState &&
            h(
              NButton,
              {
                size: 'small',
                type: 'info',
                ghost: true,
                width: 120,
                onClick: async () => {
                  await service.post('/management/ewpOrder/review/process', {
                    dtsOrder: row.orderId,
                    reviewer: row.currentHandler,
                    nextHandler: 'lichengfang 30052277',
                    reviewResult: 'pls',
                    status: 1,
                  });
                  sendMessage.success('审核通过成功，已完成工单流转');
                  fetchData();
                },
              },
              { default: () => '通过' }
            ),
          tabCheckState &&
            isCheckState &&
            h(
              NButton,
              {
                size: 'small',
                type: 'error',
                ghost: true,
                width: 120,
                onClick: async () => {
                  await service.post('/management/ewpOrder/review/process', {
                    dtsOrder: row.orderId,
                    reviewer: row.currentHandler,
                    nextHandler: row.lastImplementModifier,
                    reviewResult: 'pls',
                    status: 0,
                  });
                  sendMessage.success('审核驳回成功，已返回给定位责任人');
                  fetchData();
                },
              },
              { default: () => '驳回' }
            ),
          tabCheckState &&
            !isCheckState &&
            h(
              NButton,
              {
                size: 'small',
                type: 'info',
                ghost: true,
                width: 120,
                onClick: async () => openCheckEditModal?.(row, true),
              },
              { default: () => '通过' }
            ),
          tabCheckState &&
            !isCheckState &&
            h(
              NButton,
              {
                size: 'small',
                type: 'error',
                ghost: true,
                width: 120,
                onClick: async () => openCheckEditModal?.(row, false),
              },
              { default: () => '驳回' }
            ),
        ]);
      },
    });
  }
  // 从localStorage获取表格设置
  const columnsSetting = JSON.parse(
    window.localStorage.getItem('__4796_admin_dts_columns_setting') || '{}'
  );

  // 应用表格设置
  baseColumns.forEach((col) => {
    if ('key' in col) {
      // 如果是只读模式，只显示readOnlyVisibleColumns中的列
      if (isOnlyViewRole) {
        col.show = readOnlyVisibleColumns.includes(col.key);
      } else if (col.defaultShow === false && !columnsSetting[col.key]) {
        // 如果有默认不显示的设置，且localStorage中没有对应的设置，则使用默认设置
        col.show = false;
      } else if (columnsSetting[col.key]) {
        // 否则使用localStorage中的设置
        col.show = columnsSetting[col.key].show !== 'false';
      }
    }
  });

  // 过滤掉 show 为 false 的列
  const filteredColumns = baseColumns.filter((col) => {
    if ('type' in col && col.type === 'selection') return !isOnlyViewRole; // 只读模式不显示选择框
    return col.show !== false;
  });

  return filteredColumns;
};

// 修改 getAppLevelOptions 函数，添加返回类型
const getAppLevelOptions = async (): Promise<{ label: string; value: string }[]> => {
  try {
    const data = await service.get('/management/appInfo/label');
    if (data && Array.isArray(data)) {
      return data.map((item: string) => ({
        label: item,
        value: item,
      }));
    }
    return [];
  } catch (error) {
    console.error('Failed to fetch app labels:', error);
    return [];
  }
};

// 创建一个 ref 来存储选项
const appLabelOptions = ref<{ label: string; value: string }[]>([]);
const appLevelOptions = ref([]);
const getEwpOwner = async () => {
  try {
    const response = await service.get('/management/appInfo/appLevel');
    if (response && Array.isArray(response)) {
      appLevelOptions.value = response.map((item) => ({
        label: item,
        value: item,
      }));
    } else {
      appLevelOptions.value = [];
    }
  } catch (error) {
    console.error('Error fetching app levels:', error);
    appLevelOptions.value = [];
  }
};
// 初始化加载选项
const initAppLabelOptions = async () => {
  appLabelOptions.value = await getAppLevelOptions();
  getEwpOwner();
};

// 初始化调用
initAppLabelOptions();

const dtsAccountOptions = await getPersonOptionsOnlyName(DTS_HANDLE_TAG);

export const dtsNameAccountOptions = await getPersonOptionsNameAccount(DTS_HANDLE_TAG);

export const getSearchFormItems = () => [
  {
    field: 'orderId',
    label: '问题单号',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'appName',
    label: '应用名称',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'vip',
    label: 'vip',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      multiple: true,
      options: [
        { label: 'vip', value: 'vip' },
        { label: 'svip', value: 'svip' },
      ],
    },
  },
  {
    field: 'top',
    label: 'TOP',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: [
        { label: 'TOP2000', value: 'TOP2000' },
        { label: 'TOP38', value: 'TOP38' },
      ],
    },
  },
  {
    field: 'priority',
    label: '优先级',
    component: 'Select',
    isMultiple: true,
    componentProps: {
      ...selectStyle,
      multiple: true,
      options: [
        { label: 'P0', value: 'P0' },
        { label: 'P1', value: 'P1' },
        { label: 'P2', value: 'P2' },
      ],
    },
  },
  {
    field: 'overdueFlags',
    label: '超期状态',
    component: 'Select',
    isMultiple: true,
    componentProps: {
      ...selectStyle,
      multiple: true,
      options: [
        { label: '未超期', value: '未超期' },
        { label: '即将超期', value: '即将超期' },
        { label: '定界超期', value: '定界超期' },
      ],
    },
  },
  {
    label: '模块',
    field: 'module',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: moduleOptions.value,
      multiple: true,
    },
  },
  {
    field: 'description',
    label: '问题单描述',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'issueTrackingList',
    label: '问题跟踪',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      multiple: true,
      options: resOptions,
    },
  },
  {
    field: 'knowId',
    label: '知识id',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'deviceType',
    label: '设备型号',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'appLevel',
    label: '标签',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      multiple: true,
      options: appLevelOptions.value,
    },
  },
  {
    field: 'ewpOwner',
    label: 'EWP责任人',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: dtsAccountOptions,
    },
  },
  // {
  //   field: 'weightValueRange',
  //   label: '权重值',
  //   component: 'InputGroup',
  //   componentProps: {
  //     ...commonStyle,
  //   },
  // },
  {
    field: 'severity',
    label: '严重程度',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      multiple: true,
      options: severityOptions,
    },
  },
  {
    field: 'status',
    label: 'DTS单状态',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      multiple: true,
      options: dtsStatusOptions,
    },
  },
  {
    field: 'ewpStatus',
    label: 'EWP状态',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      multiple: true,
      options: ewpStatusOptions,
    },
  },
  {
    field: 'dtsAttribution',
    label: '问题归属',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: dtsAttributionOptions,
    },
  },

  {
    field: 'currentHandler',
    label: '当前处理人',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'isVipClosed',
    label: 'vip侧闭环',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      multiple: true,
      options: isVipClosedOptions,
    },
  },
  {
    field: 'maxLevel',
    label: '最高级别',
    component: 'Select',
    isMultiple: true,
    componentProps: {
      ...selectStyle,
      options: [
        { label: 'L1', value: 'L1' },
        { label: 'L2', value: 'L2' },
      ],
    },
  },
  {
    field: 'irOrderStatus',
    label: 'IR单状态',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: [
        { label: '已提单', value: '已提单' },
        { label: '未提单', value: '未提单' },
      ],
    },
  },
  {
    field: 'irStatus',
    label: 'IR单解决状态',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      multiple: true,
      options: [
        { label: '已解决', value: '已解决' },
        { label: '未解决', value: '未解决' },
        { label: '处理中', value: '处理中' },
        { label: '待验证', value: '待验证' },
        { label: '已接纳', value: '已接纳' },
        { label: '不接纳', value: '不接纳' },
      ],
    },
  },
  /*{
    field: 'commonProblemMark',
    label: '专项标签',
    component: 'Select',
    componentProps: {
      ...selectStyle,
      options: [
        { label: '登录', value: '登录' },
        { label: '稳定性', value: '稳定性' },
        { label: '性能功耗', value: '性能功耗' },
        { label: '体验断裂', value: '体验断裂' },
        { label: '无', value: '' },
      ],
    },
  },*/
  {
    field: 'solvePlanDate',
    label: '计划解决时间',
    component: 'Input',
    componentProps: commonStyle,
  },
  {
    field: 'createTime',
    label: '问题创建时间',
    component: 'DateRangePicker',
  },
  {
    field: 'firstImplementModifyTime',
    label: '定界时间',
    component: 'DateRangePicker',
  },
  {
    field: 'firstImplementModifier',
    label: '定界人',
    component: 'Input',
    componentProps: commonStyle,
  },
  // {
  //   field: 'dtsType',
  //   label: '问题类型',
  //   component: 'Select',
  //   componentProps: {
  //     ...selectStyle,
  //     options: [
  //       { label: '应用缺失', value: 0 },
  //       { label: '功能完善', value: 1 },
  //     ],
  //   },
  // },
  // {
  //   field: 'sourceName',
  //   label: '清单来源',
  //   component: 'Select',
  //   componentProps: {
  //     ...selectStyle,
  //     multiple: true,
  //     options: [
  //       { label: '代表处', value: '代表处' },
  //       { label: '互联网', value: '互联网' },
  //       { label: '行业系统部', value: '行业系统部' },
  //     ],
  //   },
  // },

  // {
  //   field: 'represent',
  //   label: '代表处',
  //   component: 'Input',
  //   componentProps: commonStyle,
  // },
  // {
  //   field: 'errorStatus',
  //   label: '异常状态',
  //   component: 'Select',
  //   componentProps: {
  //     ...selectStyle,
  //     options: [
  //       { label: 'A 意愿类', value: 'A 意愿类' },
  //       { label: 'B 技术类', value: 'B 技术类' },
  //       { label: 'C 正常类', value: 'C 正常类' },
  //       { label: 'D 人力类', value: 'D 人力类' },
  //       { label: 'E 时间类', value: 'E 时间类' },
  //       { label: 'F 激励类', value: 'F 激励类' },
  //     ],
  //   },
  // },
  // {
  //   field: 'errorStatusSubdivision',
  //   label: '异常状态细分',
  //   component: 'Select',
  //   componentProps: {
  //     ...selectStyle,
  //     options: [
  //       { label: 'A-1 意愿类：无意愿开发', value: 'A-1 意愿类：无意愿开发' },
  //       { label: 'A-2 意愿类：有意愿无计划', value: 'A-2 意愿类：有意愿无计划' },
  //       { label: 'A-3 意愿类：开发中(完）无意愿上架', value: 'A-3 意愿类：开发中(完）无意愿上架' },
  //       { label: 'A-4 意愿类：3月底无法上架', value: 'A-4 意愿类：3月底无法上架' },
  //       {
  //         label: 'B-1 技术类：应用开发依赖第三方SDK未提供',
  //         value: 'B-1 技术类：应用开发依赖第三方SDK未提供',
  //       },
  //       { label: 'B-2 技术类：依赖系统技术解决', value: 'B-2 技术类：依赖系统技术解决' },
  //       { label: 'B-3 技术类：依赖微信/钉钉/飞书', value: 'B-3 技术类：依赖微信/钉钉/飞书' },
  //       { label: 'E-1 时间类：开发中未提交验收', value: 'E-1 时间类：开发中未提交验收' },
  //       {
  //         label: 'E-2 时间类：核心功能已开发完成正在验收',
  //         value: 'E-2 时间类：核心功能已开发完成正在验收',
  //       },
  //       {
  //         label: 'E-3 时间类：已验收完成正在修改问题',
  //         value: 'E-3 时间类：已验收完成正在修改问题',
  //       },
  //       { label: 'E-4 时间类：验收通过正在申请上架', value: 'E-4 时间类：验收通过正在申请上架' },
  //       { label: 'F-1 激励类：需要申请ISV人力激励', value: 'F-1 激励类：需要申请ISV人力激励' },
  //       { label: 'F-2 激励类：需要申请TMT激励', value: 'F-2 激励类：需要申请TMT激励' },
  //       { label: 'G 其他', value: 'G 其他' },
  //     ],
  //   },
  // },
];
