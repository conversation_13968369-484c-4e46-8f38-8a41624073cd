import { h, ref } from 'vue';
import { NTag, NInput, NSelect, NButton, NSpace } from 'naive-ui';
import type { DataTableColumns } from 'naive-ui';
import { Top38Item } from './types';

// 根据状态返回不同颜色的标签
function renderStatus(row) {
  const statusMap = {
    已解决: { type: 'success', color: '#18a058', textColor: '#fff' },
    处理中: { type: 'warning', color: '#f0a020', textColor: '#fff' },
    待解决: { type: 'error', color: '#d03050', textColor: '#fff' },
    已关闭: { type: 'info', color: '#2080f0', textColor: '#fff' },
  };

  const status = row.status || '待解决';
  const config = statusMap[status] || { type: 'default', color: '#747a8a', textColor: '#fff' };

  return h(
    NTag,
    {
      type: config.type,
      size: 'small',
      color: config.color,
      bordered: false,
      style: {
        padding: '0 8px',
        fontWeight: 500,
      },
    },
    { default: () => status }
  );
}

// 根据优先级返回不同类型的标签
function renderPriority(row) {
  const priorityMap = {
    极高: { type: 'error', color: '#d03050', textColor: '#fff' },
    高: { type: 'warning', color: '#f0a020', textColor: '#fff' },
    中: { type: 'info', color: '#2080f0', textColor: '#fff' },
    低: { type: 'success', color: '#18a058', textColor: '#fff' },
  };

  const priority = row.priority || '中';
  const config = priorityMap[priority] || { type: 'default', color: '#747a8a', textColor: '#fff' };

  return h(
    NTag,
    {
      type: config.type,
      size: 'small',
      color: config.color,
      bordered: false,
      style: {
        padding: '0 8px',
        fontWeight: 500,
      },
    },
    { default: () => priority }
  );
}

// 表格内编辑状态
export const editingRow = ref<string | number | null>(null);
export const editingCell = ref<string | null>(null);
export const editingValue = ref<any>(null);

// 开始编辑单元格
export function startEditing(rowId: string | number, cellKey: string, value: any) {
  editingRow.value = rowId;
  editingCell.value = cellKey;
  editingValue.value = value;
}

// 保存编辑的值
export function saveEditing(onSave: (rowId: string | number, cellKey: string, value: any) => void) {
  if (editingRow.value && editingCell.value) {
    onSave(editingRow.value, editingCell.value, editingValue.value);
    cancelEditing();
  }
}

// 取消编辑
export function cancelEditing() {
  editingRow.value = null;
  editingCell.value = null;
  editingValue.value = null;
}

// 可编辑文本单元格渲染函数
export function renderEditableCell(
  key: string,
  onSave: (rowId: string | number, cellKey: string, value: any) => void
) {
  return (row: Top38Item) => {
    const isEditing = editingRow.value === row.id && editingCell.value === key;

    if (isEditing) {
      return h(
        NSpace,
        { align: 'center', justify: 'space-between' },
        {
          default: () => [
            h(NInput, {
              value: editingValue.value,
              onUpdateValue: (v) => {
                editingValue.value = v;
              },
              size: 'small',
              style: { width: '80%' },
            }),
            h(
              NSpace,
              { size: 'small' },
              {
                default: () => [
                  h(
                    NButton,
                    {
                      size: 'tiny',
                      type: 'primary',
                      onClick: () => saveEditing(onSave),
                    },
                    { default: () => '保存' }
                  ),
                  h(
                    NButton,
                    {
                      size: 'tiny',
                      onClick: () => cancelEditing(),
                    },
                    { default: () => '取消' }
                  ),
                ],
              }
            ),
          ],
        }
      );
    }

    return h(
      'div',
      {
        style: 'cursor: pointer; padding: 0 4px',
        /*onClick: () => {
          startEditing(row.id!, key, row[key]);
        },*/
      },
      row[key] || '-'
    );
  };
}

// 可编辑文本单元格渲染函数
export function renderDtsTicketCell(
  key: string,
  onSave: (rowId: string | number, cellKey: string, value: any) => void
) {
  return (row: Top38Item) => {
    const isEditing = editingRow.value === row.id && editingCell.value === key;

    if (isEditing) {
      return h(
        NSpace,
        { align: 'center', justify: 'space-between' },
        {
          default: () => [
            h(NInput, {
              value: editingValue.value,
              onUpdateValue: (v) => {
                editingValue.value = v;
              },
              size: 'small',
              style: { width: '80%' },
            }),
            h(
              NSpace,
              { size: 'small' },
              {
                default: () => [
                  h(
                    NButton,
                    {
                      size: 'tiny',
                      type: 'primary',
                      onClick: () => saveEditing(onSave),
                    },
                    { default: () => '保存' }
                  ),
                  h(
                    NButton,
                    {
                      size: 'tiny',
                      onClick: () => cancelEditing(),
                    },
                    { default: () => '取消' }
                  ),
                ],
              }
            ),
          ],
        }
      );
    }

    if (row[key] && row[key].length > 0) {
      const arr = row[key].split(',');
      const nTags = [];
      arr.forEach((ele) => {
        nTags.push(
          h(
            NTag,
            {
              style: {
                margin: '2px 2px',
                cursor: 'pointer',
              },
              type: 'info',
              border: false,
              onClick: async () => {
                window.open(
                  `https://dts-szv.clouddragon.huawei.com/DTSPortal/ticket/${ele}`,
                  '_blank'
                );
              },
            },
            {
              default: () => ele,
            }
          )
        );
      });
      return h('div', {}, nTags);
    }
    return h(
      'div',
      {
        style: 'cursor: pointer; padding: 0 4px',
        /*onClick: () => {
          startEditing(row.id!, key, row[key]);
        },*/
      },
      row[key] || '-'
    );
  };
}

// 可编辑下拉单元格渲染函数
export function renderEditableSelect(
  key: string,
  options: { label: string; value: string }[],
  onSave: (rowId: string | number, cellKey: string, value: any) => void
) {
  return (row: Top38Item) => {
    const isEditing = editingRow.value === row.id && editingCell.value === key;

    if (isEditing) {
      return h(
        NSpace,
        { align: 'center', justify: 'space-between' },
        {
          default: () => [
            h(NSelect, {
              value: editingValue.value,
              onUpdateValue: (v) => {
                editingValue.value = v;
              },
              options,
              size: 'small',
              style: { width: '80%' },
            }),
            h(
              NSpace,
              { size: 'small' },
              {
                default: () => [
                  h(
                    NButton,
                    {
                      size: 'tiny',
                      type: 'primary',
                      onClick: () => saveEditing(onSave),
                    },
                    { default: () => '保存' }
                  ),
                  h(
                    NButton,
                    {
                      size: 'tiny',
                      onClick: () => cancelEditing(),
                    },
                    { default: () => '取消' }
                  ),
                ],
              }
            ),
          ],
        }
      );
    }

    return h(
      'div',
      {
        style: 'cursor: pointer; padding: 0 4px',
        /* onClick: () => {
          startEditing(row.id!, key, row[key]);
        },*/
      },
      row[key] || '-'
    );
  };
}

// 列显示状态
export const columnVisible = ref({
  id: true,
  application: true,
  issueSource: true,
  issueCategory: true,
  priority: true,
  dtsTicket: true,
  topVoiceOpinion: true,
  mainResponsibleParty: true,
  responsiblePerson: true,
  goLiveTime: true,
  planProgress: true,
  status: true,
  proposer: true,
  product: true,
  suggestedPriority: false,
  suggestedResolutionTime: false,
  reasonHighPriorityUnmet: false,
  referenceVolume: false,
  workOrder: false,
  workOrderLab: false,
  vocOther: false,
  demandMatch: false,
  week17: false,
  week18: false,
  totalCumulative: false,
  proportion: false,
  keywords: false,
  entrySource: false,
  issueDescription: false,
  module: false,
  standardDescription: false,
  assistanceLevel: false,
  sentimentLevel: false,
});

// 列定义
export const allColumns: DataTableColumns = [
  { title: 'ID', key: 'id', resizable: true },
  { title: '应用', key: 'application', resizable: true },
  { title: '问题来源', key: 'issueSource', resizable: true },
  { title: '问题分类', key: 'issueCategory', resizable: true },
  { title: '优先级', key: 'priority', resizable: true, render: renderPriority },
  { title: 'DTS单号', key: 'dtsTicket', resizable: true },
  { title: 'TOP声音/舆情', key: 'topVoiceOpinion', resizable: true, ellipsis: true },
  { title: '主责任方', key: 'mainResponsibleParty', resizable: true },
  { title: '责任人', key: 'responsiblePerson', resizable: true },
  { title: '上线时间', key: 'goLiveTime', resizable: true },
  { title: '计划与进展', key: 'planProgress', resizable: true, ellipsis: true },
  { title: '状态', key: 'status', resizable: true, render: renderStatus },
  { title: '提出人', key: 'proposer', resizable: true },
  { title: '所属产品', key: 'product', resizable: true },
  { title: '建议优先级', key: 'suggestedPriority', resizable: true },
  { title: '建议解决时间', key: 'suggestedResolutionTime', resizable: true },
  { title: '极高优先级问题说明', key: 'reasonHighPriorityUnmet', resizable: true, ellipsis: true },
  { title: '参考声量', key: 'referenceVolume', resizable: true },
  { title: '工单', key: 'workOrder', resizable: true },
  { title: '工单实验室', key: 'workOrderLab', resizable: true },
  { title: 'VOC及其他', key: 'vocOther', resizable: true, ellipsis: true },
  { title: '诉求匹配度', key: 'demandMatch', resizable: true },
  { title: 'W17', key: 'week17', resizable: true },
  { title: 'W18', key: 'week18', resizable: true },
  { title: '累计', key: 'totalCumulative', resizable: true },
  { title: '比重', key: 'proportion', resizable: true },
  { title: '关键字', key: 'keywords', resizable: true, ellipsis: true },
  { title: '录入来源', key: 'entrySource', resizable: true },
  { title: '问题单描述', key: 'issueDescription', resizable: true, ellipsis: true },
  { title: '模块', key: 'module', resizable: true },
  { title: '标准描述', key: 'standardDescription', resizable: true, ellipsis: true },
  { title: '求助级别', key: 'assistanceLevel', resizable: true },
  { title: '舆情等级', key: 'sentimentLevel', resizable: true },
];

// 状态选项
export const statusOptions = [
  { label: '全部', value: '' },
  { label: 'Open', value: 'Open' },
  { label: 'Tracking', value: 'Tracking' },
  { label: 'Closed', value: 'Closed' },
];

// 优先级选项
export const priorityOptions = [
  { label: '全部', value: '' },
  { label: '极高', value: '极高' },
  { label: '高', value: '高' },
  { label: '中', value: '中' },
  { label: '低', value: '低' },
];

// 获取当前可见的列
export const getVisibleColumns = () => {
  // 确保我们只过滤有效的列
  return allColumns.filter((col) => {
    // 确保col.key存在且是字符串类型
    return typeof col.key === 'string' && columnVisible.value[col.key];
  });
};

// 导出原始列定义（为了兼容性）
export const columns = allColumns;

// 用于搜索表单显示的主要字段（第一行显示）
export const mainSearchFields = [
  {
    field: 'application',
    label: '应用',
    component: 'Input',
  },
  {
    field: 'issueCategory',
    label: '问题分类',
    component: 'Input',
  },
  {
    field: 'priority',
    label: '优先级',
    component: 'Select',
    componentProps: {
      multiple: false,
      filterable: true,
      options: priorityOptions,
    },
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      multiple: false,
      filterable: true,
      options: statusOptions,
    },
  },
];

export const getPlaceholder = (item) =>
  `请${item.component === 'Input' ? '输入' : '选择'}${item.label}`;

export const expandedSearchFields = [
  {
    field: 'dtsTicket',
    label: 'dts单号',
    component: 'Input',
  },
  {
    field: 'issueSource',
    label: '问题来源',
    component: 'Input',
  },
  /*  {
    field: 'topVoiceOpinion',
    label: 'TOP声音/舆情',
    component: 'Input',
  },*/
  {
    field: 'mainResponsibleParty',
    label: '主责任方',
    component: 'Input',
  },
  {
    field: 'responsiblePerson',
    label: '责任人',
    component: 'Input',
  },
  /*  {
    label: '上线时间',
    field: 'goLiveTime',
    component: 'Date'
  },*/
  {
    label: '计划与进展',
    field: 'planProgress',
    component: 'Input',
  },
  {
    label: '提出人',
    field: 'proposer',
    component: 'Input',
  },
  {
    label: '所属产品',
    field: 'product',
    component: 'Input',
  },
  {
    label: '建议优先级',
    field: 'suggestedPriority',
    component: 'Input',
  },
  /*  {
    label: '建议解决时间',
    field: 'suggestedResolutionTime',
    component: 'Date',
  },*/
  // {
  //   label: '极高优先级问题说明',
  //   field: 'reasonHighPriorityUnmet',
  //   component: 'Input',
  // },
  /*  {
    label: '参考声量',
    field: 'referenceVolume',
    component: 'numberRange',
  },*/
  // {
  //   label: '工单',
  //   field: 'workOrder',
  //   component: 'Input',
  // },
  // {
  //   label: '工单实验室',
  //   field: 'workOrderLab',
  //   component: 'Input',
  // },
  // {
  //   label: 'VOC及其他',
  //   field: 'vocOther',
  //   component: 'Input',
  // },
  // {
  //   label: '诉求匹配度',
  //   field: 'demandMatch',
  //   component: 'Input',
  // },
  // {
  //   label: 'W17',
  //   field: 'week17',
  //   component: 'Input',
  // },
  // {
  //   label: 'W18',
  //   field: 'week18',
  //   component: 'Input',
  // },
  // {
  //   label: '累计',
  //   field: 'totalCumulative',
  //   component: 'Input',
  // },
  // {
  //   label: '比重',
  //   field: 'proportion',
  //   component: 'Input',
  // },
  // {
  //   label: '关键字',
  //   field: 'keywords',
  //   component: 'Input',
  // },
  // {
  //   label: '录入来源',
  //   field: 'entrySource',
  //   component: 'Input',
  // },
  // {
  //   label: '问题单描述',
  //   field: 'issueDescription',
  //   component: 'Input',
  // },
  // {
  //   label: '模块',
  //   field: 'module',
  //   component: 'Input',
  // },
  // {
  //   label: '标准描述',
  //   field: 'standardDescription',
  //   component: 'Input',
  // },
  // {
  //   label: '求助级别',
  //   field: 'assistanceLevel',
  //   component: 'Input',
  // },
  // {
  //   label: '舆情等级',
  //   field: 'sentimentLevel',
  //   component: 'Input',
  // },
];
