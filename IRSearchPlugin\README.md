# IRSearchPlugin

## 使用说明
1. chrome浏览器打开chrome://extensions/
2. 开启开发者模式
3. 加载已解压的扩展程序-选择插件文件夹
![Animated png](./Snipaste/Snipaste_2024-12-02_21-21-46.png)
4. 进入IR详情页，右上角角色切换至一级运营
5. 指派类型选择二级运营人员点击确定时会弹出知识检索输入框
![Animated png](./Snipaste/Snipaste_2024-12-02_21-17-56.png)
6.输入需要检索的内容搜索后弹出搜索结果
![Animated png](./Snipaste/Snipaste_2024-12-02_21-20-32.png)
7.根据结果选择是否有匹配的搜索结果

## 注意事项：
插件中请求了HTTP的请求，原IR平台使用的是HTTPS，会触发浏览器的安全限制，如遇到请求失败请参考如下方式设置：
方式一：
    设置隐私安全允许不安全内容，
    ![Animated png](./Snipaste/Snipaste_2024-12-05_11-51-51.png)
    ![Animated png](./Snipaste/Snipaste_2024-12-05_11-52-11.png)
方式二：
    1、chrome://flags，进入页面搜索 Insecure origins treated as secure
    2、将Insecure origins treated as secure功能启用，下方输入框输入http://***********:8080, http://***********:8084将其视为安全源
    3、点击重新启动
    ![Animated png](./Snipaste/Snipaste_2024-12-05_11-57-42.png)