import service from '@/utils/axios';

export const dashboardAddOrUpdate = (data) => {
  return service({
    url: `/demo/dashboard/addOrUpdate`,
    method: 'post',
    data,
  });
};
export const dashboardDelete = (data) => {
  return service({
    url: `/demo/dashboard/delete`,
    method: 'get',
    params: data,
  });
};

export const dashboardQuery = (data) => {
  return service({
    url: `/demo/dashboard/queryPage`,
    method: 'get',
    params: data,
  });
};
export const dashboardImport = (data) => {
  return service({
    url: `/demo/dashboard/import`,
    method: 'post',
    data,
    headers: {
      'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
};
export const dashboardDownloadTemplate = () => {
  return service({
    url: `/demo/dashboard/downloadTemplate`,
    method: 'get',
    responseType: 'blob',
  });
};
