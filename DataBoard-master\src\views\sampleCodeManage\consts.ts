import { SelectOption } from "naive-ui";
import { FilterOption } from "naive-ui/es/data-table/src/interface";
import { CodeDemoDto } from "@/api/system/code";

export const ALL_DEMO_TYPES: string[] = [
  'ArkTs API',
  'ArkUI 典型页面',
  'ArkUI 基础组件',
  'NDK',
  'Web',
  '布局&一多',
  '创新场景',
  '典型开发场景',
  '动画',
  '媒体',
  '三方库',
  '生态规则',
  '图形',
  '行业最佳实践',
  '应用级案例',
  '应用框架',
  '主体验收',
];
export const ALL_API_VERSION: string[] = [
  'API 12',
  'API 13',
  'API 14',
  'API 15',
  'API 16',
  'API 17',
  'API 18',
];
export const SAMPLE_CODE_WEBSITE_URL = 'https://dtse.cbg.huawei.com:8777/web/#/';
export const getFilterOptions = (filters: string[]): FilterOption[] => {
  return filters.map(item => ({
    value: item,
    label: item,
  }));
}
export const getDefaultDemoInfo = (): CodeDemoDto => {
  return {
    id: 0,
    demoName: '',
    demoDescription: '',
    demoLink: '',
    externalLink: '',
    lineNumber: 0,
    reviewSuggestions: '',
    reviewStatus: null,
    currentHandler: '',
    reviewTime: '',
    demoSubmitter: '',
    demoId: '',
    apiVersion: 'API 17',
    type: 'ArkTS',
    selectValue: '',
    samples: [],
    tags: [],
    demoCount: 0,
  }
}
export const REVIEW_RULES: string[] = [
  '非原创demo严禁入库，引入外部开发者demo涉嫌版权信息安全。',
  '使用外部图片，需检查是否涉版权风险，优先使用内部无风险资源图片（图片中不含内部信息）。',
  `严禁含有伙伴资源，常见场景：
    · 应用名或项目路径含有伙伴信息（禁止使用伙伴应用名拼音缩写等，应改成功能名称或行业通用名称）；
    · 引用伙伴文件或图片等资源（ux、图片资源）；
    · 应用内文字提示等含伙伴信息（xxxx公司隐私协议、各种文字ui或提示含伙伴主体名称）。`,
  '代码中严禁含有签名、copyright、Lisence。',
  '代码中严禁含有内网地址、内部文件、内部涉密图片、图片水印含工号、邮箱、密码等内部信息。',
  `项目文件中需移除自动生成的文件和目录，如：
    · .hvigor
    · .idea
    · .VSCodeCounter
    · build
    · oh_modules
    · local.properties
    · package-lock.json`,
]
