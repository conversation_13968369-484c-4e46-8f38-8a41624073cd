import { h } from 'vue';
import { NSelect, NInput, NTag, useMessage } from 'naive-ui';
import { saveSpotCheckOrder } from '@/api/dataview/myOrder/spotCheck';
import { useUserStore } from '@/store/modules/user';

// 获取用户信息
const userStore = useUserStore();
const getUserInfo = () => userStore.getUserInfo || {};

export const boundarySpecOptions = [
  {
    code: '1.1',
    text: '禁止随意使用功能缺失、应用缺失、小概率等相关知识，如要使用必须在定界过程中写明原因',
  },
  { code: '1.2', text: '禁止问题分类选择错误或和关联的知识分类不一致' },
  { code: '1.3', text: '禁止无定界分析过程或者定界过程内容过于简单' },
  { code: '1.4', text: '禁止定界内容为空' },
  { code: '1.5', text: '禁止定界过程的内容和知识中的问题定位的内容无关联' },
];
export const analysisSpecOptions = [{ code: '2.1', text: '禁止分析结论与定界过程无关联' }];
export const suggestionSpecOptions = [
  { code: '3.1', text: '禁止无修改建议或错误的建议' },
  { code: '3.2', text: '禁止修改建议和分析结论无关联' },
  { code: '3.3', text: '禁止修改建议和知识无关联' },
];
export const processSpecOptions = [{ code: '4.1', text: '禁止同一个问题单多次提交优化知识请求' }];
export const checkResultOptions = [
  { label: '合格', value: '合格' },
  { label: '不合格', value: '不合格' },
  {
    label: '空',
    value: '-',
  },
];
export const rectifiedOptions = [
  { label: '是', value: '是' },
  { label: '否', value: '否' },
];
export const reviewResultOptions = [
  { label: '通过', value: '通过' },
  { label: '未通过', value: '未通过' },
];

// 存储过滤状态
const filterState = {};

// 通用文本过滤函数
const createTextFilter = (key) => ({
  filterOptionValue: null,
  filterOptionValues: null,
  renderFilterInput: () => {
    return h(NInput, {
      placeholder: '输入关键词搜索',
      onUpdateValue(value) {
        filterState[key] = value;
      },
    });
  },
  filter(value, row) {
    if (!filterState[key]) return true;
    const cellValue = row[key];
    if (cellValue === null || cellValue === undefined) return false;
    return String(cellValue).toLowerCase().includes(String(filterState[key]).toLowerCase());
  },
});
const message = useMessage();
// 日期过滤函数
const createDateFilter = (key) => ({
  filterOptionValue: null,
  filterOptionValues: null,
  renderFilterInput: () => {
    return h(NInput, {
      placeholder: '输入日期搜索 (YYYY/MM/DD)',
      onUpdateValue(value) {
        filterState[key] = value;
      },
    });
  },
  filter(value, row) {
    if (!filterState[key]) return true;
    if (!row[key]) return false;
    const dateStr = new Date(row[key]).toLocaleDateString();
    return dateStr.includes(filterState[key]);
  },
});

// 添加用于展示规范选项的辅助函数
const renderSpecificationOptions = (codes, options) => {
  if (!codes || !Array.isArray(codes)) return '';

  return codes
    .map((code) => {
      const option = options.find((opt) => opt.code === code);
      return option ? `${option.code} ${option.text}` : code;
    })
    .join(', ');
};

export const columns = [
  {
    title: '抽检日期',
    key: 'checkDate',
    resizable: true,
    ...createDateFilter('checkDate'),
    render(row) {
      return row.checkDate ? row.checkDate : '';
    },
  },
  {
    title: '定界时间',
    key: 'delimitationDate',
    resizable: true,
    ...createDateFilter('delimitationDate'),
    render(row) {
      return row.delimitationDate ? row.delimitationDate : '';
    },
  },
  {
    title: '应用名称',
    key: 'appName',
    resizable: true,
    ...createTextFilter('appName'),
  },
  {
    title: '问题单号',
    key: 'orderId',
    resizable: true,
    ...createTextFilter('orderId'),
    render: (row) => {
      return h(
        NTag,
        {
          type: 'info',
          bordered: false,
          onClick: async () => {
            const { orderId } = row;
            window.open(
              `https://dtse.cbg.huawei.com/listingProtection/process?orderId=${orderId}`,
              '_blank'
            );
          },
        },
        { default: () => row.orderId }
      );
    },
  },
  {
    title: '知识id',
    key: 'knowledgeId',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    ...createTextFilter('knowledgeId'),
    render: (row) => {
      return h(
        NTag,
        {
          bordered: false,
          onClick: async () => {
            const { knowledgeId } = row;
            window.open(`http://***********/knowledgeDetail/${knowledgeId}`, '_blank');
          },
        },
        { default: () => row.knowledgeId }
      );
    },
  },
  {
    title: '模块',
    key: 'module',
    resizable: true,
    ...createTextFilter('module'),
  },
  {
    title: '抽检人',
    key: 'checker',
    resizable: true,
    ...createTextFilter('checker'),
  },
  {
    title: '定界过程规范',
    key: 'delimitationSpecifications',
    resizable: true,
    filterMultiple: true,
    ellipsis: {
      tooltip: true,
    },
    filterOptions: boundarySpecOptions.map((opt) => ({
      label: `${opt.code} ${opt.text}`,
      value: opt.code,
    })),
    filter(value, row) {
      if (!value || value.length === 0) return true;
      if (!row.delimitationSpecifications && !row.delimitationSpecification) return false;

      const specifications = row.delimitationSpecifications || row.delimitationSpecification;
      if (!Array.isArray(specifications)) return false;

      return value.some((v) => specifications.includes(v));
    },
    render(row) {
      const specifications = row.delimitationSpecifications || row.delimitationSpecification;
      if (!specifications || !Array.isArray(specifications)) return '';

      return renderSpecificationOptions(specifications, boundarySpecOptions);
    },
  },
  {
    title: '分析结论规范',
    key: 'analysisSpecifications',
    resizable: true,
    filterMultiple: true,
    ellipsis: {
      tooltip: true,
    },
    filterOptions: analysisSpecOptions.map((opt) => ({
      label: `${opt.code} ${opt.text}`,
      value: opt.code,
    })),
    filter(value, row) {
      if (!value || value.length === 0) return true;
      if (!row.analysisSpecifications && !row.analysisSpecification) return false;

      const specifications = row.analysisSpecifications || row.analysisSpecification;
      if (!Array.isArray(specifications)) return false;

      return value.some((v) => specifications.includes(v));
    },
    render(row) {
      const specifications = row.analysisSpecifications || row.analysisSpecification;
      if (!specifications || !Array.isArray(specifications)) return '';

      return renderSpecificationOptions(specifications, analysisSpecOptions);
    },
  },
  {
    title: '修改建议规范',
    key: 'amendmentSpecifications',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    filterMultiple: true,
    filterOptions: suggestionSpecOptions.map((opt) => ({
      label: `${opt.code} ${opt.text}`,
      value: opt.code,
    })),
    filter(value, row) {
      if (!value || value.length === 0) return true;
      if (!row.amendmentSpecifications && !row.amendmentSpecification) return false;

      const specifications = row.amendmentSpecifications || row.amendmentSpecification;
      if (!Array.isArray(specifications)) return false;

      return value.some((v) => specifications.includes(v));
    },
    render(row) {
      const specifications = row.amendmentSpecifications || row.amendmentSpecification;
      if (!specifications || !Array.isArray(specifications)) return '';

      return renderSpecificationOptions(specifications, suggestionSpecOptions);
    },
  },
  {
    title: '流程规范',
    key: 'procedureSpecifications',
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
    filterMultiple: true,
    filterOptions: processSpecOptions.map((opt) => ({
      label: `${opt.code} ${opt.text}`,
      value: opt.code,
    })),
    filter(value, row) {
      if (!value || value.length === 0) return true;
      if (!row.procedureSpecifications && !row.procedureSpecification) return false;

      const specifications = row.procedureSpecifications || row.procedureSpecification;
      if (!Array.isArray(specifications)) return false;

      return value.some((v) => specifications.includes(v));
    },
    render(row) {
      const specifications = row.procedureSpecifications || row.procedureSpecification;
      if (!specifications || !Array.isArray(specifications)) return '';

      return renderSpecificationOptions(specifications, processSpecOptions);
    },
  },
  {
    title: '备注',
    key: 'checkComment',
    resizable: true,
    ...createTextFilter('checkComment'),
  },
  {
    title: 'EWP应用责任人',
    key: 'ewpOwner',
    resizable: true,
    ...createTextFilter('ewpOwner'),
  },
  {
    title: '小组',
    key: 'ewpGroup',
    resizable: true,
    ...createTextFilter('ewpGroup'),
  },
  {
    title: '是否完成整改',
    key: 'amendmentDone',
    resizable: true,
    filterMultiple: false,
    filterOptions: rectifiedOptions,
    filter(value, row) {
      if (!value) return true;
      return row.amendmentDone === value;
    },
  },
  {
    title: '巡检人复审结果',
    key: 'reviewResult',
    resizable: true,
    filterMultiple: false,
    filterOptions: reviewResultOptions,
    filter(value, row) {
      if (!value) return true;
      return row.reviewResult === value;
    },
  },
  {
    title: '澄清说明',
    key: 'clarification',
    resizable: true,
    ...createTextFilter('clarification'),
  },
  {
    title: '复审超期',
    key: 'reviewOverdue',
    resizable: true,
  },
  {
    title: '整改超期',
    key: 'amendmentOverdue',
    resizable: true,
  },
  {
    title: '抽检超期',
    key: 'spotcheckResult',
    resizable: true,
  },
  {
    title: '抽检结果',
    key: 'checkResult',
    resizable: true,
    filterMultiple: false,
    fixed: 'right' as const,
    filterOptions: checkResultOptions,
    filter(value, row) {
      if (!value) return true;
      return row.checkResult === value;
    },
    render(row) {
      const userInfo = getUserInfo();
      // 检查当前用户是否为抽检人或管理员
      const hasPermission = userInfo.userName === row.checker;

      return h(NSelect, {
        value: row.checkResult,
        options: checkResultOptions,
        size: 'small',
        disabled: !hasPermission, // 根据权限控制是否禁用
        style: { width: '150px' },
        async onUpdateValue(value) {
          // 再次检查权限（防止用户通过其他方式修改disabled属性）
          if (!hasPermission) {
            message.error('您没有权限修改此项');
            return;
          }

          row.checkResult = value;
          try {
            await saveSpotCheckOrder({
              ...row,
              checkResult: value,
            });
            message.success('更新成功');
          } catch (error) {
            message.error('更新失败');
          }
        },
      });
    },
  },
];
