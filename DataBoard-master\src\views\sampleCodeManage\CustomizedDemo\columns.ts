import { DataTableColumns, NCarousel, NEllipsis, NImage } from 'naive-ui';
import { CodeWishVo, getAllStatusOptions } from './index';
import { h } from 'vue';
import { REQUIREMENT_REVIEW_STATUS_MAP } from './consts';
import emptyImage from '@/assets/images/emptyImage.png';

export let COLUMNS: DataTableColumns<CodeWishVo> = [
  {
    title: '名称/ID',
    key: 'demoName',
    width: '180px',
    fixed: 'left',
    render(row) {
      return [h('div', {}, [h(NEllipsis, row.demoName), h('div', row.id)])];
    },
  },
  {
    title: '描述',
    key: 'demoDescription',
    width: '180px',
    resizable: true,
    minWidth: '100px',
    ellipsis: {
      tooltip: true,
      lineClamp: 2,
    },
  },
  {
    title: '示例',
    key: 'imgUrlList',
    width: '150px',
    resizable: true,
    minWidth: '100px',
    ellipsis: {
      tooltip: true,
    },
    render(row) {
      if (row.imgList.length) {
        return h(
          NCarousel,
          {
            mousewheel: true,
            direction: 'vertical',
            dotPlacement: 'right',
            style: 'width: 100%; height: 60px',
          },
          row.imgList.map((item) =>
            h(NImage, {
              src: item.url!,
              fallbackSrc: emptyImage,
              style: 'height: 60px!important',
            })
          )
        );
      }
    },
  },
  {
    title: '提交人',
    key: 'demoSubmitter',
    width: '150px',
    resizable: true,
    minWidth: '100px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '提交时间',
    key: 'createTime',
    width: '180px',
    resizable: true,
    minWidth: '100px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '期望交付时间',
    key: 'expectedTime',
    width: '180px',
    sorter: true,
    resizable: true,
    minWidth: '50px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '需求评审通过时间',
    key: 'requirementReviewPassingTime',
    width: '180px',
    resizable: true,
    minWidth: '50px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '交付评审通过时间',
    key: 'passingTime',
    width: '180px',
    resizable: true,
    minWidth: '50px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '托管时间',
    key: 'giteeTime',
    width: '180px',
    resizable: true,
    minWidth: '50px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '来源',
    key: 'source',
    width: '100px',
    resizable: true,
    minWidth: '50px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '分值',
    key: 'score',
    width: '60px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '当前处理人',
    key: 'currentHandler',
    width: '150px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '承诺交付时间',
    key: 'promisedTime',
    width: '180px',
    resizable: true,
    minWidth: '100px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '样例代码',
    key: 'linkDemo',
    width: '200px',
    resizable: true,
    minWidth: '100px',
    ellipsis: {
      tooltip: true,
    },
    render() { },
  },
  {
    title: '领域',
    key: 'field',
    width: '140px',
    resizable: true,
    minWidth: '100px',
  },
  {
    title: '交付责任人',
    key: 'demoAuthor',
    width: '150px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '状态',
    key: 'reviewStatus',
    width: '80px',
    render(row: CodeWishVo) {
      return REQUIREMENT_REVIEW_STATUS_MAP[row.reviewStatus!];
    },
  },
  {
    title: '需求评审意见',
    key: 'requirementReviewSuggestions',
    width: '180px',
    resizable: true,
    minWidth: '100px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '需求评审人',
    key: 'reviewer',
    width: '140px',
    resizable: true,
    minWidth: '100px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '交付评审意见',
    key: 'reviewSuggestions',
    width: '180px',
    resizable: true,
    minWidth: '100px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '交付评审组',
    key: 'reviewers',
    width: '180px',
    resizable: true,
    minWidth: '100px',
    ellipsis: {
      tooltip: true,
    },
    render(row: CodeWishVo) {
      return row.reviewers?.join('，');
    },
  },
  {
    title: '外发责任人',
    key: 'outGoingPerson',
    width: '150px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '托管责任人',
    key: 'custodian',
    width: '150px',
    ellipsis: {
      tooltip: true,
    },
  }
];
export let COLUMNSTABLE: DataTableColumns<CodeWishVo> = [
  {
    title: '名称/ID',
    key: 'demoName',
    width: '180px',
    fixed: 'left',
    resizable: true,
    render(row) {
      return [h('div', {}, [h(NEllipsis, row.demoName), h('div', row.id)])];
    },
  },
  {
    title: '描述',
    key: 'demoDescription',
    width: '300PX',
    resizable: true,
    // minWidth: '100px',
    ellipsis: {
      tooltip: true,
      lineClamp: 2,
    },
  },
  {
    title: '提交人',
    key: 'demoSubmitter',
    width: '150px',
    resizable: true,
    minWidth: '100px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '提交时间',
    key: 'createTime',
    width: '180px',
    resizable: true,
    minWidth: '100px',
    ellipsis: {
      tooltip: true,
    },
  },

  {
    title: '来源',
    key: 'source',
    width: '100px',
    resizable: true,
    minWidth: '50px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '需求评审人',
    key: 'reviewer',
    width: '140px',
    resizable: true,
    minWidth: '100px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '需求评审通过时间',
    key: 'requirementReviewPassingTime',
    width: '160px',
    resizable: true,
    minWidth: '50px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '交付责任人',
    key: 'demoAuthor',
    width: '150px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '交付评审组',
    key: 'reviewers',
    width: '160px',
    resizable: true,
    minWidth: '100px',
    ellipsis: {
      tooltip: true,
    },
    render(row: CodeWishVo) {
      return row.reviewers?.join('，');
    },
  },
  {
    title: '交付评审通过时间',
    key: 'passingTime',
    width: '160px',
    resizable: true,
    minWidth: '50px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '承诺交付时间',
    key: 'promisedTime',
    width: '160px',
    resizable: true,
    minWidth: '100px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '当前处理人',
    key: 'currentHandler',
    width: '150px',
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '期望交付时间',
    key: 'expectedTime',
    width: '160px',
    sorter: true,
    resizable: true,
    ellipsis: {
      tooltip: true,
    },
  },
  {
    title: '状态',
    key: 'reviewStatus',
    width: '100px',
    render(row: CodeWishVo) {
      return REQUIREMENT_REVIEW_STATUS_MAP[row.reviewStatus!];
    },
  },
  {
    title: '操作',
    key: 'operation',
    width: '150px',
    fixed: 'right',
    ellipsis: {
      tooltip: true,
    },
    render() { },
  },
];
