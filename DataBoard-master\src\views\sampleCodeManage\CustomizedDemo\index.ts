import { CodeWishReviewStatusEnum } from '@/enums/CodeWishReviewStatusEnum';
import { storage } from '@/utils/Storage';
import { CURRENT_USER } from '@/store/mutation-types';
import { UserDto } from '@/api/system/usermanage';
import { SelectOption, UploadFileInfo } from 'naive-ui';
import { useRoute } from 'vue-router';
import {
  AddCustomizedDemoReq,
  CodeWishDto,
  UpdateCustomizedDemoReq,
} from '@/api/system/customizedDemo';
import dayjs from 'dayjs';
import { useUserStore } from '@/store/modules/user';
import { FilterOption } from 'naive-ui/es/data-table/src/interface';
import {
  ALL_REVIEW_STATUS,
  NEXT_REVIEW_STATUS_MAP,
  NO_CURRENT_HANDLER_REVIEW_STATUS,
  REQUIREMENT_REVIEW_STATUS_MAP,
} from './consts';
import { CodeDemoReviewStatusEnum } from '@/enums/CodeDemoReviewStatusEnum';
export const formatTime = (time) => {
  try {
    const date = new Date(time);
    let Y = date.getFullYear();
    let M = `00${date.getMonth() + 1}`.slice(-2);
    let D = `00${date.getDate()}`.slice(-2);

    return `${Y}-${M}-${D}`;
  } catch (error) {}
};
export interface CodeWishFilterStatus {
  wishId: string;
  wishName: string;
  wishSubmitter: string;
  currentHandler: string;
  demoAuthor: string;
  reviewStatus: CodeWishReviewStatusEnum[];
  onlyISubmitted: boolean;
  onlyIDelivered: boolean;
  onlyMyTodo: boolean;
  sources: string[];
  createTimes: null;
  createStartTime: string;
  createEndTime: string;
}
export interface CodeWishFormModel {
  id: number;
  demoName: string;
  demoDescription: string;
  source: string;
  demoSubmitter: string;
  expectedTime: number | null;
  currentHandler: string;
  demoAuthor: string;
  demoLink: string;
  promisedTime: number | null;
  reviewStatus: CodeWishReviewStatusEnum | null;
  reviewSuggestions: string;
  requirementReviewSuggestions: string;
  reviewTime: number | null;
  reviewResult: string;
  linkDemoId: string;
  linkDemoName: string;
  linkDemoReviewStatus: CodeDemoReviewStatusEnum | null;
  reviewer: string;
  reviewers: string[];
  score: string;
  demoModificationType: string;
  // 是否是自动生成的demo单
  isAutoWish: boolean;
  imgList: UploadFileInfo[];
  outGoingPerson: string;
  custodian: string;
  externalLink: string;
  field: string;
  createTimes: null;
  // createEndTime:string;
}
export interface CodeWishVo {
  id: number;
  createTime: string;
  currentHandler: string;
  demoAuthor: string;
  demoLink: string;
  /* 承诺交付时间 */
  promisedTime: string;
  reviewStatus: CodeWishReviewStatusEnum;
  reviewSuggestions: string;
  requirementReviewSuggestions: string;
  demoName: string;
  demoDescription: string;
  requirementReviewPassingTime: string;
  passingTime: string;
  expectedTime: string;
  demoSubmitter: string;
  linkDemoId: string;
  linkDemoName: string;
  linkDemoReviewStatus: CodeDemoReviewStatusEnum | null;
  reviewers: string[];
  // reviewer: string[];
  score: number;
  demoModificationType: string;
  reviewer: string;
  // 是否是自动生成的demo单
  isAutoWish: boolean;
  imgList: UploadFileInfo[];
  outGoingPerson: string;
  custodian: string;
  reviewTime: string;
  reviewResult: string;
  source: string;
  field: string;
}
// 获取全部状态数据
export function getAllStatusOptions(): FilterOption[] {
  return ALL_REVIEW_STATUS.map((item) => ({
    value: item,
    label: REQUIREMENT_REVIEW_STATUS_MAP[item],
  }));
}
// 获取demo单当前可选的评审状态和下一步评审状态数据
export function getWishReviewOptions(curStatus: CodeWishReviewStatusEnum): {
  reviewStatus: CodeWishReviewStatusEnum | null;
  reviewOptions: SelectOption[];
} {
  const options = NEXT_REVIEW_STATUS_MAP[curStatus] ?? [];
  return {
    // 如果只有一个可选状态，则直接赋值为该状态；如果有多个可选状态，则赋值为null让用户选择
    reviewStatus: options.length === 1 ? (options[0].value as CodeWishReviewStatusEnum) : null,
    reviewOptions: options,
  };
}
const userInfo = useUserStore().getUserInfo;
export function getDefaultFilterStatus(): CodeWishFilterStatus {
  const route = useRoute();
  return {
    wishId: '',
    wishName: '',
    demoAuthor: '',
    currentHandler: route?.params.todo ? userInfo.label : '',
    wishSubmitter: '',
    reviewStatus: [],
    onlyISubmitted: false,
    onlyIDelivered: false,
    onlyMyTodo: true, // 默认选只看我的待办
    sources: [],
    createStartTime: '',
    createEndTime: '',
    createTimes: null,
  };
}
export function getDefaultCodeWishFormModel(): CodeWishFormModel {
  return {
    id: 0,
    demoName: '',
    demoDescription: `Demo描述：
Demo场景描述：
API版本：
Demo提出人：
问题单链接：`,
    source: '',
    demoSubmitter: '',
    demoAuthor: '',
    expectedTime: null,
    currentHandler: '',
    demoLink: '',
    promisedTime: null,
    reviewStatus: null,
    reviewSuggestions: '',
    requirementReviewSuggestions: '',
    reviewer: '',
    linkDemoId: '',
    linkDemoName: '',
    linkDemoReviewStatus: null,
    isAutoWish: true,
    reviewers: [],
    score: '1',
    demoModificationType: '',
    imgList: [],
    outGoingPerson: '',
    custodian: '',
    externalLink: '',
    field: '',
  };
}
export function getAddCustomizedDemoParams(params: CodeWishFormModel): AddCustomizedDemoReq {
  return {
    demoName: params.demoName,
    demoDescription: params.demoDescription,
    demoSubmitter: useUserStore().getUserInfo.label,
    expectedTime: dayjs(params.expectedTime).format('YYYY-MM-DD HH:mm:ss'),
    imgUrlList: params.imgList.map((item) => item.url!),
    source: params.source,
    field: params.field,
    reviewer: params.reviewer,
    reviewers: params.reviewers,
  };
}

export function getUpdateBasicInfoParams(
  params: CodeWishFormModel,
  draft?: boolean
): UpdateCustomizedDemoReq {
  return {
    id: params.id,
    currentHandler: draft ? params.currentHandler : '',
    demoName: params.demoName,
    demoDescription: params.demoDescription,
    reviewResult: params.reviewResult,
    reviewTime: dayjs(params.reviewTime).format('YYYY-MM-DD'),
    expectedTime: dayjs(params.expectedTime).format('YYYY-MM-DD HH:mm:ss'),
    reviewStatus: draft
      ? CodeWishReviewStatusEnum.TO_BE_SUBMITTED
      : CodeWishReviewStatusEnum.TO_BE_ACCEPTED,
    // imgUrlList: params.imgList.map(item => item.url!),
    source: params.source,
    field: params.field,
    reviewer: params.reviewer,
    reviewers: params.reviewers,
    createStartTime: params.createTimes ? params.createTimes[0] : null,
    createEndTime: params.createTimes ? params.createTimes[1] : null,
  };
}
export function getWishReviewParams(params: CodeWishFormModel): UpdateCustomizedDemoReq {
  // 未指派时，状态变为待认领
  if (params.reviewStatus === CodeWishReviewStatusEnum.UNDER_DEVELOPMENT && !params.demoAuthor) {
    params.reviewStatus = CodeWishReviewStatusEnum.TO_BE_CLAIMED;
  }
  let result: UpdateCustomizedDemoReq = {
    id: params.id,
    demoAuthor: params.demoAuthor ?? '',
    currentHandler: getCurrentHandler(params),
    requirementReviewSuggestions: params.requirementReviewSuggestions,
    reviewResult: params.reviewResult,
    reviewTime: dayjs(params.reviewTime).format('YYYY-MM-DD'),
    reviewer: useUserStore().getUserInfo.label,
    reviewStatus: params.reviewStatus,
    score: Number(params.score),
  };
  if (params.promisedTime) {
    result.promisedTime = dayjs(params.promisedTime).format('YYYY-MM-DD HH:mm:ss');
  }
  return result;
}
export function getDeliverParams(params: CodeWishFormModel): UpdateCustomizedDemoReq {
  return {
    id: params.id,
    currentHandler: 'null',
    reviewStatus: CodeWishReviewStatusEnum.TO_BE_REVIEWED,
  };
}
export function getDesignatedOutGoingPersonParams(
  params: CodeWishFormModel
): UpdateCustomizedDemoReq {
  return {
    id: params.id,
    currentHandler: params.outGoingPerson,
    outGoingPerson: params.outGoingPerson,
    reviewStatus: CodeWishReviewStatusEnum.TO_BE_SENT_OUT,
  };
}
export function getOutgoingParams(wishId: number): UpdateCustomizedDemoReq {
  return {
    id: wishId,
    currentHandler: '黎文晓/l30044066,王帆/w60070018',
    custodian: '黎文晓/l30044066,王帆/w60070018',
    reviewStatus: CodeWishReviewStatusEnum.TO_BE_HOSTED,
  };
}
export function getHostingParams(wishId: number): UpdateCustomizedDemoReq {
  return {
    id: wishId,
    currentHandler: 'null',
    custodian: useUserStore().getUserInfo.label,
    reviewStatus: CodeWishReviewStatusEnum.HOSTED,
  };
}
export function getCodeReviewParams(params: CodeWishFormModel): UpdateCustomizedDemoReq {
  return {
    id: params.id,
    currentHandler: getCurrentHandler(params),
    reviewSuggestions: params.reviewSuggestions,
    reviewStatus: params.reviewStatus,
    reviewTime: dayjs(params.reviewTime).format('YYYY-MM-DD'),
    reviewResult: params.reviewResult,
    reviewers: [useUserStore().getUserInfo.label],
    score: Number(params.score),
  };
}
export function getCurrentHandler(wishInfo: CodeWishVo | CodeWishFormModel): string {
  switch (wishInfo.reviewStatus) {
    case CodeWishReviewStatusEnum.PASSED:
    case CodeWishReviewStatusEnum.REJECTED:
    case CodeWishReviewStatusEnum.TO_BE_ACCEPTED:
    case CodeWishReviewStatusEnum.TO_BE_REVIEWED:
    case CodeWishReviewStatusEnum.ABANDONED:
    case CodeWishReviewStatusEnum.TO_BE_CLAIMED:
    case CodeWishReviewStatusEnum.TO_BE_HOSTED:
    case CodeWishReviewStatusEnum.HOSTED:
      return 'null';
    case CodeWishReviewStatusEnum.TO_BE_SUBMITTED:
      return wishInfo.demoSubmitter;
    case CodeWishReviewStatusEnum.UNDER_DEVELOPMENT:
      return wishInfo.demoAuthor ?? 'null';
    case CodeWishReviewStatusEnum.TO_BE_SENT_OUT:
      return wishInfo.outGoingPerson;
    default:
      return 'null';
  }
}
export function getSubmitReviewParams(params: CodeWishFormModel): UpdateCustomizedDemoReq {
  return {
    id: params.id,
    currentHandler: '',
    reviewStatus: CodeWishReviewStatusEnum.TO_BE_ACCEPTED,
  };
}
export function getClaimingParams(id: number): UpdateCustomizedDemoReq {
  const userLabel = useUserStore().getUserInfo.label;
  return {
    id: id,
    currentHandler: userLabel,
    demoAuthor: userLabel,
    reviewStatus: CodeWishReviewStatusEnum.UNDER_DEVELOPMENT,
  };
}

export function isUserAdmin(): boolean {
  return !!storage.get<UserDto>(CURRENT_USER)?.roles?.includes('3_admin');
}
// 数据赋值
export function getCodeWishVo(data: CodeWishDto): CodeWishVo {
  return {
    id: data.id,
    createTime: dayjs(data.createTime).format('YYYY-MM-DD HH:mm:ss'),
    demoName: data.demoName,
    demoDescription: data.demoDescription ?? '',
    demoSubmitter: data.demoSubmitter ?? '',
    demoAuthor: data.demoAuthor ?? '',
    expectedTime: data.expectedTime,
    currentHandler: data.currentHandler,
    demoLink: data.demoLink ?? '',
    promisedTime: data.promisedTime,
    reviewStatus: data.reviewStatus || CodeWishReviewStatusEnum.TO_BE_ACCEPTED,
    reviewSuggestions: data.reviewSuggestions ?? '',
    requirementReviewSuggestions: data.requirementReviewSuggestions ?? '',
    reviewTime: data.reviewTime ?? '',
    reviewer: data.reviewer,
    giteeTime: data.giteeTime,
    linkDemoId: data.linkDemoId ?? '',
    linkDemoName: data.linkDemoName ?? '',
    linkDemoReviewStatus: data.linkDemoReviewStatus,
    isAutoWish: !data.expectedTime,
    reviewers: data.reviewers ?? [],
    requirementReviewPassingTime: data.requirementReviewPassingTime ?? '',
    passingTime: data.passingTime ?? '',
    score: data.score || 0,
    demoModificationType: data.demoModificationType ?? '',
    imgList:
      data.tdemoSampleTableList?.map((item) => ({
        id: String(item.id),
        url: item.imgUrl,
        name: item.imgUrl + '.png',
        status: 'finished',
      })) ?? [],
    outGoingPerson: data.outGoingPerson ?? '',
    custodian: data.custodian ?? '',
    source: data.source,
    field: data.field,
    // reviewer: data.reviewer,
    // reviewers: data.reviewers,
  };
}
function getCurrentHandlerVo(data: CodeWishDto): string {
  return NO_CURRENT_HANDLER_REVIEW_STATUS.includes(data.reviewStatus)
    ? ''
    : data.currentHandler ?? '';
}
export function getCodeWishFormModel(data: CodeWishVo): CodeWishFormModel {
  return {
    id: data.id,
    demoName: data.demoName,
    demoDescription: data.demoDescription,
    demoSubmitter: data.demoSubmitter,
    demoAuthor: data.demoAuthor,
    expectedTime: new Date(data.expectedTime).getTime(),
    currentHandler: data.currentHandler,
    demoLink: data.demoLink,
    promisedTime: data.promisedTime ? new Date(data.promisedTime).getTime() : null,
    reviewStatus: data.reviewStatus,
    reviewSuggestions: data.reviewSuggestions,
    requirementReviewSuggestions: data.requirementReviewSuggestions,
    reviewer: data.reviewer,
    linkDemoId: data.linkDemoId,
    linkDemoName: data.linkDemoName,
    linkDemoReviewStatus: data.linkDemoReviewStatus,
    isAutoWish: data.isAutoWish,
    reviewers: data.reviewers,
    score: String(data.score),
    demoModificationType: data.demoModificationType,
    imgList: data.imgList,
    outGoingPerson: data.outGoingPerson,
    custodian: data.custodian,
    source: data.source,
    field: data.field,
  };
}
