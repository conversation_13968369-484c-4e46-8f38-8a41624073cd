export const mockData = {
  '2024-09-24': {
    闭环: 111,
    跟踪: 222,
    待锁定: 333,
    待提醒: 444,
    待处理: 555,
    挂起: 666,
    总量: 777,
  },
  '2024-09-25': {
    闭环: 111,
    跟踪: 222,
    待锁定: 333,
    待提醒: 444,
    待处理: 555,
    挂起: 777,
    总量: 777,
  },
};

export const mockDataProblem = [
  {
    app_Name: '235553449',
    ewp_owner: '2024/8/29',
    app_type: '4796应用',
    app_type2: '新华大字典',
    app_level: '新华大字典无法华为账号登录',
    app_level2: '',
    app_level3: 'FUT',
    app_level4: '1',
    app_level5: '',
    app_level6: 'Mate60 Pro',
    app_level7: 'Analyzed(pending)',
    app_level8: '低',
    app_level9: '',
    app_level10: '',
    app_level11: '',
    app_level12: '',
    app_level13: 'ALN-AL00-5.0.0.60(SP12DEVC00E61R4P9)',
    app_level14: '新华大字典，用华为账号无法登录。',
    app_level15: '孔维宇',
    app_level16: '',
  },
  {
    app_Name: '235540791',
    ewp_owner: '2024/8/29',
    app_type: '游戏',
    app_type2: '侠隐风云',
    app_level: '侠隐风云数据丢失',
    app_level2: '一般',
    app_level3: 'FUT',
    app_level4: '1',
    app_level5: '最新进展：问题未复现，继续跟踪',
    app_level6: 'Mate60 Pro',
    app_level7: 'Analyzed(pending)',
    app_level8: '低',
    app_level9: '刘思源 00490867',
    app_level10: '曾才惠 00534823',
    app_level11: '不复现',
    app_level12: '',
    app_level13: 'ALN-AL00-5.0.0.60(SP12DEVC00E61R5P9)',
    app_level14:
      '侠隐风云，游戏数据丢失之前在里面玩的角色人物在今天晚上登录游戏显示里面的游戏角色人物都已经没有了',
    app_level15: '孔维宇',
    app_level16: '数据丢失',
  },
];
