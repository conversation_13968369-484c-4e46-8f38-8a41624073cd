<template>
  <div>
    <n-card style="margin-bottom: 10px">
      <n-form
        :model="searchForm"
        label-placement="left"
        label-width="110"
        require-mark-placement="right-hanging"
        size="medium"
        v-if="collapse"
      >
        <n-grid :cols="4" :x-gap="24">
          <n-form-item-gi
            v-for="item in searchFormItems"
            :key="item.field"
            :label="item.label"
            :path="item.field"
          >
            <n-input
              v-if="item.component === 'Input'"
              placeholder="输入关键字查找"
              v-model:value="searchForm[item.field]"
            />
          </n-form-item-gi>
        </n-grid>
      </n-form>
      <n-space justify="center">
        <n-button secondary strong type="default" @click="refreshSearch()"> 重置 </n-button>
        <n-button secondary strong type="primary" @click="formSearch()"> 查询 </n-button>
        <n-button type="primary" icon-placement="right" @click="collapse = !collapse">
          <template #icon>
            <n-icon size="14" class="unfold-icon" v-if="collapse">
              <UpOutlined />
            </n-icon>
            <n-icon size="14" class="unfold-icon" v-else>
              <DownOutlined />
            </n-icon>
          </template>
          {{ collapse ? '收起' : '展开' }}
        </n-button>
      </n-space>
    </n-card>
    <n-card>
      <n-space>
        <n-button secondary strong type="warning" @click="search()">
          <template #icon>
            <n-icon>
              <Refresh />
            </n-icon>
          </template>
          刷新
        </n-button>
        <n-button secondary strong type="primary" @click="showListImportModel = true">
          导入
        </n-button>
        <n-button secondary strong type="primary" @click="handleExportIR()"> 导出 </n-button>
      </n-space>
      <n-data-table
        remote
        :columns="columns"
        :data="data"
        :pagination="paginationReactive"
        :loading="loading"
        style="margin-top: 20px"
        scroll-x
      />
    </n-card>
    <!-- 导入 -->
    <n-modal v-model:show="showListImportModel">
      <n-card
        style="width: 600px"
        title="批量导入"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-button text @click="downloadTemplate" style="margin-bottom: 20px"
          >点击下载导入模板</n-button
        >
        <n-upload
          action="#"
          :custom-request="customRequest"
          accept=".xls,.xlsx,"
          :max="1"
          directory-dnd
        >
          <n-upload-dragger>
            <div style="margin-bottom: 12px">
              <n-icon size="48" :depth="3">
                <Add />
              </n-icon>
            </div>
            <n-text style="font-size: 16px"> 点击或者拖动文件到该区域来上传 </n-text>
          </n-upload-dragger>
        </n-upload>
      </n-card>
    </n-modal>
    <!-- 编辑 -->
    <n-modal v-model:show="showEditModal">
      <n-card
        style="width: 1200px"
        title="编辑工单信息"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-form
          ref="formRef"
          :model="editableFormData"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="medium"
          label-align="right"
        >
          <n-grid :cols="24" :x-gap="24">
            <n-form-item-gi
              :span="12"
              v-for="item in editFormItems"
              :key="item.field"
              :label="item.label"
              :path="item.field"
            >
              <n-input
                v-if="item.component === 'Input'"
                v-model:value="editableFormData[item.field]"
                :disabled="item.field === 'problemId'"
              />
              <n-date-picker
                v-if="item.component === 'DatePicker'"
                v-model:value="editableFormData[item.field]"
                type="datetime"
                clearable
                format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              />
            </n-form-item-gi>
          </n-grid>
        </n-form>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" @click="editSubmit"> 确认 </n-button>
            <n-button secondary strong type="error" @click="showEditModal = false"> 取消 </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, reactive, h } from 'vue';
  import { Add, Refresh } from '@vicons/ionicons5';
  import { DownOutlined, UpOutlined } from '@vicons/antd';
  import { debounce, cloneDeep } from 'lodash-es';
  import { UploadCustomRequestOptions, useMessage, NButton, useDialog } from 'naive-ui';
  import { defaultSearchForm, searchFormItems, editFormItems } from './index';
  import { communityDelete } from '@/api/developerCommunity/issuePage';
  import {
    downloadTemplateService,
    importData,
    exportData,
    serachList,
    updateData,
  } from '@/api/developerCommunity/auditManagement';
  import dayjs from 'dayjs';

  const showListImportModel = ref(false); // 导入
  const loading = ref(false); // 表格loading
  const searchForm = ref(cloneDeep(defaultSearchForm)); // 筛选
  const collapse = ref(false); // 收起/展开
  const dialog = useDialog();
  const showEditModal = ref(false);
  const editableFormData = ref({}); // 编辑
  const data = ref([]); // 表格数据
  const message = useMessage();
  /**
   * 表格列
   */
  const columns = ref([
    {
      title: '问答ID',
      key: 'problemId',
      width: 210,
      fixed: 'left',
      ellipsis: {
        tooltip: true,
      },
      render(row) {
        return h(
          'a',
          {
            href: '#',
            onClick: (e) => {
              e.preventDefault();
              if (row.link) {
                window.open(row.link);
              }
            },
            style: {
              color: row.link ? '#1288ff' : '#333639',
              textDecoration: 'none',
              cursor: row.link ? 'pointer' : 'default',
            },
          },
          row.problemId
        );
      },
    },
    {
      title: '处理人',
      key: 'processorName',
      width: 160,
      render(row) {
        return `${row.processorName || ''}/${row.processorId || ''}`;
      },
    },
    {
      title: '交叉审核人',
      key: 'reviewer',
      width: 160,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '审核状态',
      key: 'reviewStatus',
      width: 160,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '审核问题分类',
      key: 'reviewResultCategory',
      width: 160,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '审核意见',
      key: 'reviewComments',
      width: 210,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '是否整改',
      key: 'reviewIsRectify',
      width: 160,
    },
    {
      title: '复审状态',
      key: 'reviewStatusAgain',
      width: 160,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '复审意见',
      key: 'reviewCommentsAgain',
      width: 210,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      title: '审核时间',
      key: 'reviewTime',
      width: 190,
      render(row) {
        return row.reviewTime ? dayjs(row.reviewTime).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '整改截止日期',
      key: 'rectifyDeadline',
      width: 190,
      render(row) {
        return row.rectifyDeadline ? dayjs(row.rectifyDeadline).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '操作',
      key: 'processorId',
      width: 160,
      fixed: 'right',
      render(row) {
        return [
          h(
            NButton,
            {
              strong: true,
              tertiary: true,
              type: 'info',
              size: 'small',
              style: 'margin-right: 12px',
              onClick: () => {
                showEditModal.value = true;
                editableFormData.value = cloneDeep(row);
              },
            },
            [h('div', '编辑')]
          ),
          h(
            NButton,
            {
              strong: true,
              tertiary: true,
              type: 'error',
              size: 'small',
              onClick: () => {
                dialog.error({
                  title: '警告',
                  content: '是否删除工单信息',
                  positiveText: '确定',
                  negativeText: '取消',
                  onPositiveClick: async () => {
                    try {
                      await communityDelete({
                        problemId: row.problemId,
                      });
                      message.success('删除成功');
                      search();
                    } catch {
                      message.error('删除失败');
                    }
                  },
                });
              },
            },
            [h('div', '删除')]
          ),
        ];
      },
    },
  ]);

  /**
   * 查询列表
   */
  const search = debounce(async () => {
    loading.value = true;
    try {
      const searchData = { ...searchForm.value };
      const res = await serachList({
        pageNumber: paginationReactive.value.page.toString(),
        pageSize: paginationReactive.value.pageSize.toString(),
        ...searchData,
      });
      if (res.status === '200') {
        data.value = res.data.data;
        paginationReactive.value.itemCount = res.data.page.total;
        message.success('查询成功！');
      } else {
        message.error('查询失败！');
      }
    } catch (error) {}
    loading.value = false;
  }, 300);

  /**
   * 下载导入模板
   */
  const downloadTemplate = () => {
    downloadTemplateService()
      .then((res) => {
        if (!res) {
          message.error('下载失败！');
        }
      })
      .catch((e) => {
        message.error(`下载失败，原因：${e?.response?.data?.message}`);
      });
  };

  /**
   * 导入
   */
  const customRequest = async ({ file, onError }: UploadCustomRequestOptions) => {
    const formData = new FormData();
    formData.append('file', file.file as File);
    try {
      let res = await importData(formData);
      if (res.status == '200') {
        search();
        message.success('导入成功');
      } else {
        message.error('导入失败！');
      }
    } catch (err) {
      onError();
      message.error(`导入失败，原因：${err?.response?.data?.message}`);
    } finally {
      showListImportModel.value = false;
    }
  };

  /**
   * 导出
   */
  const handleExportIR = () => {
    exportData(searchForm.value)
      .then((res) => {
        if (!res) {
          message.error('导出失败！');
        }
      })
      .catch((e) => {
        message.error(`导出失败，原因：${e?.response?.data?.message}`);
      });
  };

  /**
   * 查询
   */
  const formSearch = () => {
    paginationReactive.value.page = 1;
    search();
  };

  /**
   * 重置
   */
  const refreshSearch = () => {
    searchForm.value = cloneDeep(defaultSearchForm);
    paginationReactive.value.page = 1;
    search();
  };

  /**
   * 分页
   */
  const paginationReactive = ref(
    reactive({
      page: 1,
      pageSize: 10,
      pageCount: 10,
      itemCount: 10,
      showSizePicker: true,
      pageSizes: [10, 20, 50],
      prefix({ itemCount }) {
        return `总数：${itemCount}`;
      },
      onChange: (page: number) => {
        paginationReactive.value.page = page;
        search();
      },
      onUpdatePageSize: (pageSize: number) => {
        paginationReactive.value.pageSize = pageSize;
        paginationReactive.value.page = 1;
        search();
      },
    })
  );

  /**
   * 编辑确认
   */
  const editSubmit = async () => {
    try {
      const submitData = cloneDeep(editableFormData.value);
      submitData.reviewTime = submitData.reviewTime
        ? dayjs(submitData.reviewTime).format('YYYY-MM-DD HH:mm:ss')
        : '';
      submitData.rectifyDeadline = submitData.rectifyDeadline
        ? dayjs(submitData.rectifyDeadline).format('YYYY-MM-DD HH:mm:ss')
        : '';
      console.log(submitData);
      const res = await updateData(submitData);
      if (res.status === '200') {
        message.success('更新成功！');
        search();
      } else {
        message.success('更新失败！');
      }
    } catch (error) {
      Error();
      message.error(`编辑失败，原因：${error?.response?.data?.message}`);
    } finally {
      showEditModal.value = false;
    }
  };

  onMounted(() => {
    search();
  });
</script>
