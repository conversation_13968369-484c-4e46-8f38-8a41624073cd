<template>
  <div>
    <div class="layout-page-header">
      <n-space align="center">
        <n-button secondary strong type="warning" @click="getCodeList()">
          <template #icon>
            <n-icon>
              <Refresh/>
            </n-icon>
          </template>
          刷新
        </n-button>
        <n-input-group>
          <n-select v-model:value="filterStatus.searchKey" :options="SEARCH_FILTER_OPTIONS" style="width: 90px"/>
          <n-input
            v-model:value="filterStatus.searchValue"
            type="text"
            placeholder="选择输入名称或ID"
            style="min-width: 300px"
            @keyup.enter="getCodeList"
            clearable
          />
        </n-input-group>
        <n-checkbox v-if="!isAdmin" v-model:checked="filterStatus.onlyMine">
          只看我的
        </n-checkbox>
      </n-space>
    </div>
    <n-data-table
      :columns="codeDemoColumns"
      :data="data"
      :pagination="codeDemoPagination"
      :loading="isLoading"
      @update:filters="handleFiltersChange"
      remote
      style="margin-top: 20px"
    />
    <!--删除弹窗-->
    <n-modal v-model:show="showDeleteModal" :on-after-leave="initDeleteInfo">
      <n-card
        style="width: 600px"
        title="删除样例代码"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <template #header-extra></template>
        <n-form
          ref="deleteFormRef"
          :model="deleteInfo"
          :rules="formRules"
          label-placement="left"
          label-width="auto"
          require-mark-placement="right-hanging"
          size="medium"
          :style="{
            maxWidth: '640px',
          }"
        >
          <n-form-item label="删除原因" path="deleteReason">
            <n-input type="textarea" v-model:value="deleteInfo.deleteReason"
                     placeholder="请输入删除原因"/>
          </n-form-item>
        </n-form>
        <template #footer>
          <n-space>
            <n-button secondary strong type="primary" @click="handleDeleteSubmit"> 确认</n-button>
            <n-button secondary strong type="error" @click="showDeleteModal = false"> 取消
            </n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
    <!--外溢记录弹窗-->
    <n-modal v-model:show="showOutgoingRecordsModal" :on-after-leave="initModel">
      <n-card
        style="width: 1000px"
        title="外发记录"
        :bordered="false"
        size="huge"
        role="dialog"
        aria-modal="true"
      >
        <n-data-table
          :columns="outgoingRecordsColumns"
          :data="outgoingRecords"
          :pagination="outgoingRecordsPagination"
          :loading="isLoading"
          :paginate-single-page="false"
          remote
        >
          <template #empty>
            <n-empty description="暂无外发记录"></n-empty>
          </template>
        </n-data-table>
        <template #footer>
          <n-space justify="center">
            <n-button secondary strong type="default" @click="showOutgoingRecordsModal = false"> 关闭</n-button>
          </n-space>
        </template>
      </n-card>
    </n-modal>
  </div>
</template>

<script lang="ts" setup>
import { Refresh } from '@vicons/ionicons5';
import {
  DataTableBaseColumn,
  DataTableColumns,
  DataTableFilterState,
  NButton,
  NIcon,
  useDialog,
  useMessage,
} from 'naive-ui';
import { h, reactive, ref, VNode, watch } from 'vue';
import {
  CodeDemoDto,
  deleteDemo,
  DeleteDemoReq,
  OutgoingRecordsDto,
  queryDemoList,
  QueryDemoParams,
  queryOutgoingRecords,
} from "@/api/system/code";
import { CodeDemoReviewStatusEnum } from "@/enums/CodeDemoReviewStatusEnum";
import {
  getDefaultDeleteInfo,
  getDefaultFilterStatus,
  isUserAdmin,
} from './index';
import {
  ALL_DEMO_TYPES,
  getDefaultDemoInfo,
  getFilterOptions,
} from "../consts";
import {  useUserStore } from "@/store/modules/user";
import { CODE_DEMO_COLUMNS, OUTGOING_RECORDS_COLUMNS } from "./columns";
import { CODE_OUTGOING_FORM_RULES } from "@/views/sampleCodeManage/codeOutgoing/formRules";
import { FilterStatus } from "./types";
import { SEARCH_FILTER_OPTIONS } from "./consts";
import { useRoute } from "vue-router";

const showOutgoingRecordsModal = ref(false);
const showDeleteModal = ref(false);
const isAdd = ref(false);
const isLoading = ref(false);
const userInfo = useUserStore().getUserInfo;
const isAdmin = isUserAdmin() || userInfo.isSuperAdmin;
const dialog = useDialog();
const deleteFormRef = ref();
const message = useMessage();
const route = useRoute();
const filterStatus = ref<FilterStatus>(getDefaultFilterStatus(route.query.demoId as string));
const data = ref<CodeDemoDto[]>([]);
const codeInfo = ref<CodeDemoDto>(getDefaultDemoInfo());
const outgoingRecords = ref<OutgoingRecordsDto[]>([]);
const deleteInfo = ref<DeleteDemoReq>(getDefaultDeleteInfo());
const reviewStatus = ref(null);
const typeOptions = getFilterOptions(ALL_DEMO_TYPES);
let formRules = CODE_OUTGOING_FORM_RULES;

watch(
  filterStatus,
  (filterStatus, prev) => {
    codeDemoPagination.page = 1;
    getCodeList()
  },
  {
    deep: true,
  }
)

// 获取操作按钮
function getOperations(row: CodeDemoDto): VNode[] {
  let isSubmitter = row.demoSubmitter && userInfo.label.includes(row.demoSubmitter);
  let isToBeModified = row.reviewStatus === CodeDemoReviewStatusEnum.TO_BE_MODIFIED;
  let result: VNode[] = [];
  // 显示外溢按钮：用户是管理员但不是提交人，且状态为通过
  // if (!isSubmitter && isAdmin && isToBeSpilled) {
  //   result.push(h(
  //     NButton,
  //     {
  //       strong: true,
  //       tertiary: true,
  //       type: 'info',
  //       size: 'medium',
  //       text: true,
  //       style: 'margin-right:20px',
  //       onClick: () => {
  //         codeInfo.value = {...row};
  //         showReviewInfoModal.value = true;
  //         reviewStatusOptions.value = getReviewOptions(row.reviewStatus!);
  //         codeInfo.value.reviewStatus = CodeDemoReviewStatusEnum.SPILLED;
  //       },
  //     },
  //     [h('div', '外溢')]
  //   ))
  // }
  // 显示外溢记录按钮：全部
  result.push(h(
      NButton,
      {
        strong: true,
        tertiary: true,
        type: 'info',
        size: 'medium',
        text: true,
        // 禁用：demoCount未0
        // disabled: row.demoCount > 0,
        style: 'margin-right:20px',
        onClick: () => {
          showOutgoingRecordsModal.value = true;
          codeInfo.value = {...row};
          getOutgoingRecords();
        },
      },
      [h('div', '外发记录')]
  ))
  // 显示删除按钮：用户是提交人且状态为待修改，或用户是管理员
  if ((isSubmitter && isToBeModified) || (isAdmin)) {
    result.push(h(
      NButton,
      {
        strong: true,
        tertiary: true,
        type: 'error',
        size: 'medium',
        text: true,
        onClick: () => {
          showDeleteModal.value = true;
          deleteInfo.value.deleteDemoIdList = [row.demoId];
        },
      },
      [h('div', '删除')]
    ))
  }
  return result;
}

const codeDemoColumns: DataTableColumns<CodeDemoDto> = [
  ...CODE_DEMO_COLUMNS,
  {
    title: '操作',
    key: '12%',
    resizable: true,
    minWidth: '50px',
    width: 120,
    ellipsis: {
      tooltip: true
    },
    render(row: CodeDemoDto) {
      return getOperations(row);
    },
  },
];
const codeDemoPagination = reactive({
  page: 1,
  pageSize: 5,
  showSizePicker: true,
  pageSizes: [5, 10, 20],
  itemCount: 0,
  prefix({ itemCount }) {
    return `总数：${itemCount}`;
  },
  onChange: (page: number) => {
    codeDemoPagination.page = page;
    getCodeList();
  },
  onUpdatePageSize: (pageSize: number) => {
    codeDemoPagination.pageSize = pageSize;
    codeDemoPagination.page = 1;
    getCodeList();
  },
});
const outgoingRecordsColumns: DataTableColumns<OutgoingRecordsDto> = OUTGOING_RECORDS_COLUMNS;
const outgoingRecordsPagination = reactive({
  page: 1,
  pageSize: 5,
  showSizePicker: true,
  pageSizes: [5, 10, 20],
  itemCount: 0,
  onChange: (page: number) => {
    outgoingRecordsPagination.page = page;
    getOutgoingRecords();
  },
  onUpdatePageSize: (pageSize: number) => {
    outgoingRecordsPagination.pageSize = pageSize;
    outgoingRecordsPagination.page = 1;
    getOutgoingRecords();
  },
});
// 获取demo列表
const getCodeList = async () => {
  isLoading.value = true;
  data.value = [];
  let demo: QueryDemoParams = {};
  if (filterStatus.value.searchValue) {
    demo[filterStatus.value.searchKey] = filterStatus.value.searchValue;
  }
  if (filterStatus.value.apiVersion?.length) {
    demo.apiVersionList = filterStatus.value.apiVersion;
  }
  if (filterStatus.value.reviewStatus?.length) {
    demo.reviewStatusList = filterStatus.value.reviewStatus;
  }
  if (filterStatus.value.selectValue?.length) {
    demo.selectValueList = filterStatus.value.selectValue;
  }
  if (filterStatus.value.onlyMine) {
    demo.demoSubmitter = userInfo.label;
  }
  const response = await queryDemoList({
    pageNum: codeDemoPagination.page,
    pageSize: codeDemoPagination.pageSize,
    demo,
  });
  if (response?.data) {
    data.value = response.data.data;
    codeDemoPagination.itemCount = response.data.pageInfo?.total;
  }
  isLoading.value = false;
};

function initModel() {
  codeInfo.value = getDefaultDemoInfo();
}

function initDeleteInfo() {
  deleteInfo.value = getDefaultDeleteInfo();
}
const handleDeleteSubmit = async () => {
  deleteFormRef.value.validate(async (errors) => {
    if (!errors) {
      deleteDemo(deleteInfo.value).then((res) => {
        if (res?.data) {
          // todo: 自动生成的demo删除关联心愿单
          message.success('删除样例代码成功！');
          showDeleteModal.value = false;
          getCodeList();
        } else {
          message.error('删除样例代码失败！');
        }
      }).catch((e) => {
        message.error(`删除样例代码失败，原因：${e?.response?.data?.error}`);
      })
    } else {
      console.log(errors)
    }
  });
}
const handleFiltersChange = (filters: DataTableFilterState, initiatorColumn: DataTableBaseColumn) => {
  filterStatus.value[initiatorColumn.key] = filters[initiatorColumn.key]
}
const getOutgoingRecords = () => {
  isLoading.value = true;
  outgoingRecords.value = [];
  queryOutgoingRecords({
    demoId: codeInfo.value.demoId,
    pageNum: 1,
    pageSize: 10,
  }).then(res => {
    if (res?.data?.data) {
      outgoingRecords.value = res.data.data;
      outgoingRecordsPagination.itemCount = res.data.pageInfo?.total;
    }
  }).finally(() => {
    isLoading.value = false;
  })
}

// 初始化获取样例代码列表
getCodeList();
</script>

<style lang="less" scoped>
.layout-page-header {
  margin-top: 20px;

  .n-space {
    width: 30%;
  }
}
</style>
