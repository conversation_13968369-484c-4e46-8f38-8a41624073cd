export function formatDateTime(date, format) {
  date = new Date(date);
  const o = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours() % 12 === 0 ? 12 : date.getHours() % 12, // 小时
    'H+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
    a: date.getHours() < 12 ? '上午' : '下午', // 上午/下午
    A: date.getHours() < 12 ? 'AM' : 'PM', // AM/PM
  };
  if (/(y+)/.test(format)) {
    format = format.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length));
  }
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      );
    }
  }
  return format;
}

// 获取当前时间的时间戳
var currentTimeStamp = Date.now();
// 获取七天前的时间戳
var sevenDaysAgoTimeStamp = currentTimeStamp - 7 * 24 * 60 * 60 * 1000;

// 将时间戳转换为日期对象
var sevenDaysAgoDate = new Date(sevenDaysAgoTimeStamp);

// 将日期对象设置为零点
sevenDaysAgoDate.setHours(0, 0, 0, 0);

// 获取零点的时间戳
var sevenDaysAgoMidnightTimeStamp = sevenDaysAgoDate.getTime();
export function getDefaultProblemHandleFilters() {
  return {
    lastTestConclusionList: [
      'KCP5：通过',
      'KCP5：不通过',
      'KCP5：驳回',
      'KCP5：带风险通过',
      'KCP6：通过',
      'KCP6：不通过',
      'KCP6：驳回',
      'KCP6：带风险通过',
      'KCP7：通过',
      'KCP7：不通过',
      'KCP7：驳回',
      'KCP7：带风险通过',
    ],
    roundList: [],
    functionEnhancementPriorityList: [],
    label: [],
    isServiceCompanyUndertakes: '',
    dateList: [sevenDaysAgoMidnightTimeStamp, currentTimeStamp],
    dateType: '1',
    isOnlyAppId: '1',
    onlyTop2k:true
  };
}
export const testResList = [
  {
    label: 'KCP5：通过',
    value: 'KCP5：通过',
  },
  {
    label: 'KCP5：不通过',
    value: 'KCP5：不通过',
  },
  {
    label: 'KCP5：驳回',
    value: 'KCP5：驳回',
  },
  {
    label: 'KCP5：测试中',
    value: 'KCP5：测试中',
  },
  {
    label: 'KCP5：带风险通过',
    value: 'KCP5：带风险通过',
  },
  {
    label: 'KCP6：通过',
    value: 'KCP6：通过',
  },
  {
    label: 'KCP6：不通过',
    value: 'KCP6：不通过',
  },
  {
    label: 'KCP6：驳回',
    value: 'KCP6：驳回',
  },
  {
    label: 'KCP6：测试中',
    value: 'KCP6：测试中',
  },
  {
    label: 'KCP6：带风险通过',
    value: 'KCP6：带风险通过',
  },
  {
    label: 'KCP7：通过',
    value: 'KCP7：通过',
  },
  {
    label: 'KCP7：不通过',
    value: 'KCP7：不通过',
  },
  {
    label: 'KCP7：驳回',
    value: 'KCP7：驳回',
  },
  {
    label: 'KCP7：测试中',
    value: 'KCP7：测试中',
  },
  {
    label: 'KCP7：带风险通过',
    value: 'KCP7：带风险通过',
  },
];
export const getLastIndex = (index, names, types, data) => {
  const sameNameIndex = data.slice(index).findIndex((item) => {
    return names.some((name, i) => item[types[i]] !== name);
  });
  return sameNameIndex === -1 ? data.length : sameNameIndex;
};
export const functionEnhancementPriorityList = [
  {
    label: '高',
    value: '高',
  },
  {
    label: '中',
    value: '中',
  },
  {
    label: '中2',
    value: '中2',
  },
  {
    label: '低',
    value: '低',
  },
];
export const labelList = [
  {
    label: '4796',
    value: '4796',
  },
  {
    label: '生态丰富度5165',
    value: '生态丰富度5165',
  },
  {
    label: '垂域专精',
    value: '垂域专精',
  },
  {
    label: '心愿单',
    value: '心愿单',
  },
  {
    label: '企业内部办公应用',
    value: '企业内部办公应用',
  },
  {
    label: '头部互联网',
    value: '头部互联网',
  },
  {
    label: '区域重点',
    value: '区域重点',
  },
  {
    label: '行业总部',
    value: '行业总部',
  },
];
export const isServiceList = [
  {
    label: '是',
    value: '是',
  },
  {
    label: '否',
    value: '否',
  },
];
